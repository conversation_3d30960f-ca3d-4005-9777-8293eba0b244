{"dart.flutterWebRenderer": "canvaskit", "dart.flutterGenerateLocalizationsOnSave": "manualIfDirty", "dart.flutterSdkPath": "/Users/<USER>/fvm/versions/3.27.4", "dart.sdkPath": "/Users/<USER>/fvm/versions/3.27.4/bin/cache/dart-sdk", "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"tsconfig.json": "tsconfig.*.json, env.d.ts", "vite.config.*": "jsconfig*, vitest.config.*, cypress.config.*, playwright.config.*", "package.json": "package-lock.json, pnpm*, .yarnrc*, yarn*, .eslint*, eslint*, .prettier*, prettier*, .editorconfig"}, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[svelte]": {"editor.defaultFormatter": "svelte.svelte-vscode", "editor.formatOnSave": true}, "prettier.documentSelectors": ["**/*.svelte"], "prettier.requireConfig": true}