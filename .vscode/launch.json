{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "fiaranow_flutter",
      "cwd": "fiaranow_flutter",
      "request": "launch",
      "type": "dart",
      "deviceId": "emulator-5554",
    },
    {
      "name": "fiaranow_flutter (profile mode)",
      "cwd": "fiaranow_flutter",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "fiaranow_flutter (release mode on Xiaomi 12 Pro)",
      "cwd": "fiaranow_flutter",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release",
      "deviceId": "22081212UG"
    },
    {
      "name": "fiaranow_flutter (Xiaomi 12 Pro)",
      "cwd": "fiaranow_flutter",
      "request": "launch",
      "type": "dart",
      "deviceId": "22081212UG"
    },
    {
      "name": "fiaranow_flutter (Poco X3 Pro)",
      "cwd": "fiaranow_flutter",
      "request": "launch",
      "type": "dart",
      "deviceId": "M2102J20SG"
    },
    {
      "name": "fiaranow_flutter (Pixel 9 Emulator)",
      "cwd": "fiaranow_flutter",
      "request": "launch",
      "type": "dart",
      "deviceId": "emulator-5556"
    },
    {
      "type": "node",
      "request": "attach",
      "name": "Attach to Functions Emulator",
      "port": 9229,
      "restart": true,
    },
    {
      "name": "fiaranow_flutter (iPhone Driver, 12 Pro)",
      "cwd": "fiaranow_flutter",
      "request": "launch",
      "type": "dart",
      "deviceId": "A5AE0777-9F99-45C0-A359-FA7B9F94D8BB"
    },
    {
      "name": "fiaranow_flutter (iPhone 16 Pro)",
      "cwd": "fiaranow_flutter",
      "request": "launch",
      "type": "dart",
      "deviceId": "60B14CF3-B8B1-46D0-B3A4-1D3183040882"
    },
    {
      "name": "My iPhone",
      "cwd": "fiaranow_flutter",
      "request": "launch",
      "type": "dart",
      "deviceId": "00008140-00120D223C13001C"
    }
  ]
}