# Migration Flow Documentation

This document outlines the comprehensive migration workflow system for the Fiaranow project. The system tracks migration execution state in Firestore and provides detailed logging, error handling, and progress monitoring through the admin interface.

## 1. Overview

The migration system consists of:
- **Migration Scripts**: Stored in `firebase/data-tools/migrations/` folder, versioned by script ID and version with counter prefix
- **Migration Tracker**: Firestore collection `/migrations` that tracks execution state
- **Migration Runner**: Enhanced `run-migrations.js` script that handles execution and state tracking
- **Admin Interface**: Management page at `/zedeck/migrations` for monitoring and reports
- **Maintenance Mode**: Integration with `global_configurations/appVersion` for app offline control

## 2. Migration Document Schema

Each migration is tracked in the `/migrations` collection with the following structure:

```typescript
interface MigrationDocument {
  // Basic Information
  id: string;                    // Migration script ID with counter prefix (e.g., "001__add-tenant-support")
  title: string;                 // Human-readable title
  description: string;           // Detailed description of what the migration does
  version: string;               // Semantic version (e.g., "1.0.0", "1.1.0")
  
  // Execution State
  status: 'running' | 'completed' | 'failed' | 'partial';
  startedAt: Date;               // When migration started
  completedAt?: Date;            // When migration completed (success or failure)
  
  // Progress Tracking
  totalSteps: number;            // Total operations to be performed
  completedSteps: number;        // Operations completed successfully
  skippedSteps: number;          // Operations skipped
  failedSteps: number;           // Operations that failed
  
  // Error Handling & Logging
  errors: Array<{
    message: string;             // Error message
    context: Record<string, any>; // Additional context (IDs, data, etc.)
    timestamp: Date;             // When error occurred
    step: string;                // Which step failed
  }>;
  
  logs: Array<{
    level: 'info' | 'warning' | 'error';
    message: string;
    timestamp: Date;
    context?: Record<string, any>;
  }>;
  
  // Configuration
  dryRun: boolean;               // Whether this was a dry run
  targetEnvironment: 'emulator' | 'production';
  maintenanceModeSet: boolean;   // Whether app was put in maintenance mode
  
  // Metadata
  executedBy: string;            // Who executed the migration
  executionHost: string;         // Machine/environment where executed
  
  // Rollback Information (if applicable)
  rollbackPossible: boolean;     // Whether migration can be rolled back
  rollbackSteps?: Array<{
    description: string;
    action: string;
  }>;
  
  // Dependencies
  dependencies?: string[];       // Other migration IDs that must run first
  
  // Migration-specific fields can be added by individual scripts
}
```

## 3. Migration Lifecycle States

### 3.1 Status Definitions

- **`running`**: Migration is currently being executed  
- **`completed`**: Migration finished successfully  
- **`failed`**: Migration failed and could not complete
- **`partial`**: Migration partially completed with some failures

### 3.2 State Transitions

```
running → completed
       → failed  
       → partial
```

### 3.3 Recovery Scenarios

- **Failed migrations**: Can be re-run with same or higher version
- **Partial migrations**: Resume from where they left off
- **Completed migrations**: Cannot re-run same version, but can run higher version

## 4. Version Control and Execution Rules

### 4.1 Version-Based Execution

- Each migration script has an `id` (with counter prefix) and `version`
- Same script with higher version can be executed even if lower version completed
- Same script with same version cannot be re-executed if already completed
- Version comparison follows semantic versioning (1.0.0 < 1.1.0 < 2.0.0)

### 4.2 Pre-Execution Checks

- Verify migration ID and version against existing records
- Check if migration dependencies are satisfied
- Validate migration metadata
- Ensure database connectivity
- Determine if maintenance mode should be activated

## 5. Maintenance Mode Integration

For migrations that require app downtime:

```javascript
// Set maintenance mode before critical migration
await db.collection('global_configurations').doc('appVersion').update({
  underMaintenanceMessage: 'System maintenance in progress. Please try again later.'
});

// Clear maintenance mode after migration
await db.collection('global_configurations').doc('appVersion').update({
  underMaintenanceMessage: null
});
```

The `underMaintenanceMessage` field controls app accessibility - when set, mobile apps will block user access.

## 6. Migration Script Structure

### 6.1 Enhanced Migration Template

```javascript
/**
 * Migration: [Migration Name]
 * Version: 1.0.0
 * Description: [What this migration does]
 * 
 * Dependencies: [] // Array of migration IDs that must run first
 * Rollback: possible | not-possible
 * MaintenanceRequired: true | false
 */

module.exports = {
  // Metadata
  id: '001__migration-name',     // Counter prefix + migration name
  title: 'Human Readable Migration Title',
  description: 'Detailed description of what this migration accomplishes',
  version: '1.0.0',
  rollbackPossible: true,
  dependencies: [], // ['001__other-migration-id']
  maintenanceRequired: false, // Whether to set maintenance mode
  
  // Execution function
  async run({ db, auth, FieldValue, isDryRun, migrationTracker, log, logSuccess, logError, logWarning }) {
    // Set total steps for progress tracking
    await migrationTracker.setTotalSteps(1000);
    
    // Set maintenance mode if required
    if (this.maintenanceRequired && !isDryRun) {
      await migrationTracker.setMaintenanceMode('Migration in progress...');
    }
    
    try {
      // Step 1: Process collection A
      await migrationTracker.startStep('process-collection-a', 'Processing collection A');
      const collectionA = await db.collection('collection_a').get();
      
      for (const doc of collectionA.docs) {
        try {
          // Process document
          await processDocument(doc);
          await migrationTracker.recordSuccess();
        } catch (error) {
          await migrationTracker.recordFailure(error, { 
            documentId: doc.id, 
            step: 'process-collection-a' 
          });
        }
      }
      
      // Log important information (use sparingly)
      await migrationTracker.log('info', `Processed ${collectionA.size} documents`);
      
      // Migration-specific data can be added directly to the document
      await migrationTracker.updateMigrationDoc({
        processedCollections: ['collection_a'],
        customMetric: collectionA.size
      });
      
    } catch (error) {
      await migrationTracker.recordCriticalFailure(error);
      throw error;
    } finally {
      // Clear maintenance mode if it was set
      if (this.maintenanceRequired && !isDryRun) {
        await migrationTracker.clearMaintenanceMode();
      }
    }
  },
  
  // Optional: Rollback function
  async rollback({ db, auth, FieldValue, migrationTracker }) {
    // Rollback logic if rollbackPossible is true
  }
};
```

### 6.2 Migration Tracker API

```javascript
class MigrationTracker {
  constructor(migrationId, migrationVersion, db);
  
  // Setup methods
  async initialize(migrationMetadata);
  async setTotalSteps(count);
  async startStep(stepId, description);
  
  // Progress tracking
  async recordSuccess();
  async recordSkipped();
  async recordFailure(error, context = {});
  async recordCriticalFailure(error);
  
  // Logging (use sparingly - only for crucial information)
  async log(level, message, context = {});
  
  // Maintenance mode
  async setMaintenanceMode(message);
  async clearMaintenanceMode();
  
  // Document updates
  async updateMigrationDoc(fields);
  
  // State management
  async markCompleted();
  async markFailed();
  async markPartial();
}
```

## 7. Database Collections

### 7.1 `/migrations` Collection

- **Document ID**: `{migrationId}-v{version}` (e.g., "001__add-tenant-support-v1.0.0")
- **Purpose**: Track migration execution state and store all logs/reports
- **Size Limit**: Keep under 1MB per document
- **Indexing**: Index on `id`, `version`, `status`, `startedAt`

## 8. Admin Interface

### 8.1 Migration Management Page

**Route**: `/zedeck/migrations`

**Features**:
- List all migrations with latest executions at the top
- Show status, version, and execution time
- Real-time progress monitoring for running migrations
- Error details and recovery recommendations

### 8.2 Migration Detail View

**Route**: `/zedeck/migrations/{migrationId}-v{version}`

**Features**:
- Complete migration report generated by the script
- Progress breakdown (total/completed/skipped/failed steps)
- Error logs with context
- Custom fields added by the migration script
- Execution timeline and performance metrics

## 9. Migration Management Commands

### 9.1 CLI Commands

```bash
# Run a specific migration
node run-migrations.js migration-name [--emulator] [--dry-run]

# Check migration status
node run-migrations.js --status migration-name

# List all migrations and their status
node run-migrations.js --list

# Rollback migration (if supported)
node run-migrations.js migration-name --rollback
```

**Note**: Migrations must be run one by one. No batch execution to ensure careful monitoring and control.

## 10. Best Practices

### 10.1 Migration Design

1. **Versioning**: Always increment version for script changes
2. **Counter Prefix**: Use sequential counter prefix for organization (001__, 002__, etc.)
3. **Idempotent Operations**: Migrations should be safe to run multiple times
4. **Batch Processing**: Use batched operations for large datasets
5. **Progress Tracking**: Provide meaningful progress updates
6. **Minimal Logging**: Log only crucial information to keep documents under 1MB
7. **Rollback Planning**: Design rollback procedures where possible

### 10.2 Error Handling

1. **Graceful Failures**: Handle partial failures appropriately
2. **Context Preservation**: Include relevant context in error reports
3. **Recovery Instructions**: Provide clear next steps in migration reports

### 10.3 Performance

1. **Batch Size**: Use appropriate batch sizes (500 operations max)
2. **Rate Limiting**: Respect Firestore rate limits
3. **Maintenance Mode**: Use sparingly and only when necessary

## 11. Examples

### 11.1 Simple Migration

```javascript
module.exports = {
  id: '002__add-default-settings',
  title: 'Add Default Settings to Users',
  description: 'Adds default notification settings to all users',
  version: '1.0.0',
  maintenanceRequired: false,
  
  async run({ db, migrationTracker }) {
    const users = await db.collection('users').get();
    await migrationTracker.setTotalSteps(users.size);
    
    let addedSettings = 0;
    
    for (const user of users.docs) {
      if (!user.data().settings) {
        await user.ref.update({
          settings: { notifications: true }
        });
        await migrationTracker.recordSuccess();
        addedSettings++;
      } else {
        await migrationTracker.recordSkipped();
      }
    }
    
    // Add custom data to migration document
    await migrationTracker.updateMigrationDoc({
      usersWithNewSettings: addedSettings,
      totalUsersChecked: users.size
    });
  }
};
```

### 11.2 Migration with Maintenance Mode

```javascript
module.exports = {
  id: '003__restructure-user-schema',
  title: 'Restructure User Data Schema',
  description: 'Major schema change requiring app downtime',
  version: '2.0.0',
  dependencies: ['002__add-default-settings'],
  rollbackPossible: true,
  maintenanceRequired: true,
  
  async run({ db, migrationTracker }) {
    // Maintenance mode will be set automatically
    
    await migrationTracker.log('info', 'Starting major schema restructure');
    
    // Complex migration logic with multiple steps
    await migrationTracker.startStep('backup-data', 'Creating data backup');
    // ... backup logic
    
    await migrationTracker.startStep('restructure-data', 'Restructuring data');
    // ... restructure logic
    
    // Custom tracking
    await migrationTracker.updateMigrationDoc({
      backupLocation: 'gs://backup-bucket/schema-migration-backup',
      affectedCollections: ['users', 'user_preferences']
    });
    
    // Maintenance mode will be cleared automatically
  }
};
```

## 12. Conclusion

This simplified but robust migration system provides:
- **Version-controlled execution** preventing accidental re-runs
- **Admin interface** for monitoring and reporting at `/zedeck/migrations`
- **Maintenance mode integration** for critical migrations
- **Comprehensive tracking** within migration documents
- **One-by-one execution** for careful control and monitoring

The system balances simplicity with robustness, ensuring migrations are properly tracked and managed while keeping the implementation straightforward and maintainable. 