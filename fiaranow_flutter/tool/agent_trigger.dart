#!/usr/bin/env dart

import 'dart:convert';
import 'dart:io';
import 'package:vm_service/vm_service.dart' as vm;
import 'package:vm_service/vm_service_io.dart';

void main(List<String> args) async {
  if (args.isEmpty) {
    print('Usage: dart run tool/agent_trigger.dart <VM_SERVICE_URI> [command] [options]');
    print('');
    print('VM Service URI format: ws://127.0.0.1:PORT/AUTH_CODE/ws');
    print('You can find this in the Flutter DevTools or when running with --observe');
    print('');
    print('Commands:');
    print('  auth <action>                    - Trigger authentication state changes');
    print('    Actions: signOut, refreshToken');
    print('');
    print('  config reload                    - Reload app configuration');
    print('');
    print('  connection <true|false>          - Simulate connection status');
    print('');
    print('  tendency <rideNow|reserve>       - Set user tendency');
    print('');
    print('  state                            - Get current app state');
    print('');
    print('Examples:');
    print('  dart run tool/agent_trigger.dart ws://127.0.0.1:12345/abc123/ws auth signOut');
    print('  dart run tool/agent_trigger.dart ws://127.0.0.1:12345/abc123/ws connection false');
    print('  dart run tool/agent_trigger.dart ws://127.0.0.1:12345/abc123/ws tendency reserve');
    exit(1);
  }

  var vmServiceUri = args[0];
  final command = args.length > 1 ? args[1] : 'state';
  final subCommand = args.length > 2 ? args[2] : '';

  // Auto-convert HTTP URI to WebSocket URI
  if (vmServiceUri.startsWith('http://')) {
    vmServiceUri = vmServiceUri.replaceFirst('http://', 'ws://');
    if (!vmServiceUri.endsWith('/ws')) {
      vmServiceUri = '$vmServiceUri/ws';
    }
    print('Auto-converted HTTP to WebSocket URI: $vmServiceUri');
  }

  try {
    print('Connecting to VM Service at: $vmServiceUri');
    
    final vmService = await vmServiceConnectUri(vmServiceUri);
    print('Connected successfully!');

    // Get the main isolate
    final vm.VM vmInfo = await vmService.getVM();
    final mainIsolate = vmInfo.isolates!.first;
    
    Map<String, dynamic>? response;
    
    switch (command) {
      case 'auth':
        if (subCommand.isEmpty) {
          print('Error: auth command requires an action (signOut, refreshToken)');
          exit(1);
        }
        print('Triggering authentication state change: $subCommand');
        response = await vmService.callServiceExtension(
          'ext.fiaranow.trigger_agent_action',
          isolateId: mainIsolate.id!,
          args: {
            'action': 'trigger_auth_action',
            'prompt': subCommand,
          },
        ).then((r) => r.json);
        break;
        
      case 'config':
        if (subCommand == 'reload') {
          print('Reloading app configuration...');
          response = await vmService.callServiceExtension(
            'ext.fiaranow.trigger_agent_action',
            isolateId: mainIsolate.id!,
            args: {
              'action': 'trigger_config_reload',
            },
          ).then((r) => r.json);
        } else {
          print('Error: config command requires "reload" subcommand');
          exit(1);
        }
        break;
        
      case 'connection':
        if (subCommand != 'true' && subCommand != 'false') {
          print('Error: connection command requires true or false');
          exit(1);
        }
        print('Simulating connection status: $subCommand');
        response = await vmService.callServiceExtension(
          'ext.fiaranow.trigger_agent_action',
          isolateId: mainIsolate.id!,
          args: {
            'action': 'simulate_connection_loss',
            'prompt': subCommand,
          },
        ).then((r) => r.json);
        break;
        
      case 'tendency':
        if (subCommand != 'rideNow' && subCommand != 'reserve') {
          print('Error: tendency must be either "rideNow" or "reserve"');
          exit(1);
        }
        print('Setting user tendency to: $subCommand');
        response = await vmService.callServiceExtension(
          'ext.fiaranow.trigger_agent_action',
          isolateId: mainIsolate.id!,
          args: {
            'action': 'change_user_tendency',
            'prompt': subCommand,
          },
        ).then((r) => r.json);
        break;
        
      case 'state':
        print('Getting current app state...');
        response = await vmService.callServiceExtension(
          'ext.fiaranow.trigger_agent_action',
          isolateId: mainIsolate.id!,
          args: {
            'action': 'get_state',
          },
        ).then((r) => r.json);
        break;
        
      default:
        print('Unknown command: $command');
        exit(1);
    }
    
    if (response != null) {
      print('\nResponse:');
      print(const JsonEncoder.withIndent('  ').convert(response));
    }
    
    await vmService.dispose();
    
  } catch (e) {
    print('Error: $e');
    print('');
    print('Make sure:');
    print('1. The Flutter app is running in debug mode');
    print('2. The VM Service URI is correct');
    print('3. You can get the URI by running Flutter with: flutter run --observe');
    exit(1);
  }
}