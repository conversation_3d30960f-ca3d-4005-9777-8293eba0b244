# Agent Trigger Tool

A command-line tool for programmatically controlling the Fiaranow Flutter app during development and testing.

## Prerequisites

- Flutter app must be running in debug mode
- VM Service must be accessible

## Getting the VM Service URI

When running Flutter in debug mode, use the `--observe` flag:

```bash
flutter run --observe
```

You'll see output like:
```
An Observatory debugger and profiler on iPhone is available at: http://127.0.0.1:12345/abc123/
The Flutter DevTools debugger and profiler on iPhone is available at: http://127.0.0.1:54321?uri=http://127.0.0.1:12345/abc123/
```

Convert the Observatory URL to a WebSocket URL:
- From: `http://127.0.0.1:12345/abc123/`
- To: `ws://127.0.0.1:12345/abc123/ws`

## Usage

```bash
dart run tool/agent_trigger.dart <VM_SERVICE_URI> [command] [options]
```

## Available Commands

### Authentication State Changes
```bash
# Sign out the current user
dart run tool/agent_trigger.dart ws://127.0.0.1:12345/abc123/ws auth signOut

# Refresh the authentication token
dart run tool/agent_trigger.dart ws://127.0.0.1:12345/abc123/ws auth refreshToken
```

### Configuration Management
```bash
# Reload app configuration from Firebase
dart run tool/agent_trigger.dart ws://127.0.0.1:12345/abc123/ws config reload
```

### Connection Simulation
```bash
# Simulate offline state
dart run tool/agent_trigger.dart ws://127.0.0.1:12345/abc123/ws connection false

# Simulate online state
dart run tool/agent_trigger.dart ws://127.0.0.1:12345/abc123/ws connection true
```

### User Preferences
```bash
# Set user tendency to "Ride Now"
dart run tool/agent_trigger.dart ws://127.0.0.1:12345/abc123/ws tendency rideNow

# Set user tendency to "Reserve"
dart run tool/agent_trigger.dart ws://127.0.0.1:12345/abc123/ws tendency reserve
```

### App State
```bash
# Get current app state
dart run tool/agent_trigger.dart ws://127.0.0.1:12345/abc123/ws state
```

## Integration with Agent Logging

All commands trigger corresponding agent logging events that can be viewed in:
- Flutter DevTools console
- Application logs
- Agent monitoring dashboards

Events are structured with:
- Event type (e.g., `agent.connection_status_changed`)
- Timestamp
- Relevant data payload

## Example Workflow

1. Start Flutter app with observation:
   ```bash
   flutter run --observe
   ```

2. Copy the VM Service URI from the output

3. Test connection loss handling:
   ```bash
   dart run tool/agent_trigger.dart ws://127.0.0.1:12345/abc123/ws connection false
   # Wait for app to react
   dart run tool/agent_trigger.dart ws://127.0.0.1:12345/abc123/ws connection true
   ```

4. View the agent logs in DevTools to see the triggered events