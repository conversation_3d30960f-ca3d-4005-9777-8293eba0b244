import 'package:cloud_functions/cloud_functions.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

import 'config/tenant_config.dart';
import 'models/Trip.dart';
import 'states/AuthState.dart';

// Logger for Firebase Cloud Functions
final Logger _logger = Logger('FirebaseCloudFunctions');

class LatLng {
  final double lat;
  final double lon;

  LatLng({required this.lat, required this.lon});

  Map<String, dynamic> toMap() {
    return {
      'lat': lat,
      'lon': lon,
    };
  }

  factory LatLng.fromMap(Map<String, dynamic> map) {
    return LatLng(
      lat: map['lat'],
      lon: map['lng'] ?? map['lon'],
    );
  }
}

class LatLngBounds {
  final LatLng northeast;
  final LatLng southwest;

  LatLngBounds({required this.northeast, required this.southwest});

  factory LatLngBounds.fromMap(Map<String, dynamic> map) {
    final northeastMap = Map<String, dynamic>.from(map['northeast']);
    final southwestMap = Map<String, dynamic>.from(map['southwest']);
    return LatLngBounds(
      northeast: LatLng.fromMap(northeastMap),
      southwest: LatLng.fromMap(southwestMap),
    );
  }
}

Future<RouteData> getRouteData(LatLng startLocation, LatLng arrivalLocation, {bool highQuality = false}) async {
  final HttpsCallable callable = FirebaseFunctions.instanceFor(region: 'europe-west3').httpsCallable('getRouteData');
  final response = await callable.call({
    'tenantId': TenantConfig.TENANT_ID,
    'startLocation': startLocation.toMap(),
    'arrivalLocation': arrivalLocation.toMap(),
    'highQuality': highQuality,
  });

  final Map<String, dynamic> data = Map<String, dynamic>.from(response.data as Map);
  return RouteData.fromMap(data);
}

Future<List<RouteData>> getMultipleRoutesData(LatLng startLocation, LatLng arrivalLocation, {bool highQuality = false}) async {
  try {
    final HttpsCallable callable = FirebaseFunctions.instanceFor(region: 'europe-west3').httpsCallable('getMultipleRoutesData');
    final HttpsCallableResult result = await callable.call({
      'startLocation': startLocation.toMap(),
      'arrivalLocation': arrivalLocation.toMap(),
      'highQuality': highQuality,
    });

    final List<dynamic> routesDataList = result.data;
    return routesDataList.map((routeData) {
      final Map<String, dynamic> routeDataMap = Map<String, dynamic>.from(routeData as Map);
      return RouteData.fromMap(routeDataMap);
    }).toList();
  } catch (e) {
    _logger.severe('❌ Error calling getMultipleRoutesData', e);
    rethrow;
  }
}

/// Cancels a driver request for a trip
Future<bool> cancelDriverRequest(String tripId) async {
  try {
    final HttpsCallable callable = FirebaseFunctions.instanceFor(region: 'europe-west3').httpsCallable('cancelDriverRequest');
    final response = await callable.call({
      'tenantId': TenantConfig.TENANT_ID,
      'tripId': tripId,
    });

    final data = Map<String, dynamic>.from(response.data);
    return data['success'] ?? false;
  } catch (e) {
    _logger.severe('Error cancelling driver request', e);
    return false;
  }
}

/// Driver accepts a trip request
Future<bool> driverAcceptRequest(String tripId, LatLng currentLocation) async {
  try {
    final authState = Get.find<AuthState>();
    final currentUser = authState.currentMobileUser.value;

    if (currentUser == null) {
      _logger.warning('Current user is null');
      return false;
    }

    final HttpsCallable callable = FirebaseFunctions.instanceFor(region: 'europe-west3').httpsCallable('driverAcceptRequest');
    final response = await callable.call({
      'tenantId': TenantConfig.TENANT_ID,
      'tripId': tripId,
      'driverUid': currentUser.uid,
      'currentLocation': currentLocation.toMap(),
      'driverInfo': {
        'displayName': currentUser.ensuredDisplayName,
        'photoURL': currentUser.photoURL,
        'phoneNumber': currentUser.phoneNumber,
      },
    });

    final data = Map<String, dynamic>.from(response.data);
    return data['success'] ?? false;
  } catch (e) {
    _logger.severe('Error accepting trip request', e);
    return false;
  }
}

/// Driver rejects a trip request
Future<bool> driverRejectRequest(String tripId, String reasonType, {String? customReason}) async {
  try {
    final authState = Get.find<AuthState>();
    final currentUser = authState.currentMobileUser.value;

    if (currentUser == null) {
      _logger.warning('Current user is null');
      return false;
    }

    final HttpsCallable callable = FirebaseFunctions.instanceFor(region: 'europe-west3').httpsCallable('driverRejectRequest');
    final response = await callable.call({
      'tenantId': TenantConfig.TENANT_ID,
      'tripId': tripId,
      'driverUid': currentUser.uid,
      'reasonType': reasonType,
      if (customReason != null) 'customReason': customReason,
    });

    final data = Map<String, dynamic>.from(response.data);
    return data['success'] ?? false;
  } catch (e) {
    _logger.severe('Error rejecting trip request', e);
    return false;
  }
}
