import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:fiaranow_flutter/models/Payment.dart';
import 'package:fiaranow_flutter/states/AppState.dart';
import 'package:fiaranow_flutter/states/NavigationState.dart';
import 'package:fiaranow_flutter/widgets/PassengerCountSlider.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Shows a payment method selection dialog and returns the selected payment method
/// Returns null if the user cancels the dialog
Future<String?> showPaymentMethodSelectionDialog(BuildContext context, {String? analyticsSource}) async {
  FirebaseAnalytics.instance
      .logEvent(name: 'payment_method_selection_shown', parameters: {'widget_name': analyticsSource ?? 'unknown'});

  final choice = await Get.dialog<String>(
    AlertDialog(
      title: Text(AppLocalizations.of(context)!.mapScreen_choosePaymentMethod), // "Choose a Payment Method"
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.money),
            title: Text(AppLocalizations.of(context)!.mapScreen_cashPayment), // "Cash Payment"
            onTap: () {
              Get.back(result: PaymentMethod.cash.name);
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.phone_android),
            title: Text(AppLocalizations.of(context)!.mapScreen_mobileMoneyPayment), // "Mobile Money Payment"
            onTap: () {
              Get.back(result: PaymentMethod.mobile.name);
            },
          ),
        ],
      ),
    ),
    barrierDismissible: true,
  );

  return choice;
}

/// Shows a passenger count selection dialog and returns the selected count
/// Returns null if the user cancels the dialog
Future<int?> showPassengerCountSelectionDialog(BuildContext context, {String? analyticsSource}) async {
  final navigationState = Get.find<NavigationState>();
  final appState = Get.find<AppState>();

  FirebaseAnalytics.instance
      .logEvent(name: 'passenger_count_selection_shown', parameters: {'widget_name': analyticsSource ?? 'unknown'});

  final choice = await Get.dialog<int>(
    AlertDialog(
      title: Text(AppLocalizations.of(context)!.passengerCountSlider_title), // "Number of Passengers"
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          PassengerCountSlider(
            passengerCount: navigationState.passengerCount,
            maxPassengers: appState.tripConfiguration.value.maxPassengerCount,
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                onPressed: () => Get.back(),
                child: Text(AppLocalizations.of(context)!.paymentDialog_cancel), // "Cancel"
              ),
              ElevatedButton(
                onPressed: () {
                  FirebaseAnalytics.instance.logEvent(
                    name: 'passenger_count_selected',
                    parameters: {
                      'count': navigationState.passengerCount.value.toString(),
                      'widget_name': analyticsSource ?? 'unknown'
                    },
                  );
                  Get.back(result: navigationState.passengerCount.value);
                },
                child: Text(AppLocalizations.of(context)!.passengerCountDialog_confirm), // "Confirm"
              ),
            ],
          ),
        ],
      ),
    ),
    barrierDismissible: true,
  );

  return choice;
}
