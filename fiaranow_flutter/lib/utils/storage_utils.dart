import 'package:firebase_storage/firebase_storage.dart';
import 'package:logging/logging.dart';

/// Utilities for handling Firebase Storage references
class StorageUtils {
  static final Logger _logger = Logger('StorageUtils');

  /// Extract the storage path from a full URL or return the path as-is
  static String getStoragePath(String urlOrPath) {
    // If it's already just a path, return it
    if (!urlOrPath.startsWith('http://') && !urlOrPath.startsWith('https://') && !urlOrPath.startsWith('gs://')) {
      return urlOrPath;
    }

    // Handle gs:// URLs
    if (urlOrPath.startsWith('gs://')) {
      final uri = Uri.parse(urlOrPath);
      // Remove the bucket name and return just the path
      return uri.path.startsWith('/') ? uri.path.substring(1) : uri.path;
    }

    // Handle http(s):// URLs from Firebase Storage
    try {
      final uri = Uri.parse(urlOrPath);
      // Extract path from Firebase Storage URL format
      // e.g., http://10.0.2.2:9199/v0/b/bucket/o/path%2Fto%2Ffile?alt=media
      final pathMatch = RegExp(r'/o/([^?]+)').firstMatch(uri.path);
      if (pathMatch != null) {
        // Decode the URL-encoded path
        return Uri.decodeComponent(pathMatch.group(1)!);
      }
    } catch (e) {
      _logger.severe('Error parsing storage URL', e);
    }

    // If we can't parse it, return as-is
    return urlOrPath;
  }

  /// Get a Firebase Storage reference from a path
  static Reference getStorageRef(String path) {
    return FirebaseStorage.instance.ref().child(path);
  }

  /// Get the download URL for a storage path
  static Future<String> getDownloadUrl(String path) async {
    try {
      _logger.info('📸 Getting download URL for path: $path');
      final ref = getStorageRef(path);
      final url = await ref.getDownloadURL();
      _logger.info('📸 Got download URL: $url');
      return url;
    } catch (e) {
      _logger.severe('❌ Error getting download URL for path $path', e);
      rethrow;
    }
  }

  /// Check if a string is a full URL
  static bool isFullUrl(String value) {
    return value.startsWith('http://') || value.startsWith('https://') || value.startsWith('gs://');
  }
}
