/// Tenant configuration for the Fiaranow app
/// This file contains the hardcoded tenant ID for the mobile app
class TenantConfig {
  /// The tenant ID for this app instance
  /// This should be configured per tenant deployment
  static const String TENANT_ID = 'fiaranow';

  /// Get the tenant-specific path for a collection
  static String getTenantPath(String collection) {
    return 'tenants/$TENANT_ID/$collection';
  }

  /// Get the tenant-specific collection reference path
  static String getTenantCollection(String collection) {
    return getTenantPath(collection);
  }
}