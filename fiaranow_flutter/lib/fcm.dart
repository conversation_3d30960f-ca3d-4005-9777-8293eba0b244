import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';
import 'package:permission_handler/permission_handler.dart'; // For PermissionStatus enum

import 'services/NotificationStateService.dart';

// Global logger for FCM functions
final Logger _logger = Logger('FCM');

bool isFlutterLocalNotificationsInitialized = false;
late FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin;
late AndroidNotificationChannel driverChannel;
late AndroidNotificationChannel passengerChannel;
late AndroidNotificationChannel passengerRingtoneChannel;
int? currentNotificationId;

/// Handle notification tap when app is in foreground or background
void _handleNotificationTap(String? payload) {
  if (payload == null) return;

  try {
    // Parse the payload to extract notification data
    // For chat notifications, payload contains sessionId and other data
    final Map<String, dynamic> data = {};

    // Split payload by & to get key-value pairs
    final pairs = payload.split('&');
    for (final pair in pairs) {
      final keyValue = pair.split('=');
      if (keyValue.length == 2) {
        data[keyValue[0]] = keyValue[1];
      }
    }

    _navigateBasedOnNotificationType(data);
  } catch (e) {
    _logger.severe('Error parsing notification payload', e);
  }
}

/// Navigate to appropriate screen based on notification type
void _navigateBasedOnNotificationType(Map<String, dynamic> data) {
  final type = data['type'];
  
  // Check if this is a mobile user notification
  final notificationId = data['notificationId'];
  if (notificationId != null) {
    // Handle through NotificationStateService
    if (Get.isRegistered<NotificationStateService>()) {
      final notificationService = Get.find<NotificationStateService>();
      notificationService.handleNotificationTap(data);
      return;
    }
  }

  switch (type) {
    case 'chat_message':
      final sessionId = data['sessionId'];
      final sessionTitle = data['sessionTitle'] ?? 'Chat Support'; // Default title

      if (sessionId != null) {
        _logger.info('📱 Navigating to chat session: $sessionId');
        Get.toNamed('/chat', arguments: {
          'sessionId': sessionId,
          'sessionTitle': sessionTitle,
        });
      }
      break;
      
    case 'driver_moving':
    case 'driver_arrived':
    case 'trip_paid':
    case 'reservation_reminder':
    case 'driver_timeout':
    case 'document_expiry':
    case 'trip_request':
    case 'trip_assignment':
      // Handle mobile user notifications
      if (Get.isRegistered<NotificationStateService>()) {
        final notificationService = Get.find<NotificationStateService>();
        notificationService.handleNotificationTap(data);
      }
      break;

    // Add other notification types here in the future
    default:
      _logger.info('📱 Unknown notification type: $type');
      break;
  }
}

/// Initialize notifications without requesting permissions immediately
Future<void> initializeFlutterNotifications() async {
  if (isFlutterLocalNotificationsInitialized) {
    return;
  }

  flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  if (Platform.isAndroid) {
    // Create an Android Notification Channel for drivers
    driverChannel = const AndroidNotificationChannel(
      'new_trip_notification', // channel ID
      'New Trip Notification', // channel name
      importance: Importance.max,
      sound: RawResourceAndroidNotificationSound('phone_ringtone_ultra'),
      playSound: true,
      description: 'Notification for new trip requests (for Drivers)',
      enableVibration: true,
      enableLights: true,
      showBadge: true,
    );

    // Create an Android Notification Channel for passengers
    passengerChannel = const AndroidNotificationChannel(
      'passenger_trip_notification', // channel ID
      'Trip Status Notification', // channel name
      importance: Importance.high,
      playSound: true,
      description: 'Notification for trip status updates (for Passengers)',
      enableVibration: true,
      enableLights: true,
      showBadge: true,
    );

    // Create driver notification channel
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(driverChannel);

    // Create passenger notification channel
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(passengerChannel);

    // Create passenger ringtone notification channel
    passengerRingtoneChannel = const AndroidNotificationChannel(
      'passenger_ringtone_channel', // channel ID
      'Passenger Ringtone Notifications', // channel name
      importance: Importance.max,
      sound: RawResourceAndroidNotificationSound('passenger_ringtone'),
      playSound: true,
      description: 'Important trip notifications with ringtone for Passengers',
      enableVibration: true,
      enableLights: true,
      showBadge: true,
    );

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(passengerRingtoneChannel);

    // Create silent notification channel for notifications without sound
    const silentNotificationChannel = AndroidNotificationChannel(
      'silent_notification', // channel ID
      'Silent Notifications', // channel name
      importance: Importance.high,
      playSound: false, // Explicitly disable sound
      description: 'Notifications that are delivered silently without sound or vibration',
      enableVibration: false,
      enableLights: false,
      showBadge: true,
    );

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(silentNotificationChannel);
  }

  // Configure how the notification appears in the foreground
  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: true,
    sound: true,
  );

  // Initialize platform-specific settings
  const initializationSettingsAndroid = AndroidInitializationSettings('@mipmap/ic_launcher');
  const initializationSettingsIOS = DarwinInitializationSettings(
    requestAlertPermission: false, // Don't request permissions immediately
    requestBadgePermission: false,
    requestSoundPermission: false,
  );

  const initializationSettings = InitializationSettings(
    android: initializationSettingsAndroid,
    iOS: initializationSettingsIOS,
  );

  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: (NotificationResponse response) {
      // Handle notification taps here 🎯
      _logger.info('📱 Notification tapped with payload: ${response.payload}');
      _handleNotificationTap(response.payload);
    },
  );

  isFlutterLocalNotificationsInitialized = true;
}

/// Request notification permissions explicitly when needed
Future<bool> requestNotificationPermissions() async {
  if (Platform.isIOS) {
    // For iOS, we need to explicitly request permissions
    final settings = await FirebaseMessaging.instance.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    return settings.authorizationStatus == AuthorizationStatus.authorized ||
        settings.authorizationStatus == AuthorizationStatus.provisional;
  }

  // For Android, permissions are generally granted by default
  return true;
}

Future<void> showNotification(RemoteMessage message) async {
  // Get title and body directly from the notification payload
  final title = message.notification?.title;
  final body = message.notification?.body;

  // If we don't have a title or body, don't show notification
  if (title == null || body == null) {
    return;
  }

  currentNotificationId = message.hashCode;

  // Create payload for notification tap handling 🎯
  String? payload;
  if (message.data.isNotEmpty) {
    // Build payload string from message data
    final payloadPairs = <String>[];
    message.data.forEach((key, value) {
      payloadPairs.add('$key=$value');
    });
    
    // Add the system notification ID for dismissal
    payloadPairs.add('systemNotificationId=$currentNotificationId');
    
    payload = payloadPairs.join('&');
    _logger.info('📱 Created notification payload: $payload');
  }

  if (Platform.isAndroid && !kIsWeb) {
    // Android-specific notification handling
    AndroidNotification? android = message.notification?.android;
    if (android != null) {
      // Determine which channel to use
      String channelId = android.channelId ?? 'new_trip_notification';
      String channelName;
      if (channelId == 'passenger_trip_notification') {
        channelName = 'Trip Status Notification';
      } else if (channelId == 'passenger_ringtone_channel') {
        channelName = 'Passenger Ringtone Notifications';
      } else if (channelId == 'silent_notification') {
        channelName = 'Silent Notifications';
      } else {
        channelName = 'New Trip Notification';
      }

      // Set appropriate sound and other settings based on channel
      AndroidNotificationDetails androidDetails;
      if (channelId == 'passenger_ringtone_channel') {
        // For passenger ringtone notifications
        androidDetails = AndroidNotificationDetails(
          channelId,
          channelName,
          importance: Importance.max,
          priority: Priority.high,
          sound: const RawResourceAndroidNotificationSound('passenger_ringtone'),
          playSound: true,
        );
      } else if (channelId == 'passenger_trip_notification') {
        // For regular passenger notifications, use default sound
        androidDetails = AndroidNotificationDetails(
          channelId,
          channelName,
          importance: Importance.high,
          priority: Priority.high,
          playSound: true,
        );
      } else if (channelId == 'silent_notification') {
        // For silent notifications, no sound or vibration
        androidDetails = AndroidNotificationDetails(
          channelId,
          channelName,
          importance: Importance.high,
          priority: Priority.high,
          playSound: false,
          enableVibration: false,
          enableLights: false,
        );
      } else {
        // For driver notifications, use the special ringtone
        androidDetails = AndroidNotificationDetails(
          channelId,
          channelName,
          importance: Importance.max,
          priority: Priority.high,
          sound: const RawResourceAndroidNotificationSound('phone_ringtone_ultra'),
          additionalFlags: Int32List.fromList(<int>[4]), // FLAG_INSISTENT
          playSound: true,
          timeoutAfter: 60000, // 60 seconds
        );
      }

      await flutterLocalNotificationsPlugin.show(
        currentNotificationId!,
        title,
        body,
        NotificationDetails(
          android: androidDetails,
        ),
        payload: payload, // 🎯 Add payload for tap handling
      );
    }
  } else if (Platform.isIOS && !kIsWeb) {
    // iOS-specific notification handling
    String? sound;

    // Check if this is a passenger ringtone notification
    if (message.data['notificationType'] == 'driver_moving' || message.data['notificationType'] == 'driver_arrived') {
      // Check if the notification should use a ringtone based on the channel
      final channelId = message.notification?.android?.channelId;
      if (channelId == 'passenger_ringtone_channel') {
        sound = 'passenger_ringtone.caf';
      }
    } else if (message.data['isDriverNotification'] == 'true') {
      sound = 'phone_ringtone_ultra.caf';
    }

    final iOSPlatformChannelSpecifics = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      sound: sound,
      // Note: Custom sounds need to be added to the iOS project
    );

    await flutterLocalNotificationsPlugin.show(
      currentNotificationId!,
      title,
      body,
      NotificationDetails(
        iOS: iOSPlatformChannelSpecifics,
      ),
      payload: payload, // 🎯 Add payload for tap handling
    );
  }
}

Future<void> cancelNotification() async {
  if (currentNotificationId != null) {
    try {
      await flutterLocalNotificationsPlugin.cancel(currentNotificationId!);
      currentNotificationId = null;
    } catch (e) {
      // Ignore error
    }
  }
}

/// Get the current notification permission status on iOS from Firebase.
Future<PermissionStatus> getIosNotificationStatus() async {
  if (Platform.isIOS) {
    final settings = await FirebaseMessaging.instance.getNotificationSettings();
    switch (settings.authorizationStatus) {
      case AuthorizationStatus.authorized:
      case AuthorizationStatus.provisional: // Provisional typically means notifications are delivered quietly.
        return PermissionStatus.granted;
      case AuthorizationStatus.denied:
        return PermissionStatus.denied;
      case AuthorizationStatus.notDetermined:
        return PermissionStatus.denied;
    }
  }
  // Should not be called for non-iOS platforms, but return denied as a fallback.
  return PermissionStatus.denied;
}

/// Get the FCM token, ensuring APNS token is available on iOS
Future<String?> getFCMToken() async {
  try {
    // For iOS, we need to ensure the APNS token is available first
    if (Platform.isIOS) {
      // Check for APNS token availability
      final apnsToken = await FirebaseMessaging.instance.getAPNSToken();

      // Log the status for debugging
      if (apnsToken == null) {
        _logger.warning('Warning: APNS token is not yet available. Waiting before requesting FCM token.');

        // We could implement retry logic here, but for now we'll just return null
        // as the token will eventually be available through the onTokenRefresh listener
        return null;
      }

      _logger.info('APNS token is available, proceeding to get FCM token');
    }

    // Get the FCM token once we're sure the APNS token is available (or on non-iOS platforms)
    return await FirebaseMessaging.instance.getToken();
  } catch (e) {
    _logger.severe('Error getting FCM token', e);
    return null;
  }
}
