import 'package:flutter/material.dart';

import '../models/MobileUserNotification.dart';

class NotificationListItem extends StatelessWidget {
  final MobileUserNotification notification;
  final VoidCallback onTap;

  const NotificationListItem({
    super.key,
    required this.notification,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      elevation: notification.isRead ? 0 : 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(
            color: notification.isRead ? null : Theme.of(context).primaryColor.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: notification.isRead 
                ? Colors.transparent 
                : Theme.of(context).primaryColor.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Notification icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: _getIconBackgroundColor(notification.type),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getNotificationIcon(notification.type),
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                
                // Notification content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              notification.title,
                              style: TextStyle(
                                fontWeight: notification.isRead 
                                  ? FontWeight.normal 
                                  : FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ),
                          if (!notification.isRead)
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor,
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        notification.body,
                        style: TextStyle(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey[300]
                              : Colors.grey[600],
                          fontSize: 14,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        notification.displayTime,
                        style: TextStyle(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey[400]
                              : Colors.grey[500],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Chevron
                Icon(
                  Icons.chevron_right,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[500]
                      : Colors.grey[400],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getIconBackgroundColor(String type) {
    switch (type) {
      case 'driver_moving':
      case 'driver_arrived':
        return Colors.blue;
      case 'trip_paid':
        return Colors.green;
      case 'reservation_reminder':
        return Colors.orange;
      case 'driver_timeout':
        return Colors.red;
      case 'document_expiry':
        return Colors.deepOrange;
      case 'trip_request':
        return Colors.indigo;
      case 'trip_cancelled':
        return Colors.red;
      case 'trip_completed':
        return Colors.green;
      case 'system_update':
        return Colors.purple;
      case 'general':
      default:
        return Colors.grey;
    }
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'driver_moving':
        return Icons.directions_car;
      case 'driver_arrived':
        return Icons.location_on;
      case 'trip_paid':
        return Icons.payment;
      case 'reservation_reminder':
        return Icons.alarm;
      case 'driver_timeout':
        return Icons.timer_off;
      case 'document_expiry':
        return Icons.warning;
      case 'trip_request':
        return Icons.notifications_active;
      case 'trip_cancelled':
        return Icons.cancel;
      case 'trip_completed':
        return Icons.check_circle;
      case 'system_update':
        return Icons.system_update;
      case 'general':
      default:
        return Icons.notifications;
    }
  }
}