import 'package:flutter/material.dart';

/// A reusable widget for displaying prices with proper formatting
/// <PERSON>les floating point precision issues and supports various styling options
class PriceDisplay extends StatelessWidget {
  final double? amount;
  final String currency;
  final TextStyle? style;
  final bool showCurrencySymbol;
  final int decimalPlaces;
  final String? fallbackText;
  final bool useISOCode;
  final MainAxisAlignment alignment;
  final bool showIcon;
  final IconData? icon;
  final double? iconSize;
  final Color? iconColor;

  const PriceDisplay({
    super.key,
    required this.amount,
    this.currency = 'MGA', // Default to MGA (Malagasy Ariary ISO code)
    this.style,
    this.showCurrencySymbol = true,
    this.decimalPlaces = 0,
    this.fallbackText,
    this.useISOCode = false, // For now, default to false to use "Ar"
    this.alignment = MainAxisAlignment.start,
    this.showIcon = false,
    this.icon,
    this.iconSize,
    this.iconColor,
  });

  String get _currencyDisplay {
    if (!showCurrencySymbol) return '';

    // Map ISO codes to display symbols
    switch (currency) {
      case 'MGA':
      case 'Ar': // Handle both MGA and Ar inputs
        return 'Ar'; // Always display as "Ar"
      case 'EUR':
        return '€';
      case 'USD':
        return '\$';
      default:
        // If the currency is already 'Ar', return it; otherwise return as-is
        return currency == 'Ar' ? 'Ar' : currency;
    }
  }

  String get _formattedAmount {
    if (amount == null) {
      return fallbackText ?? 'N/A';
    }

    // Format the amount with the specified decimal places
    String formatted = amount!.toStringAsFixed(decimalPlaces);

    // Add thousand separators for better readability
    if (decimalPlaces == 0) {
      // For whole numbers, add spaces as thousand separators
      return formatted
          .replaceAllMapped(
            RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
            (Match m) => '${m[1]} ',
          )
          .trim();
    } else {
      // For decimal numbers, handle the integer part only
      List<String> parts = formatted.split('.');
      parts[0] = parts[0]
          .replaceAllMapped(
            RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
            (Match m) => '${m[1]} ',
          )
          .trim();
      return parts.join('.');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (amount == null && fallbackText == null) {
      return const SizedBox.shrink();
    }

    // For fallback text, just return it as-is
    if (amount == null) {
      return Text(fallbackText!, style: style);
    }

    // Get the appropriate text color based on theme
    final theme = Theme.of(context);
    final defaultTextColor =
        theme.textTheme.bodyLarge?.color ?? (theme.brightness == Brightness.dark ? Colors.white : Colors.black);

    // Use the provided style's color, or fall back to theme-appropriate color
    final baseTextColor = style?.color ?? defaultTextColor;

    // Build the price display with styled components
    final Widget priceWidget;

    if (currency == 'EUR') {
      // EUR symbol before amount
      priceWidget = RichText(
        text: TextSpan(
          style: DefaultTextStyle.of(context).style,
          children: [
            TextSpan(
              text: '$_currencyDisplay$_formattedAmount',
              style: style ?? TextStyle(color: baseTextColor),
            ),
          ],
        ),
      );
    } else {
      // Other currencies after amount with reduced opacity
      priceWidget = RichText(
        text: TextSpan(
          style: DefaultTextStyle.of(context).style,
          children: [
            TextSpan(
              text: _formattedAmount,
              style: style ?? TextStyle(color: baseTextColor),
            ),
            if (showCurrencySymbol) ...[
              const TextSpan(text: ' '),
              TextSpan(
                text: _currencyDisplay,
                style: (style ?? TextStyle(color: baseTextColor)).copyWith(
                  color: baseTextColor.withValues(alpha: 0.6),
                ),
              ),
            ],
          ],
        ),
      );
    }

    if (showIcon && icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: alignment,
        children: [
          Icon(
            icon,
            size: iconSize ?? 16,
            color: iconColor ?? Colors.grey,
          ),
          const SizedBox(width: 4),
          priceWidget,
        ],
      );
    }

    return priceWidget;
  }

  /// Factory constructor for bold price display
  factory PriceDisplay.bold({
    required double? amount,
    String currency = 'MGA',
    int decimalPlaces = 0,
    String? fallbackText,
    double? fontSize,
    Color? color,
    bool useISOCode = false,
  }) {
    return PriceDisplay(
      amount: amount,
      currency: currency,
      decimalPlaces: decimalPlaces,
      fallbackText: fallbackText,
      useISOCode: useISOCode,
      style: TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: fontSize,
        color: color,
      ),
    );
  }

  /// Factory constructor for large price display (e.g., main price in trip control)
  factory PriceDisplay.large({
    required double? amount,
    String currency = 'MGA',
    int decimalPlaces = 0,
    String? fallbackText,
    Color? color,
    bool useISOCode = false,
  }) {
    return PriceDisplay(
      amount: amount,
      currency: currency,
      decimalPlaces: decimalPlaces,
      fallbackText: fallbackText,
      useISOCode: useISOCode,
      style: TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: 18,
        color: color,
      ),
    );
  }

  /// Factory constructor for subtle price display
  factory PriceDisplay.subtle({
    required double? amount,
    String currency = 'MGA',
    int decimalPlaces = 0,
    String? fallbackText,
    bool useISOCode = false,
  }) {
    return PriceDisplay(
      amount: amount,
      currency: currency,
      decimalPlaces: decimalPlaces,
      fallbackText: fallbackText,
      useISOCode: useISOCode,
      style: TextStyle(
        color: Colors.grey[600],
        fontSize: 14,
      ),
    );
  }

  /// Factory constructor for price with icon
  factory PriceDisplay.withIcon({
    required double? amount,
    String currency = 'MGA',
    required IconData icon,
    int decimalPlaces = 0,
    String? fallbackText,
    TextStyle? style,
    double? iconSize,
    Color? iconColor,
    bool useISOCode = false,
  }) {
    return PriceDisplay(
      amount: amount,
      currency: currency,
      decimalPlaces: decimalPlaces,
      fallbackText: fallbackText,
      style: style,
      showIcon: true,
      icon: icon,
      iconSize: iconSize,
      iconColor: iconColor,
      useISOCode: useISOCode,
    );
  }
}
