import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../l10n/app_localizations.dart';
import '../services/TripStateService.dart';

enum ButtonVariant {
  primary, // Main action - solid background
  secondary, // Secondary action - outlined
  danger, // Destructive action - red
  success, // Positive action - green
  warning, // Warning action - orange
}

class TripActionButton extends StatelessWidget {
  final String label;
  final VoidCallback? onPressed;
  final String? tripId;
  final String? transitionType;
  final Color? color;
  final Color? textColor;
  final double? width;
  final double? height;
  final IconData? icon;
  final bool showErrorSnackbar;
  final ButtonVariant variant;
  final bool isOutlined;

  const TripActionButton({
    super.key,
    required this.label,
    this.onPressed,
    this.tripId,
    this.transitionType,
    this.color,
    this.textColor,
    this.width,
    this.height,
    this.icon,
    this.showErrorSnackbar = true,
    this.variant = ButtonVariant.primary,
    this.isOutlined = false,
  });

  @override
  Widget build(BuildContext context) {
    // If tripId and transitionType are provided, use reactive loading state
    if (tripId != null && transitionType != null) {
      final key = '${tripId}_$transitionType';

      return Obx(() {
        // Access the RxBool directly from the service's map
        final loadingRx = TripStateService.instance.getLoadingRx(key);
        final isLoading = loadingRx.value;

        return _buildButton(
          context,
          isLoading: isLoading,
          onPressed: isLoading
              ? null
              : () {
                  if (onPressed != null) {
                    onPressed!();

                    // Check for errors after action
                    if (showErrorSnackbar && TripStateService.instance.lastError.isNotEmpty) {
                      Get.snackbar(
                        AppLocalizations.of(context)!.tripActionButton_error, // "Erreur"
                        TripStateService.instance.lastError.value,
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.red,
                        colorText: Colors.white,
                        duration: const Duration(seconds: 3),
                      );
                    }
                  }
                },
        );
      });
    }

    // Otherwise, build a simple button
    return _buildButton(
      context,
      isLoading: false,
      onPressed: onPressed,
    );
  }

  Widget _buildButton(
    BuildContext context, {
    required bool isLoading,
    required VoidCallback? onPressed,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Enhanced color system with better contrast
    final ButtonColors buttonColors = _getButtonColors(theme, isDark);

    final effectiveColor = color ?? buttonColors.backgroundColor;
    final effectiveTextColor = textColor ?? buttonColors.foregroundColor;
    final effectiveIconColor = buttonColors.iconColor;

    return SizedBox(
      width: width,
      height: height ?? 48,
      child: isOutlined
          ? OutlinedButton(
              onPressed: onPressed,
              style: OutlinedButton.styleFrom(
                foregroundColor: effectiveTextColor,
                side: BorderSide(
                  color: effectiveColor,
                  width: 2,
                ),
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 6),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _buildButtonContent(effectiveTextColor, effectiveIconColor, isLoading),
            )
          : ElevatedButton(
              onPressed: onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: effectiveColor,
                foregroundColor: effectiveTextColor,
                disabledBackgroundColor: effectiveColor.withValues(alpha: 0.6),
                disabledForegroundColor: effectiveTextColor.withValues(alpha: 0.6),
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 6),
                elevation: variant == ButtonVariant.primary ? 2 : 1,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _buildButtonContent(effectiveTextColor, effectiveIconColor, isLoading),
            ),
    );
  }

  Widget _buildButtonContent(Color textColor, Color iconColor, bool isLoading) {
    if (isLoading) {
      return SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(textColor),
        ),
      );
    }

    if (icon == null) {
      return Text(
        label,
        style: TextStyle(
          fontSize: 13,
          fontWeight: FontWeight.w600,
          color: textColor,
        ),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          size: 20,
          color: iconColor, // Use enhanced icon color for better visibility
        ),
        const SizedBox(width: 8),
        Flexible(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: textColor,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    );
  }

  ButtonColors _getButtonColors(ThemeData theme, bool isDark) {
    switch (variant) {
      case ButtonVariant.primary:
        return ButtonColors(
          backgroundColor: theme.primaryColor,
          foregroundColor: Colors.white,
          iconColor: Colors.white,
        );
      case ButtonVariant.secondary:
        return ButtonColors(
          backgroundColor: isDark ? Colors.grey[800]! : Colors.grey[100]!,
          foregroundColor: isDark ? Colors.white : Colors.black87,
          iconColor: isDark ? Colors.white70 : Colors.black54,
        );
      case ButtonVariant.danger:
        return ButtonColors(
          backgroundColor: Colors.red[600]!,
          foregroundColor: Colors.white,
          iconColor: Colors.white,
        );
      case ButtonVariant.success:
        return ButtonColors(
          backgroundColor: Colors.green[600]!,
          foregroundColor: Colors.white,
          iconColor: Colors.white,
        );
      case ButtonVariant.warning:
        return ButtonColors(
          backgroundColor: Colors.orange[600]!,
          foregroundColor: Colors.white,
          iconColor: Colors.white,
        );
    }
  }
}

class ButtonColors {
  final Color backgroundColor;
  final Color foregroundColor;
  final Color iconColor;

  ButtonColors({
    required this.backgroundColor,
    required this.foregroundColor,
    required this.iconColor,
  });
}

// Specialized trip action buttons with enhanced variants

class CancelTripButton extends StatelessWidget {
  final String tripId;
  final String? reason;
  final VoidCallback? onSuccess;
  final double? width;
  final bool isOutlined;

  const CancelTripButton({
    super.key,
    required this.tripId,
    this.reason,
    this.onSuccess,
    this.width,
    this.isOutlined = false,
  });

  @override
  Widget build(BuildContext context) {
    return TripActionButton(
      label: AppLocalizations.of(context)!.tripActionButton_cancel, // "Annuler"
      tripId: tripId,
      transitionType: 'cancelTrip',
      variant: ButtonVariant.danger,
      icon: Icons.cancel,
      width: width,
      isOutlined: isOutlined,
      onPressed: () async {
        final success = await TripStateService.instance.cancelTrip(
          tripId: tripId,
          reason: reason,
        );

        if (success && onSuccess != null) {
          onSuccess!();
        }
      },
    );
  }
}

class StartTripButton extends StatelessWidget {
  final String tripId;
  final String userType;
  final Map<String, dynamic>? tripConfiguration;
  final VoidCallback? onSuccess;
  final double? width;
  final bool isOutlined;

  const StartTripButton({
    super.key,
    required this.tripId,
    required this.userType,
    this.tripConfiguration,
    this.onSuccess,
    this.width,
    this.isOutlined = false,
  });

  @override
  Widget build(BuildContext context) {
    return TripActionButton(
      label: AppLocalizations.of(context)!.tripActionButton_start, // "Démarrer"
      tripId: tripId,
      transitionType: 'startTrip',
      variant: ButtonVariant.success,
      icon: Icons.play_arrow,
      width: width,
      isOutlined: isOutlined,
      onPressed: () async {
        final success = await TripStateService.instance.startTrip(
          tripId: tripId,
          userType: userType,
          tripConfiguration: tripConfiguration,
        );

        if (success && onSuccess != null) {
          onSuccess!();
        }
      },
    );
  }
}

class CompleteTripButton extends StatelessWidget {
  final String tripId;
  final Map<String, dynamic>? finalRouteData;
  final Map<String, dynamic>? costData;
  final VoidCallback? onSuccess;
  final double? width;
  final bool isOutlined;

  const CompleteTripButton({
    super.key,
    required this.tripId,
    this.finalRouteData,
    this.costData,
    this.onSuccess,
    this.width,
    this.isOutlined = false,
  });

  @override
  Widget build(BuildContext context) {
    return TripActionButton(
      label: AppLocalizations.of(context)!.tripActionButton_complete, // "Terminer"
      tripId: tripId,
      transitionType: 'completeTrip',
      variant: ButtonVariant.primary,
      icon: Icons.check_circle,
      width: width,
      isOutlined: isOutlined,
      onPressed: () async {
        final success = await TripStateService.instance.completeTrip(
          tripId: tripId,
          finalRouteData: finalRouteData,
          costData: costData,
        );

        if (success && onSuccess != null) {
          onSuccess!();
        }
      },
    );
  }
}
