import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../l10n/app_localizations.dart';
import '../models/MobileUser.dart';
import '../models/Trip.dart';
import '../states/NavigationState.dart';

enum DriverActionType {
  select,
  cancelRequest,
}

class DriverActionButton extends StatefulWidget {
  final String label;
  final DriverActionType actionType;
  final VoidCallback? onSuccess;
  final Color? color;
  final Color? textColor;
  final double? width;
  final double? height;
  final bool enabled;
  final bool isOutlined;

  // For select action
  final String? tripId;
  final MobileUser? driver;
  final Map<String, dynamic>? analyticsParams;
  final NavigationState? navigationState;

  // For cancel action
  final Trip? trip;
  final String? driverUid;

  const DriverActionButton({
    super.key,
    required this.label,
    required this.actionType,
    this.onSuccess,
    this.color,
    this.textColor,
    this.width,
    this.height,
    this.enabled = true,
    this.isOutlined = false,
    // Select action parameters
    this.tripId,
    this.driver,
    this.analyticsParams,
    this.navigationState,
    // Cancel action parameters
    this.trip,
    this.driverUid,
  }) : assert(
          (actionType == DriverActionType.select && driver != null && navigationState != null) ||
              (actionType == DriverActionType.cancelRequest && trip != null && driverUid != null),
          'Required parameters must be provided based on actionType',
        );

  @override
  State<DriverActionButton> createState() => _DriverActionButtonState();
}

class _DriverActionButtonState extends State<DriverActionButton> {
  bool _isLoading = false;

  Future<void> _handleAction() async {
    if (_isLoading || !widget.enabled) return;

    setState(() {
      _isLoading = true;
    });

    try {
      switch (widget.actionType) {
        case DriverActionType.select:
          await _handleSelectDriver();
          break;
        case DriverActionType.cancelRequest:
          await _handleCancelRequest();
          break;
      }

      if (widget.onSuccess != null) {
        widget.onSuccess!();
      }
    } catch (e) {
      _showError(e.toString());
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _handleSelectDriver() async {
    if (widget.tripId == null) return;

    FirebaseAnalytics.instance.logEvent(
      name: 'driver_selected_from_list',
      parameters: widget.analyticsParams != null ? Map<String, Object>.from(widget.analyticsParams!) : {},
    );

    await widget.navigationState!.selectDriver(widget.driver!);
  }

  Future<void> _handleCancelRequest() async {
    FirebaseAnalytics.instance.logEvent(
      name: 'driver_request_cancelled',
      parameters: {
        'driver_id': widget.driverUid!,
        'trip_id': widget.trip!.id,
        'widget_name': 'drivers_list',
        'reason': 'user_initiated'
      },
    );

    await widget.trip!.cancelDriverRequest();
  }

  void _showError(String error) {
    Get.snackbar(
      AppLocalizations.of(context)!.mapScreen_error,
      widget.actionType == DriverActionType.cancelRequest ? AppLocalizations.of(context)!.mapScreen_cancelRequestFailed : error,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Enhanced color system with better contrast
    final ButtonColors buttonColors = _getButtonColors(theme, isDark);

    final effectiveColor = widget.color ?? buttonColors.backgroundColor;
    final effectiveTextColor = widget.textColor ?? buttonColors.foregroundColor;
    final effectiveIconColor = buttonColors.iconColor;

    return SizedBox(
      width: widget.width,
      height: widget.height ?? 48,
      child: widget.isOutlined
          ? OutlinedButton(
              onPressed: (widget.enabled && !_isLoading) ? _handleAction : null,
              style: OutlinedButton.styleFrom(
                foregroundColor: effectiveTextColor,
                side: BorderSide(
                  color: effectiveColor,
                  width: 2,
                ),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _buildButtonContent(effectiveTextColor, effectiveIconColor),
            )
          : ElevatedButton(
              onPressed: (widget.enabled && !_isLoading) ? _handleAction : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: effectiveColor,
                foregroundColor: effectiveTextColor,
                disabledBackgroundColor: effectiveColor.withValues(alpha: 0.6),
                disabledForegroundColor: effectiveTextColor.withValues(alpha: 0.6),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                elevation: widget.actionType == DriverActionType.select ? 2 : 1,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _buildButtonContent(effectiveTextColor, effectiveIconColor),
            ),
    );
  }

  Widget _buildButtonContent(Color textColor, Color iconColor) {
    if (_isLoading) {
      return SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(textColor),
        ),
      );
    }

    return Text(
      widget.label,
      style: TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.w600,
        color: textColor,
      ),
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    );
  }

  ButtonColors _getButtonColors(ThemeData theme, bool isDark) {
    switch (widget.actionType) {
      case DriverActionType.select:
        if (!widget.enabled) {
          return ButtonColors(
            backgroundColor: isDark ? Colors.grey[700]! : Colors.grey[300]!,
            foregroundColor: isDark ? Colors.grey[500]! : Colors.grey[600]!,
            iconColor: isDark ? Colors.grey[500]! : Colors.grey[600]!,
          );
        }
        return ButtonColors(
          backgroundColor: theme.primaryColor,
          foregroundColor: Colors.white,
          iconColor: Colors.white,
        );
      case DriverActionType.cancelRequest:
        return ButtonColors(
          backgroundColor: Colors.red[600]!,
          foregroundColor: Colors.white,
          iconColor: Colors.white,
        );
    }
  }
}

class ButtonColors {
  final Color backgroundColor;
  final Color foregroundColor;
  final Color iconColor;

  ButtonColors({
    required this.backgroundColor,
    required this.foregroundColor,
    required this.iconColor,
  });
}

// Convenience constructors for specific actions

class SelectDriverButton extends StatelessWidget {
  final String label;
  final String? tripId;
  final MobileUser driver;
  final Map<String, dynamic> analyticsParams;
  final NavigationState navigationState;
  final VoidCallback? onSuccess;
  final double? width;
  final bool enabled;
  final bool isOutlined;

  const SelectDriverButton({
    super.key,
    required this.label,
    required this.tripId,
    required this.driver,
    required this.analyticsParams,
    required this.navigationState,
    this.onSuccess,
    this.width,
    this.enabled = true,
    this.isOutlined = false,
  });

  @override
  Widget build(BuildContext context) {
    return DriverActionButton(
      label: label,
      actionType: DriverActionType.select,
      tripId: tripId,
      driver: driver,
      analyticsParams: analyticsParams,
      navigationState: navigationState,
      onSuccess: onSuccess,
      width: width,
      enabled: enabled,
      isOutlined: isOutlined,
    );
  }
}

class CancelDriverRequestButton extends StatelessWidget {
  final Trip trip;
  final String driverUid;
  final VoidCallback? onSuccess;
  final double? width;
  final bool isOutlined;

  const CancelDriverRequestButton({
    super.key,
    required this.trip,
    required this.driverUid,
    this.onSuccess,
    this.width,
    this.isOutlined = false,
  });

  @override
  Widget build(BuildContext context) {
    return DriverActionButton(
      label: AppLocalizations.of(context)!.mapScreen_cancel,
      actionType: DriverActionType.cancelRequest,
      trip: trip,
      driverUid: driverUid,
      onSuccess: onSuccess,
      width: width,
      isOutlined: isOutlined,
    );
  }
}
