import 'package:flutter/material.dart';
import 'package:logging/logging.dart';

import '../utils/storage_utils.dart';

/// A widget that displays an image from Firebase Storage
/// Handles both full URLs and storage paths
class StorageImage extends StatefulWidget {
  final String pathOrUrl;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Widget Function(BuildContext, Object, StackTrace?)? errorBuilder;

  const StorageImage({
    super.key,
    required this.pathOrUrl,
    this.width,
    this.height,
    this.fit,
    this.errorBuilder,
  });

  @override
  State<StorageImage> createState() => _StorageImageState();
}

class _StorageImageState extends State<StorageImage> {
  final Logger _logger = Logger('StorageImage');
  late Future<String> _urlFuture;

  @override
  void initState() {
    super.initState();
    _initializeUrl();
  }

  @override
  void didUpdateWidget(StorageImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.pathOrUrl != widget.pathOrUrl) {
      _initializeUrl();
    }
  }

  void _initializeUrl() {
    _logger.info('📸 StorageImage: Loading image from: ${widget.pathOrUrl}');

    if (StorageUtils.isFullUrl(widget.pathOrUrl)) {
      // Already a full URL, use it directly
      _urlFuture = Future.value(widget.pathOrUrl);
    } else {
      // It's a storage path, get the download URL
      _urlFuture = StorageUtils.getDownloadUrl(widget.pathOrUrl);
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<String>(
      future: _urlFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox(
            width: widget.width,
            height: widget.height,
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasData) {
          _logger.info('📸 StorageImage: Got URL: ${snapshot.data}');
          return Image.network(
            snapshot.data!,
            width: widget.width,
            height: widget.height,
            fit: widget.fit,
            errorBuilder: widget.errorBuilder ??
                (context, error, stackTrace) {
                  _logger.severe('❌ StorageImage error', error, stackTrace);
                  _logger.fine('❌ Stack trace: $stackTrace');
                  return Container(
                    width: widget.width,
                    height: widget.height,
                    color: Colors.grey[300],
                    child: const Icon(Icons.error, color: Colors.red),
                  );
                },
          );
        }

        // Error state
        if (widget.errorBuilder != null) {
          return widget.errorBuilder!(context, snapshot.error ?? 'Unknown error', snapshot.stackTrace);
        }

        return Container(
          width: widget.width,
          height: widget.height,
          color: Colors.grey[300],
          child: const Icon(Icons.error, color: Colors.red),
        );
      },
    );
  }
}
