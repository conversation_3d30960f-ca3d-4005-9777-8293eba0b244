import 'package:flutter/material.dart';

enum ButtonGroupDirection {
  horizontal,
  vertical,
}

enum ButtonGroupAlignment {
  start,
  center,
  end,
  spaceBetween,
  spaceEvenly,
  spaceAround,
}

/// A widget that provides consistent layout and spacing for multiple buttons
/// with improved UX patterns and visual hierarchy.
class ButtonGroup extends StatelessWidget {
  final List<Widget> children;
  final ButtonGroupDirection direction;
  final ButtonGroupAlignment alignment;
  final double spacing;
  final EdgeInsets? padding;
  final bool expandChildren;
  final CrossAxisAlignment crossAxisAlignment;

  const ButtonGroup({
    super.key,
    required this.children,
    this.direction = ButtonGroupDirection.horizontal,
    this.alignment = ButtonGroupAlignment.start,
    this.spacing = 12.0,
    this.padding,
    this.expandChildren = false,
    this.crossAxisAlignment = CrossAxisAlignment.center,
  });

  /// Creates a horizontal button group with equal width buttons
  const ButtonGroup.horizontal({
    super.key,
    required this.children,
    this.alignment = ButtonGroupAlignment.spaceBetween,
    this.spacing = 12.0,
    this.padding,
    this.expandChildren = true,
    this.crossAxisAlignment = CrossAxisAlignment.center,
  }) : direction = ButtonGroupDirection.horizontal;

  /// Creates a vertical button group
  const ButtonGroup.vertical({
    super.key,
    required this.children,
    this.alignment = ButtonGroupAlignment.start,
    this.spacing = 12.0,
    this.padding,
    this.expandChildren = false,
    this.crossAxisAlignment = CrossAxisAlignment.stretch,
  }) : direction = ButtonGroupDirection.vertical;

  /// Creates a primary/secondary button pair with proper visual hierarchy
  const ButtonGroup.primarySecondary({
    super.key,
    required Widget primary,
    required Widget secondary,
    this.spacing = 12.0,
    this.padding,
    this.crossAxisAlignment = CrossAxisAlignment.center,
  })  : children = const [],
        direction = ButtonGroupDirection.horizontal,
        alignment = ButtonGroupAlignment.spaceBetween,
        expandChildren = true;

  @override
  Widget build(BuildContext context) {
    if (children.isEmpty) return const SizedBox.shrink();

    Widget content;

    if (direction == ButtonGroupDirection.horizontal) {
      content = _buildHorizontalLayout();
    } else {
      content = _buildVerticalLayout();
    }

    if (padding != null) {
      content = Padding(
        padding: padding!,
        child: content,
      );
    }

    return content;
  }

  Widget _buildHorizontalLayout() {
    switch (alignment) {
      case ButtonGroupAlignment.spaceBetween:
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: crossAxisAlignment,
          children: _buildHorizontalChildren(),
        );
      case ButtonGroupAlignment.spaceEvenly:
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          crossAxisAlignment: crossAxisAlignment,
          children: _buildHorizontalChildren(),
        );
      case ButtonGroupAlignment.spaceAround:
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: crossAxisAlignment,
          children: _buildHorizontalChildren(),
        );
      case ButtonGroupAlignment.center:
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: crossAxisAlignment,
          children: _buildSpacedChildren(),
        );
      case ButtonGroupAlignment.end:
        return Row(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: crossAxisAlignment,
          children: _buildSpacedChildren(),
        );
      case ButtonGroupAlignment.start:
        return Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: crossAxisAlignment,
          children: _buildSpacedChildren(),
        );
    }
  }

  Widget _buildVerticalLayout() {
    return Column(
      crossAxisAlignment: crossAxisAlignment,
      children: _buildSpacedChildren(),
    );
  }

  List<Widget> _buildHorizontalChildren() {
    if (!expandChildren) {
      return _buildSpacedChildren();
    }

    // For expanded children, we still need spacing between them
    if (children.length <= 1) return children.map((child) => Expanded(child: child)).toList();

    final spacedChildren = <Widget>[];
    for (int i = 0; i < children.length; i++) {
      spacedChildren.add(Expanded(child: children[i]));

      if (i < children.length - 1) {
        spacedChildren.add(SizedBox(width: spacing));
      }
    }
    return spacedChildren;
  }

  List<Widget> _buildSpacedChildren() {
    if (children.length <= 1) return children;

    final spacedChildren = <Widget>[];
    for (int i = 0; i < children.length; i++) {
      Widget child = children[i];

      if (expandChildren && direction == ButtonGroupDirection.horizontal) {
        child = Expanded(child: child);
      }

      spacedChildren.add(child);

      if (i < children.length - 1) {
        spacedChildren.add(
          direction == ButtonGroupDirection.horizontal ? SizedBox(width: spacing) : SizedBox(height: spacing),
        );
      }
    }
    return spacedChildren;
  }
}

/// A specialized button group for accept/reject patterns
class AcceptRejectButtons extends StatelessWidget {
  final Widget acceptButton;
  final Widget rejectButton;
  final double spacing;
  final EdgeInsets? padding;

  const AcceptRejectButtons({
    super.key,
    required this.acceptButton,
    required this.rejectButton,
    this.spacing = 12.0,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ButtonGroup.horizontal(
      spacing: spacing,
      padding: padding,
      children: [
        acceptButton,
        rejectButton,
      ],
    );
  }
}

/// A specialized button group for camera/gallery selection
class MediaPickerButtons extends StatelessWidget {
  final Widget cameraButton;
  final Widget galleryButton;
  final double spacing;
  final EdgeInsets? padding;
  final bool vertical;

  const MediaPickerButtons({
    super.key,
    required this.cameraButton,
    required this.galleryButton,
    this.spacing = 12.0,
    this.padding,
    this.vertical = false,
  });

  @override
  Widget build(BuildContext context) {
    return vertical
        ? ButtonGroup.vertical(
            spacing: spacing,
            padding: padding,
            children: [cameraButton, galleryButton],
          )
        : ButtonGroup.horizontal(
            spacing: spacing,
            padding: padding,
            children: [cameraButton, galleryButton],
          );
  }
}
