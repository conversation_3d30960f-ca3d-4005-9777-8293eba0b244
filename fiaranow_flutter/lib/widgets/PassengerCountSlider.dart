import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../l10n/app_localizations.dart';

class PassengerCountSlider extends StatelessWidget {
  final Rx<int> passengerCount;
  final int maxPassengers;
  final Function(int)? onChanged;

  const PassengerCountSlider({
    super.key,
    required this.passengerCount,
    required this.maxPassengers,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              AppLocalizations.of(context)!.passengerCountSlider_title, // "Number of Passengers"
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(width: 16),
            Obx(
              () => Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  passengerCount.value.toString(),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            const Text('1'),
            Expanded(
              child: Obx(
                () => Slider(
                  value: passengerCount.value.toDouble(),
                  min: 1,
                  max: maxPassengers.toDouble(),
                  divisions: maxPassengers - 1,
                  label: passengerCount.value.toString(),
                  onChanged: (double value) {
                    passengerCount.value = value.round();
                    onChanged?.call(value.round());
                  },
                ),
              ),
            ),
            Text(maxPassengers.toString()),
          ],
        ),
      ],
    );
  }
}