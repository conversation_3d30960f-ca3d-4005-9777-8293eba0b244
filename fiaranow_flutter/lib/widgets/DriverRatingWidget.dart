import 'package:flutter/material.dart';

import '../l10n/app_localizations.dart';

/// 🌟 Widget to display driver ratings with visual indicators
class DriverRatingWidget extends StatelessWidget {
  final double? rating;
  final int? tripCount;
  final double size;
  final bool showTripCount;

  const DriverRatingWidget({
    super.key,
    this.rating,
    this.tripCount,
    this.size = 16,
    this.showTripCount = true,
  });

  @override
  Widget build(BuildContext context) {
    // 🆕 Show "New Driver" for drivers without ratings
    if (rating == null || tripCount == null || tripCount == 0) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.star_border, size: size, color: Colors.grey),
          const SizedBox(width: 4),
          Text(
            AppLocalizations.of(context)!.driverRating_newDriver, // "New Driver"
            style: TextStyle(
              fontSize: size * 0.8,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      );
    }

    final displayRating = rating!.toStringAsFixed(1);
    final ratingColor = _getRatingColor(rating!);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(Icons.star, size: size, color: ratingColor),
        const SizedBox(width: 4),
        Text(
          displayRating,
          style: TextStyle(
            fontSize: size * 0.8,
            color: ratingColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        // 📊 Show trip count for drivers with 5+ trips
        if (showTripCount && tripCount! >= 5) ...[
          const SizedBox(width: 4),
          Text(
            '($tripCount)',
            style: TextStyle(
              fontSize: size * 0.7,
              color: Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }

  /// 🎨 Get color based on rating value
  Color _getRatingColor(double rating) {
    if (rating >= 4.5) return Colors.green[700]!;
    if (rating >= 4.0) return Colors.green;
    if (rating >= 3.5) return Colors.orange;
    if (rating >= 3.0) return Colors.orange[700]!;
    return Colors.red;
  }
}

/// 🌟 Compact version for list views
class DriverRatingCompact extends StatelessWidget {
  final double? rating;
  final int? tripCount;

  const DriverRatingCompact({
    super.key,
    this.rating,
    this.tripCount,
  });

  @override
  Widget build(BuildContext context) {
    if (rating == null || tripCount == null || tripCount == 0) {
      return const Icon(Icons.star_border, size: 14, color: Colors.grey);
    }

    final ratingColor = _getRatingColor(rating!);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(Icons.star, size: 14, color: ratingColor),
        const SizedBox(width: 2),
        Text(
          rating!.toStringAsFixed(1),
          style: TextStyle(
            fontSize: 11,
            color: ratingColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Color _getRatingColor(double rating) {
    if (rating >= 4.5) return Colors.green[700]!;
    if (rating >= 3.5) return Colors.orange;
    return Colors.red;
  }
}
