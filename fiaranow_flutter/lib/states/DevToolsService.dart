import 'dart:convert';
import 'dart:developer' as developer;

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

class AgentLoggingService extends GetxService {
  final Logger _logger = Logger('AgentLogging');

  static AgentLoggingService get to => Get.find();

  @override
  void onInit() {
    super.onInit();
    _logger.info('Initializing AgentLoggingService');
  }

  void logAgentEvent(String eventType, {Map<String, dynamic>? data}) {
    try {
      // Create the log entry with structured data
      final logEntry = <String, dynamic>{
        'eventType': 'agent.$eventType',
        'timestamp': DateTime.now().toIso8601String(),
        'data': data ?? {},
      };

      // Encode to JSON
      final jsonMessage = jsonEncode(logEntry);

      // Log using dart:developer for DevTools
      developer.log(
        jsonMessage,
        name: 'agent.$eventType',
        time: DateTime.now(),
      );

      // Also log using the logger for consistency - FIX: Convert data to string properly
      _logger.info('Agent event: $eventType');
      if (data != null && data.isNotEmpty) {
        _logger.info('Data: ${jsonEncode(data)}');
      }

      // Track event in analytics
      FirebaseAnalytics.instance.logEvent(
        name: 'agent_$eventType',
        parameters: data?.map((key, value) => MapEntry(key, value.toString())) ?? {},
      );
    } catch (e) {
      _logger.severe('Error logging agent event: $e');
    }
  }

  void logAgentAction(String action, {Map<String, dynamic>? details}) {
    logAgentEvent('action', data: {
      'action': action,
      ...?details,
    });
  }

  void logAgentDecision(String decision, {Map<String, dynamic>? context}) {
    logAgentEvent('decision', data: {
      'decision': decision,
      'context': context ?? {},
    });
  }

  void logAgentPerformance(String metric, double value, {Map<String, dynamic>? metadata}) {
    logAgentEvent('performance', data: {
      'metric': metric,
      'value': value,
      'metadata': metadata ?? {},
    });
  }

  void logAgentError(String error, {dynamic stackTrace, Map<String, dynamic>? context}) {
    logAgentEvent('error', data: {
      'error': error,
      'stackTrace': stackTrace?.toString(),
      'context': context ?? {},
    });

    // Also capture as exception in Crashlytics
    if (error is Exception || error is Error) {
      FirebaseCrashlytics.instance.recordError(error, stackTrace as StackTrace?, fatal: false);
    } else {
      FirebaseCrashlytics.instance.recordError(Exception(error.toString()), stackTrace as StackTrace?, fatal: false);
    }
  }
}
