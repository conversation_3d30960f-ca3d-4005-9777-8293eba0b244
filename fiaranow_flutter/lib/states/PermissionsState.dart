import 'dart:async';
import 'dart:io';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/widgets.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';
import 'package:permission_handler/permission_handler.dart';

import '../fcm.dart';

class PermissionsState extends GetxController with WidgetsBindingObserver {
  final Logger _logger = Logger('Permissions');

  // Observable values for permissions status
  final Rx<PermissionStatus?> notificationPermission = Rx<PermissionStatus?>(null);
  final Rx<LocationPermission?> locationPermission = Rx<LocationPermission?>(null);
  final RxBool locationServiceEnabled = false.obs;

  Timer? _permissionCheckTimer;
  static const Duration _quickCheckInterval = Duration(seconds: 5);
  static const Duration _maintenanceCheckInterval = Duration(minutes: 1);

  @override
  void onInit() {
    _logger.info('Initializing permissions state');
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    checkPermissions();
    _startPermissionCheck();
  }

  @override
  void onClose() {
    _logger.fine('Cleaning up resources');
    _permissionCheckTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      _logger.info('App resumed, checking permissions.');
      checkPermissions();
    }
  }

  void _startPermissionCheck() {
    _logger.fine('Starting quick permission check (every 5s)');
    _permissionCheckTimer?.cancel();

    // Initial check with quick interval
    _permissionCheckTimer = Timer.periodic(_quickCheckInterval, (timer) {
      checkPermissions();

      // If all permissions are satisfactory, switch to maintenance mode
      if (_areAllPermissionsSatisfactory()) {
        timer.cancel();
        _startMaintenanceCheck();
      }
    });
  }

  void _startMaintenanceCheck() {
    _logger.fine('Starting maintenance permission check (every 1min)');
    _permissionCheckTimer = Timer.periodic(_maintenanceCheckInterval, (timer) {
      checkPermissions();

      // If permissions are no longer satisfactory, switch back to quick check
      if (!_areAllPermissionsSatisfactory()) {
        _logger.warning('Permissions no longer satisfactory, switching back to quick check');
        timer.cancel();
        _startPermissionCheck();
      }
    });
  }

  bool _areAllPermissionsSatisfactory() {
    final result = hasNotificationPermission() && hasLocationPermission() && locationServiceEnabled.value;
    // Disabled log: _logger.finest('Permissions check - Notification: ${hasNotificationPermission()}, Location: ${hasLocationPermission()}, Service: ${locationServiceEnabled.value}');
    return result;
  }

  Future<void> checkPermissions() async {
    // Check notification permission
    if (Platform.isIOS) {
      final notifStatus = await getIosNotificationStatus(); // from fcm.dart
      if (notifStatus != notificationPermission.value) {
        _logger.info('iOS Notification permission changed (via FCM): ${notificationPermission.value} -> $notifStatus');
        notificationPermission.value = notifStatus;
      }
    } else {
      // Android
      final notifStatus = await Permission.notification.status;
      if (notifStatus != notificationPermission.value) {
        _logger.info('Android Notification permission changed: ${notificationPermission.value} -> $notifStatus');
        notificationPermission.value = notifStatus;
      }
    }

    // Check location service status
    final locService = await Geolocator.isLocationServiceEnabled();
    if (locService != locationServiceEnabled.value) {
      _logger.info('Location Service Enabled changed: ${locationServiceEnabled.value} -> $locService');
      locationServiceEnabled.value = locService;
    }

    // Check location permission
    final locPermission = await Geolocator.checkPermission();
    if (locPermission != locationPermission.value) {
      _logger.info('Location permission changed: ${locationPermission.value} -> $locPermission');
      locationPermission.value = locPermission;
    }
  }

  Future<bool> requestNotificationPermission() async {
    _logger.info('📱 Requesting notification permission');

    bool granted = false;
    PermissionStatus status;

    if (Platform.isIOS) {
      _logger.info('Attempting iOS notification permission request via fcm.dart/FirebaseMessaging...');
      await initializeFlutterNotifications(); // Sets up local notifications and foreground presentation options.

      bool fcmReportedGrant = await requestNotificationPermissions(); // This is from fcm.dart

      // After requesting, get the definitive status using our new FCM status checker
      status = await getIosNotificationStatus(); // from fcm.dart
      granted = status == PermissionStatus.granted;
      notificationPermission.value = status; // Update state with the final status

      _logger.info(
          'iOS FCM request reported grant: $fcmReportedGrant. Final status via getIosNotificationStatus: $status, effectively granted: $granted');
    } else {
      // For Android, use permission_handler as before
      status = await Permission.notification.request();
      notificationPermission.value = status;
      granted = status.isGranted;

      _logger.info('Android notification permission request result: $status');
    }

    // Immediately trigger permission check after request to ensure all states are aligned
    checkPermissions();

    FirebaseAnalytics.instance.logEvent(
        name: 'request_notification_permission',
        parameters: {'granted': granted.toString(), 'status_after_request': status.toString()});
    return granted;
  }

  Future<bool> requestLocationPermission({bool background = false}) async {
    _logger.info('📍 Requesting location permission (background: $background)');

    // First check if location services are enabled
    final serviceEnabled = await Geolocator.isLocationServiceEnabled();
    locationServiceEnabled.value = serviceEnabled;

    if (!serviceEnabled) {
      _logger.warning('Location services are disabled');
      return false;
    }

    // Get current permission status
    LocationPermission permission = await Geolocator.checkPermission();
    _logger.fine('Current location permission: $permission');

    // If denied, request permission
    if (permission == LocationPermission.denied) {
      permission = background ? await Geolocator.requestPermission() : await _requestBackgroundLocation();

      locationPermission.value = permission;
      // Immediately trigger permission check after request
      checkPermissions();

      _logger.info('Location permission request result: $permission');
      FirebaseAnalytics.instance.logEvent(name: 'request_location_permission', parameters: {
        'background': background.toString(),
        'granted': (permission == LocationPermission.always || permission == LocationPermission.whileInUse).toString(),
      });
      return permission == LocationPermission.always || permission == LocationPermission.whileInUse;
    }

    // If denied forever, return false
    if (permission == LocationPermission.deniedForever) {
      _logger.warning('Location permission is denied forever');
      return false;
    }

    // If we want background location but only have whileInUse
    if (background && permission == LocationPermission.whileInUse) {
      _logger.info('Upgrading whileInUse to background location permission');
      permission = await _requestBackgroundLocation();
      locationPermission.value = permission;
      // Immediately trigger permission check after request
      checkPermissions();

      _logger.info('Background location permission request result: $permission');
      FirebaseAnalytics.instance.logEvent(name: 'request_location_permission', parameters: {
        'background': background.toString(),
        'granted': (permission == LocationPermission.always).toString(),
      });
      return permission == LocationPermission.always;
    }

    return true;
  }

  Future<LocationPermission> _requestBackgroundLocation() async {
    _logger.fine('Requesting background location permission');
    // First request whileInUse permission if not granted
    var permission = await Geolocator.requestPermission();
    if (permission == LocationPermission.whileInUse) {
      // Then request background permission
      _logger.fine('WhileInUse granted, requesting always permission');
      permission = await Geolocator.requestPermission();
    }
    return permission;
  }

  Future<void> openSettings() async {
    _logger.info('Opening app settings');
    await openAppSettings();
  }

  Future<void> openLocationSettings() async {
    _logger.info('Opening location settings');
    await Geolocator.openLocationSettings();
    // Immediately trigger permission check after opening settings
    checkPermissions();
  }

  bool hasNotificationPermission() {
    // On iOS, notificationPermission is updated when permissions are requested
    // On Android, it's updated by the periodic check
    return notificationPermission.value?.isGranted ?? false;
  }

  bool isNotificationPermissionPermanentlyDenied() {
    if (Platform.isIOS) {
      // For iOS with FCM, if it's 'denied', the user must go to settings.
      // We treat 'denied' from FCM as 'permanently denied' for our app's logic.
      return notificationPermission.value == PermissionStatus.denied;
    }
    // For Android, permission_handler provides a specific 'permanentlyDenied' status.
    return notificationPermission.value == PermissionStatus.permanentlyDenied;
  }

  bool hasLocationPermission({bool background = false}) {
    if (!locationServiceEnabled.value) return false;

    if (GetPlatform.isIOS) {
      return locationPermission.value == LocationPermission.always || locationPermission.value == LocationPermission.whileInUse;
    }

    return background
        ? locationPermission.value == LocationPermission.always
        : locationPermission.value == LocationPermission.always || locationPermission.value == LocationPermission.whileInUse;
  }

  bool hasBackgroundLocationPermission() {
    if (!locationServiceEnabled.value) return false;
    return locationPermission.value == LocationPermission.always;
  }

  bool hasAllDriverPermissions() {
    return hasNotificationPermission() && hasBackgroundLocationPermission();
  }

  Future<bool> requestAllDriverPermissions() async {
    _logger.info('Requesting all driver permissions');
    
    // First request notification permission
    if (!hasNotificationPermission()) {
      final notificationGranted = await requestNotificationPermission();
      if (!notificationGranted) {
        _logger.warning('Notification permission denied');
        return false;
      }
    }
    
    // Then request background location permission
    if (!hasBackgroundLocationPermission()) {
      final locationGranted = await requestLocationPermission(background: true);
      if (!locationGranted) {
        _logger.warning('Background location permission denied');
        return false;
      }
    }
    
    final allGranted = hasAllDriverPermissions();
    _logger.info('All driver permissions granted: $allGranted');
    
    FirebaseAnalytics.instance.logEvent(
      name: 'driver_permissions_requested',
      parameters: {
        'all_granted': allGranted.toString(),
        'notification': hasNotificationPermission().toString(),
        'background_location': hasBackgroundLocationPermission().toString(),
      },
    );
    
    return allGranted;
  }
}
