import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

import '../config/tenant_config.dart';
import '../models/ChatMessage.dart';
import 'AuthState.dart';

class ChatState extends GetxController {
  final Logger _logger = Logger('ChatState');
  final String sessionId;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthState _authState = Get.find<AuthState>();

  final RxList<ChatMessage> messages = <ChatMessage>[].obs;
  final RxBool isLoading = true.obs;
  final RxInt unreadCount = 0.obs;

  StreamSubscription? _messagesSubscription;

  static const int _messagesLimit = 50;

  ChatState({required this.sessionId}) {
    _initializeChat();
  }

  @override
  void onClose() {
    _messagesSubscription?.cancel();
    super.onClose();
  }

  Future<void> _initializeChat() async {
    _setupRealtimeListener();
    _markMessagesAsRead();
  }

  void _setupRealtimeListener() {
    try {
      isLoading.value = true;

      // ✅ Single real-time listener for 50 messages - Firebase recommended approach
      final query = _firestore
          .collection(TenantConfig.getTenantPath('chat_sessions'))
          .doc(sessionId)
          .collection('chat_messages')
          .orderBy('timestamp', descending: true)
          .limit(_messagesLimit);

      _messagesSubscription = query.snapshots().listen((snapshot) {
        try {
          // ✅ Firebase efficiently handles what changed - only pay for actual changes
          final newMessages = snapshot.docs.map((doc) => ChatMessage.fromFirestore(doc)).toList();

          messages.value = newMessages;

          // Mark new messages as read if they're not from current user
          final currentUser = _authState.currentMobileUser.value;
          if (currentUser != null) {
            for (var message in newMessages) {
              if (message.senderUid != currentUser.uid && !message.isRead(currentUser.uid)) {
                _markMessageAsRead(message.id);
              }
            }
          }

          isLoading.value = false;
        } catch (e) {
          _logger.severe('Error processing real-time messages', e);
          isLoading.value = false;
        }
      }, onError: (error) {
        _logger.severe('Error in real-time listener', error);
        isLoading.value = false;
      });
    } catch (e) {
      _logger.severe('Error setting up real-time listener', e);
      isLoading.value = false;
    }
  }

  Future<void> sendMessage(String text, {String? imageUrl, String? imagePath}) async {
    if (text.isEmpty && imageUrl == null && imagePath == null) return;

    final currentUser = _authState.currentMobileUser.value;
    if (currentUser == null) return;

    try {
      final messageData = {
        'senderUid': currentUser.uid,
        'senderType': SenderType.passenger.toString().split('.').last,
        'message': text,
        'timestamp': FieldValue.serverTimestamp(),
        'readBy': {currentUser.uid: FieldValue.serverTimestamp()},
        'messageType': (imageUrl != null || imagePath != null)
            ? MessageType.image.toString().split('.').last
            : MessageType.text.toString().split('.').last,
      };

      // Store the path or URL in imageUrl field for backward compatibility
      if (imagePath != null) {
        messageData['imageUrl'] = imagePath;
      } else if (imageUrl != null) {
        messageData['imageUrl'] = imageUrl;
      }

      // ✅ Add message to Firestore - real-time listener will automatically update UI
      await _firestore
          .collection(TenantConfig.getTenantPath('chat_sessions'))
          .doc(sessionId)
          .collection('chat_messages')
          .add(messageData);

      // Update session's last message
      await _firestore.collection(TenantConfig.getTenantPath('chat_sessions')).doc(sessionId).update({
        'lastMessageAt': FieldValue.serverTimestamp(),
        'lastMessage': text.isNotEmpty ? text : '📷 Image',
      });
    } catch (e) {
      _logger.severe('Error sending message', e);
      Get.snackbar(
        'Error',
        'Failed to send message. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> _markMessagesAsRead() async {
    final currentUser = _authState.currentMobileUser.value;
    if (currentUser == null) return;

    // Mark unread messages as read
    for (var message in messages) {
      if (!message.isRead(currentUser.uid) && message.senderUid != currentUser.uid) {
        await _markMessageAsRead(message.id);
      }
    }
  }

  Future<void> _markMessageAsRead(String messageId) async {
    final currentUser = _authState.currentMobileUser.value;
    if (currentUser == null) return;

    try {
      await _firestore
          .collection(TenantConfig.getTenantPath('chat_sessions'))
          .doc(sessionId)
          .collection('chat_messages')
          .doc(messageId)
          .update({
        'readBy.${currentUser.uid}': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      _logger.severe('Error marking message as read', e);
    }
  }

  void updateUnreadCount() {
    final currentUser = _authState.currentMobileUser.value;
    if (currentUser == null) return;

    int count = 0;
    for (var message in messages) {
      if (!message.isRead(currentUser.uid) && message.senderUid != currentUser.uid) {
        count++;
      }
    }
    unreadCount.value = count;
  }
}
