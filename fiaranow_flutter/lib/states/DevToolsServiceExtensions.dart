import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:developer' show ServiceExtensionResponse;

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

import 'AppState.dart';
import 'AuthState.dart';

/// Manages service extensions for DevTools integration
/// Allows external tools to trigger actions in the app
class DevToolsServiceExtensions extends GetxController {
  final Logger _logger = Logger('DevToolsServiceExtensions');
  
  // Service extension name
  static const String _extensionName = 'ext.fiaranow.trigger_agent_action';
  
  @override
  void onInit() {
    super.onInit();
    _logger.info('Initializing DevTools Service Extensions');
    registerAgentServiceExtensions();
  }
  
  /// Registers the service extension for agent actions
  void registerAgentServiceExtensions() {
    if (!kDebugMode) {
      _logger.warning('Service extensions are only available in debug mode');
      return;
    }
    
    developer.registerExtension(
      _extensionName,
      (String method, Map<String, String> parameters) async {
        _logger.info('Service extension called: $method with parameters: $parameters');
        
        try {
          // Extract parameters
          final String action = parameters['action'] ?? '';
          final String prompt = parameters['prompt'] ?? '';
          final String target = parameters['target'] ?? '';
          
          if (action.isEmpty) {
            return _createErrorResponse(
              code: -32602,
              message: 'Invalid params',
              data: 'Action parameter is required',
            );
          }
          
          // Handle the action
          final result = await _handleAction(
            action: action,
            prompt: prompt,
            target: target,
          );
          
          return _createSuccessResponse(result);
        } catch (e, stackTrace) {
          _logger.severe('Error handling service extension', e, stackTrace);
          return _createErrorResponse(
            code: ServiceExtensionResponse.extensionError,
            message: 'Internal error',
            data: e.toString(),
          );
        }
      },
    );
    
    _logger.info('Service extension registered: $_extensionName');
  }
  
  /// Handles different agent actions
  Future<Map<String, dynamic>> _handleAction({
    required String action,
    required String prompt,
    required String target,
  }) async {
    _logger.info('Handling action: $action');
    
    switch (action) {
      case 'trigger_auth_action':
        return await _handleAuthAction(prompt, target);
        
      case 'trigger_config_reload':
        return await _handleConfigReload(prompt, target);
        
      case 'simulate_connection_loss':
        return await _handleConnectionLoss(prompt, target);
        
      case 'change_user_tendency':
        return await _handleUserTendencyChange(prompt, target);
        
      case 'get_state':
        return await _handleGetState();
        
      default:
        throw ArgumentError('Unknown action: $action');
    }
  }
  
  /// Handles authentication state changes
  Future<Map<String, dynamic>> _handleAuthAction(String prompt, String target) async {
    final authState = Get.find<AuthState>();
    
    switch (target) {
      case 'logout':
        await authState.logout();
        return {
          'success': true,
          'action': 'trigger_auth_action',
          'target': target,
          'result': 'User logged out successfully',
          'currentUser': null,
        };
        
      case 'refresh':
        final user = authState.currentUser.value;
        if (user != null) {
          await user.reload();
          authState.currentUser.refresh();
        }
        return {
          'success': true,
          'action': 'trigger_auth_action',
          'target': target,
          'result': 'User authentication refreshed',
          'currentUser': user?.uid,
        };
        
      case 'check_status':
        final user = authState.currentUser.value;
        final mobileUser = authState.currentMobileUser.value;
        return {
          'success': true,
          'action': 'trigger_auth_action',
          'target': target,
          'result': 'Authentication status retrieved',
          'currentUser': user?.uid,
          'mobileUser': mobileUser?.toFirestore(),
          'isAuthenticated': user != null,
        };
        
      default:
        throw ArgumentError('Unknown auth target: $target');
    }
  }
  
  /// Handles configuration reload
  Future<Map<String, dynamic>> _handleConfigReload(String prompt, String target) async {
    final appState = Get.find<AppState>();
    
    // Force reload configuration from Firestore
    await appState.refreshConfiguration();
    
    return {
      'success': true,
      'action': 'trigger_config_reload',
      'target': target,
      'result': 'Configuration reloaded successfully',
      'currentVersion': appState.currentVersion.value,
      'requiredMinimumVersion': appState.requiredMinimumVersion.value,
      'underMaintenanceMessage': appState.underMaintenanceMessage.value,
      'userTendency': appState.userTendency.value.name,
      'tripConfiguration': appState.tripConfiguration.value.toJson(),
    };
  }
  
  /// Handles connection loss simulation
  Future<Map<String, dynamic>> _handleConnectionLoss(String prompt, String target) async {
    final appState = Get.find<AppState>();
    
    switch (target) {
      case 'offline':
        appState.isOnline.value = false;
        return {
          'success': true,
          'action': 'simulate_connection_loss',
          'target': target,
          'result': 'App is now in offline mode',
          'isOnline': false,
        };
        
      case 'online':
        appState.isOnline.value = true;
        return {
          'success': true,
          'action': 'simulate_connection_loss',
          'target': target,
          'result': 'App is now in online mode',
          'isOnline': true,
        };
        
      case 'toggle':
        appState.isOnline.value = !appState.isOnline.value;
        return {
          'success': true,
          'action': 'simulate_connection_loss',
          'target': target,
          'result': 'Connection status toggled',
          'isOnline': appState.isOnline.value,
        };
        
      default:
        throw ArgumentError('Unknown connection target: $target');
    }
  }
  
  /// Handles user tendency changes
  Future<Map<String, dynamic>> _handleUserTendencyChange(String prompt, String target) async {
    final appState = Get.find<AppState>();
    
    UserTendency? newTendency;
    
    switch (target) {
      case 'ride_now':
      case 'rideNow':
        newTendency = UserTendency.rideNow;
        break;
      case 'reserve':
        newTendency = UserTendency.reserve;
        break;
      default:
        // Try to parse from prompt if target is not recognized
        if (prompt.toLowerCase().contains('ride now')) {
          newTendency = UserTendency.rideNow;
        } else if (prompt.toLowerCase().contains('reserve')) {
          newTendency = UserTendency.reserve;
        } else {
          throw ArgumentError('Unknown user tendency target: $target');
        }
    }
    
    final previousTendency = appState.userTendency.value;
    appState.userTendency.value = newTendency;
    
    // Save to preferences
    await appState.saveUserTendency(newTendency);
    
    return {
      'success': true,
      'action': 'change_user_tendency',
      'target': target,
      'result': 'User tendency changed successfully',
      'previousTendency': previousTendency.name,
      'newTendency': newTendency.name,
    };
  }
  
  /// Handles getting current app state
  Future<Map<String, dynamic>> _handleGetState() async {
    final appState = Get.find<AppState>();
    final authState = Get.find<AuthState>();
    
    return {
      'success': true,
      'action': 'get_state',
      'result': {
        'isOnline': appState.isOnline.value,
        'userTendency': appState.userTendency.value.toString(),
        'isAuthenticated': authState.currentUser.value != null,
        'currentUser': authState.currentUser.value?.email,
        'currentVersion': appState.currentVersion.value,
        'underMaintenanceMessage': appState.underMaintenanceMessage.value,
        'requiredMinimumVersion': appState.requiredMinimumVersion.value,
        'clockDriftMillis': appState.clockDriftMillis.value,
      },
    };
  }
  
  /// Creates a success response in JSON-RPC format
  ServiceExtensionResponse _createSuccessResponse(Map<String, dynamic> result) {
    return ServiceExtensionResponse.result(
      jsonEncode({
        'result': result,
        'timestamp': DateTime.now().toIso8601String(),
      }),
    );
  }
  
  /// Creates an error response in JSON-RPC format
  ServiceExtensionResponse _createErrorResponse({
    required int code,
    required String message,
    String? data,
  }) {
    return ServiceExtensionResponse.error(
      code,
      jsonEncode({
        'error': {
          'code': code,
          'message': message,
          if (data != null) 'data': data,
        },
        'timestamp': DateTime.now().toIso8601String(),
      }),
    );
  }
}