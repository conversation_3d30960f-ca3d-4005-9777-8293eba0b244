import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:fiaranow_flutter/models/EventLog.dart';
import 'package:fiaranow_flutter/models/MobileUser.dart';
import 'package:fiaranow_flutter/models/ServiceStatusUpdate.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:logging/logging.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../config/tenant_config.dart';
import '../fcm.dart';
import '../l10n/app_localizations.dart';
import 'DevToolsService.dart';

class AuthState extends GetxController {
  final Logger _logger = Logger('Auth');

  var currentUser = Rxn<User>();
  var currentMobileUser = Rxn<MobileUser>();
  var uid = '';
  var deviceId = ''; // Store the current device ID

  StreamSubscription<DocumentSnapshot<MobileUser>>? _currentMobileUserSub;
  Timer? _lastSeenTimer;
  StreamSubscription<DocumentSnapshot>? _deviceVerificationSub;
  Timer? _fcmTokenRetryTimer;

  @override
  void onInit() {
    super.onInit();
    _logger.info('Initializing AuthState');

    // Set the current user when the controller is initialized
    currentUser.value = FirebaseAuth.instance.currentUser;

    // Get device ID
    _initializeDeviceId();

    // Listen to auth state changes
    FirebaseAuth.instance.authStateChanges().listen((User? user) {
      _logger.info('Auth state changed: ${user?.uid ?? 'logged out'}');

      // Log auth state change with structured data
      final agentLoggingService = Get.find<AgentLoggingService>();
      agentLoggingService.logAgentEvent('auth.state_changed', data: {
        'isLoggedIn': user != null,
        'uid': user?.uid,
        'email': user?.email,
        'displayName': user?.displayName,
        'timestamp': DateTime.now().toIso8601String()
      });

      currentUser.value = user;

      // If logged-in, update the firestore `/mobile_users/{uid}` entry
      if (uid == '' && user != null && user.uid.isNotEmpty) {
        //
        // This part of the code is executed only once when the user logs in.
        // Note that this is not the `MobileUser` instance but the `User` instance (Firebase).
        //

        FirebaseCrashlytics.instance.setUserIdentifier(user.uid);
        FirebaseCrashlytics.instance.setCustomKey('email', user.email ?? '');
        FirebaseCrashlytics.instance.setCustomKey('name', user.displayName ?? '');

        FirebaseAnalytics.instance.setUserId(id: user.uid);

        // Log device verification start
        final agentLoggingService = Get.find<AgentLoggingService>();
        agentLoggingService.logAgentEvent('auth.device_verification_start',
            data: {'uid': user.uid, 'deviceId': deviceId, 'timestamp': DateTime.now().toIso8601String()});

        // Check if user is already logged in on another device
        _verifyDeviceAndCompleteLogin(user);
      } else if (uid != '' && user == null) {
        uid = '';

        // Reset the current mobile user and cancel the subscriptions
        _currentMobileUserSub?.cancel();
        _currentMobileUserSub = null;
        _deviceVerificationSub?.cancel();
        _deviceVerificationSub = null;
        currentMobileUser.value = null;

        // Log sign-out event
        final agentLoggingService = Get.find<AgentLoggingService>();
        agentLoggingService
            .logAgentEvent('auth.sign_out', data: {'reason': 'user_initiated', 'timestamp': DateTime.now().toIso8601String()});

        // Cancel the timer if the user signs out
        _lastSeenTimer?.cancel();
        _lastSeenTimer = null;
      }
    });
  }

  // Initialize device ID
  Future<void> _initializeDeviceId() async {
    try {
      // Try to get the stored device ID first
      final prefs = await SharedPreferences.getInstance();
      final storedDeviceId = prefs.getString('device_id');

      // If we already have a stored device ID, use it
      if (storedDeviceId != null && storedDeviceId.isNotEmpty) {
        deviceId = storedDeviceId;
        _logger.info('Retrieved stored device ID: $deviceId');
        return;
      }

      // Otherwise, generate a new device ID
      final deviceInfoPlugin = DeviceInfoPlugin();
      if (defaultTargetPlatform == TargetPlatform.android) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        deviceId = androidInfo.id;
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        deviceId = iosInfo.identifierForVendor ?? '';
      }

      // If we couldn't get a device ID, generate a random one
      if (deviceId.isEmpty) {
        deviceId = DateTime.now().millisecondsSinceEpoch.toString();
        _logger.info('Generated random device ID: $deviceId');
      } else {
        _logger.info('Retrieved device ID from device: $deviceId');
      }

      // Store the device ID for future use
      await prefs.setString('device_id', deviceId);
    } catch (e) {
      _logger.severe('Error getting device ID: $e');

      try {
        // Try to get the stored device ID as a fallback
        final prefs = await SharedPreferences.getInstance();
        final storedDeviceId = prefs.getString('device_id');

        if (storedDeviceId != null && storedDeviceId.isNotEmpty) {
          deviceId = storedDeviceId;
          _logger.info('Retrieved stored device ID after error: $deviceId');
          return;
        }

        // Generate a random ID as last resort
        deviceId = DateTime.now().millisecondsSinceEpoch.toString();
        // Store the random device ID
        await prefs.setString('device_id', deviceId);
        _logger.info('Generated and stored random device ID after error: $deviceId');
      } catch (innerError) {
        // If all else fails, just generate a random ID without storing it
        deviceId = DateTime.now().millisecondsSinceEpoch.toString();
        _logger.severe('Error storing device ID: $innerError');
      }
    }
  }

  // Verify device and complete login
  Future<void> _verifyDeviceAndCompleteLogin(User user) async {
    uid = user.uid;

    // Log Firebase JWT token for agent inspection
    final agentLoggingService = Get.find<AgentLoggingService>();

    // Get user document reference
    final userDocRef = FirebaseFirestore.instance.collection('mobile_users').doc(user.uid);

    // Get current user data
    try {
      final userDoc = await userDocRef.get();

      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        final storedDeviceId = userData['deviceId'];

        // If device ID exists and is different from current device
        if (storedDeviceId != null && storedDeviceId != '' && storedDeviceId != deviceId) {
          _logger.warning('User already logged in on another device: $storedDeviceId');

          // Show dialog to user
          final logoutOtherDevices = await Get.dialog<bool>(
            AlertDialog(
              title: Text(AppLocalizations.of(Get.context!)!.auth_accountInUseTitle),
              content: Text(AppLocalizations.of(Get.context!)!.auth_accountInUseMessage),
              actions: [
                TextButton(
                  onPressed: () => Get.back(result: false),
                  child: Text(AppLocalizations.of(Get.context!)!.auth_cancelLogin),
                ),
                TextButton(
                  onPressed: () => Get.back(result: true),
                  child: Text(AppLocalizations.of(Get.context!)!.auth_logoutOtherDevices),
                ),
              ],
            ),
            barrierDismissible: false,
          );
          if (logoutOtherDevices == true) {
            // Log forced logout decision
            agentLoggingService.logAgentEvent('auth.forced_logout_decision', data: {
              'decision': 'logout_other_devices',
              'uid': user.uid,
              'currentDeviceId': deviceId,
              'previousDeviceId': storedDeviceId,
              'timestamp': DateTime.now().toIso8601String()
            });
            _forceLogoutOtherDevices(user);
            // Continue with login, don't return
          } else {
            // Log user cancellation
            agentLoggingService.logAgentEvent('auth.forced_logout_decision', data: {
              'decision': 'cancel_login',
              'uid': user.uid,
              'currentDeviceId': deviceId,
              'previousDeviceId': storedDeviceId,
              'timestamp': DateTime.now().toIso8601String()
            });
            // Log out current user from Firebase Auth
            FirebaseAuth.instance.signOut();
            // Sign out from Google Sign-in
            GoogleSignIn().signOut();
            Get.back();
            // Close the application
            SystemNavigator.pop();
            return;
          }
        }
      }

      // Log sign-in event
      agentLoggingService.logAgentEvent('auth.sign_in',
          data: {'uid': user.uid, 'email': user.email, 'deviceId': deviceId, 'timestamp': DateTime.now().toIso8601String()});

      // Update user data with current device ID
      await userDocRef.set(
        {
          'uid': user.uid,
          'email': user.email,
          'displayName': (user.displayName?.isEmpty ?? false) ? user.email : user.displayName,
          'photoURL': user.photoURL,
          'lastSeen': FieldValue.serverTimestamp(), // Server time
          'lastSeenDT': DateTime.now(), // Device time
          'deviceId': deviceId, // Store device ID
        },
        SetOptions(merge: true),
      );

      // Ensure tenant state exists for current tenant
      const tenantId = TenantConfig.TENANT_ID;
      final tenantStateRef = userDocRef.collection('tenant_states').doc(tenantId);

      try {
        final tenantStateDoc = await tenantStateRef.get();

        if (!tenantStateDoc.exists) {
          // Create new tenant state
          await tenantStateRef.set({
            'uid': user.uid,
            'tenantId': tenantId,
            'isActive': true,
            'isServiceActive': false,
            'driverTags': [],
            'joinedAt': FieldValue.serverTimestamp(),
            'lastActiveAt': FieldValue.serverTimestamp(),
          });

          _logger.info('Created new tenant state for user $uid in tenant $tenantId');
        } else {
          // Update lastActiveAt for existing tenant state
          await tenantStateRef.update({
            'lastActiveAt': FieldValue.serverTimestamp(),
          });
        }

        // Ensure user's tenantIDs array includes current tenant
        final currentTenantIDs = (await userDocRef.get()).data()?['tenantIDs'] as List<dynamic>?;
        if (currentTenantIDs == null || !currentTenantIDs.contains(tenantId)) {
          await userDocRef.update({
            'tenantIDs': FieldValue.arrayUnion([tenantId]),
          });
        }
      } catch (e) {
        _logger.severe('Error creating/updating tenant state: $e');
        // Continue with login even if tenant state creation fails
      }

      // Log Firebase user data updates
      agentLoggingService.logAgentEvent('firebase.user_data_updated', data: {
        'uid': user.uid,
        'email': user.email,
        'displayName': (user.displayName?.isEmpty ?? false) ? user.email : user.displayName,
        'deviceId': deviceId,
        'timestamp': DateTime.now().toIso8601String()
      });

      // Initialize comprehensive FCM token management
      await _initializeFCMTokenManagement();
    } catch (e) {
      _logger.severe('Network error during login verification: $e');

      // Log sign-out event
      final agentLoggingService = Get.find<AgentLoggingService>();
      agentLoggingService.logAgentEvent('auth.sign_out',
          data: {'reason': 'network_error', 'uid': uid, 'error': e.toString(), 'timestamp': DateTime.now().toIso8601String()});

      // Sign out the user
      FirebaseAuth.instance.signOut();
      GoogleSignIn().signOut();
      uid = ''; // Reset uid

      // Show a snackbar for network error
      Get.showSnackbar(
        GetSnackBar(
          title: AppLocalizations.of(Get.context!)!.auth_networkErrorTitle,
          message: AppLocalizations.of(Get.context!)!.auth_networkErrorMessage,
          duration: const Duration(seconds: 15),
          isDismissible: true,
          backgroundColor: Colors.red,
          icon: const Icon(Icons.signal_wifi_off, color: Colors.white),
        ),
      );

      return;
    }

    // Listen to the firestore `/mobile_users/{uid}` entry
    Locale? lastLocale;
    _currentMobileUserSub = mobileUsersColl.doc(user.uid).snapshots().listen((doc) {
      currentMobileUser.value = doc.data();

      // If the primary language has changed, update the locale
      if (lastLocale != currentMobileUser.value?.primaryLanguage && currentMobileUser.value?.primaryLanguage != null) {
        lastLocale = currentMobileUser.value?.primaryLanguage;
        if (lastLocale != null) {
          Get.updateLocale(lastLocale!);
        }
      }
    });

    // Start the timer to update lastSeen and lastSeenDT every 20 seconds
    _lastSeenTimer = Timer.periodic(const Duration(seconds: 20), (timer) {
      FirebaseFirestore.instance.collection('mobile_users').doc(uid).update({
        'lastSeen': FieldValue.serverTimestamp(),
        'lastSeenDT': DateTime.now(),
      });
    });

    // Listen for device ID changes (to detect forced logout)
    _deviceVerificationSub = FirebaseFirestore.instance.collection('mobile_users').doc(uid).snapshots().listen((snapshot) {
      if (snapshot.exists) {
        final data = snapshot.data() as Map<String, dynamic>;
        final storedDeviceId = data['deviceId'];

        // If device ID changed and doesn't match current device, log out
        if (storedDeviceId != null && storedDeviceId != deviceId) {
          _logger.warning('Forced logout: Device ID changed');

          // Log forced logout event
          final agentLoggingService = Get.find<AgentLoggingService>();
          agentLoggingService.logAgentEvent('auth.forced_logout', data: {
            'reason': 'device_id_changed',
            'uid': uid,
            'currentDeviceId': deviceId,
            'storedDeviceId': storedDeviceId,
            'timestamp': DateTime.now().toIso8601String()
          });

          // Sign out from Firebase Auth
          FirebaseAuth.instance.signOut();

          // Sign out from Google Sign-in
          GoogleSignIn().signOut();

          // Show message to user
          Get.dialog<void>(
            AlertDialog(
              title: Text(AppLocalizations.of(Get.context!)!.auth_forcedLogoutTitle),
              content: Text(AppLocalizations.of(Get.context!)!.auth_forcedLogoutMessage),
              actions: [
                TextButton(
                  onPressed: () {
                    Get.back();
                    // Close the application
                    SystemNavigator.pop();
                  },
                  child: Text(AppLocalizations.of(Get.context!)!.auth_forcedLogoutButton),
                ),
              ],
            ),
            barrierDismissible: false,
          );
        }
      }
    });
  }

  // Force logout from other devices
  Future<void> _forceLogoutOtherDevices(User user) async {
    try {
      // Update device ID to current device
      await FirebaseFirestore.instance.collection('mobile_users').doc(user.uid).update({
        'deviceId': deviceId,
      });

      _logger.info('Forced logout from other devices');
    } catch (e) {
      _logger.severe('Error forcing logout: $e');
    }
  }

  /// Set FCM token with retry mechanism and proper error handling
  Future<void> _setFCMToken() async {
    const int maxRetries = 5;
    const Duration retryDelay = Duration(seconds: 3);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        _logger.info('🔑 FCM Token attempt $attempt/$maxRetries');

        String? token;
        if (defaultTargetPlatform == TargetPlatform.iOS) {
          // For iOS, use the robust getFCMToken function from fcm.dart
          token = await getFCMToken();

          // If token is still null, try alternative approach for iOS simulator
          if (token == null) {
            _logger.info('⚠️ APNS token not available, trying direct FCM token (iOS simulator fallback)');
            token = await FirebaseMessaging.instance.getToken();
          }
        } else {
          // For Android and other platforms
          token = await FirebaseMessaging.instance.getToken();
        }

        if (token != null) {
          await FirebaseFirestore.instance.collection('mobile_users').doc(uid).update({
            'fcmToken': token,
          });
          _logger.info('✅ FCM token successfully set on attempt $attempt');

          // Cancel any pending retry timer since we succeeded
          _fcmTokenRetryTimer?.cancel();
          _fcmTokenRetryTimer = null;
          return;
        } else {
          _logger.warning('⚠️ FCM token is null on attempt $attempt');
        }
      } catch (e) {
        _logger.severe('❌ FCM token setting failed on attempt $attempt: $e');
      }

      // Wait before retry (except on last attempt)
      if (attempt < maxRetries) {
        _logger.info('⏳ Retrying FCM token in ${retryDelay.inSeconds} seconds...');
        await Future.delayed(retryDelay);
      }
    }

    _logger.severe('❌ FCM token setting failed after $maxRetries attempts');

    // Start a periodic retry timer for cases where token becomes available later
    _startPeriodicFCMTokenRetry();
  }

  /// Start periodic retry for FCM token (useful for iOS simulator and edge cases)
  void _startPeriodicFCMTokenRetry() {
    _fcmTokenRetryTimer?.cancel();
    _fcmTokenRetryTimer = Timer.periodic(const Duration(minutes: 2), (timer) async {
      _logger.info('🔄 Periodic FCM token retry');

      try {
        // Check if token is already set
        final userDoc = await FirebaseFirestore.instance.collection('mobile_users').doc(uid).get();
        final currentToken = userDoc.data()?['fcmToken'] as String?;

        if (currentToken != null && currentToken.isNotEmpty) {
          _logger.info('✅ FCM token already exists, stopping periodic retry');
          timer.cancel();
          return;
        }

        // Try to get token again
        String? token;
        if (defaultTargetPlatform == TargetPlatform.iOS) {
          token = await getFCMToken();
          token ??= await FirebaseMessaging.instance.getToken();
        } else {
          token = await FirebaseMessaging.instance.getToken();
        }

        if (token != null) {
          await FirebaseFirestore.instance.collection('mobile_users').doc(uid).update({
            'fcmToken': token,
          });
          _logger.info('✅ FCM token set successfully via periodic retry');
          timer.cancel();
        }
      } catch (e) {
        _logger.warning('⚠️ Periodic FCM token retry failed: $e');
      }
    });
  }

  /// Set up FCM token refresh listener with robust error handling
  void _setupFCMTokenRefreshListener() {
    FirebaseMessaging.instance.onTokenRefresh.listen((newToken) async {
      _logger.info('🔄 FCM token refresh received');

      try {
        if (defaultTargetPlatform == TargetPlatform.iOS) {
          // For iOS, check APNS token availability
          final apnsToken = await FirebaseMessaging.instance.getAPNSToken();
          if (apnsToken == null) {
            _logger.warning('⚠️ APNS token is null during token refresh, trying anyway (iOS simulator)');
          }
        }

        await FirebaseFirestore.instance.collection('mobile_users').doc(uid).update({
          'fcmToken': newToken,
        });
        _logger.info('✅ FCM token updated successfully on refresh');
      } catch (e) {
        _logger.severe('❌ FCM token refresh update failed: $e');
      }
    });
  }

  /// Enhanced FCM token management with comprehensive monitoring
  Future<void> _initializeFCMTokenManagement() async {
    _logger.info('🔧 Initializing FCM token management');

    // Set up token refresh listener
    _setupFCMTokenRefreshListener();

    // Try to set token immediately
    await _setFCMToken();

    _logger.info('✅ FCM token management initialized');
  }

  @override
  void onClose() {
    // Cancel the timer when the controller is disposed
    _lastSeenTimer?.cancel();
    _fcmTokenRetryTimer?.cancel();
    _deviceVerificationSub?.cancel();
    super.onClose();
  }

  void setPrimaryUserType(UserType userType) {
    _logger.info('Setting primary user type to: ${userType.name}');

    // Log primary user type change
    final agentLoggingService = Get.find<AgentLoggingService>();
    agentLoggingService.logAgentEvent('user.primary_type_changed', data: {
      'uid': uid,
      'userType': userType.name,
      'userTypeIndex': userType.index,
      'timestamp': DateTime.now().toIso8601String()
    });

    mobileUsersColl.doc(uid).update({'primaryUserType': userType.index});
    FirebaseAnalytics.instance.logEvent(name: 'set_primary_user_type', parameters: {'type': userType.name});
  }

  void setPrimaryLanguage(String languageCode, String countryCode) {
    mobileUsersColl.doc(uid).update({
      'primaryLanguage': {
        'languageCode': languageCode,
        'countryCode': countryCode,
      },
    });
  }

  Future<void> setServiceActive(ServiceStatusUpdate update) async {
    if (uid.isEmpty) {
      _logger.severe('Cannot set service active: uid is empty');
      FirebaseCrashlytics.instance.recordError(
        Exception('Cannot set service active: uid is empty'),
        StackTrace.current,
        fatal: false,
      );
      return;
    }

    // Check if currentMobileUser is available
    final currentUser = currentMobileUser.value;
    if (currentUser == null) {
      _logger.severe('Cannot set service active: currentMobileUser is null');
      FirebaseCrashlytics.instance.recordError(
        Exception('Cannot set service active: currentMobileUser is null'),
        StackTrace.current,
        fatal: false,
      );
      return;
    }

    // Get current tenant
    const tenantId = TenantConfig.TENANT_ID;

    // Log service active status change
    final agentLoggingService = Get.find<AgentLoggingService>();
    agentLoggingService.logAgentEvent('service.status_changed', data: {
      'uid': uid,
      'isActive': update.isActive,
      'reasonType': update.reasonType.toString(),
      'customReason': update.customReason,
      'tenantId': tenantId,
      'timestamp': DateTime.now().toIso8601String()
    });

    try {
      await Future.wait([
        // Update tenant state instead of mobile user status
        mobileUsersColl.doc(uid).collection('tenant_states').doc(tenantId).update({'isServiceActive': update.isActive}),

        // Create event log
        eventLogsColl.add(EventLog(
          id: '', // Firestore will generate this
          uid: uid,
          type: EventLogType.driverServiceStatusUpdate,
          timestamp: DateTime.now(), // This will be overridden by FieldValue.serverTimestamp()
          timestampDT: DateTime.now(), // Device time
          driver: {
            'uid': currentUser.uid,
            'displayName': currentUser.displayName,
            'photoURL': currentUser.photoURL,
            'email': currentUser.email,
          },
          serviceStatusReasonType: update.reasonType,
          reason: update.customReason,
        )),
      ]);

      _logger.info('✅ Service status updated successfully: ${update.isActive ? "activated" : "deactivated"}');

      // Track successful service status change
      FirebaseAnalytics.instance.logEvent(name: 'service_status_changed_success', parameters: {
        'uid': uid,
        'isActive': update.isActive.toString(),
        'reasonType': update.reasonType.toString(),
        'tenantId': tenantId,
      });
    } catch (error, stackTrace) {
      _logger.severe('❌ Error updating service status: $error', error, stackTrace);

      // Report the error to Crashlytics with detailed context
      FirebaseCrashlytics.instance.recordError(
        error,
        stackTrace,
        reason: 'service_status_change_error',
        fatal: false,
      );

      // Also track a specific error event with context
      FirebaseAnalytics.instance.logEvent(name: 'service_status_change_error', parameters: {
        'uid': uid,
        'isActive': update.isActive.toString(),
        'reasonType': update.reasonType.toString(),
        'tenantId': tenantId,
        'error': error.toString(),
        'error_type': error.runtimeType.toString(),
      });

      // Log additional error details for debugging
      agentLoggingService.logAgentError('service_status_change_failed', stackTrace: stackTrace, context: {
        'uid': uid,
        'isActive': update.isActive,
        'reasonType': update.reasonType.toString(),
        'tenantId': tenantId,
        'operation': 'setServiceActive',
      });

      // Re-throw the error so the UI can handle it appropriately
      rethrow;
    }

    FirebaseAnalytics.instance.logEvent(name: 'set_service_active', parameters: {'is_active': update.isActive.toString()});
  }

  void setPhoneNumber(String phoneNumber) {
    mobileUsersColl.doc(uid).update({'phoneNumber': phoneNumber});
  }

  /// Logs out the current user
  Future<void> logout() async {
    _logger.info('Logging out user: $uid');

    try {
      // Log sign-out event
      final agentLoggingService = Get.find<AgentLoggingService>();
      agentLoggingService.logAgentEvent('auth.sign_out',
          data: {'reason': 'user_initiated', 'uid': uid, 'timestamp': DateTime.now().toIso8601String()});

      // Sign out from Firebase Auth
      await FirebaseAuth.instance.signOut();

      // Sign out from Google Sign-in
      await GoogleSignIn().signOut();

      _logger.info('User logged out successfully');
    } catch (e) {
      _logger.severe('Error during logout', e);
      rethrow;
    }
  }
}
