import 'dart:async';
import 'dart:math' as math;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fiaranow_flutter/models/Trip.dart';
import 'package:fiaranow_flutter/models/TripStatus.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_google_places_sdk/flutter_google_places_sdk.dart' as places_sdk;
import 'package:geocoding/geocoding.dart';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:logging/logging.dart';

import '../../config/tenant_config.dart';
import '../../models/MobileUser.dart';
import '../AppState.dart';
import 'BaseNavigationState.dart';

enum PreparationSteps {
  choosingPositions, // -> positionsConfirmed
  reservingTrip, // -> tripCreated
  selectingDriver, // -> tripCreated
  tripCreated,
}

mixin PreparationMixin on BaseNavigationState {
  final Logger _logger = Logger('Preparation');

  // -----------------
  // Address Input Controllers & State
  // -----------------
  final TextEditingController startAddressController = TextEditingController();
  final TextEditingController destinationAddressController = TextEditingController();
  var isStartAddressFocused = false.obs;
  var isDestinationAddressFocused = false.obs;
  var startAddress = RxnString();
  var destinationAddress = RxnString();

  // -----------------
  // Place Predictions
  // -----------------
  var startAddressPredictions = <places_sdk.AutocompletePrediction>[].obs;
  var destinationAddressPredictions = <places_sdk.AutocompletePrediction>[].obs;
  Timer? _predictionsDebounceTimer;

  // -----------------
  // Route State
  // -----------------
  var routeOverviews = <RouteData>[].obs;
  var selectedRouteIndex = 0.obs;
  var routeChosen = false.obs;
  UserTendency _tendencyAtStart = UserTendency.rideNow;

  // -----------------
  // Trip State
  // -----------------
  StreamSubscription<DocumentSnapshot<Trip>>? currentTripSub;
  var currentRiderTrip = Rxn<Trip>();
  String? currentRequestedPaymentMethod;
  var passengerCount = 1.obs;

  // -----------------
  // Address Management Methods
  // -----------------
  void clearStartAddress() {
    startAddressController.clear();
    startPosition.value = null;
    startAddressPredictions.clear();
    startAddress.value = null;
    routeOverviews.clear();
    routeChosen.value = false;
    clearCurrentRiderTrip();
  }

  void clearDestinationAddress() {
    destinationAddressController.clear();
    destinationPosition.value = null;
    destinationAddressPredictions.clear();
    destinationAddress.value = null;
    routeOverviews.clear();
    routeChosen.value = false;
    clearCurrentRiderTrip();
  }

  Future<void> populateAddressNameFromPosition(LatLng position, bool isStartAddress) async {
    if (isStartAddress && startAddress.value?.isNotEmpty == true) return;
    if (!isStartAddress && destinationAddress.value?.isNotEmpty == true) return;

    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(position.latitude, position.longitude);
      if (placemarks.isNotEmpty) {
        Placemark placemark = placemarks.first;
        List<String> addressParts = [];

        if (placemark.name != null && placemark.name!.isNotEmpty) addressParts.add(placemark.name!);
        if (placemark.locality != null && placemark.locality!.isNotEmpty) addressParts.add(placemark.locality!);
        if (placemark.country != null && placemark.country!.isNotEmpty) addressParts.add(placemark.country!);

        String address = addressParts.join(', ');

        if (isStartAddress) {
          startAddress.value = address;
          startAddressController.text = address;
        } else {
          destinationAddress.value = address;
          destinationAddressController.text = address;
        }
      } else {
        String shortCode = '${position.latitude.toStringAsFixed(4)}, ${position.longitude.toStringAsFixed(4)}';
        if (isStartAddress) {
          startAddress.value = shortCode;
          startAddressController.text = shortCode;
        } else {
          destinationAddress.value = shortCode;
          destinationAddressController.text = shortCode;
        }
      }
    } catch (e) {
      String shortCode = '${position.latitude.toStringAsFixed(4)}, ${position.longitude.toStringAsFixed(4)}';
      if (isStartAddress) {
        startAddress.value = shortCode;
        startAddressController.text = shortCode;
      } else {
        destinationAddress.value = shortCode;
        destinationAddressController.text = shortCode;
      }
    }
  }

  // -----------------
  // Place Prediction Methods
  // -----------------
  Future<void> getPlacePredictions(String input, bool isStartAddress, Function onError) async {
    if (_predictionsDebounceTimer?.isActive ?? false) _predictionsDebounceTimer!.cancel();

    if (input.isEmpty) {
      if (isStartAddress) {
        startAddressPredictions.clear();
      } else {
        destinationAddressPredictions.clear();
      }
      return;
    }

    _predictionsDebounceTimer = Timer(const Duration(milliseconds: 500), () async {
      try {
        final response = await placesSdk.findAutocompletePredictions(
          input,
          countries: ['MG'],
          origin: places_sdk.LatLng(lat: currentPosition.value!.latitude, lng: currentPosition.value!.longitude),
        );

        if (response.predictions.isNotEmpty) {
          if (isStartAddress) {
            startAddressPredictions.assignAll(response.predictions);
          } else {
            destinationAddressPredictions.assignAll(response.predictions);
          }
        }
      } catch (e) {
        onError(e);
      }
    });
  }

  Future<LatLng> handlePredictionPlaceSelect(places_sdk.AutocompletePrediction prediction, bool isStartAddress) async {
    final placeDetails = await placesSdk.fetchPlace(
      prediction.placeId,
      fields: [places_sdk.PlaceField.Location],
    );
    final location = placeDetails.place!.latLng!;

    if (isStartAddress) {
      startAddress.value = prediction.fullText;
      startAddressController.text = prediction.fullText;
      startAddressPredictions.clear();
      isStartAddressFocused.value = false;
      setStartPosition(LatLng(location.lat, location.lng));
    } else {
      destinationAddress.value = prediction.fullText;
      destinationAddressController.text = prediction.fullText;
      destinationAddressPredictions.clear();
      isDestinationAddressFocused.value = false;
      setDestinationPosition(LatLng(location.lat, location.lng));
    }

    return LatLng(location.lat, location.lng);
  }

  // -----------------
  // Position Management Methods
  // -----------------
  void setStartPosition(LatLng position) {
    startPosition.value = position;
    clearCurrentRiderTrip();
  }

  void setDestinationPosition(LatLng position) {
    destinationPosition.value = position;
    clearCurrentRiderTrip();
  }

  // -----------------
  // Route Management Methods
  // -----------------
  void setRouteChosen(bool chosen) {
    routeChosen.value = chosen;
    _tendencyAtStart = Get.find<AppState>().userTendency.value;
    FirebaseAnalytics.instance.logEvent(name: 'set_route_chosen', parameters: {'chosen': chosen.toString()});
  }

  PreparationSteps get preparationStep {
    if (routeChosen.value == false) {
      return PreparationSteps.choosingPositions;
    }

    if (currentRiderTrip.value == null) {
      if (_tendencyAtStart == UserTendency.reserve) {
        return PreparationSteps.reservingTrip;
      } else {
        return PreparationSteps.selectingDriver;
      }
    }

    return PreparationSteps.tripCreated;
  }

  // -----------------
  // Trip Management Methods
  // -----------------

  void restoreStateFromTrip(UserType activity, Trip trip) {
    // Set start position if available
    if (trip.startLocation != null) {
      startPosition.value = LatLng(trip.startLocation!.lat, trip.startLocation!.lon);
    }

    // Set destination position if available
    if (trip.arrivalLocation != null) {
      destinationPosition.value = LatLng(trip.arrivalLocation!.lat, trip.arrivalLocation!.lon);
    }

    // Restore passenger count
    passengerCount.value = trip.passengerCount;

    isStartAddressFocused.value = false;
    isDestinationAddressFocused.value = false;

    // Restore route overviews - use helper method for new trips with routeDataIds
    _restoreRouteOverviews(activity, trip);
  }

  Future<void> _restoreRouteOverviews(UserType activity, Trip trip) async {
    // Try to get route overviews using the new helper method
    final routes = await trip.getRouteOverviews();
    if (routes.isNotEmpty) {
      routeOverviews.assignAll(routes);
      selectedRouteIndex.value = trip.selectedRouteIndex ?? 0;
    }

    // Animate camera when map is ready
    executeWhenReady(() {
      if (mapController == null) {
        _logger.warning('MapController still null after initialization');
        return;
      }

      // Handle different location scenarios for camera positioning
      if (trip.startLocation != null && trip.arrivalLocation != null && trip.routeData != null) {
      // Scenario 1: Both locations and route data available - frame the route
      final bounds = trip.routeData!.bounds;
      mapController!.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50));
    } else if (trip.startLocation != null && trip.arrivalLocation != null) {
      // Scenario 2: Both locations but no route data - frame both with padding
      final bounds = LatLngBounds(
        southwest: LatLng(
          math.min(trip.startLocation!.lat, trip.arrivalLocation!.lat) - 0.01,
          math.min(trip.startLocation!.lon, trip.arrivalLocation!.lon) - 0.01,
        ),
        northeast: LatLng(
          math.max(trip.startLocation!.lat, trip.arrivalLocation!.lat) + 0.01,
          math.max(trip.startLocation!.lon, trip.arrivalLocation!.lon) + 0.01,
        ),
      );
      mapController!.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50));
    } else if (trip.startLocation != null) {
      // Scenario 3: Only start location - center to that with zoom level 16
      mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(trip.startLocation!.lat, trip.startLocation!.lon),
            zoom: 16.0,
          ),
        ),
      );
    } else if (currentPosition.value != null) {
      // Scenario 4: No locations - center to user's current location with zoom level 16
      mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: currentPosition.value!,
            zoom: 16.0,
          ),
        ),
      );
    }
    });

    if (activity == UserType.rider) {
      routeChosen.value = true;
      
      // CRITICAL: Final check - never set a dismissed trip as current
      if (trip.passengerDismissed == true) {
        _logger.severe('CRITICAL: Attempted to set dismissed trip ${trip.id} as current trip!');
        return;
      }
      
      // Set the current trip immediately
      currentRiderTrip.value = trip;

      currentTripSub?.cancel();
      currentTripSub = tripsColl.doc(trip.id).snapshots().listen((snapshot) {
        if (snapshot.exists) {
          final updatedTrip = snapshot.data();
          // Check if trip has been dismissed by passenger
          if (updatedTrip?.passengerDismissed == true) {
            _logger.info('Trip ${trip.id} has been dismissed by passenger, clearing it');
            clearCurrentRiderTrip();
            reset(stopCurrentTripSub: true);
          } else {
            currentRiderTrip.value = updatedTrip;
          }
        } else {
          // Trip has been deleted
          clearCurrentRiderTrip();
          reset(stopCurrentTripSub: true);
        }
      });

      // Start following driver location if trip has a driver
      if (trip.uidChosenDriver != null &&
          (trip.status == TripStatus.driverApproaching ||
              trip.status == TripStatus.driverAwaiting ||
              trip.status == TripStatus.inProgress)) {
        _logger.info('🚗 Restoring driver tracking for trip ${trip.id} with driver ${trip.uidChosenDriver}');
        Future.delayed(const Duration(seconds: 2)).then((_) {
          _logger.info('🚗 Starting driver location following after delay');
          startFollowingDriverLocation();
        });
      } else {
        _logger.info('🚗 Not restoring driver tracking - trip.uidChosenDriver: ${trip.uidChosenDriver}, status: ${trip.status}');
      }
    }
  }

  void clearCurrentRiderTrip() {
    currentTripSub?.cancel();
    currentTripSub = null;

    if (currentRiderTrip.value != null && currentRiderTrip.value!.status == TripStatus.preparing) {
      currentRiderTrip.value?.deleteTrip();
    }
    currentRiderTrip.value = null;
  }

  // -----------------
  // Driver Tracking Methods
  // -----------------
  Future<void> startListeningForNearbyDrivers(LatLng position) async {
    _logger.info('Starting to listen for nearby drivers at ${position.latitude}, ${position.longitude}');

    final geo = GeoFirePoint(GeoPoint(position.latitude, position.longitude));
    final collRef = FirebaseFirestore.instance.collection('mobile_users');
    final stream = GeoCollectionReference(collRef).subscribeWithinWithDistance(
      center: geo,
      radiusInKm: 10, // Always use a large radius to get as much drivers as possible, then filter them out
      field: 'position',
      geopointFrom: (Map<String, dynamic> data) {
        return (data['position'] as Map<String, dynamic>)['geopoint'] as GeoPoint;
      },
      queryBuilder: (query) {
        return query
            .where('isServiceActiveByTenant.${TenantConfig.TENANT_ID}', isEqualTo: true)
            .where('primaryUserType', isEqualTo: UserType.driver.index)
            .where('occupiedByTripId', isEqualTo: null);
      },
    );

    nearbyDriversStreamSubscription = stream.listen((List<GeoDocumentSnapshot> documentList) {
      _logger.fine('Received ${documentList.length} nearby drivers');

      nearbyDrivers.clear();
      nearbyDriverDetails.clear();

      var skippedDrivers = 0;
      for (var document in documentList) {
        final data = document.documentSnapshot.data() as Map<String, dynamic>;

        // Skip self (if driver)
        if (data['uid'] == authState.uid) {
          skippedDrivers++;
          continue;
        }

        // Skip if driver has not been seen (online) for more than 1 minute
        final lastSeen = (data['lastSeen'] as Timestamp).toDate();
        if (DateTime.now().difference(lastSeen).inMinutes > 1) {
          skippedDrivers++;
          continue;
        }

        final geoPoint = data['position']['geopoint'] as GeoPoint;
        nearbyDrivers.add(GeoFirePoint(geoPoint));

        // Add driver details
        nearbyDriverDetails.add(MobileUser.fromFirestore(document.documentSnapshot as DocumentSnapshot<Map<String, dynamic>>));
      }

      _logger.fine('Added ${nearbyDrivers.length} drivers (skipped $skippedDrivers)');
    });

    // Check if there's already an active trip
    if (currentRiderTrip.value == null) {
      _logger.info('Creating new preparing trip');

      // Debug logging
      _logger.info('Creating trip with passengerCount: ${passengerCount.value}');

      // Check if currentMobileUser is available
      final currentMobileUser = authState.currentMobileUser.value;
      if (currentMobileUser == null) {
        _logger.severe('Cannot create trip: currentMobileUser is null');
        return;
      }

      // Create a new Trip document that has the status of `preparing`
      final tripRef = Trip.createTrip(
        uidPassenger: authState.uid,
        status: TripStatus.preparing,
        startPosition: startPosition.value!,
        destinationPosition: destinationPosition.value!,
        user: currentMobileUser,
        routeData: routeOverviews[selectedRouteIndex.value],
        routeOverviews: routeOverviews.toList(),
        selectedRouteIndex: selectedRouteIndex.value,
        driverRouteData: null,
        startLocationName: startAddress.value,
        arrivalLocationName: destinationAddress.value,
        tripConfiguration: Get.find<AppState>().tripConfiguration.value,
        customerRequestedPaymentMethod: currentRequestedPaymentMethod!,
        passengerCount: passengerCount.value,
      );

      // Subscribe to the trip document for updates
      currentTripSub = tripRef.snapshots().listen((snapshot) {
        if (!snapshot.exists) {
          // Cancel the subscription if the document gets deleted
          currentTripSub?.cancel();
          currentTripSub = null;
          currentRiderTrip.value = null;
          return;
        }

        currentRiderTrip.value = snapshot.data()!;
      });
    } else {
      // Trip already exists - check if we need to update passenger count
      if (currentRiderTrip.value!.passengerCount != passengerCount.value) {
        _logger.info(
            'Updating existing trip passenger count from ${currentRiderTrip.value!.passengerCount} to ${passengerCount.value}');

        // Update the trip with the new passenger count
        currentRiderTrip.value!.ref.update({
          'passengerCount': passengerCount.value,
        });
      }
    }

    FirebaseAnalytics.instance.logEvent(name: 'start_listening_nearby_drivers');
  }

  void stopListeningNearbyDrivers() {
    _logger.info('Stopping nearby drivers listener');
    nearbyDriversStreamSubscription?.cancel();
    nearbyDriversStreamSubscription = null;
    nearbyDrivers.clear();
    nearbyDriverDetails.clear();

    showNearbyDrivers.value = false;

    FirebaseAnalytics.instance.logEvent(name: 'stop_listening_nearby_drivers');
  }
}
