import 'dart:async';
import 'dart:io';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter_google_places_sdk/flutter_google_places_sdk.dart' as places_sdk;
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:logging/logging.dart';

import '../../models/MobileUser.dart';
import '../AuthState.dart';

abstract class BaseNavigationState extends GetxController {
  // -----------------
  // Authentication State
  // -----------------
  final AuthState authState = Get.find<AuthState>();
  
  // -----------------
  // Initialization State
  // -----------------
  var isFullyInitialized = false.obs;
  final List<Function()> _pendingActions = [];

  // -----------------
  // User Mode State
  // -----------------
  var drivingMode = UserType.driver.obs;

  // -----------------
  // Map Control State
  // -----------------
  GoogleMapController? mapController;
  void setMapController(GoogleMapController controller) {
    mapController = controller;
    FirebaseAnalytics.instance.logEvent(name: 'set_map_controller');
    
    // Check if there's a pending camera animation for the current trip
    onMapControllerReady();
  }
  
  // This method can be overridden in NavigationState
  void onMapControllerReady() {
    // Default implementation - can be overridden
  }

  var zoomLevel = 15.0.obs;

  // -----------------
  // Location State
  // -----------------
  var currentPosition = Rxn<LatLng>();
  var startPosition = Rxn<LatLng>();
  var destinationPosition = Rxn<LatLng>();
  var locationFullyFunctional = false.obs;

  // -----------------
  // Navigation State
  // -----------------
  var isNavigating = false.obs;

  // -----------------
  // Places SDK Configuration
  // -----------------
  final _placesSdk = places_sdk.FlutterGooglePlacesSdk(
    Platform.isAndroid
        ? 'AIzaSyCT17CniSQO5jV8hDF63QjxpYjeRmbw5kc' // Android
        : 'AIzaSyCYv7VWSjBh8_XziLq9QqwrjEygnA9uNmE', // iOS
    locale: Get.locale,
  );
  places_sdk.FlutterGooglePlacesSdk get placesSdk => _placesSdk;

  // -----------------
  // Nearby Drivers State
  // -----------------
  var showNearbyDrivers = false.obs;
  StreamSubscription<List<GeoDocumentSnapshot>>? nearbyDriversStreamSubscription;
  var nearbyDrivers = <GeoFirePoint>[].obs;
  var nearbyDriverDetails = <MobileUser>[].obs;

  // -----------------
  // Logging Configuration
  // -----------------
  final Logger logger = Logger('BaseNavigation');

  // -----------------
  // Abstract Methods
  // -----------------
  void reset({
    bool stopNearbyDriversSub = false,
    bool stopCurrentTripSub = false,
    bool stopDriverTripRequestsSub = false,
  });
  void startFollowingDriverLocation();

  // -----------------
  // Initialization Methods
  // -----------------
  // Execute an action when the app is fully initialized
  void executeWhenReady(Function() action) {
    if (isFullyInitialized.value) {
      // Already initialized, execute immediately
      action();
    } else {
      // Queue the action for later
      _pendingActions.add(action);
    }
  }
  
  void processPendingActions() {
    if (_pendingActions.isEmpty) return;
    
    // Copy and clear the list to avoid re-entrancy issues
    final actions = List<Function()>.from(_pendingActions);
    _pendingActions.clear();
    
    // Execute all pending actions
    for (final action in actions) {
      try {
        action();
      } catch (e) {
        // Log error but continue with other actions
        logger.severe('Error executing pending action', e);
      }
    }
  }

  // -----------------
  // Automatic cleanup
  // -----------------
  final List<Function> _postCleanupFunctions = [];

  void addToPostCleanupFunc(Function fn) {
    _postCleanupFunctions.add(fn);
  }

  void _executePostCleanup() {
    for (var fn in _postCleanupFunctions) {
      try {
        fn();
      } catch (_) {
        // Ignore any exceptions during cleanup
      }
    }
    _postCleanupFunctions.clear();
  }

  @override
  void onClose() {
    logger.fine('Cleaning up resources');
    _executePostCleanup();
    super.onClose();
  }
}
