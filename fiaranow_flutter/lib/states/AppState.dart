import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../config/tenant_config.dart';
import '../models/ConfigurationModel.dart';
import '../models/ConfigurationModel/TripConfigurationModel.dart';
import '../models/MobileUser.dart';
import 'DevToolsService.dart';

class AppState extends GetxController {
  // -----------------
  // State Variables
  // -----------------
  var currentVersion = ''.obs;
  var requiredMinimumVersion = ''.obs;
  var underMaintenanceMessage = ''.obs;
  var userTendency = UserTendency.rideNow.obs;
  var tripConfiguration = TripConfigurationModel.getDefault().obs;
  var isOnline = true.obs;
  var clockDriftMillis = 0.0.obs;

  final _realtimeDb = FirebaseDatabase.instance;
  StreamSubscription<DatabaseEvent>? _clockSkewSubscription;
  Timer? _connectionProbeTimer;

  final Logger _logger = Logger('App');
  late final AgentLoggingService _agentLogging;

  @override
  void onInit() {
    _logger.info('Initializing AppState');
    super.onInit();

    // Initialize agent logging service
    _agentLogging = Get.find<AgentLoggingService>();

    _fetchAppVersion();
    _listenToConfigurationChanges();
    _setupConnectionProbe();
    _setupClockSkew();

    FirebaseAnalytics.instance.logAppOpen();
  }

  @override
  void onClose() {
    _logger.fine('Cleaning up resources');
    _connectionProbeTimer?.cancel();
    _clockSkewSubscription?.cancel();
    super.onClose();
  }

  void _setupConnectionProbe() {
    _logger.fine('Setting up connection probe timer');

    _connectionProbeTimer = Timer.periodic(const Duration(seconds: 10), (timer) async {
      try {
        // Create a timeout of 3 seconds
        await FirebaseFirestore.instance
            .collection('global_configurations')
            .doc('appVersion')
            .get(const GetOptions(source: Source.server))
            .timeout(const Duration(seconds: 3));

        if (!isOnline.value) {
          _logger.info('🔵 Connection restored');
          isOnline.value = true;
          _agentLogging.logAgentEvent('connection_status_changed', data: {
            'isOnline': true,
            'previousStatus': false,
          });
          Get.snackbar(
            AppLocalizations.of(Get.context!)!.appState_connectionStatusTitle, // "Connection Status"
            AppLocalizations.of(Get.context!)!.appState_connectionRestored, // "You are connected to the Internet."
            snackPosition: SnackPosition.TOP,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
        }
      } on TimeoutException catch (e) {
        // Handle timeout silently - this is expected when offline
        if (isOnline.value) {
          _logger.fine('Connection probe timed out: ${e.message}');
          isOnline.value = false;
          _agentLogging.logAgentEvent('connection_status_changed', data: {
            'isOnline': false,
            'previousStatus': true,
            'error': 'timeout',
          });
        }
      } catch (e) {
        // Handle other connection errors
        if (isOnline.value) {
          _logger.fine('🔴 Connection lost: $e');
          isOnline.value = false;
          _agentLogging.logAgentEvent('connection_status_changed', data: {
            'isOnline': false,
            'previousStatus': true,
            'error': e.toString(),
          });
          Get.snackbar(
            AppLocalizations.of(Get.context!)!.appState_connectionStatusTitle, // "Connection Status"
            AppLocalizations.of(Get.context!)!.appState_connectionLost, // "Your Internet connection is not doing well right now."
            snackPosition: SnackPosition.TOP,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      }
    });
  }

  void _setupClockSkew() {
    _logger.fine('Setting up clock skew listener');

    _clockSkewSubscription = _realtimeDb.ref('.info/serverTimeOffset').onValue.listen(
      (DatabaseEvent event) {
        final dynamic offset = event.snapshot.value;
        if (offset != null) {
          // Convert to double regardless of whether it's int or double
          final double newOffset = (offset is int) ? offset.toDouble() : (offset as double);
          _logger.fine('Clock drift updated: ${newOffset}ms');
          clockDriftMillis.value = newOffset;
        }
      },
      onError: (error) {
        _logger.severe('Error getting server time offset', error);
      },
    );
  }

  Future<void> _fetchAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      _logger.info('App version: ${packageInfo.version}');
      currentVersion.value = packageInfo.version;
    } catch (e) {
      _logger.severe('Error fetching app version', e);
    }
  }

  void _listenToConfigurationChanges() {
    _logger.fine('Starting to listen for configuration changes');

    FirebaseFirestore.instance.collection('global_configurations').doc('appVersion').snapshots().listen(
      (doc) {
        if (doc.exists) {
          final minVersion = doc.data()?['requiredMinimumVersion'] ?? '0.0.0';
          final maintenance = doc.data()?['underMaintenanceMessage'] ?? '';

          _logger.info(
              'Configuration updated - Required min version: $minVersion, Maintenance: ${maintenance.isNotEmpty ? "YES" : "NO"}');

          requiredMinimumVersion.value = minVersion;
          underMaintenanceMessage.value = maintenance;

          _agentLogging.logAgentEvent('configuration_changed', data: {
            'requiredMinimumVersion': minVersion,
            'underMaintenanceMessage': maintenance,
            'isUnderMaintenance': maintenance.isNotEmpty,
          });
        } else {
          _logger.warning('Configuration document does not exist');
        }
      },
      onError: (error) {
        _logger.severe('Error listening to configuration changes', error);
      },
    );
  }

  void _listenToTripConfigurationChanges() {
    _logger.fine('Starting to listen for trip configuration changes');

    configurationsColl.doc(TripConfigurationModel.id).snapshots().listen(
      (doc) {
        if (doc.exists) {
          final config = doc.data();
          if (config?.value is TripConfigurationModel) {
            final tripConfig = config!.value as TripConfigurationModel;
            tripConfiguration.value = tripConfig;
            _logger.info('Trip configuration updated successfully');

            _agentLogging.logAgentEvent('trip_configuration_changed', data: {
              'configuration': tripConfig.toMap(),
            });
          } else {
            _logger.warning('Invalid trip configuration format');
          }
        } else {
          _logger.warning('Trip configuration document does not exist');
        }
      },
      onError: (error) {
        _logger.severe('Error listening to trip configuration changes', error);
      },
    );
  }

  void onMobileUserAvailable(MobileUser? user) {
    _logger.info('Mobile user available: ${user?.uid}');
    _listenToTripConfigurationChanges();
  }

  void setUserTendency(UserTendency tendency) {
    final previousTendency = userTendency.value;
    _logger.info('User tendency changed to: ${tendency.name}');
    userTendency.value = tendency;

    _agentLogging.logAgentEvent('user_tendency_changed', data: {
      'previousTendency': previousTendency.name,
      'newTendency': tendency.name,
    });

    FirebaseAnalytics.instance.logEvent(name: 'set_user_tendency', parameters: {'tendency': tendency.name});
  }

  String getAppVersion() {
    return currentVersion.value;
  }

  /// Refreshes configuration from Firestore
  Future<void> refreshConfiguration() async {
    _logger.info('Refreshing configuration from Firestore');

    // Force fetch app version configuration
    try {
      final doc = await FirebaseFirestore.instance
          .collection('global_configurations')
          .doc('appVersion')
          .get(const GetOptions(source: Source.server));

      if (doc.exists) {
        final minVersion = doc.data()?['requiredMinimumVersion'] ?? '0.0.0';
        final maintenance = doc.data()?['underMaintenanceMessage'] ?? '';

        requiredMinimumVersion.value = minVersion;
        underMaintenanceMessage.value = maintenance;
        _logger.info('Configuration refreshed successfully');
      }
    } catch (e) {
      _logger.severe('Error refreshing configuration', e);
      rethrow;
    }

    // Force fetch trip configuration
    try {
      const configPath = 'tenants/${TenantConfig.TENANT_ID}/configurations/${TripConfigurationModel.id}';
      _logger.info('Fetching trip config from: $configPath');

      final tripDoc = await configurationsColl.doc(TripConfigurationModel.id).get(const GetOptions(source: Source.server));
      if (tripDoc.exists) {
        final config = tripDoc.data();
        if (config?.value is TripConfigurationModel) {
          tripConfiguration.value = config!.value as TripConfigurationModel;
          _logger.info('Trip configuration refreshed successfully');
        } else {
          // Try to parse directly if it's a map
          final rawData = tripDoc.data()?.value;
          if (rawData is Map<String, dynamic>) {
            final tripConfig = TripConfigurationModel.fromMap(rawData);
            tripConfiguration.value = tripConfig;
            _logger.info('Trip configuration refreshed successfully');
          }
        }
      } else {
        _logger.warning('Trip config document does not exist at: $configPath');
      }
    } catch (e) {
      _logger.severe('Error refreshing trip configuration', e);
    }
  }

  /// Saves user tendency to shared preferences
  Future<void> saveUserTendency(UserTendency tendency) async {
    _logger.info('Saving user tendency: ${tendency.name}');
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_tendency', tendency.name);
      setUserTendency(tendency);
    } catch (e) {
      _logger.severe('Error saving user tendency', e);
      rethrow;
    }
  }
}

enum UserTendency { rideNow, reserve }
