import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum AppThemeMode { system, light, dark }

class ThemeState extends GetxController {
  final Logger _logger = Logger('ThemeState');

  // Observable theme mode
  var currentThemeMode = AppThemeMode.system.obs;

  @override
  void onInit() {
    super.onInit();
    _logger.info('Initializing ThemeState');
    _loadThemeFromPreferences();
  }

  /// Load theme preference from SharedPreferences
  Future<void> _loadThemeFromPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeString = prefs.getString('app_theme_mode') ?? 'system';
      final themeMode = _stringToThemeMode(themeString);
      currentThemeMode.value = themeMode;
      _logger.info('Loaded theme preference: ${themeMode.name}');
    } catch (e) {
      _logger.severe('Error loading theme preference', e);
      currentThemeMode.value = AppThemeMode.system;
    }
  }

  /// Save theme preference to SharedPreferences
  Future<void> _saveThemeToPreferences(AppThemeMode themeMode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('app_theme_mode', themeMode.name);
      _logger.info('Saved theme preference: ${themeMode.name}');
    } catch (e) {
      _logger.severe('Error saving theme preference', e);
    }
  }

  /// Set theme mode and save to preferences
  Future<void> setThemeMode(AppThemeMode themeMode) async {
    _logger.info('Setting theme mode to: ${themeMode.name}');
    currentThemeMode.value = themeMode;
    await _saveThemeToPreferences(themeMode);
  }

  /// Convert string to AppThemeMode enum
  AppThemeMode _stringToThemeMode(String themeString) {
    switch (themeString) {
      case 'light':
        return AppThemeMode.light;
      case 'dark':
        return AppThemeMode.dark;
      case 'system':
      default:
        return AppThemeMode.system;
    }
  }

  /// Convert AppThemeMode to Flutter's ThemeMode
  ThemeMode get flutterThemeMode {
    switch (currentThemeMode.value) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }
}
