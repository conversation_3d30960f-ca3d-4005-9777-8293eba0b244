import 'dart:convert';
import 'dart:io';

import 'package:logging/logging.dart';

// Logger for ARB synchronization
final Logger _logger = Logger('SyncARB');

void main() {
  // Set up logging for standalone script
  Logger.root.level = Level.ALL;
  Logger.root.onRecord.listen((record) {
    // Note: This print is intentional - it's the logger output handler for a standalone script
    print('${record.level.name}: ${record.message}');
  });

  try {
    // Read the ARB files
    final enFile = File('app_en.arb');
    final frFile = File('app_fr.arb');

    if (!enFile.existsSync() || !frFile.existsSync()) {
      _logger.severe('Error: ARB files not found!');
      exit(1);
    }

    // Parse the JSON content
    final Map<String, dynamic> enContent = json.decode(enFile.readAsStringSync());
    final Map<String, dynamic> frContent = json.decode(frFile.readAsStringSync());

    // Create new French content with same order as English
    final Map<String, dynamic> newFrContent = {
      '@@locale': 'fr',
    };

    // Iterate through English entries to maintain order
    for (var entry in enContent.entries) {
      final key = entry.key;
      if (key == '@@locale') continue;

      if (key.startsWith('@')) {
        // Only copy metadata if it's not just a description
        final metadata = entry.value as Map<String, dynamic>;
        if (metadata.length > 1 || !metadata.containsKey('description')) {
          newFrContent[key] = entry.value;
        }
      } else {
        // Handle translation
        if (frContent.containsKey(key)) {
          // Keep existing translation
          newFrContent[key] = frContent[key];
        } else {
          // Mark new entry for translation
          newFrContent[key] = '+++${entry.value}';
        }
      }
    }

    // Write the synchronized French file with pretty printing
    const JsonEncoder encoder = JsonEncoder.withIndent('  ');
    frFile.writeAsStringSync(encoder.convert(newFrContent));

    // Print statistics
    final newKeysCount = enContent.keys.where((key) => !key.startsWith('@')).where((key) => !frContent.containsKey(key)).length;

    final removedKeysCount =
        frContent.keys.where((key) => !key.startsWith('@')).where((key) => !enContent.containsKey(key)).length;

    _logger.info('Synchronization complete!');
    _logger.info('New keys to translate: $newKeysCount');
    _logger.info('Removed keys: $removedKeysCount');

    exit(0); // Explicitly exit with success code
  } catch (e) {
    _logger.severe('Error: $e');
    exit(1); // Exit with error code
  }
}
