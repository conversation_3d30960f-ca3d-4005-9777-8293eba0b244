{"@@locale": "fr", "mainPage_appTitle": "<PERSON><PERSON><PERSON>", "mainPage_choosePrimaryActivity": "Choisissez votre activité principale", "mainPage_rider": "Passager", "mainPage_driver": "Conducteur", "mainPage_changeAnytime": "Vous pouvez le modifier à tout moment depuis les paramètres, pas d'inquiétude.", "menuDrawer_menu": "<PERSON><PERSON>", "menuDrawer_logout": "Déconnexion", "dialog_logoutConfirmation": "Êtes-vous sûr de vouloir vous déconnecter ?", "dialog_no": "Non", "dialog_yes": "O<PERSON>", "passengerCountDialog_confirm": "Confirmer", "mainPage_chooseLanguage": "Choisir la langue", "menuDrawer_theme": "Thème", "menuDrawer_themeSystem": "Système", "menuDrawer_themeLight": "<PERSON>", "menuDrawer_themeDark": "Sombre", "mainPage_english": "<PERSON><PERSON><PERSON>", "mainPage_french": "Français", "mainPage_welcome": "Bienvenue", "@mainPage_welcome": {"description": "Un message de bienvenue sur l'écran d'accueil"}, "notification_screenTitle": "Activer les notifications", "notification_enableButton": "Activer les notifications", "notification_description": "En tant que conducteur, vous devez activer les notifications pour recevoir les demandes de course et les mises à jour importantes.", "notification_requiredTitle": "Notifications requises", "notification_requiredContent": "Les notifications sont requises pour le mode conducteur. Veuillez les activer dans les paramètres.", "notification_cancel": "Annuler", "notification_openSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON> les paramètres", "driverMode_permissionTitle": "Permissions du mode conducteur", "driverMode_permissionDescription": "Pour opérer en tant que conducteur, nous avons besoin de votre permission pour envoyer des notifications et suivre votre position en arrière-plan.", "driverMode_notificationPermission": "Notifications pour les demandes de course", "driverMode_backgroundLocationPermission": "Localisation en arrière-plan (Toujours autoriser)", "driverMode_grantPermissions": "Accorder les permissions", "driverMode_locationRequiredTitle": "Permission de localisation requise", "driverMode_locationRequiredContent": "La permission de localisation est requise pour le mode conducteur. Veuillez l'activer dans les paramètres.", "driverMode_backgroundLocationTitle": "Localisation en arrière-plan requise", "driverMode_backgroundLocationExplanation": "Le mode conducteur nécessite la permission de localisation 'Toujours autoriser' pour suivre votre position et vous attribuer des courses même lorsque l'application est en arrière-plan.", "driverMode_backgroundLocationInstructions": "Veuillez suivre ces étapes :", "driverMode_iosStep1": "<PERSON><PERSON><PERSON><PERSON> sur 'Ouvrir les paramètres' ci-dessous", "driverMode_iosStep2": "Sélectionnez 'Localisation' et choisissez 'Toujours'", "driverMode_iosStep3": "Retournez à l'application et réessayez", "driverMode_androidStep1": "<PERSON><PERSON><PERSON><PERSON> sur 'Ouvrir les paramètres' ci-dessous", "driverMode_androidStep2": "Sélectionnez 'Localisation' et choisissez 'Autoriser tout le temps'", "driverMode_androidStep3": "Retournez à l'application et réessayez", "genericError": "Une erreur s'est produite. Veuillez réessayer.", "driverProfileForm_screenTitle": "<PERSON><PERSON> du conducteur", "driverProfileForm_vehicleBrand": "Marque du véhicule", "driverProfileForm_vehicleModel": "Mod<PERSON><PERSON> du véhicule", "driverProfileForm_vehicleColor": "Couleur du véhicule", "driverProfileForm_vehicleYear": "Année de fabrication", "driverProfileForm_registrationNumber": "Numéro d'immatriculation", "driverProfileForm_saveButton": "Enregistrer", "driverProfileForm_maxPassengers": "Nombre maximum de passagers", "mainPage_editDriverProfile": "Modifier le profil du conducteur", "mainPage_notADriver": "Pas un conducteur", "mainPage_setPrimaryActivityToDriver": "<PERSON><PERSON> de<PERSON> d'abord définir votre activité principale sur Conducteur.", "mainPage_profile": "Profil", "mainPage_maps": "<PERSON><PERSON>", "mainPage_history": "Historique", "mainPage_home": "Accueil", "mainPage_primaryActivity": "Activité principale", "mainPage_driverProfile": "Profil conducteur", "mapScreen_pickupAddressInputText": "<PERSON><PERSON><PERSON>", "mapScreen_destinationAddressInputText": "<PERSON><PERSON><PERSON> d'a<PERSON>", "mapScreen_confirmPickupLocationButton": "Confirmer le lieu de départ ?", "mapScreen_confirmDestinationButton": "Confirmer le lieu d'arrivée ?", "mapScreen_noDriversAvailable": "Aucun conducteur disponible à proximité", "mapScreen_availableDriversHeader": "Conducteurs disponibles", "mapScreen_selectDriverButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mapScreen_notEnoughSeats": "Pas assez de places", "mapScreen_seatsAvailable": "Places: {max<PERSON><PERSON><PERSON>ers}", "mapScreen_seatsNeeded": "(Be<PERSON><PERSON> de {passengerCount})", "mapScreen_changeAddressesButton": "Modifier l'adresse", "mapScreen_showNearbyDriversButton": "Afficher les conducteurs à proximité", "mapScreen_stopServiceButton": "Arrêter le service", "mapScreen_startServiceButton": "Démarrer le service", "mapScreen_checkInternetButton": "Vérifier la connexion Internet", "home_welcomeText": "Bienvenue à Fiaranow", "home_welcome": "Bienvenue", "phoneNumberForm_screenTitle": "Définir le numéro de téléphone", "phoneNumberForm_phoneNumber": "Numéro de téléphone", "phoneNumberForm_saveButton": "Enregistrer", "mapScreen_driverProfileNeedsConfirmation": "Le profil du conducteur doit être confirmé", "mapScreen_tripDistance": "Distance du trajet: {distance} km", "mapScreen_estimatedDuration": "Du<PERSON>e estimée: {duration}", "mapScreen_noDriversAvailableNearby": "Aucun conducteur disponible à proximité", "mapScreen_roadDistance": "Distance routière: {distance} km", "mapScreen_approachingETA": "ETA d'approche: {eta} minutes", "mapScreen_cancel": "Annuler", "mapScreen_tripRequests": "<PERSON><PERSON><PERSON> de traj<PERSON>", "mapScreen_noTripRequests": "Aucune demande de trajet pour le moment.", "mapScreen_from": "De: {startLat}, {startLon}", "mapScreen_to": "À: {destLat}, {destLon}", "mapScreen_tripDistanceLabel": "Distance du trajet: {distance} km, ETA: {eta} minutes", "mapScreen_approachingDistance": "Distance d'approche: {distance} km, ETA: {eta} minutes", "mapScreen_accept": "Accepter", "mapScreen_reject": "<PERSON><PERSON><PERSON>", "mapScreen_confirmRejection": "Confirm<PERSON> le rejet", "mapScreen_confirmRejectionMessage": "Êtes-vous sûr de vouloir rejeter cette demande de trajet?", "mapScreen_noActiveTrip": "Aucun trajet actif.", "mapScreen_tripControl": "Contrôle du trajet", "mapScreen_tripHeader": "<PERSON><PERSON><PERSON>", "mapScreen_youShouldNowBeDriving": "Vous devriez maintenant conduire pour vous approcher du client...", "mapScreen_iHaveArrived": "Je suis arrivé (En attente du passager)", "mapScreen_waitingForBothToStart": "En attente de démarrage du trajet...", "mapScreen_pleaseWaitForPassenger": "Veuillez attendre que le passager démarre également le trajet.", "mapScreen_tripInProgress": "Le trajet est en cours.", "mapScreen_completeTrip": "<PERSON><PERSON><PERSON> le trajet", "mapScreen_tripCompleted": "Le trajet est terminé.", "mapScreen_markAsPaid": "Marquer comme payé", "mapScreen_pleaseWaitForDriver": "Veu<PERSON>z attendre que le conducteur démarre également le trajet.", "mapScreen_changeAddresses": "Changer d'ad<PERSON>e(s)", "mapScreen_showNearbyDrivers": "Conducteurs à proximité", "home_choosePrimaryActivity": "Choisissez votre activité principale", "home_setPhoneNumber": "Définir votre numéro de téléphone", "home_allowPushNotifications": "Autoriser les notifications push", "home_allowLocationPermission": "Autoriser la permission de localisation", "home_getRideNow": "Un trajet maintenant !", "home_reserveRide": "Réserver un trajet", "home_specialOffer": "Offre spéciale", "home_reserveCarNoGas": "• Réservez une voiture pour toute la journée pour €25", "home_reserveCarWithGas": "• Réservez pour toute la journée pour €75", "tripDetails_title": "<PERSON><PERSON><PERSON> du trajet", "tripDetails_pickupTime": "Heure de récupération", "tripDetails_distance": "Distance", "tripDetails_duration": "<PERSON><PERSON><PERSON>", "tripDetails_cost": "Coût", "tripDetails_passenger": "Passager", "tripDetails_passengerCount": "Nombre de passagers", "tripDetails_status": "Statut", "tripDetails_createdAt": "<PERSON><PERSON><PERSON>", "tripDetails_completedAt": "<PERSON><PERSON><PERSON><PERSON> le", "tripDetails_cancelledAt": "<PERSON><PERSON><PERSON> le", "tripDetails_startLocation": "<PERSON><PERSON> <PERSON>", "tripDetails_arrivalLocation": "<PERSON>u d'arriv<PERSON>", "tripDetails_driverLocation": "<PERSON><PERSON> du conducteur", "tripDetails_showOnMap": "Afficher sur la carte", "mapScreen_locationPermissionTitle": "Autorisation de localisation", "mapScreen_locationPermissionMessageDriver": "Pour recevoir des courses, nous avons besoin d'accéder à votre localisation", "mapScreen_locationPermissionMessageRider": "Pour trouver des chauffeurs à proximité, nous avons besoin d'accéder à votre localisation", "mapScreen_enableLocationButton": "Activer la localisation", "mapScreen_reserveThisTrip": "Réserver ce trajet", "mapScreen_routeSelectionTitle": "Sélection d'itinéraire ({count})", "mapScreen_routeReserveTitle": "Réserver un itinéraire ({count})", "mapScreen_reservationDateLabel": "Date", "mapScreen_reservationTimeLabel": "<PERSON><PERSON>", "mapScreen_confirmReservation": "Confirmer la réservation", "mapScreen_estimatedTripCost": "Coût estimé du trajet : ", "mapScreen_finalTripCost": "Coût final du trajet :", "mapScreen_currentTripCost": "Coût estimé du trajet :", "mapScreen_sorryTripCancelled": "<PERSON><PERSON><PERSON><PERSON>, ce trajet a été annulé.", "mapScreen_driverAssigned": "Un chauffeur vous a été attribué.", "mapScreen_driverWillBeAssigned": "Un chauffeur vous sera attribué. Veuillez patienter.", "mapScreen_driverApproaching": "Le chauffeur s'approche de vous...", "mapScreen_waitingTime": "Temps d'attente :", "mapScreen_startTrip": "<PERSON><PERSON><PERSON><PERSON> le trajet", "mapScreen_enjoyTheRide": "Profitez du trajet!", "mapScreen_thankYouForRiding": "Merci d'avoir utilisé Fi<PERSON>ow!", "mapScreen_tripPaid": "Le trajet est payé.", "mapScreen_reserveRoute": "Réserver un itinéraire ({count})", "mapScreen_selectRoute": "Sélectionner un itinéraire ({count})", "mapScreen_routeWarningMessage": "Pendant le trajet, le chauffeur pourrait choisir un itinéraire différent en fonction des conditions routières, du trafic ou à votre demande. Le coût du trajet sera mis à jour en conséquence.", "mapScreen_route": "Itinéraire {number}", "mapScreen_routeDetails": "Durée : {duration}, Distance : {distance} km, Coût : {cost} Ar", "mapScreen_success": "Su<PERSON>ès", "mapScreen_tripReservedSuccessfully": "<PERSON><PERSON><PERSON> réservé avec succès.", "mapScreen_error": "<PERSON><PERSON><PERSON>", "mapScreen_driverAlreadyAssigned": "Vous êtes déjà assigné à un autre trajet. Veuillez terminer ou annuler votre trajet actuel d'abord.", "mapScreen_driverNotAvailable": "Vous ne remplissez pas les conditions de disponibilité. Veuillez vérifier vos documents et le statut de votre véhicule.", "mapScreen_tripNotFound": "Ce trajet n'est plus disponible.", "mapScreen_locationNotAvailable": "Votre position n'est pas disponible. Veuillez activer les services de localisation et réessayer.", "mapScreen_selectFutureTime": "<PERSON><PERSON><PERSON><PERSON> sélectionner une heure au moins 15 minutes dans le futur.", "exit_dialog_title": "Quitter l'application", "exit_dialog_message": "Voulez-vous vraiment quitter l'application ?", "mapScreen_choosePaymentMethod": "Choisir un mode de paiement", "mapScreen_cashPayment": "Paiement en espèces", "mapScreen_mobileMoneyPayment": "Paiement par Mobile Money", "mapScreen_chooseRideType": "Choisir le type de trajet", "mapScreen_reserveRide": "Réserver un trajet", "mapScreen_chooseDateAndTime": "Choisir la date et l'heure", "mapScreen_rideNow": "Voyager maintenant", "mapScreen_findAvailableDrivers": "Trouver des chauffeurs disponibles", "mapScreen_deviceClockInaccurate": "L'horloge de votre appareil est inexacte", "mapScreen_adjustDeviceTimeSettings": "<PERSON><PERSON><PERSON><PERSON> régler les paramètres d'heure de votre appareil sur Automatique pour continuer à utiliser l'application.", "mapScreen_currentTimeDifference": "Différence de temps actuelle : {seconds} secondes", "mapScreen_deviceClockOff": "L'horloge de votre appareil est décalée de {seconds} secondes. Pensez à activer les paramètres d'heure automatiques.", "mapScreen_dismissClockWarning": "<PERSON><PERSON><PERSON>", "mapScreen_failedToGetRoutes": "Impossible d'obtenir les itinéraires. Veuillez vérifier votre connexion Internet.", "serviceStatusUpdate_startService": "Démarrer le service", "serviceStatusUpdate_stopService": "Arrêter le service", "serviceStatusUpdate_whyStarting": "Pourquoi démarrez-vous votre service ?", "serviceStatusUpdate_whyStopping": "Pourquoi arrêtez-vous votre service ?", "serviceStatusUpdate_reason": "<PERSON>son", "serviceStatusUpdate_specifyReason": "Veuillez spécifier votre raison", "serviceStatusReason_morningServiceStart": "Démarrage du service le matin", "serviceStatusReason_eveningServiceStart": "Démarrage du service le soir", "serviceStatusReason_lunchBreak": "<PERSON>use dé<PERSON>r", "serviceStatusReason_prayerBreak": "Pause prière", "serviceStatusReason_fuelRefill": "Ravitaillement en carburant", "serviceStatusReason_vehicleMaintenance": "Entretien du véhicule", "serviceStatusReason_endOfShift": "Fin de service", "serviceStatusReason_emergencyStop": "<PERSON><PERSON><PERSON><PERSON> d'urgence", "serviceStatusReason_switchActivity": "Changement d'activité", "serviceStatusReason_appRelaunch": "Relance de l'application", "serviceStatusReason_custom": "<PERSON><PERSON><PERSON><PERSON>", "tripRejectionScreen_title": "Refus de course", "tripRejectionScreen_selectReason": "Veuillez sélectionner une raison pour refuser cette course :", "tripRejectionScreen_vehicleMalfunction": "Problème de véhicule", "tripRejectionScreen_tooFarPickup": "Point de départ trop éloigné", "tripRejectionScreen_heavyTraffic": "Trafic dense dans la zone", "tripRejectionScreen_unsafeArea": "Zone non sécurisée", "tripRejectionScreen_endingShiftSoon": "Fin de service proche", "tripRejectionScreen_vehicleCleaning": "Nettoyage du véhicule nécessaire", "tripRejectionScreen_passengerCapacityFull": "Capacité passagers atteinte", "tripRejectionScreen_batteryLow": "Batterie faible", "tripRejectionScreen_weatherConditions": "Mauvaises conditions météo", "tripRejectionScreen_custom": "<PERSON>tre raison (à préciser)", "tripRejectionScreen_customReasonLabel": "Veuillez préciser votre raison", "tripRejectionScreen_confirm": "Confirmer le refus", "tripRejectionScreen_error": "<PERSON><PERSON><PERSON>", "tripRejectionScreen_pleaseEnterReason": "Veuillez saisir votre raison de refus", "mapScreen_cancelRequestFailed": "Échec de l'annulation de la demande. Veuillez réessayer.", "mapScreen_pickupAndTripDistance": "Distance de récupération et trajet : {distance} km", "mapScreen_pickupDistance": "Distance de récupération : {distance} km", "mapScreen_pickupLocation": "Lieu de récupération", "mapScreen_pickupTime": "Heure de récupération", "mapScreen_destinationLocation": "<PERSON>u d'arriv<PERSON>", "menuDrawer_profileSettings": "Paramètres du profil", "menuDrawer_currentMode": "Mode actuel", "menuDrawer_phoneNumber": "Numéro de téléphone", "menuDrawer_permissions": "Autorisations", "menuDrawer_pushNotifications": "Notifications push", "menuDrawer_gpsLocation": "Localisation GPS", "menuDrawer_gpsLocationBackground": "Localisation GPS (Arrière-plan)", "menuDrawer_locationWhileInUseDriverSubtitle": "<PERSON>tre sur 'Toujours' pour une expérience conducteur optimale.", "menuDrawer_driverDocuments": "Mes documents", "menuDrawer_driverDocumentsDesc": "Télécharger et gérer vos documents", "updateRequiredScreen_message": "Une mise à jour est requise pour continuer à utiliser l'application.", "updateRequiredScreen_updateNow": "Mettre à jour maintenant", "mapScreen_fullDayReservation": "Réservation journée", "mapScreen_fullDayReservationPrompt": "Une réservation journée entière vous permet d'avoir un chauffeur à votre disposition toute la journée. Le chauffeur vous prendra à l'heure et à l'endroit spécifiés, et sera disponible pour vous conduire où vous voulez tout au long de la journée.", "mapScreen_fullDayPriceOptions": "Choisissez l'option de prix", "mapScreen_fullDayFixedPrice": "75€ (carburant inclus)", "mapScreen_gasIncluded": "Carburant inclus", "mapScreen_fullDayGasExcluded": "25€ (carburant en sus)", "mapScreen_gasNotIncluded": "Carburant en sus", "mapScreen_fullDayReservationSuccess": "Réservation journée entière créée avec succès !", "auth_accountInUseTitle": "Compte d<PERSON>à utilisé", "auth_accountInUseMessage": "Votre compte est déjà connecté sur un autre appareil. Voulez-vous vous déconnecter des autres appareils ?", "auth_logoutOtherDevices": "O<PERSON>", "auth_cancelLogin": "Non", "auth_forcedLogoutTitle": "Déconnecté", "auth_forcedLogoutMessage": "Votre compte a été connecté sur un autre appareil.", "auth_forcedLogoutButton": "OK", "tripStatus_preparing": "En préparation", "tripStatus_requestingDriver": "Recherche de chauffeur", "tripStatus_reserved": "Réservé", "tripStatus_driverApproaching": "Chauffeur en approche", "tripStatus_driverAwaiting": "Chauffeur en attente", "tripStatus_inProgress": "En cours", "tripStatus_completed": "<PERSON><PERSON><PERSON><PERSON>", "tripStatus_cancelled": "<PERSON><PERSON><PERSON>", "tripStatus_paid": "<PERSON><PERSON>", "tripDetails_pricingOption": "Option de prix", "tripDetails_priceDetails": "Détails du prix", "tripDetails_locationDetails": "Détails de l'emplacement", "tripDetails_unknownDestination": "Destination inconnue", "tripDetails_deleteTrip": "Su<PERSON><PERSON><PERSON> le trajet", "history_noTripsYet": "Aucun trajet pour le moment", "@history_noTripsYet": {"description": "Texte affiché lorsqu'il n'y a aucun trajet dans l'historique"}, "history_title": "Historique", "@history_title": {"description": "Titre de l'écran de l'historique des trajets"}, "mapScreen_enjoyFullDayRide": "Profitez de votre journée !", "mapScreen_locationFallback": "<PERSON><PERSON>", "mapScreen_destinationFallback": "Destination", "auth_networkErrorTitle": "<PERSON><PERSON><PERSON>", "auth_networkErrorMessage": "Veuillez vérifier votre connexion internet et réessayer.", "navigationState_error": "<PERSON><PERSON><PERSON>", "navigationState_driverTripRequestsError": "Erreur lors de l'écoute des demandes de trajets du conducteur. Veuillez redémarrer l'application.", "navigationState_locationError": "Erreur de localisation", "navigationState_locationTrackingError": "Impossible de suivre votre position. Veuillez vérifier vos paramètres de localisation et redémarrer l'application.", "appState_connectionStatusTitle": "État de la connexion", "appState_connectionRestored": "Vous êtes connecté à Internet.", "appState_connectionLost": "Votre connexion Internet ne fonctionne pas bien en ce moment.", "foregroundService_channelName": "Service de trajet", "foregroundService_channelDescription": "Maintient l'application active pendant un trajet en cours.", "foregroundService_tripInProgress": "Trajet en cours", "foregroundService_tripOngoing": "Votre trajet est en cours. Appuyez pour revenir à l'application.", "menuDrawer_notificationPermanentlyDenied": "Refusé par l'utilisateur", "tripActionButton_error": "<PERSON><PERSON><PERSON>", "tripActionButton_cancel": "Annuler", "tripActionButton_start": "<PERSON><PERSON><PERSON><PERSON>", "tripActionButton_complete": "<PERSON><PERSON><PERSON>", "mainPage_chat": "Aide", "chat_title": "Assistance par chat", "chat_new": "Nouvelle conversation", "chat_no_conversations": "Aucune conversation", "chat_start_conversation_hint": "<PERSON><PERSON><PERSON><PERSON> une nouvelle conversation pour obtenir de l'aide", "chat_new_dialog_title": "<PERSON><PERSON><PERSON><PERSON> une nouvelle conversation", "chat_subject": "Sujet", "chat_category": "<PERSON><PERSON><PERSON><PERSON>", "chat_start": "<PERSON><PERSON><PERSON><PERSON>", "chat_category_general": "Assistance générale", "chat_category_trip": "Assistance trajet", "chat_category_payment": "Assistance paiement", "chat_category_technical": "Assistance technique", "chat_category_feedback": "Suivi des commentaires", "chat_trip_button": "<PERSON><PERSON><PERSON>", "related_feedback": "Commentaires associés", "trip_feedback_title": "Évaluation du trajet", "trip_feedback_button": "Donner un avis", "trip_details": "<PERSON><PERSON><PERSON> du trajet", "rate_your_trip": "<PERSON><PERSON><PERSON>z votre trajet", "feedback_message": "Message de commentaire", "feedback_message_hint": "Parlez-nous de votre expérience...", "add_photos": "Ajouter des photos (facultatif)", "feedback_already_submitted": "Vous avez déjà envoyé un commentaire pour ce trajet", "submit_feedback": "Envoyer le commentaire", "max_images_reached": "Maximum 5 images autorisées", "please_rate_trip": "Veuillez évaluer votre trajet", "feedback_submitted_success": "Commentaire envoyé avec succès", "app_feedback_title": "Commentaires sur l'application", "app_feedback_description": "Aidez-nous à améliorer l'application en partageant vos commentaires, rapports de bugs ou suggestions.", "app_feedback_hint": "<PERSON><PERSON><PERSON><PERSON><PERSON> le problème ou la suggestion en détail...", "current_screen_screenshot": "Capture d'écran actuelle", "include_screenshot": "<PERSON>lure la capture d'écran", "additional_images": "Images supplémentaires", "max_5_images": "Maximum 5 images (5 Mo chacune)", "image_too_large": "La taille de l'image doit être inférieure à 5 Mo", "please_enter_message": "Veuillez entrer un message", "error_with_details": "Erreur : {details}", "error_creating_chat": "E<PERSON>ur lors de la création de la discussion : {error}", "error_loading_feedback": "Erreur lors du chargement des commentaires associés : {error}", "error_picking_image": "Erreur lors de la sélection de l'image : {error}", "error_sending_image": "Erreur lors de l'envoi de l'image : {error}", "error_checking_feedback": "Erreur lors de la vérification des commentaires existants : {error}", "error_uploading_image": "Erreur lors du téléchargement de l'image : {error}", "error_submitting_feedback": "Erreur lors de l'envoi du commentaire : {error}", "error_capturing_screenshot": "E<PERSON>ur lors de la capture d'écran : {error}", "settings": "Paramètres", "notificationSettings": "Paramètres de notification", "tripNotifications": "Notifications de trajet", "ringtoneNotifications": "Notifications avec sonnerie", "ringtoneNotificationsDesc": "Jouer une sonnerie pour les notifications importantes", "mainPage_driverMoving": "Chauffeur en mouvement", "mainPage_driverMovingDesc": "Être notifié lorsque votre chauffeur commence à se diriger vers vous", "mainPage_driverArrived": "<PERSON><PERSON><PERSON> a<PERSON>", "mainPage_driverArrivedDesc": "Être notifié lorsque votre chauffeur est arrivé au lieu de récupération", "mainPage_paymentCompleted": "Paiement terminé", "mainPage_paymentCompletedDesc": "Être notifié lorsque le paiement de votre trajet est traité", "mainPage_reservations": "Réservations", "mainPage_reservationReminders": "Rappels de réservation", "mainPage_reservationRemindersDesc": "Recevoir des rappels avant vos trajets programmés", "mainPage_ringtonePermissionTitle": "Activer les notifications avec sonnerie?", "mainPage_ringtonePermissionMessage": "Souhaitez-vous recevoir des notifications importantes de trajet avec une sonnerie?", "mainPage_ringtonePermissionDescription": "<PERSON><PERSON> inclut les notifications lorsque votre chauffeur est en route ou est arrivé.", "mainPage_noThanks": "Non Merci", "mainPage_enableRingtone": "<PERSON>r la Sonnerie", "passengerCountSlider_title": "Nombre de passagers", "addVehicle_title": "Ajouter un véhicule", "no_documents_uploaded": "Aucun document téléchargé", "no_vehicles_added": "Aucun véhicule ajouté", "vehicle_capacity_passengers": "Capacité : {max<PERSON><PERSON><PERSON>ers} passagers", "vehicle_currentlyAssigned": "Actuellement assigné", "vehicle_error": "Erreur : {error}", "vehicle_assignedVehicle": "Véhicule assigné", "vehicle_myVehicles": "Mes véhicules", "document_expiresInDays": "Expire dans {days} jours", "document_expiresOn": "Expire le : {date}", "menuDrawer_welcome": "Bienvenue", "mainPage_logout": "Déconnexion", "mainPage_menu": "<PERSON><PERSON>", "mainPage_no": "Non", "mainPage_yes": "O<PERSON>", "mainPage_save": "Enregistrer", "mainPage_cancel": "Annuler", "mainPage_error": "<PERSON><PERSON><PERSON>", "mainPage_settings": "Paramètres", "mainPage_notificationSettings": "Paramètres de notification", "mainPage_tripNotifications": "Notifications de trajet", "mainPage_ringtoneNotifications": "Notifications avec sonnerie", "mainPage_ringtoneNotificationsDesc": "Jouer une sonnerie pour les notifications importantes", "settings_general": "Général", "mainPage_usingDefaultEnabled": "Utilise le défaut (activé)", "mainPage_usingDefaultDisabled": "Utilise le défaut (désactivé)", "notificationSettings_failedToLoadPreferences": "Échec du chargement des préférences", "notificationSettings_checkConnectionAndRetry": "Veuillez vérifier votre connexion et réessayer", "notificationSettings_retry": "<PERSON><PERSON><PERSON><PERSON>", "notificationSettings_loadingPreferences": "Chargement des préférences...", "notificationSettings_general": "Général", "notificationSettings_tripNotifications": "Notifications de trajet", "notificationSettings_reservations": "Réservations", "notificationSettings_driverMoving": "Chauffeur en mouvement", "notificationSettings_driverMovingDesc": "Être notifié lorsque votre chauffeur commence à se diriger vers vous", "notificationSettings_driverArrived": "<PERSON><PERSON><PERSON> a<PERSON>", "notificationSettings_driverArrivedDesc": "Être notifié lorsque votre chauffeur est arrivé au lieu de récupération", "notificationSettings_paymentCompleted": "Paiement terminé", "notificationSettings_paymentCompletedDesc": "Être notifié lorsque le paiement de votre trajet est traité", "notificationSettings_reservationReminders": "Rappels de réservation", "notificationSettings_reservationRemindersDesc": "Recevoir des rappels avant vos trajets programmés", "driverDocuments_unableToOpenDocument": "Impossible d'ouvrir ce document", "documentDetail_title": "Détails du document", "documentDetail_notAvailable": "Document non disponible", "documentDetail_failedToLoadImage": "Échec du chargement de l'image", "documentDetail_previewNotAvailable": "Aperçu du document non disponible", "documentDetail_unsupportedFormat": "Format de fichier non pris en charge", "documentDetail_notFound": "Nous n'avons pas pu trouver ce document", "documentDetail_expired": "Ce document a expiré", "documentDetail_documentName": "Nom du document", "documentDetail_uploadDate": "Date de téléchargement", "documentDetail_expiryDate": "Date d'expiration", "documentDetail_notes": "Notes", "documentDetail_reviewInformation": "Informations de révision", "documentDetail_reviewedDate": "Date de révision", "documentDetail_adminNotes": "Notes de l'administrateur", "documentDetail_documentPreview": "Aperçu du document", "documentUpload_permissionDenied": "Permission refusée", "documentUpload_cameraPermissionRequired": "L'autorisation de l'appareil photo est nécessaire pour prendre des photos", "documentUpload_photoLibraryPermissionRequired": "L'autorisation de la bibliothèque photo est nécessaire pour sélectionner des photos", "documentUpload_storagePermissionRequiredPhotos": "L'autorisation de stockage est nécessaire pour sélectionner des photos", "documentUpload_storagePermissionRequiredFiles": "L'autorisation de stockage est nécessaire pour sélectionner des fichiers", "documentUpload_fileTooLarge": "Fichier trop volumineux", "documentUpload_fileSizeLimit": "Veuillez sélectionner un fichier inférieur à 5 Mo", "documentUpload_platformError": "Erreur de plateforme", "documentUpload_deviceError": "Erreur de l'appareil : {error}", "documentUpload_failedToPickFile": "Échec de la sélection du fichier. Veuillez réessayer.", "documentUpload_cameraError": "<PERSON><PERSON><PERSON> de l'appareil photo", "documentUpload_failedToCaptureImage": "Échec de la capture de l'image. Veuillez réessayer.", "documentUpload_noFileSelected": "<PERSON><PERSON><PERSON> fichier s<PERSON>", "documentUpload_selectDocumentPrompt": "Veuillez sélectionner un document à télécharger", "documentUpload_noExpiryDate": "Aucune date d'expiration", "documentUpload_selectExpiryDatePrompt": "Veuillez sélectionner une date d'expiration pour le document", "documentUpload_uploadSuccess": "Document téléchargé avec succès", "documentUpload_uploadFailed": "Échec du téléchargement", "documentUpload_firebaseError": "Erreur Firebase : {error}", "documentUpload_unexpectedError": "Une erreur inattendue s'est produite. Veuillez réessayer.", "documentUpload_documentType": "Type de document", "documentUpload_documentName": "Nom du document", "documentUpload_examplePrefix": "ex. : {example}", "documentUpload_enterDocumentName": "Veuillez entrer un nom de document", "documentUpload_expiryDate": "Date d'expiration", "documentUpload_selectExpiryDate": "Sélectionner la date d'expiration", "documentUpload_notesOptional": "Notes (facultatif)", "documentUpload_additionalInfoHint": "Toute information supplémentaire", "documentUpload_selectDocument": "Sélectionner un document", "documentUpload_chooseFile": "<PERSON><PERSON> un fichier", "documentUpload_takePhoto": "<PERSON><PERSON><PERSON> une photo", "documentUpload_acceptedFormats": "Formats acceptés : PDF, JPG, PNG (Max 5 Mo)", "paymentDialog_cancel": "Annuler", "addVehicle_saveButton": "Enregistrer", "chatDialog_cancel": "Annuler", "chat_yesterday": "<PERSON>er", "chat_daysAgo": "jours", "chat_supportTeam": "Équipe d'assistance", "chat_typeMessage": "Tapez un message...", "feedback_camera": "Appareil photo", "feedback_gallery": "Galerie", "tripDetails_locationUnknown": "Inconnu", "tripDetails_fixedPrice": "Carburant inclus", "tripDetails_perHour": "Carburant en sus", "driverDocuments_title": "Mes documents", "documentUpload_uploadButton": "Télécharger un document", "vehicleManagement_title": "Mes véhicules", "vehicleManagement_addButton": "Ajouter un véhicule", "documentUpload_title": "Télécharger un document", "driverRating_newDriver": "Nouveau Conducteur", "mapScreen_failedToGetPredictions": "Impossible d'obtenir les suggestions d'adresse. Veuillez vérifier votre connexion Internet.", "mapScreen_failedToGetPlaceDetails": "Impossible d'obtenir les détails du lieu. Veuillez vérifier votre connexion Internet.", "mapScreen_loadingMap": "Chargement de la carte...", "passengerTripControl_calculatingCost": "Calcul du coût...", "passengerTripControl_viewingTrip": "Consultation du trajet {status}", "@passengerTripControl_viewingTrip": {"placeholders": {"status": {"type": "String", "example": "terminé"}}}, "tripDetails_confirmDelete": "Confirmer la <PERSON>", "tripDetails_confirmDeleteMessage": "Êtes-vous sûr de vouloir supprimer ce trajet ?", "tripDetails_cancelButton": "Annuler", "tripDetails_deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "navigationState_cannotShowTripError": "Impossible d'afficher le trajet pendant qu'un trajet est actif", "notification_title": "Notifications", "notification_markAllAsRead": "Tout marquer comme lu", "notification_searchPlaceholder": "Rechercher des notifications", "notification_errorLoading": "E<PERSON>ur lors du chargement des notifications", "notification_retry": "<PERSON><PERSON><PERSON><PERSON>", "notification_noResultsFound": "Aucune notification trouvée", "notification_empty": "Aucune notification", "notificationDetail_title": "Détails de la notification", "notificationDetail_additionalInfo": "Informations supplémentaires", "notificationDetail_viewTrip": "Voir le trajet", "notificationDetail_updateDocuments": "Mettre à jour les documents", "notificationDetail_viewReservation": "Voir la réservation", "notificationDetail_viewDetails": "Voir les détails"}