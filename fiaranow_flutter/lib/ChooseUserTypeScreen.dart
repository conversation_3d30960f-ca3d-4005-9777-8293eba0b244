import 'package:fiaranow_flutter/models/MobileUser.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';

import 'l10n/app_localizations.dart';
import 'screens/DriverModePermissionDialog.dart';
import 'states/AuthState.dart';
import 'states/NavigationState.dart';
import 'states/PermissionsState.dart';

class ChooseUserTypeScreen extends StatefulWidget {
  const ChooseUserTypeScreen({super.key});

  @override
  State<ChooseUserTypeScreen> createState() => _ChooseUserTypeScreenState();
}

class _ChooseUserTypeScreenState extends State<ChooseUserTypeScreen> {
  final AuthState _authState = Get.find<AuthState>();
  final PermissionsState _permissionsState = Get.find<PermissionsState>();
  final analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    super.initState();
    // Log screen view
    analytics.logScreenView(screenName: 'choose_user_type');
  }

  void _updateUserType(UserType userType) async {
    final mobileUser = _authState.currentMobileUser.value;
    if (mobileUser != null) {
      final previousUserType = mobileUser.primaryUserType;
      _authState.setPrimaryUserType(userType);

      // Log user type selection
      analytics.logEvent(
        name: 'select_user_type',
        parameters: {
          'user_type': userType.toString(),
          'previous_user_type': previousUserType?.toString() ?? 'none',
          'user_id': mobileUser.uid,
          'is_first_time': (previousUserType == null).toString(),
        },
      );

      NavigationState? navigationState;
      try {
        navigationState = Get.find<NavigationState>();
      } catch (e) {
        analytics.logEvent(
          name: 'navigation_state_error',
          parameters: {
            'error': e.toString(),
            'user_id': mobileUser.uid,
          },
        );
      }

      if (userType == UserType.driver) {
        // Log driver mode selection
        analytics.logEvent(
          name: 'driver_mode_selected',
          parameters: {
            'user_id': mobileUser.uid,
          },
        );

        // Check if user has both notification and background location permissions
        final hasNotification = _permissionsState.hasNotificationPermission();
        final locationPermission = await Geolocator.checkPermission();
        final hasBackgroundLocation = locationPermission == LocationPermission.always;

        if (!hasNotification || !hasBackgroundLocation) {
          // Log permission check
          analytics.logEvent(
            name: 'driver_permissions_check',
            parameters: {
              'user_id': mobileUser.uid,
              'has_notification': hasNotification.toString(),
              'has_background_location': hasBackgroundLocation.toString(),
            },
          );

          // Show the unified permission dialog
          if (!mounted) return;
          final granted = await showDialog<bool>(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) => const DriverModePermissionDialog(),
          );

          if (granted != true) {
            // User cancelled or permissions were not granted
            analytics.logEvent(
              name: 'driver_mode_cancelled',
              parameters: {
                'user_id': mobileUser.uid,
                'reason': 'permissions_not_granted',
              },
            );
            return; // Don't switch to driver mode
          }
        }

        // Switch to Driver mode
        navigationState?.reset(stopNearbyDriversSub: true, stopCurrentTripSub: true);
        navigationState?.setDrivingMode(UserType.driver);
      } else {
        // Switch to Rider mode
        navigationState?.reset(stopNearbyDriversSub: true, stopCurrentTripSub: true, stopDriverTripRequestsSub: true);
        navigationState?.setDrivingMode(UserType.rider);
      }

      // Log mode switch completion
      analytics.logEvent(
        name: 'mode_switch_completed',
        parameters: {
          'user_type': userType.toString(),
          'user_id': mobileUser.uid,
          'has_notification_permission': _permissionsState.hasNotificationPermission().toString(),
        },
      );

      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.mainPage_choosePrimaryActivity),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: Obx(() {
            final selectedUserType = _authState.currentMobileUser.value?.primaryUserType;
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                ElevatedButton(
                  onPressed: () => _updateUserType(UserType.rider),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 20),
                    side: BorderSide(color: selectedUserType == UserType.rider ? Colors.blueAccent : Colors.grey, width: 2),
                  ),
                  child: Text(AppLocalizations.of(context)!.mainPage_rider, style: const TextStyle(fontSize: 24)),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () => _updateUserType(UserType.driver),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 20),
                    side: BorderSide(color: selectedUserType == UserType.driver ? Colors.redAccent : Colors.grey, width: 2),
                  ),
                  child: Text(AppLocalizations.of(context)!.mainPage_driver, style: const TextStyle(fontSize: 24)),
                ),
                const SizedBox(height: 30),
                Text(
                  AppLocalizations.of(context)!.mainPage_changeAnytime,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            );
          }),
        ),
      ),
    );
  }
}
