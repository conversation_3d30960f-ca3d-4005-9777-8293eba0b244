import 'package:fiaranow_flutter/states/AuthState.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'l10n/app_localizations.dart';

class ChooseLanguage extends StatefulWidget {
  const ChooseLanguage({super.key});

  @override
  State<ChooseLanguage> createState() => _ChooseLanguageState();
}

class _ChooseLanguageState extends State<ChooseLanguage> {
  final authState = Get.find<AuthState>();
  final analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    super.initState();
    // Log screen view
    analytics.logScreenView(screenName: 'choose_language');
  }

  void _setLanguage(Locale locale) {
    final mobileUser = authState.currentMobileUser.value;
    if (mobileUser != null) {
      // Log language selection event
      analytics.logEvent(
        name: 'select_language',
        parameters: {
          'language_code': locale.languageCode,
          'country_code': locale.countryCode ?? '',
          'user_id': mobileUser.uid,
          'previous_language': mobileUser.primaryLanguage?.languageCode ?? 'none',
        },
      );

      authState.setPrimaryLanguage(locale.languageCode, locale.countryCode ?? '');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.mainPage_chooseLanguage),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: Obx(() {
            final selectedLocale = authState.currentMobileUser.value?.primaryLanguage;
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                ElevatedButton.icon(
                  onPressed: () => _setLanguage(const Locale('en', 'US')),
                  icon: const Icon(Icons.flag, color: Colors.blue),
                  label: Text(AppLocalizations.of(context)!.mainPage_english, style: const TextStyle(fontSize: 24)),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 20),
                    side: BorderSide(color: selectedLocale?.languageCode == 'en' ? Colors.blueAccent : Colors.grey, width: 2),
                  ),
                ),
                const SizedBox(height: 20),
                ElevatedButton.icon(
                  onPressed: () => _setLanguage(const Locale('fr', 'FR')),
                  icon: const Icon(Icons.flag, color: Colors.red),
                  label: Text(AppLocalizations.of(context)!.mainPage_french, style: const TextStyle(fontSize: 24)),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 20),
                    side: BorderSide(color: selectedLocale?.languageCode == 'fr' ? Colors.redAccent : Colors.grey, width: 2),
                  ),
                ),
              ],
            );
          }),
        ),
      ),
    );
  }
}
