import 'dart:async';
import 'dart:io' show Platform;

import 'package:fiaranow_flutter/models/MobileUser.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

import 'l10n/app_localizations.dart';
import 'screens/ChatListScreen.dart';
import 'screens/HistoryScreen.dart';
import 'screens/MainPage/MenuDrawer.dart';
import 'screens/MapScreen.dart';
import 'services/NotificationStateService.dart';
import 'states/AppState.dart';
import 'states/AuthState.dart';
import 'states/NavigationState.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key, required this.title});

  final String title;

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> with SingleTickerProviderStateMixin {
  final Logger _logger = Logger('MainPage');

  int _selectedIndex = 0;
  final PageController _pageController = PageController();
  final AuthState _authState = Get.find<AuthState>();
  final _navigationState = Get.put(NavigationState());
  final _appState = Get.find<AppState>();

  @override
  void initState() {
    super.initState();

    // Set the PageController in NavigationState
    _navigationState.setPageController(_pageController);

    // Check if there is an ongoing Rider trip, move to MapsScreen if there is (check only once)
    bool checkDone = false;
    ever(_authState.currentMobileUser, (MobileUser? user) {
      if (user != null && !checkDone) {
        checkDone = true; // only check once
        _logger.info('User available, starting initialization for ${user.uid}');

        // User is available
        _appState.onMobileUserAvailable(user);

        // Wait for the MapController to be initialized
        Timer.periodic(const Duration(milliseconds: 500), (timer) {
          if (_navigationState.mapController != null) {
            _logger.info('MapController is ready');
            // The NavigationState now has real-time listeners that handle trip detection
            // for both drivers and passengers, so no need to manually check here
            timer.cancel();
          } else {
            _logger.fine('Waiting for MapController...');
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          return;
        }

        final shouldPop = await _onWillPop();
        if (shouldPop && context.mounted) {
          Navigator.pop(context);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: Platform.isIOS ? MainAxisAlignment.center : MainAxisAlignment.start,
            children: [
              const Text('Fiaranow'),
              Obx(() {
                return _appState.isOnline.value
                    ? const SizedBox.shrink()
                    : const Padding(
                        padding: EdgeInsets.only(left: 8),
                        child: Icon(
                          Icons.wifi_off,
                          size: 16,
                          color: Colors.red,
                        ),
                      );
              }),
            ],
          ),
          actions: [
            // Notifications icon with badge
            if (Get.isRegistered<NotificationStateService>())
              Obx(() {
                final notificationService = Get.find<NotificationStateService>();
                final unreadCount = notificationService.unreadCount.value;
                
                return Stack(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.notifications),
                      onPressed: () {
                        Navigator.pushNamed(context, '/notifications');
                      },
                    ),
                    if (unreadCount > 0)
                      Positioned(
                        right: 8,
                        top: 8,
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 16,
                            minHeight: 16,
                          ),
                          child: Text(
                            unreadCount > 99 ? '99+' : unreadCount.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                );
              })
            else
              IconButton(
                icon: const Icon(Icons.notifications),
                onPressed: () {
                  Navigator.pushNamed(context, '/notifications');
                },
              ),
            // Menu icon
            Builder(builder: (context) {
              return IconButton(
                icon: const Icon(Icons.menu),
                onPressed: () {
                  Scaffold.of(context).openEndDrawer();
                },
              );
            }),
          ],
        ),
        endDrawer: const MenuDrawer(),
        endDrawerEnableOpenDragGesture: _selectedIndex != 0,
        body: Obx(() {
          if (_authState.currentMobileUser.value == null) {
            return const Center(child: CircularProgressIndicator());
          }

          final user = _authState.currentMobileUser.value!;
          final isDriver = user.primaryUserType == UserType.driver;

          // Build pages based on user type
          final pages = <Widget>[
            MapScreen(
              onNavigateToTab: (index) {
                if (mounted) {
                  setState(() {
                    _selectedIndex = index;
                    _pageController.animateToPage(
                      index,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  });
                }
              },
            ),
            HistoryScreen(
              onNavigateToTab: (index) {
                if (mounted) {
                  setState(() {
                    _selectedIndex = index;
                    _pageController.animateToPage(
                      index,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  });
                }
              },
            ),
          ];

          // Add chat screen only for passengers
          if (!isDriver) {
            pages.add(
              ChatListScreen(
                onNavigateToTab: (index) {
                  if (mounted) {
                    setState(() {
                      _selectedIndex = index;
                      _pageController.animateToPage(
                        index,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    });
                  }
                },
              ),
            );
          }

          return PageView(
            controller: _pageController,
            physics: const NeverScrollableScrollPhysics(),
            children: pages,
          );
        }),
        bottomNavigationBar: Obx(() {
          final user = _authState.currentMobileUser.value;
          if (user == null) {
            return const SizedBox.shrink(); // Return empty widget if user is null
          }

          final isDriver = user.primaryUserType == UserType.driver;

          // Build navigation items based on user type
          final items = <BottomNavigationBarItem>[
            BottomNavigationBarItem(
              icon: const Icon(Icons.map),
              label: AppLocalizations.of(context)!.mainPage_maps,
            ),
            BottomNavigationBarItem(
              icon: const Icon(Icons.history),
              label: AppLocalizations.of(context)!.mainPage_history,
            ),
          ];

          // Add help item only for passengers
          if (!isDriver) {
            items.add(
              BottomNavigationBarItem(
                icon: const Icon(Icons.help_outline),
                label: AppLocalizations.of(context)!.mainPage_chat,
              ),
            );
          }

          // Adjust selected index if driver and was on chat tab
          int adjustedIndex = _selectedIndex;
          if (isDriver && _selectedIndex > 1) {
            adjustedIndex = 1; // Reset to history tab for drivers
          }

          return BottomNavigationBar(
            items: items,
            currentIndex: adjustedIndex,
            onTap: (index) {
              setState(() {
                _selectedIndex = index;
                _pageController.animateToPage(
                  index,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              });
            },
          );
        }),
      ),
    );
  }

  Future<bool> _onWillPop() async {
    if (!Platform.isAndroid) {
      return true;
    }

    // If we're not on the first tab (map screen), just go back to it
    if (_selectedIndex != 0) {
      if (mounted) {
        setState(() {
          _selectedIndex = 0;
          _pageController.animateToPage(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        });
      }
      return false;
    }

    // Show exit confirmation dialog
    final shouldExit = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(AppLocalizations.of(context)!.exit_dialog_title),
            content: Text(AppLocalizations.of(context)!.exit_dialog_message),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(AppLocalizations.of(context)!.dialog_no),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text(AppLocalizations.of(context)!.dialog_yes),
              ),
            ],
          ),
        ) ??
        false;

    if (shouldExit) {
      SystemNavigator.pop();
    }
    return false;
  }
}
