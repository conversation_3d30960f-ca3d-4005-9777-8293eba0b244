import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:logging/logging.dart';

import '../config/tenant_config.dart';
import '../models/MobileUser.dart';
import '../models/MobileUserNotification.dart';
import '../states/AuthState.dart';

class NotificationStateService extends GetxService {
  static NotificationStateService get instance => Get.find();
  
  final Logger _logger = Logger('NotificationStateService');
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  
  // Reactive state variables
  final RxList<MobileUserNotification> notifications = <MobileUserNotification>[].obs;
  final RxInt unreadCount = 0.obs;
  final RxBool isLoading = false.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString searchQuery = ''.obs;
  
  // Firestore listener
  StreamSubscription<QuerySnapshot>? _notificationsSubscription;
  
  // Computed list for search/filter
  List<MobileUserNotification> get filteredNotifications {
    if (searchQuery.value.isEmpty) {
      return notifications;
    }
    
    final query = searchQuery.value.toLowerCase();
    return notifications.where((notification) {
      return notification.title.toLowerCase().contains(query) ||
             notification.body.toLowerCase().contains(query) ||
             notification.type.toLowerCase().contains(query);
    }).toList();
  }
  
  @override
  void onInit() {
    super.onInit();
    _logger.info('Initializing NotificationStateService');
    _initializeService();
  }
  
  @override
  void onClose() {
    _notificationsSubscription?.cancel();
    super.onClose();
  }
  
  void _initializeService() {
    // Listen to auth state changes
    ever(Get.find<AuthState>().currentUser, (_) {
      refreshNotifications();
    });
    
    // Initialize notifications if user is already logged in
    if (Get.find<AuthState>().currentUser.value != null) {
      refreshNotifications();
    }
  }
  
  void refreshNotifications() {
    final user = Get.find<AuthState>().currentUser.value;
    if (user == null) {
      _logger.warning('No authenticated user, clearing notifications');
      notifications.clear();
      unreadCount.value = 0;
      _notificationsSubscription?.cancel();
      return;
    }
    
    _logger.info('Setting up Firestore listener for user: ${user.uid}');
    isLoading.value = true;
    hasError.value = false;
    
    // Cancel existing subscription
    _notificationsSubscription?.cancel();
    
    // Setup new Firestore listener
    const tenantId = TenantConfig.TENANT_ID;
    _notificationsSubscription = FirebaseFirestore.instance
        .collection('tenants')
        .doc(tenantId)
        .collection('mobile_user_notifications')
        .where('recipientUID', isEqualTo: user.uid)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .listen(
          (snapshot) {
            _logger.info('Received ${snapshot.docs.length} notifications from Firestore');
            
            // Convert documents to notification objects
            final notificationList = snapshot.docs
                .map((doc) => MobileUserNotification.fromFirestore(doc))
                .toList();
            
            // Update reactive lists
            notifications.value = notificationList;
            
            // Calculate unread count
            unreadCount.value = notificationList
                .where((n) => !n.isRead)
                .length;
            
            isLoading.value = false;
            hasError.value = false;
            
            _logger.info('Updated notifications: ${notifications.length} total, ${unreadCount.value} unread');
          },
          onError: (error) {
            _logger.severe('Error loading notifications', error);
            hasError.value = true;
            errorMessage.value = error.toString();
            isLoading.value = false;
          },
        );
  }
  
  Future<void> markAsRead(String notificationId) async {
    try {
      _logger.info('Marking notification as read: $notificationId');
      
      const tenantId = TenantConfig.TENANT_ID;
      await FirebaseFirestore.instance
          .collection('tenants')
          .doc(tenantId)
          .collection('mobile_user_notifications')
          .doc(notificationId)
          .update({
        'readAt': FieldValue.serverTimestamp(),
        'isRead': true,
      });
      
      _logger.info('Successfully marked notification as read');
    } catch (e) {
      _logger.severe('Error marking notification as read', e);
      rethrow;
    }
  }
  
  Future<void> markAsReadAndNavigate(String notificationId, {Function? onNavigate}) async {
    try {
      // Mark as read first
      await markAsRead(notificationId);
      
      // Find the notification
      final notification = notifications.firstWhereOrNull((n) => n.id == notificationId);
      if (notification == null) {
        _logger.warning('Notification not found: $notificationId');
        return;
      }
      
      // Navigate based on notification type and user role
      final authState = Get.find<AuthState>();
      final isDriver = authState.currentMobileUser.value?.primaryUserType == UserType.driver;
      
      // For drivers with trip-related notifications, navigate to map
      if (isDriver && notification.isTripRelated && 
          (notification.type == 'trip_request' || 
           notification.type == 'trip_assignment' ||
           notification.type == 'driver_timeout' ||
           notification.type == 'reservation_reminder')) {
        _logger.info('Driver trip notification - clearing stack and navigating to map');
        // Clear the navigation stack and go to map
        Get.offAllNamed('/');
        // The MainPage will show the map for drivers
      } else if (onNavigate != null) {
        // Use custom navigation if provided
        onNavigate();
      } else {
        // Default: navigate to detail screen
        Get.toNamed('/notification-detail', arguments: notification);
      }
    } catch (e) {
      _logger.severe('Error in markAsReadAndNavigate', e);
    }
  }
  
  Future<void> dismissSystemNotification(int notificationId) async {
    try {
      _logger.info('Dismissing system notification: $notificationId');
      await _localNotifications.cancel(notificationId);
    } catch (e) {
      _logger.warning('Error dismissing system notification', e);
    }
  }
  
  Future<void> markAllAsRead() async {
    try {
      _logger.info('Marking all notifications as read');
      
      final user = Get.find<AuthState>().currentUser.value;
      if (user == null) return;
      
      const tenantId = TenantConfig.TENANT_ID;
      final batch = FirebaseFirestore.instance.batch();
      
      // Get all unread notifications
      final unreadNotifications = notifications.where((n) => !n.isRead);
      
      for (final notification in unreadNotifications) {
        final docRef = FirebaseFirestore.instance
            .collection('tenants')
            .doc(tenantId)
            .collection('mobile_user_notifications')
            .doc(notification.id);
        
        batch.update(docRef, {
          'readAt': FieldValue.serverTimestamp(),
          'isRead': true,
        });
      }
      
      await batch.commit();
      _logger.info('Successfully marked ${unreadNotifications.length} notifications as read');
    } catch (e) {
      _logger.severe('Error marking all notifications as read', e);
      rethrow;
    }
  }
  
  void clearSearch() {
    searchQuery.value = '';
  }
  
  // Handle notification from FCM when app is opened via notification tap
  void handleNotificationTap(Map<String, dynamic> data) {
    _logger.info('Handling notification tap with data: $data');
    
    final notificationId = data['notificationId'];
    final notificationType = data['type'];
    
    // Check if driver and trip-related notification
    final authState = Get.find<AuthState>();
    final isDriver = authState.currentMobileUser.value?.primaryUserType == UserType.driver;
    
    if (isDriver && 
        (notificationType == 'trip_request' || 
         notificationType == 'trip_assignment' ||
         notificationType == 'driver_timeout' ||
         notificationType == 'reservation_reminder')) {
      _logger.info('Driver trip notification from system tray - navigating to map');
      
      // Mark as read if notification ID exists
      if (notificationId != null) {
        markAsRead(notificationId).catchError((e) {
          _logger.warning('Failed to mark notification as read: $e');
        });
      }
      
      // Clear stack and navigate to map
      Get.offAllNamed('/');
    } else if (notificationId != null) {
      // Auto-mark as read and navigate normally
      markAsReadAndNavigate(notificationId);
    }
    
    // Dismiss system notification if ID is provided
    final systemNotificationId = data['systemNotificationId'];
    if (systemNotificationId != null) {
      dismissSystemNotification(int.tryParse(systemNotificationId.toString()) ?? 0);
    }
  }
  
  // Get a specific notification by ID
  MobileUserNotification? getNotificationById(String id) {
    return notifications.firstWhereOrNull((n) => n.id == id);
  }
}