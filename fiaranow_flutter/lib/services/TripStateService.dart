import 'package:cloud_functions/cloud_functions.dart';
import 'package:get/get.dart';

import '../config/tenant_config.dart';

class TripStateService extends GetxService {
  static TripStateService get instance => Get.find();

  // Loading states for each trip transition type
  final Map<String, RxBool> _transitionLoading = {};

  // Error states
  final RxString lastError = ''.obs;

  // Get loading state for a specific trip and transition type
  bool isLoading(String tripId, String transitionType) {
    final key = '${tripId}_$transitionType';
    return _transitionLoading[key]?.value ?? false;
  }

  // Get the RxBool directly for reactive access
  RxBool getLoadingRx(String key) {
    // Ensure the RxBool exists
    if (!_transitionLoading.containsKey(key)) {
      _transitionLoading[key] = false.obs;
    }
    return _transitionLoading[key]!;
  }

  // Set loading state
  void _setLoading(String tripId, String transitionType, bool loading) {
    final key = '${tripId}_$transitionType';
    if (!_transitionLoading.containsKey(key)) {
      _transitionLoading[key] = loading.obs;
    } else {
      _transitionLoading[key]!.value = loading;
    }
  }

  // Clear error
  void clearError() {
    lastError.value = '';
  }

  // Handle Firebase Function errors with user-friendly messages
  String _getErrorMessage(dynamic error) {
    if (error is FirebaseFunctionsException) {
      switch (error.code) {
        case 'not-found':
          return 'Trajet introuvable';
        case 'permission-denied':
          return 'Vous n\'êtes pas autorisé à effectuer cette action';
        case 'failed-precondition':
          return error.message ?? 'Action non autorisée dans l\'état actuel du trajet';
        case 'unauthenticated':
          return 'Veuillez vous reconnecter';
        default:
          return error.message ?? 'Une erreur est survenue';
      }
    }
    return 'Une erreur inattendue est survenue';
  }

  // Request a driver for the trip
  Future<bool> requestDriver({
    required String tripId,
    required String driverUid,
    required Map<String, dynamic> driverLocation,
  }) async {
    _setLoading(tripId, 'requestDriver', true);
    clearError();

    try {
      final callable = FirebaseFunctions.instanceFor(region: 'europe-west3').httpsCallable('requestDriverTransition');
      final response = await callable.call({
        'tenantId': TenantConfig.TENANT_ID,
        'tripId': tripId,
        'driverUid': driverUid,
        'driverLocation': driverLocation,
      });
      final result = response.data as Map<String, dynamic>;

      return result['success'] == true;
    } catch (e) {
      lastError.value = _getErrorMessage(e);
      return false;
    } finally {
      _setLoading(tripId, 'requestDriver', false);
    }
  }

  // Driver arrived at pickup location
  Future<bool> driverArrived({required String tripId}) async {
    _setLoading(tripId, 'driverArrived', true);
    clearError();

    try {
      final callable = FirebaseFunctions.instanceFor(region: 'europe-west3').httpsCallable('driverArrivedTransition');
      final response = await callable.call({
        'tenantId': TenantConfig.TENANT_ID,
        'tripId': tripId,
      });
      final result = response.data as Map<String, dynamic>;

      return result['success'] == true;
    } catch (e) {
      lastError.value = _getErrorMessage(e);
      return false;
    } finally {
      _setLoading(tripId, 'driverArrived', false);
    }
  }

  // Start the trip
  Future<bool> startTrip({
    required String tripId,
    required String userType,
    Map<String, dynamic>? tripConfiguration,
  }) async {
    _setLoading(tripId, 'startTrip', true);
    clearError();

    try {
      final callable = FirebaseFunctions.instanceFor(region: 'europe-west3').httpsCallable('startTripTransition');
      final response = await callable.call({
        'tenantId': TenantConfig.TENANT_ID,
        'tripId': tripId,
        'userType': userType,
        if (tripConfiguration != null) 'tripConfiguration': tripConfiguration,
      });
      final result = response.data as Map<String, dynamic>;

      return result['success'] == true;
    } catch (e) {
      lastError.value = _getErrorMessage(e);
      return false;
    } finally {
      _setLoading(tripId, 'startTrip', false);
    }
  }

  // Complete the trip
  Future<bool> completeTrip({
    required String tripId,
    Map<String, dynamic>? finalRouteData,
    Map<String, dynamic>? costData,
  }) async {
    _setLoading(tripId, 'completeTrip', true);
    clearError();

    try {
      final callable = FirebaseFunctions.instanceFor(region: 'europe-west3').httpsCallable('completeTripTransition');
      final response = await callable.call({
        'tenantId': TenantConfig.TENANT_ID,
        'tripId': tripId,
        if (finalRouteData != null) 'finalRouteData': finalRouteData,
        if (costData != null) 'costData': costData,
      });
      final result = response.data as Map<String, dynamic>;

      return result['success'] == true;
    } catch (e) {
      lastError.value = _getErrorMessage(e);
      return false;
    } finally {
      _setLoading(tripId, 'completeTrip', false);
    }
  }

  // Cancel the trip
  Future<bool> cancelTrip({
    required String tripId,
    String? reason,
  }) async {
    _setLoading(tripId, 'cancelTrip', true);
    clearError();

    try {
      final callable = FirebaseFunctions.instanceFor(region: 'europe-west3').httpsCallable('cancelTripTransition');
      final response = await callable.call({
        'tenantId': TenantConfig.TENANT_ID,
        'tripId': tripId,
        if (reason != null) 'reason': reason,
      });
      final result = response.data as Map<String, dynamic>;

      return result['success'] == true;
    } catch (e) {
      lastError.value = _getErrorMessage(e);
      return false;
    } finally {
      _setLoading(tripId, 'cancelTrip', false);
    }
  }

  // Dismiss the trip
  Future<bool> dismissTrip({
    required String tripId,
    required String userType,
  }) async {
    _setLoading(tripId, 'dismissTrip', true);
    clearError();

    try {
      final callable = FirebaseFunctions.instanceFor(region: 'europe-west3').httpsCallable('dismissTripTransition');
      final response = await callable.call({
        'tenantId': TenantConfig.TENANT_ID,
        'tripId': tripId,
        'userType': userType,
      });
      final result = response.data as Map<String, dynamic>;

      return result['success'] == true;
    } catch (e) {
      lastError.value = _getErrorMessage(e);
      return false;
    } finally {
      _setLoading(tripId, 'dismissTrip', false);
    }
  }

  // Position tracking now handled via logs subcollection
  // Use trip.recordTripLog() method instead

  // Generic state transition (for advanced use cases)
  Future<bool> transitionState({
    required String tripId,
    required Map<String, dynamic> event,
  }) async {
    _setLoading(tripId, 'transition', true);
    clearError();

    try {
      final callable = FirebaseFunctions.instanceFor(region: 'europe-west3').httpsCallable('transitionTripState');
      final response = await callable.call({
        'tenantId': TenantConfig.TENANT_ID,
        'tripId': tripId,
        'event': event,
      });
      final result = response.data as Map<String, dynamic>;

      return result['success'] == true;
    } catch (e) {
      lastError.value = _getErrorMessage(e);
      return false;
    } finally {
      _setLoading(tripId, 'transition', false);
    }
  }

  // Retry logic with exponential backoff
  Future<T?> _retryWithBackoff<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
  }) async {
    int retries = 0;
    Duration delay = initialDelay;

    while (retries < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        if (e is FirebaseFunctionsException && (e.code == 'unavailable' || e.code == 'deadline-exceeded')) {
          retries++;
          if (retries < maxRetries) {
            await Future.delayed(delay);
            delay *= 2; // Exponential backoff
            continue;
          }
        }
        rethrow;
      }
    }

    return null;
  }

  // Request driver with retry
  Future<bool> requestDriverWithRetry({
    required String tripId,
    required String driverUid,
    required Map<String, dynamic> driverLocation,
  }) async {
    final result = await _retryWithBackoff(() => requestDriver(
          tripId: tripId,
          driverUid: driverUid,
          driverLocation: driverLocation,
        ));

    return result ?? false;
  }
}
