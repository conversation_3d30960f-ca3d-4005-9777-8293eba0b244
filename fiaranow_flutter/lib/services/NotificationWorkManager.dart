import 'package:workmanager/workmanager.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:logging/logging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'dart:typed_data';
import '../fcm.dart';
import '../firebase_options.dart';

const String NOTIFICATION_TASK = 'notification_processing_task';
const String NOTIFICATION_WORK_TAG = 'fcm_notifications';

class NotificationWorkManager {
  static final Logger _logger = Logger('NotificationWorkManager');

  /// Initialize WorkManager for notification processing
  static Future<void> initialize() async {
    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: true, // Set to false in production
    );
    _logger.info('WorkManager initialized for notifications');
  }

  /// Schedule notification processing work
  static Future<void> scheduleNotificationWork(RemoteMessage message) async {
    try {
      await Workmanager().registerOneOffTask(
        'notification_${message.messageId}',
        NOTIFICATION_TASK,
        tag: NOTIFICATION_WORK_TAG,
        inputData: {
          'messageId': message.messageId ?? '',
          'title': message.notification?.title ?? '',
          'body': message.notification?.body ?? '',
          'data': message.data,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
        constraints: Constraints(
          networkType: NetworkType.connected, // Require network for data processing
        ),
      );
      _logger.info('Scheduled notification work for message: ${message.messageId}');
    } catch (e) {
      _logger.severe('Failed to schedule notification work: $e');
      // Fallback: show notification immediately
      await _showFallbackNotification(message);
    }
  }

  /// Cancel all pending notification work
  static Future<void> cancelAllWork() async {
    await Workmanager().cancelByTag(NOTIFICATION_WORK_TAG);
  }

  /// Fallback notification for WorkManager failures
  static Future<void> _showFallbackNotification(RemoteMessage message) async {
    try {
      // Initialize minimal notification display
      await initializeFlutterNotifications();
      await showNotification(message);
    } catch (e) {
      _logger.severe('Fallback notification also failed: $e');
    }
  }
}

/// WorkManager callback dispatcher
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    final Logger logger = Logger('WorkManager');
    
    try {
      logger.info('Executing background task: $task');
      
      if (task == NOTIFICATION_TASK) {
        await _processNotification(inputData);
        return Future.value(true);
      }
      
      logger.warning('Unknown task: $task');
      return Future.value(false);
    } catch (e) {
      logger.severe('WorkManager task failed: $e');
      return Future.value(false);
    }
  });
}

/// Process notification in WorkManager context
Future<void> _processNotification(Map<String, dynamic>? inputData) async {
  final Logger logger = Logger('NotificationProcessor');
  
  if (inputData == null) {
    logger.severe('No input data for notification processing');
    return;
  }

  try {
    // Initialize Firebase if needed (this is now in WorkManager context)
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }

    // Initialize notification system
    await initializeFlutterNotifications();

    // Reconstruct message from input data
    final messageData = Map<String, dynamic>.from(inputData['data'] ?? {});
    final title = inputData['title'] as String? ?? 'Notification';
    final body = inputData['body'] as String? ?? '';

    // Create notification details based on message type
    await _processSpecificNotification(title, body, messageData);
    
    logger.info('Successfully processed notification in WorkManager');
  } catch (e) {
    logger.severe('Failed to process notification in WorkManager: $e');
    rethrow; // Let WorkManager handle retry
  }
}

/// Process specific notification types with enhanced logic
Future<void> _processSpecificNotification(
  String title, 
  String body, 
  Map<String, dynamic> data
) async {
  final Logger logger = Logger('NotificationProcessor');
  
  try {
    // Determine notification type and channel
    final notificationType = data['notificationType'] as String?;
    final isDriverNotification = data['isDriverNotification'] == 'true';
    
    String channelId;
    AndroidNotificationDetails androidDetails;

    // Configure notification based on type
    if (isDriverNotification || 
        ['trip_request', 'trip_cancelled', 'passenger_arrived'].contains(notificationType)) {
      // High-priority driver notifications with special ringtone
      channelId = 'driver_channel';
      androidDetails = AndroidNotificationDetails(
        channelId,
        'Driver Notifications',
        importance: Importance.max,
        priority: Priority.high,
        sound: const RawResourceAndroidNotificationSound('phone_ringtone_ultra'),
        additionalFlags: Int32List.fromList(<int>[4]), // FLAG_INSISTENT
        playSound: true,
        enableVibration: true,
        timeoutAfter: 60000, // 60 seconds
      );
    } else if (['driver_moving', 'driver_arrived', 'trip_paid'].contains(notificationType)) {
      // Silent notifications for status updates
      channelId = 'silent_notification';
      androidDetails = const AndroidNotificationDetails(
        'silent_notification',
        'Silent Notifications',
        importance: Importance.high,
        priority: Priority.high,
        playSound: false,
        enableVibration: false,
        enableLights: false,
      );
    } else {
      // Default passenger notifications
      channelId = 'passenger_channel';
      androidDetails = const AndroidNotificationDetails(
        'passenger_channel',
        'Passenger Notifications',
        importance: Importance.high,
        priority: Priority.high,
        playSound: true,
        enableVibration: true,
      );
    }

    // Show the notification
    await flutterLocalNotificationsPlugin.show(
      data['notificationId']?.hashCode ?? DateTime.now().millisecondsSinceEpoch,
      title,
      body,
      NotificationDetails(android: androidDetails),
      payload: data.isNotEmpty ? data.entries.map((e) => '${e.key}=${e.value}').join('&') : null,
    );

    logger.info('Displayed notification: $title');
  } catch (e) {
    logger.severe('Failed to display notification: $e');
    // Try basic notification as fallback
    await flutterLocalNotificationsPlugin.show(
      DateTime.now().millisecondsSinceEpoch,
      title,
      body,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'default_channel',
          'Default Notifications',
          importance: Importance.high,
          priority: Priority.high,
        ),
      ),
    );
  }
}