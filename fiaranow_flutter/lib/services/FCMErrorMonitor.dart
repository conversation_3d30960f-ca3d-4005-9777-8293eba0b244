import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:logging/logging.dart';

class FCMErrorMonitor {
  static final Logger _logger = Logger('FCMErrorMonitor');
  static int _errorCount = 0;
  static DateTime? _lastErrorTime;

  static void reportError(String operation, dynamic error, StackTrace? stackTrace) {
    _errorCount++;
    _lastErrorTime = DateTime.now();

    _logger.severe('FCM Error in $operation: $error', error, stackTrace);

    // Report to Crashlytics for monitoring
    FirebaseCrashlytics.instance.recordError(
      error,
      stackTrace,
      reason: 'FCM Error in $operation',
      fatal: false,
    );

    // Log event to Firebase Analytics
    FirebaseAnalytics.instance.logEvent(
      name: 'fcm_error',
      parameters: {
        'operation': operation,
        'error_type': error.runtimeType.toString(),
        'error_count': _errorCount.toString(),
        'timestamp': _lastErrorTime!.toIso8601String(),
      },
    );

    // Critical: if too many errors, disable notifications temporarily
    if (_errorCount > 10) {
      _logger.warning('Too many FCM errors, consider implementing circuit breaker');
    }
  }

  static void reportSuccess(String operation) {
    _logger.info('FCM Success: $operation');

    // Reset error count on success
    if (_errorCount > 0) {
      _logger.info('FCM recovered after $_errorCount errors');
      _errorCount = 0;
    }

    FirebaseAnalytics.instance.logEvent(
      name: 'fcm_success',
      parameters: {
        'operation': operation,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  static Map<String, dynamic> getErrorStats() {
    return {
      'error_count': _errorCount,
      'last_error_time': _lastErrorTime?.toIso8601String(),
      'is_healthy': _errorCount < 5,
    };
  }
}
