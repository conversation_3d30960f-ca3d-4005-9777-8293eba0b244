import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logging/logging.dart';

import '../config/tenant_config.dart';

/// Manages chat presence for users in chat sessions
class ChatPresenceManager {
  final Logger _logger = Logger('ChatPresenceManager');
  final String userId;
  final String userType; // 'user' or 'admin'
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  String? _currentSessionId;
  Timer? _heartbeatTimer;

  ChatPresenceManager({
    required this.userId,
    required this.userType,
  });

  /// Enter a chat session and start tracking presence
  Future<void> enterChatSession(String sessionId) async {
    try {
      _currentSessionId = sessionId;

      // Create or update presence document
      await _firestore.collection(TenantConfig.getTenantPath('chat_presence')).doc('${sessionId}_$userId').set({
        'userId': userId,
        'userType': userType,
        'sessionId': sessionId,
        'isActive': true,
        'lastSeen': FieldValue.serverTimestamp(),
      });

      // Start heartbeat timer
      _startHeartbeat();
    } catch (e) {
      _logger.severe('Error entering chat session', e);
    }
  }

  /// Leave the current chat session
  Future<void> leaveChatSession() async {
    if (_currentSessionId == null) return;

    try {
      // Stop heartbeat
      _stopHeartbeat();

      // Update presence to inactive
      await _firestore.collection(TenantConfig.getTenantPath('chat_presence')).doc('${_currentSessionId}_$userId').update({
        'isActive': false,
        'lastSeen': FieldValue.serverTimestamp(),
      });

      _currentSessionId = null;
    } catch (e) {
      _logger.severe('Error leaving chat session', e);
    }
  }

  /// Update presence when app lifecycle changes
  Future<void> updatePresenceForLifecycle({required bool isActive}) async {
    if (_currentSessionId == null) return;

    try {
      await _firestore.collection(TenantConfig.getTenantPath('chat_presence')).doc('${_currentSessionId}_$userId').update({
        'isActive': isActive,
        'lastSeen': FieldValue.serverTimestamp(),
      });

      if (isActive) {
        _startHeartbeat();
      } else {
        _stopHeartbeat();
      }
    } catch (e) {
      _logger.severe('Error updating presence for lifecycle', e);
    }
  }

  /// Start heartbeat timer to keep presence alive
  void _startHeartbeat() {
    _stopHeartbeat(); // Cancel any existing timer

    // Update heartbeat every 30 seconds
    _heartbeatTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _updateHeartbeat();
    });
  }

  /// Stop heartbeat timer
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// Update heartbeat timestamp
  Future<void> _updateHeartbeat() async {
    if (_currentSessionId == null) return;

    try {
      await _firestore.collection(TenantConfig.getTenantPath('chat_presence')).doc('${_currentSessionId}_$userId').update({
        'lastSeen': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      _logger.severe('Error updating heartbeat', e);
    }
  }

  /// Clean up resources
  void dispose() {
    _stopHeartbeat();
  }
}
