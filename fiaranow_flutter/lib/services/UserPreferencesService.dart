import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

import '../config/tenant_config.dart';
import '../models/UserPreferences.dart';
import '../states/AuthState.dart';

class UserPreferencesService extends GetxService {
  final Logger _logger = Logger('UserPreferencesService');
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthState _authState = Get.find<AuthState>();

  final Rx<UserPreferences?> userPreferences = Rx<UserPreferences?>(null);
  final Rx<bool?> adminRingtoneDefault = Rx<bool?>(null);

  StreamSubscription<DocumentSnapshot>? _preferencesSubscription;
  StreamSubscription<DocumentSnapshot>? _adminConfigSubscription;

  @override
  void onInit() {
    super.onInit();
    // Initialize with default preferences immediately
    _initializeDefaultPreferences();

    // Set up admin configuration listener
    _setupAdminConfigListener();

    // Listen to user state changes to update preferences subscription
    ever(_authState.currentMobileUser, (_) {
      _setupPreferencesListener();
    });
    // Initial setup
    _setupPreferencesListener();
  }

  void _initializeDefaultPreferences() {
    final user = _authState.currentMobileUser.value;
    if (user != null && userPreferences.value == null) {
      // Set default preferences immediately to avoid null state
      userPreferences.value = UserPreferences(
        userId: user.uid,
        notificationSettings: NotificationSettings.defaultSettings(),
      );
    }
  }

  void _setupAdminConfigListener() {
    // Cancel existing subscription
    _adminConfigSubscription?.cancel();

    // Listen to admin notification configuration
    _adminConfigSubscription = _firestore
        .collection('tenants')
        .doc(TenantConfig.TENANT_ID)
        .collection('configurations')
        .doc('passengerNotifications')
        .snapshots()
        .listen((snapshot) {
      if (snapshot.exists) {
        final data = snapshot.data();
        adminRingtoneDefault.value = data?['value']?['enableRingtoneByDefault'] ?? true;
      } else {
        adminRingtoneDefault.value = true; // Default if document doesn't exist
      }
    }, onError: (error) {
      _logger.severe('Error listening to admin configuration', error);
      // Set default on error
      adminRingtoneDefault.value = true;
    });
  }

  void _setupPreferencesListener() {
    // Cancel existing subscription
    _preferencesSubscription?.cancel();
    _preferencesSubscription = null;

    final user = _authState.currentMobileUser.value;
    if (user == null) {
      userPreferences.value = null;
      return;
    }

    // Set default preferences first to avoid loading state
    userPreferences.value ??= UserPreferences(
      userId: user.uid,
      notificationSettings: NotificationSettings.defaultSettings(),
    );

    // Listen to user preferences
    _preferencesSubscription = _firestore
        .collection('mobile_users')
        .doc(user.uid)
        .collection('preferences')
        .doc('notification_settings')
        .snapshots()
        .listen((snapshot) {
      if (snapshot.exists) {
        try {
          userPreferences.value = UserPreferences.fromFirestore(snapshot);
        } catch (e) {
          _logger.severe('Error parsing user preferences', e);
          // Keep current preferences on parse error
        }
      } else {
        // Create default preferences in Firestore if they don't exist
        _createDefaultPreferencesInFirestore();
      }
    }, onError: (error) {
      _logger.severe('Error listening to user preferences', error);
      // Keep current preferences on error
    });
  }

  Future<void> _createDefaultPreferencesInFirestore() async {
    final user = _authState.currentMobileUser.value;
    if (user == null) return;

    try {
      final defaultPreferences = userPreferences.value ??
          UserPreferences(
            userId: user.uid,
            notificationSettings: NotificationSettings.defaultSettings(),
          );

      await _firestore
          .collection('mobile_users')
          .doc(user.uid)
          .collection('preferences')
          .doc('notification_settings')
          .set(defaultPreferences.toMap());
    } catch (e) {
      _logger.severe('Error creating default preferences in Firestore', e);
      // Preferences remain in memory even if Firestore write fails
    }
  }

  Future<bool> updateNotificationSettings(NotificationSettings settings) async {
    final user = _authState.currentMobileUser.value;
    if (user == null) return false;

    try {
      await _firestore.collection('mobile_users').doc(user.uid).collection('preferences').doc('notification_settings').update({
        'notificationSettings': settings.toMap(),
      });
      return true;
    } catch (e) {
      _logger.severe('Error updating notification settings', e);
      return false;
    }
  }

  Future<bool> updateRingtoneSetting(bool? enableRingtone) async {
    final currentPrefs = userPreferences.value;
    if (currentPrefs == null) return false;

    final updatedSettings = NotificationSettings(
      enableRingtone: enableRingtone,
      enableDriverMovingNotification: currentPrefs.notificationSettings.enableDriverMovingNotification,
      enableDriverArrivedNotification: currentPrefs.notificationSettings.enableDriverArrivedNotification,
      enableTripPaidNotification: currentPrefs.notificationSettings.enableTripPaidNotification,
      enableReservationReminders: currentPrefs.notificationSettings.enableReservationReminders,
    );

    return updateNotificationSettings(updatedSettings);
  }

  // Get the effective ringtone setting considering admin defaults
  Future<bool> getEffectiveRingtoneSetting() async {
    final prefs = userPreferences.value;

    // If user has a specific preference set, use it
    if (prefs != null && prefs.notificationSettings.enableRingtone != null) {
      return prefs.notificationSettings.enableRingtone!;
    }

    // Otherwise, use admin default (or true if not available)
    return adminRingtoneDefault.value ?? true;
  }

  @override
  void onClose() {
    _preferencesSubscription?.cancel();
    _adminConfigSubscription?.cancel();
    super.onClose();
  }
}
