import 'dart:io';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:geolocator/geolocator.dart';
import 'package:logging/logging.dart';

import '../l10n/app_localizations.dart';
import '../models/MobileUser.dart';
import '../states/AuthState.dart';
import '../states/NavigationState.dart';
import '../states/PermissionsState.dart';

class DriverModePermissionDialog extends StatefulWidget {
  const DriverModePermissionDialog({super.key});

  @override
  State<DriverModePermissionDialog> createState() => _DriverModePermissionDialogState();
}

class _DriverModePermissionDialogState extends State<DriverModePermissionDialog> {
  final Logger _logger = Logger('DriverModePermissionDialog');
  final PermissionsState _permissionsState = Get.find<PermissionsState>();
  final AuthState _authState = Get.find<AuthState>();
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  bool _isRequesting = false;

  @override
  void initState() {
    super.initState();
    _analytics.logScreenView(screenName: 'driver_mode_permission_dialog');
  }

  Future<void> _requestAllPermissions() async {
    if (_isRequesting) return;

    setState(() {
      _isRequesting = true;
    });

    final userId = _authState.currentMobileUser.value?.uid ?? 'unknown';

    try {
      // Step 1: Check and request notification permission
      bool notificationGranted = _permissionsState.hasNotificationPermission();
      if (!notificationGranted) {
        _logger.info('Requesting notification permission');
        notificationGranted = await _permissionsState.requestNotificationPermission();
        
        if (!notificationGranted) {
          _analytics.logEvent(
            name: 'driver_permission_denied',
            parameters: {
              'permission_type': 'notification',
              'user_id': userId,
            },
          );
          
          if (mounted) {
            await _showPermissionDeniedDialog('notification');
          }
          return;
        }
      }

      // Step 2: Check and request background location permission
      final locationPermission = await Geolocator.checkPermission();
      bool locationGranted = locationPermission == LocationPermission.always;
      
      if (!locationGranted) {
        _logger.info('Requesting background location permission');
        
        // First request basic location permission if not granted
        if (locationPermission == LocationPermission.denied) {
          final permission = await Geolocator.requestPermission();
          if (permission == LocationPermission.deniedForever) {
            _analytics.logEvent(
              name: 'driver_permission_denied_forever',
              parameters: {
                'permission_type': 'location',
                'user_id': userId,
              },
            );
            
            if (mounted) {
              await _showPermissionDeniedDialog('location');
            }
            return;
          }
        }
        
        // Request background location permission
        locationGranted = await _permissionsState.requestLocationPermission(background: true);
        
        // Check if we have at least "always" permission for background location
        final finalPermission = await Geolocator.checkPermission();
        if (finalPermission != LocationPermission.always) {
          _analytics.logEvent(
            name: 'driver_permission_denied',
            parameters: {
              'permission_type': 'background_location',
              'user_id': userId,
              'current_permission': finalPermission.toString(),
            },
          );
          
          if (mounted) {
            await _showBackgroundLocationInstructionsDialog();
          }
          return;
        }
      }

      // All permissions granted successfully
      _analytics.logEvent(
        name: 'driver_permissions_granted',
        parameters: {
          'user_id': userId,
        },
      );

      // Update user type to driver
      _authState.setPrimaryUserType(UserType.driver);
      
      // Switch to driver mode
      final navigationState = Get.find<NavigationState>();
      navigationState.reset(stopNearbyDriversSub: true, stopCurrentTripSub: true);
      navigationState.setDrivingMode(UserType.driver);

      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e, stackTrace) {
      _logger.severe('Error requesting permissions', e, stackTrace);
      _analytics.logEvent(
        name: 'driver_permission_error',
        parameters: {
          'error': e.toString(),
          'user_id': userId,
        },
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.genericError),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRequesting = false;
        });
      }
    }
  }

  Future<void> _showPermissionDeniedDialog(String permissionType) async {
    final title = permissionType == 'notification'
        ? AppLocalizations.of(context)!.notification_requiredTitle
        : AppLocalizations.of(context)!.driverMode_locationRequiredTitle;
        
    final content = permissionType == 'notification'
        ? AppLocalizations.of(context)!.notification_requiredContent
        : AppLocalizations.of(context)!.driverMode_locationRequiredContent;

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(content),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(this.context).pop(false);
              },
              child: Text(AppLocalizations.of(context)!.notification_cancel),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _permissionsState.openSettings();
              },
              child: Text(AppLocalizations.of(context)!.notification_openSettings),
            ),
          ],
        );
      },
    );
  }

  Future<void> _showBackgroundLocationInstructionsDialog() async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context)!.driverMode_backgroundLocationTitle),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(AppLocalizations.of(context)!.driverMode_backgroundLocationExplanation),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context)!.driverMode_backgroundLocationInstructions,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              if (Platform.isIOS) ...[
                Text('1. ${AppLocalizations.of(context)!.driverMode_iosStep1}'),
                Text('2. ${AppLocalizations.of(context)!.driverMode_iosStep2}'),
                Text('3. ${AppLocalizations.of(context)!.driverMode_iosStep3}'),
              ] else ...[
                Text('1. ${AppLocalizations.of(context)!.driverMode_androidStep1}'),
                Text('2. ${AppLocalizations.of(context)!.driverMode_androidStep2}'),
                Text('3. ${AppLocalizations.of(context)!.driverMode_androidStep3}'),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(this.context).pop(false);
              },
              child: Text(AppLocalizations.of(context)!.notification_cancel),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _permissionsState.openLocationSettings();
              },
              child: Text(AppLocalizations.of(context)!.notification_openSettings),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.local_taxi,
              size: 64,
              color: Colors.orange,
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context)!.driverMode_permissionTitle,
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context)!.driverMode_permissionDescription,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Obx(() {
              final hasNotification = _permissionsState.hasNotificationPermission();
              final hasBackgroundLocation = _permissionsState.locationPermission.value == LocationPermission.always;

              return Column(
                children: [
                  _PermissionItem(
                    icon: Icons.notifications,
                    title: AppLocalizations.of(context)!.driverMode_notificationPermission,
                    isGranted: hasNotification,
                  ),
                  const SizedBox(height: 8),
                  _PermissionItem(
                    icon: Icons.location_on,
                    title: AppLocalizations.of(context)!.driverMode_backgroundLocationPermission,
                    isGranted: hasBackgroundLocation,
                  ),
                ],
              );
            }),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: _isRequesting 
                      ? null 
                      : () {
                          Navigator.of(context).pop(false);
                        },
                  child: Text(AppLocalizations.of(context)!.notification_cancel),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isRequesting ? null : _requestAllPermissions,
                  child: _isRequesting
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(AppLocalizations.of(context)!.driverMode_grantPermissions),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _PermissionItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final bool isGranted;

  const _PermissionItem({
    required this.icon,
    required this.title,
    required this.isGranted,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(
          icon,
          color: isGranted ? Colors.green : Colors.grey,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: isGranted ? Colors.green : null,
              decoration: isGranted ? TextDecoration.lineThrough : null,
            ),
          ),
        ),
        Icon(
          isGranted ? Icons.check_circle : Icons.radio_button_unchecked,
          color: isGranted ? Colors.green : Colors.grey,
          size: 20,
        ),
      ],
    );
  }
}