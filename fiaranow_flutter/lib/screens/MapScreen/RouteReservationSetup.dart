import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:fiaranow_flutter/models/Trip.dart';
import 'package:fiaranow_flutter/states/AppState.dart';
import 'package:fiaranow_flutter/states/AuthState.dart';
import 'package:fiaranow_flutter/states/NavigationState.dart';
import 'package:fiaranow_flutter/widgets/PassengerCountSlider.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class RouteReservationSetup extends StatefulWidget {
  final void Function(dynamic index) onNavigateToTab;
  final GoogleMapController? mapController;

  const RouteReservationSetup({
    super.key,
    required this.onNavigateToTab,
    required this.mapController,
  });

  @override
  State<RouteReservationSetup> createState() => _RouteReservationSetupState();
}

class _RouteReservationSetupState extends State<RouteReservationSetup> {
  final _reservationFormKey = GlobalKey<FormBuilderState>();
  final dateController = TextEditingController();
  final timeController = TextEditingController();
  DateTime selectedDate = DateTime.now();
  TimeOfDay selectedTime = TimeOfDay.now();
  bool isLoading = false;

  final navigationState = Get.find<NavigationState>();
  final appState = Get.find<AppState>();
  final authState = Get.find<AuthState>();

  @override
  void initState() {
    super.initState();
    dateController.text = "${selectedDate.toLocal()}".split(' ')[0];
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    timeController.text = selectedTime.format(context);
  }

  void _onConfirmReservation() async {
    FirebaseAnalytics.instance.logEvent(
      name: 'trip_reserved',
      parameters: {
        'date': selectedDate.toIso8601String(),
        'time': selectedTime.format(context),
        'widget_name': 'route_reservation',
      },
    );
    if (_reservationFormKey.currentState?.saveAndValidate() ?? false) {
      final reservationDateTime = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        selectedTime.hour,
        selectedTime.minute,
      );

      if (reservationDateTime.isAfter(DateTime.now().add(const Duration(minutes: 15)))) {
        if (mounted) {
          setState(() {
            isLoading = true;
          });
        }

        try {
          final selectedRoute = navigationState.routeOverviews[navigationState.selectedRouteIndex.value];
          final trip = await Trip.reserveTrip(
            uidPassenger: authState.currentMobileUser.value!.uid,
            startPosition: navigationState.startPosition.value!,
            destinationPosition: navigationState.destinationPosition.value!,
            user: authState.currentMobileUser.value!,
            reservationTime: reservationDateTime,
            routeData: selectedRoute,
            startLocationName: navigationState.startAddress.value,
            arrivalLocationName: navigationState.destinationAddress.value,
            tripConfiguration: appState.tripConfiguration.value,
            customerRequestedPaymentMethod: navigationState.currentRequestedPaymentMethod!,
            passengerCount: navigationState.passengerCount.value,
          );

          navigationState.reset();
          widget.mapController?.animateCamera(
            CameraUpdate.newCameraPosition(
              CameraPosition(
                target: navigationState.currentPosition.value!,
                zoom: 16.0,
              ),
            ),
          );

          widget.onNavigateToTab(1);

          if (mounted) {
            Get.snackbar(
              AppLocalizations.of(context)!.mapScreen_success,
              AppLocalizations.of(context)!.mapScreen_tripReservedSuccessfully,
              backgroundColor: Colors.green,
              colorText: Colors.white,
            );
          }

          FirebaseAnalytics.instance.logEvent(
            name: 'trip_reserved',
            parameters: {
              'reservation_time': reservationDateTime.toIso8601String(),
              'distance_km': selectedRoute.distanceKm.toStringAsFixed(2),
              'duration_min': (selectedRoute.durationSec / 60).toStringAsFixed(0),
              'widget_name': 'route_reservation',
              'trip_id': trip.id,
            },
          );
        } catch (error) {
          if (mounted) {
            Get.snackbar(
              AppLocalizations.of(context)!.mapScreen_error,
              error.toString(),
              backgroundColor: Colors.red,
              colorText: Colors.white,
            );
          }
        } finally {
          if (mounted) {
            setState(() {
              isLoading = false;
            });
          }
        }
      } else {
        Get.snackbar(
          AppLocalizations.of(context)!.mapScreen_error,
          AppLocalizations.of(context)!.mapScreen_selectFutureTime,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () {
                navigationState.setRouteChosen(false);
              },
            ),
            Padding(
              padding: const EdgeInsets.all(10.0),
              child: Text(
                AppLocalizations.of(context)!.mapScreen_reserveThisTrip, // "Reserve this Trip"
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        const Divider(height: 1),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: FormBuilder(
            key: _reservationFormKey,
            child: Row(
              children: [
                Expanded(
                  child: FormBuilderTextField(
                    name: 'date',
                    controller: dateController,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context)!.mapScreen_reservationDateLabel, // "Date"
                      labelStyle: TextStyle(
                        color: Theme.of(context).brightness == Brightness.dark ? Colors.white70 : Colors.black54,
                      ),
                      border: const OutlineInputBorder(),
                      filled: true,
                      fillColor: Theme.of(context).brightness == Brightness.dark ? Colors.black : Colors.white,
                    ),
                    style: TextStyle(
                      color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                    ),
                    readOnly: true,
                    onTap: () async {
                      DateTime? pickedDate = await showDatePicker(
                        context: context,
                        initialDate: selectedDate,
                        firstDate: DateTime.now(),
                        lastDate: DateTime(2050),
                      );
                      if (pickedDate != null && pickedDate != selectedDate) {
                        setState(() {
                          selectedDate = pickedDate;
                          dateController.text = "${selectedDate.toLocal()}".split(' ')[0];
                        });
                      }
                    },
                    validator: FormBuilderValidators.compose([
                      FormBuilderValidators.required(),
                    ]),
                  ),
                ),
                const SizedBox(width: 8.0),
                Expanded(
                  child: FormBuilderTextField(
                    name: 'time',
                    controller: timeController,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context)!.mapScreen_reservationTimeLabel, // "Time"
                      labelStyle: TextStyle(
                        color: Theme.of(context).brightness == Brightness.dark ? Colors.white70 : Colors.black54,
                      ),
                      border: const OutlineInputBorder(),
                      filled: true,
                      fillColor: Theme.of(context).brightness == Brightness.dark ? Colors.black : Colors.white,
                    ),
                    style: TextStyle(
                      color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                    ),
                    readOnly: true,
                    onTap: () async {
                      TimeOfDay? pickedTime = await showTimePicker(
                        context: context,
                        initialTime: selectedTime,
                      );
                      if (pickedTime != null && pickedTime != selectedTime) {
                        setState(() {
                          selectedTime = pickedTime;
                          timeController.text = selectedTime.format(context);
                        });
                      }
                    },
                    validator: FormBuilderValidators.compose([
                      FormBuilderValidators.required(),
                    ]),
                  ),
                ),
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: PassengerCountSlider(
            passengerCount: navigationState.passengerCount,
            maxPassengers: appState.tripConfiguration.value.maxPassengerCount,
          ),
        ),
        const SizedBox(height: 16),
        isLoading
            ? const CircularProgressIndicator()
            : ElevatedButton(
                onPressed: _onConfirmReservation,
                child: Text(AppLocalizations.of(context)!.mapScreen_confirmReservation), // "Confirm Reservation"
              ),
      ],
    );
  }
}
