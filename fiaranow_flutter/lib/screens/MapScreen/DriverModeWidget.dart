import 'package:fiaranow_flutter/config/tenant_config.dart';
import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:fiaranow_flutter/models/ServiceStatusUpdate.dart';
import 'package:fiaranow_flutter/screens/DriverModePermissionDialog.dart';
import 'package:fiaranow_flutter/screens/ServiceStatusUpdateScreen.dart';
import 'package:fiaranow_flutter/states/AppState.dart';
import 'package:fiaranow_flutter/states/AuthState.dart';
import 'package:fiaranow_flutter/states/NavigationState.dart';
import 'package:fiaranow_flutter/states/PermissionsState.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

class DriverModeWidget extends StatelessWidget {
  DriverModeWidget({super.key});

  final navigationState = Get.find<NavigationState>();
  final permissionsState = Get.find<PermissionsState>();
  final appState = Get.find<AppState>();
  final authState = Get.find<AuthState>();
  final Logger _logger = Logger('DriverModeWidget');

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        if (!permissionsState.hasLocationPermission(background: false)) {
          return const SizedBox.shrink();
        }

        return Stack(
          children: [
            Positioned(
              top: 10,
              left: 10,
              child: Obx(() {
                final isFollowing = navigationState.isFollowingPosition.value;
                return FloatingActionButton(
                  mini: true,
                  onPressed: () => navigationState.setFollowingPosition(!isFollowing),
                  backgroundColor: isFollowing ? const Color(0xFF275095) : const Color(0xFFAECCDB),
                  child: Icon(isFollowing ? Icons.gps_fixed : Icons.gps_not_fixed, color: Colors.white),
                );
              }),
            ),
            if (navigationState.liveTrip == null &&
                navigationState.driverTripRequests.isEmpty &&
                navigationState.viewingTrip.value == null)
              Positioned(
                bottom: 108,
                left: 0,
                right: 0,
                child: Center(
                  child: Obx(() {
                    bool isOnline = appState.isOnline.value;
                    bool isServiceActive = authState.currentMobileUser.value!.isServiceActiveForTenant(TenantConfig.TENANT_ID);
                    bool isDriverConfirmed = authState.currentMobileUser.value!.isDriverConfirmed != null;

                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: ElevatedButton(
                        onPressed: isOnline && isDriverConfirmed
                            ? () async {
                                FirebaseAnalytics.instance.logEvent(
                                  name: 'driver_service_status_changed',
                                  parameters: {
                                    'new_status': isServiceActive.toString(),
                                    'user_type': 'driver',
                                    'widget_name': 'driver_mode',
                                    'is_driver_confirmed': isDriverConfirmed.toString(),
                                    'is_online': isOnline.toString()
                                  },
                                );

                                try {
                                  if (isServiceActive) {
                                    // Stop service
                                    final update = await Get.to<ServiceStatusUpdate>(
                                        () => const ServiceStatusUpdateScreen(newStatus: false));
                                    if (update != null) {
                                      await authState.setServiceActive(update);
                                      _logger.info('✅ Service stopped successfully');
                                    }
                                  } else {
                                    // Check for background location permission before starting service
                                    final hasNotification = permissionsState.hasNotificationPermission();
                                    final locationPermission = await Geolocator.checkPermission();
                                    final hasBackgroundLocation = locationPermission == LocationPermission.always;

                                    if (!hasNotification || !hasBackgroundLocation) {
                                      _logger.info('Missing permissions for service activation - Notification: $hasNotification, Background Location: $hasBackgroundLocation');
                                      
                                      FirebaseAnalytics.instance.logEvent(
                                        name: 'driver_service_activation_blocked',
                                        parameters: {
                                          'has_notification': hasNotification.toString(),
                                          'has_background_location': hasBackgroundLocation.toString(),
                                          'user_id': authState.currentMobileUser.value?.uid ?? 'unknown',
                                        },
                                      );

                                      // Show permission dialog
                                      if (!context.mounted) return;
                                      final granted = await showDialog<bool>(
                                        context: context,
                                        barrierDismissible: false,
                                        builder: (BuildContext context) => const DriverModePermissionDialog(),
                                      );

                                      if (granted != true) {
                                        _logger.info('User cancelled permission request for service activation');
                                        return;
                                      }
                                    }

                                    // Start service
                                    final update =
                                        await Get.to<ServiceStatusUpdate>(() => const ServiceStatusUpdateScreen(newStatus: true));
                                    if (update != null) {
                                      await authState.setServiceActive(update);
                                      navigationState.moveCameraToCurrentPosition();
                                      _logger.info('✅ Service started successfully');
                                    }
                                  }
                                } catch (error, stackTrace) {
                                  _logger.severe('❌ Error changing service status: $error', error, stackTrace);

                                  // Report error to Crashlytics
                                  FirebaseCrashlytics.instance.recordError(
                                    error,
                                    stackTrace,
                                    reason: 'service_status_change_ui_error',
                                    fatal: false,
                                  );
                                  FirebaseAnalytics.instance.logEvent(name: 'service_status_change_ui_error', parameters: {
                                    'isServiceActive': isServiceActive.toString(),
                                    'error': error.toString(),
                                    'error_type': error.runtimeType.toString(),
                                    'widget': 'driver_mode_widget',
                                  });

                                  // Show actual error in GetX snackbar - never hide real errors
                                  if (context.mounted) {
                                    final action = isServiceActive ? 'stop' : 'start';
                                    Get.snackbar(
                                      AppLocalizations.of(context)!.mapScreen_error, // "Error"
                                      'Failed to $action service: ${error.toString()}',
                                      backgroundColor: Colors.red,
                                      colorText: Colors.white,
                                      snackPosition: SnackPosition.BOTTOM,
                                      duration: const Duration(seconds: 8), // Longer duration for error details
                                    );
                                  }
                                }
                              }
                            : null,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 20),
                          backgroundColor: isDriverConfirmed ? (isServiceActive ? Colors.red : Colors.green) : Colors.red,
                          side:
                              BorderSide(color: isOnline ? (isServiceActive ? Colors.red : Colors.green) : Colors.red, width: 2),
                        ),
                        child: Text(
                          isOnline
                              ? (isDriverConfirmed
                                  ? (isServiceActive
                                      ? AppLocalizations.of(context)!.mapScreen_stopServiceButton // "Stop Service"
                                      : AppLocalizations.of(context)!.mapScreen_startServiceButton) // "Start Service"
                                  : AppLocalizations.of(context)!
                                      .mapScreen_driverProfileNeedsConfirmation) // "Driver profile needs confirmation"
                              : AppLocalizations.of(context)!.mapScreen_checkInternetButton, // "Check your Internet"
                          style: TextStyle(
                            fontSize: 24,
                            color: isOnline ? (isDriverConfirmed ? Colors.white : Colors.red) : Colors.red,
                          ),
                        ),
                      ),
                    );
                  }),
                ),
              ),
          ],
        );
      },
    );
  }
}
