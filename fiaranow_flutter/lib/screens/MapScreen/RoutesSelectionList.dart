import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:fiaranow_flutter/states/AppState.dart';
import 'package:fiaranow_flutter/states/NavigationState.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class RoutesSelectionList extends StatelessWidget {
  final GoogleMapController? mapController;
  final VoidCallback onRouteSelected;

  const RoutesSelectionList({
    super.key,
    required this.mapController,
    required this.onRouteSelected,
  });

  @override
  Widget build(BuildContext context) {
    final navigationState = Get.find<NavigationState>();
    final appState = Get.find<AppState>();

    return Obx(() {
      if (navigationState.routeOverviews.isEmpty) {
        return const SizedBox.shrink();
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  // Move the camera back to the destination position
                  mapController?.animateCamera(
                    CameraUpdate.newCameraPosition(
                      CameraPosition(
                        target: navigationState.destinationPosition.value!,
                        zoom: navigationState.zoomLevel.value,
                      ),
                    ),
                  );

                  // Delete the trip from the database (if it exists)
                  navigationState.currentRiderTrip.value?.deleteTrip();

                  // Don't stop driver trip requests subscription when only canceling a route
                  navigationState.reset(stopCurrentTripSub: true, stopNearbyDriversSub: true);
                },
              ),
              Text(
                appState.userTendency.value == UserTendency.reserve
                    ? AppLocalizations.of(context)!
                        .mapScreen_reserveRoute(navigationState.routeOverviews.length) // "Reserve a Route ({count})"
                    : AppLocalizations.of(context)!
                        .mapScreen_selectRoute(navigationState.routeOverviews.length), // "Select a Route ({count})"
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          Expanded(
            child: ListView.builder(
              itemCount: navigationState.routeOverviews.length + 1, // Only +1 for info header
              itemBuilder: (context, index) {
                if (index == 0) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                    child: Row(
                      children: [
                        const Icon(Icons.info, color: Colors.orange),
                        const SizedBox(width: 8.0),
                        Expanded(
                          child: Text(
                            AppLocalizations.of(context)!
                                .mapScreen_routeWarningMessage, // "During the Trip, the driver could choose a different route depending on the road conditions, traffic, or upon Your request. The Trip cost will update accordingly."
                            style: const TextStyle(color: Colors.orange, fontSize: 10.0),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                final overview = navigationState.routeOverviews[index - 1];
                final duration = Duration(seconds: overview.durationSec);
                final durationText = duration.inMinutes >= 60
                    ? '${duration.inHours}h ${duration.inMinutes % 60} minutes'
                    : '${duration.inMinutes + 1} minutes';

                final isSelected = navigationState.selectedRouteIndex.value == index - 1;
                
                return Card(
                  margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                  elevation: isSelected ? 0 : 1,
                  shadowColor: Colors.transparent,
                  color: isSelected 
                      ? Theme.of(context).brightness == Brightness.dark
                          ? Theme.of(context).primaryColor.withValues(alpha: 0.35)
                          : Theme.of(context).primaryColor.withValues(alpha: 0.15)
                      : Theme.of(context).cardColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: isSelected 
                        ? BorderSide(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white
                                : Theme.of(context).primaryColor, 
                            width: 2
                          )
                        : BorderSide.none,
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.only(left: 16, top: 8, bottom: 8, right: 4),
                    title: Text(
                      AppLocalizations.of(context)!.mapScreen_route(index), // "Route {number}"
                      style: TextStyle(
                        color: isSelected 
                            ? Theme.of(context).brightness == Brightness.dark
                                ? Colors.white
                                : Theme.of(context).primaryColor
                            : null,
                        fontWeight: isSelected ? FontWeight.bold : null,
                      ),
                    ),
                    subtitle: Text(
                      "Duration: $durationText, Distance: ${overview.distanceKm.toStringAsFixed(2)} km",
                      style: TextStyle(
                        color: isSelected && Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey[300]
                            : null,
                      ),
                    ),
                    onTap: () {
                    FirebaseAnalytics.instance.logEvent(
                      name: 'route_previewed',
                      parameters: {
                        'route_index': (index - 1).toString(),
                        'distance_km': overview.distanceKm.toStringAsFixed(2),
                        'duration_min': (overview.durationSec / 60).toStringAsFixed(0),
                        'widget_name': 'routes_selection_list',
                      },
                    );
                    navigationState.selectedRouteIndex.value = index - 1;
                    mapController?.animateCamera(
                      CameraUpdate.newLatLngBounds(overview.bounds, 50),
                    );
                  },
                  trailing: IconButton(
                    icon: const Icon(Icons.arrow_forward),
                    onPressed: () async {
                      FirebaseAnalytics.instance.logEvent(
                        name: 'route_selected',
                        parameters: {
                          'index': index - 1,
                          'distance_km': overview.distanceKm.toStringAsFixed(2),
                          'duration_min': (overview.durationSec / 60).toStringAsFixed(0),
                          'widget_name': 'routes_selection_list'
                        },
                      );
                      navigationState.selectedRouteIndex.value = index - 1;
                      mapController?.animateCamera(
                        CameraUpdate.newLatLngBounds(overview.bounds, 50),
                      );
                      onRouteSelected();
                    },
                  ),
                  ),
                );
              },
            ),
          ),
        ],
      );
    });
  }
}
