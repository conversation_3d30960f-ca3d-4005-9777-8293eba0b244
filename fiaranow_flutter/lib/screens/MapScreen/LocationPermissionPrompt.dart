import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:fiaranow_flutter/models/MobileUser.dart';
import 'package:fiaranow_flutter/states/NavigationState.dart';
import 'package:fiaranow_flutter/states/PermissionsState.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';

class LocationPermissionPrompt extends StatelessWidget {
  final VoidCallback onAfterPermissionCheck;

  const LocationPermissionPrompt({
    super.key,
    required this.onAfterPermissionCheck,
  });

  @override
  Widget build(BuildContext context) {
    final navigationState = Get.find<NavigationState>();
    final permissionsState = Get.find<PermissionsState>();
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      color: Colors.black.withValues(alpha: 0.7),
      child: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16.0),
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: isDark ? theme.cardColor : Colors.white,
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.location_off, size: 48, color: Colors.red),
              const SizedBox(height: 16.0),
              Text(
                AppLocalizations.of(context)!.mapScreen_locationPermissionTitle, // "Location Permission Required"
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8.0),
              Obx(() {
                if (navigationState.drivingMode.value == UserType.driver) {
                  return Text(
                    AppLocalizations.of(context)!
                        .mapScreen_locationPermissionMessageDriver, // "Please allow \"Always\" location permission for the \"Fiaranow\" app to work properly in driver mode."
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: isDark ? Colors.white70 : Colors.black87,
                    ),
                  );
                } else {
                  return Text(
                    AppLocalizations.of(context)!
                        .mapScreen_locationPermissionMessageRider, // "Please allow location permission for the \"Fiaranow\" app to work properly."
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: isDark ? Colors.white70 : Colors.black87,
                    ),
                  );
                }
              }),
              const SizedBox(height: 16.0),
              ElevatedButton(
                onPressed: () async {
                  FirebaseAnalytics.instance.logEvent(
                    name: 'location_permission_requested',
                    parameters: {
                      'user_type': navigationState.drivingMode.value.name,
                      'widget_name': 'location_permission_prompt',
                      'is_background': (navigationState.drivingMode.value == UserType.driver).toString(),
                      'current_permission': permissionsState.locationPermission.value.toString()
                    },
                  );
                  // Request background location for drivers, regular location for riders
                  final isDriver = navigationState.drivingMode.value == UserType.driver;
                  final hasPermission = await permissionsState.requestLocationPermission(background: isDriver);

                  // If location service is not enabled, show dialog to open settings
                  if (!permissionsState.locationServiceEnabled.value || (isDriver && !hasPermission)) {
                    await permissionsState.openLocationSettings();
                    FirebaseAnalytics.instance.logEvent(
                      name: 'location_settings_opened',
                      parameters: {
                        'user_type': navigationState.drivingMode.value.name,
                        'widget_name': 'location_permission_prompt',
                        'reason': 'service_disabled'
                      },
                    );
                    return;
                  }

                  // If permission was granted
                  if (hasPermission) {
                    onAfterPermissionCheck();
                  }
                  // If permission denied forever, show dialog to open settings
                  else if (permissionsState.locationPermission.value == LocationPermission.deniedForever) {
                    await permissionsState.openSettings();
                  }
                },
                child: Text(AppLocalizations.of(context)!.mapScreen_enableLocationButton), // "Enable Location"
              ),
            ],
          ),
        ),
      ),
    );
  }
}
