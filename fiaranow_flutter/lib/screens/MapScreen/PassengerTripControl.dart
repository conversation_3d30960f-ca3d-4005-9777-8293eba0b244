import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:fiaranow_flutter/models/Trip.dart';
import 'package:fiaranow_flutter/models/TripStatus.dart';
import 'package:fiaranow_flutter/screens/TripFeedbackScreen.dart';
import 'package:fiaranow_flutter/services/TripStateService.dart';
import 'package:fiaranow_flutter/states/AppState.dart';
import 'package:fiaranow_flutter/states/NavigationState.dart';
import 'package:fiaranow_flutter/widgets/IconPulsating.dart';
import 'package:fiaranow_flutter/widgets/PriceDisplay.dart';
import 'package:fiaranow_flutter/widgets/TimeSinceCounter.dart';
import 'package:fiaranow_flutter/widgets/TripActionButton.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class PassengerTripControl extends StatefulWidget {
  final GoogleMapController? mapController;

  const PassengerTripControl({
    super.key,
    required this.mapController,
  });

  @override
  State<PassengerTripControl> createState() => _PassengerTripControlState();
}

class _PassengerTripControlState extends State<PassengerTripControl> {
  // Loading state for dismiss button (not using TripActionButton for IconButton)
  bool _isDismissLoading = false;

  Widget _buildLiveCostDisplay(BuildContext context, Trip trip) {
    final bool isFullDayTrip = trip.reservationType == ReservationType.fullDay;
    // Use live configuration for UI preferences like hideInProgressCosts
    final appState = Get.find<AppState>();
    final hideInProgressCostsValue = appState.tripConfiguration.value.hideInProgressCosts;
    final bool shouldHideCost = (hideInProgressCostsValue ?? false) && trip.status == TripStatus.inProgress;

    // Hide cost display if configuration says so and trip is in progress
    if (shouldHideCost) {
      return const SizedBox.shrink(); // Return empty widget
    }

    // For regular trips (non-full-day trips), only show cost if costTotal is available
    // costTotal gets updated by admin overrides and traffic jam protection
    // Passengers will see real-time cost updates through this field
    if (!isFullDayTrip && trip.costTotal == null) {
      // Don't show cost display if costTotal is not yet available
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              const SizedBox(width: 8),
              Text(AppLocalizations.of(context)!.passengerTripControl_calculatingCost), // "Calculating cost..."
            ],
          ),
        ],
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.monetization_on),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                trip.status == TripStatus.completed || trip.status == TripStatus.paid
                    ? AppLocalizations.of(context)!.mapScreen_finalTripCost // "Final Trip Cost:"
                    : AppLocalizations.of(context)!.mapScreen_currentTripCost, // "Current Trip Cost:"
                overflow: TextOverflow.visible,
              ),
            ),
            const SizedBox(width: 8),
            if (isFullDayTrip)
              PriceDisplay.bold(
                amount: trip.fullDayPriceType == FullDayPriceType.fixed ? 75 : 25,
                currency: 'EUR',
              )
            else
              PriceDisplay.bold(
                amount: trip.costTotal,
                currency: trip.realCostCurrency ?? 'MGA',
              ),
          ],
        ),
        // For €25 option, show additional gas cost if available
        if (isFullDayTrip &&
            trip.fullDayPriceType == FullDayPriceType.gasExcluded &&
            trip.costDistance != null &&
            trip.costDistance! > 0)
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.local_gas_station),
              const SizedBox(width: 8),
              Text(
                '${AppLocalizations.of(context)!.tripDetails_cost} ',
                style: const TextStyle(fontStyle: FontStyle.italic),
              ),
              PriceDisplay(
                amount: trip.costDistance,
                currency: trip.realCostCurrency ?? 'MGA',
                style: const TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ),
        if (trip.distanceTotalMeters != null && trip.distanceTotalMeters! > 0)
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.route),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context)!.mapScreen_roadDistance(
                    (trip.distanceTotalMeters! / 1000).toStringAsFixed(2)), // "Road Distance: {distance} km"
              ),
            ],
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final navigationState = Get.find<NavigationState>();

    return Obx(() {
      // Check if we're viewing a non-active trip
      if (navigationState.viewingTrip.value != null && navigationState.liveTrip == null) {
        final viewingTrip = navigationState.viewingTrip.value!;
        return _buildViewingTripControl(context, viewingTrip);
      }
      
      final trip = navigationState.liveTrip;

      if (trip == null) {
        return Center(child: Text(AppLocalizations.of(context)!.mapScreen_noActiveTrip)); // "No active trip."
      }

      final bool isFullDayTrip = trip.reservationType == ReservationType.fullDay;
      return _buildMainControl(context, trip, isFullDayTrip);
    });
  }

  Widget _buildMainControl(BuildContext context, Trip trip, bool isFullDayTrip) {
    return Column(
      children: [
        // Header
        Row(
          children: [
            const SizedBox(width: 14),
            if (trip.status == TripStatus.inProgress && trip.driverStartTime != null && trip.passengerStartTime != null)
              const IconPulsating(
                icon: Icon(Icons.play_arrow, size: 24),
                color1: Colors.black,
                color2: Colors.green,
              )
            else if (trip.status == TripStatus.completed || trip.status == TripStatus.paid)
              const Icon(Icons.emoji_flags, size: 18, color: Colors.green)
            else
              const Icon(Icons.access_time, size: 18),
            const SizedBox(width: 8),
            Text(
              isFullDayTrip
                  ? AppLocalizations.of(context)!.mapScreen_fullDayReservation // "Full Day Reservation"
                  : AppLocalizations.of(context)!.mapScreen_tripControl, // "Trip Control"
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Spacer(),
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.center_focus_strong),
                  onPressed: () {
                    if (trip.routeData?.bounds != null) {
                      widget.mapController?.animateCamera(
                        CameraUpdate.newLatLngBounds(trip.routeData!.bounds, 50),
                      );
                    }
                  },
                ),
                const SizedBox(width: 8),
                Container(
                  decoration: BoxDecoration(
                    color: Get.find<NavigationState>().isFollowingDriverPosition.value
                        ? const Color(0xFF275095)
                        : const Color(0xFFAECCDB),
                  ),
                  child: IconButton(
                    icon: Icon(
                      Get.find<NavigationState>().isFollowingDriverPosition.value ? Icons.gps_fixed : Icons.gps_not_fixed,
                      color: Get.find<NavigationState>().isFollowingDriverPosition.value ? Colors.white : null,
                    ),
                    onPressed: (trip.status == TripStatus.completed || 
                               trip.status == TripStatus.paid || 
                               trip.status == TripStatus.cancelled ||
                               trip.uidChosenDriver == null)
                        ? null
                        : () {
                            if (Get.find<NavigationState>().isFollowingDriverPosition.value) {
                              Get.find<NavigationState>().stopFollowingDriverLocation();
                            } else {
                              Get.find<NavigationState>().startFollowingDriverLocation();
                            }
                          },
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: _isDismissLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.close),
                  onPressed: (trip.status == TripStatus.paid || trip.status == TripStatus.cancelled) && !_isDismissLoading
                      ? () async {
                          setState(() => _isDismissLoading = true);
                          try {
                            await TripStateService.instance.dismissTrip(
                              tripId: trip.id,
                              userType: 'passenger',
                            );
                            Get.find<NavigationState>().stopFollowingDriverLocation();
                            Get.find<NavigationState>().reset(stopCurrentTripSub: true, stopNearbyDriversSub: true);
                          } finally {
                            if (mounted) setState(() => _isDismissLoading = false);
                          }
                        }
                      : null,
                ),
              ],
            ),
          ],
        ),
        const Divider(height: 1),

        // Contents
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16, top: 0),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (trip.status == TripStatus.cancelled) ...[
                    Text(
                      AppLocalizations.of(context)!.mapScreen_sorryTripCancelled, // "Sorry, this trip has been cancelled."
                      style: const TextStyle(fontSize: 16, color: Colors.red),
                      textAlign: TextAlign.center,
                    ),
                  ] else if (trip.status == TripStatus.reserved) ...[
                    if (trip.uidChosenDriver != null) ...[
                      Text(
                        AppLocalizations.of(context)!.mapScreen_driverAssigned, // "A driver has been assigned to you."
                        style: const TextStyle(fontSize: 16),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                    ] else ...[
                      Text(
                        AppLocalizations.of(context)!
                            .mapScreen_driverWillBeAssigned, // "A driver will be assigned to you. Please stand-by."
                        style: const TextStyle(fontSize: 16),
                        textAlign: TextAlign.center,
                      ),
                    ]
                  ] else if (trip.status == TripStatus.driverApproaching) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(AppLocalizations.of(context)!.mapScreen_driverApproaching), // "Driver is approaching you..."
                      ],
                    ),
                  ] else if (trip.status == TripStatus.driverAwaiting ||
                      (trip.status == TripStatus.inProgress &&
                          (trip.passengerStartTime == null || trip.driverStartTime == null))) ...[
                    // Only show waiting timer if we're still in driverAwaiting status
                    if (trip.status == TripStatus.driverAwaiting && trip.driverAwaitingTime != null) ...[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(AppLocalizations.of(context)!.mapScreen_waitingTime), // "Waiting time:"
                          const SizedBox(width: 8),
                          TimeSinceCounter(
                            startTime: trip.driverAwaitingTime!,
                            textStyle: const TextStyle(fontSize: 20),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Show message based on who has started
                    if (trip.status == TripStatus.inProgress &&
                        trip.passengerStartTime != null &&
                        trip.driverStartTime == null) ...[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Flexible(
                            child: Text(
                              AppLocalizations.of(context)!
                                  .mapScreen_pleaseWaitForDriver, // "Please wait for the driver to start the trip as well."
                              textAlign: TextAlign.center,
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                    ],

                    _buildLiveCostDisplay(context, trip),
                    const SizedBox(height: 16),

                    // Only show start button if passenger hasn't started yet
                    if (trip.passengerStartTime == null)
                      StartTripButton(
                        tripId: trip.id,
                        userType: 'passenger',
                        onSuccess: () {
                          FirebaseAnalytics.instance.logEvent(
                            name: 'passenger_started_trip',
                            parameters: {
                              'trip_id': trip.id,
                              'wait_time': trip.driverAwaitingTime?.toString() ?? 'unknown',
                              'widget_name': 'passenger_trip_control',
                              'driver_ready': trip.driverStartTime != null ? 'true' : 'false',
                              'is_full_day': isFullDayTrip ? 'true' : 'false',
                            },
                          );
                          Get.find<NavigationState>().startFollowingDriverLocation();
                        },
                      )
                    else
                      // Show disabled state with checkmark when passenger has started
                      Container(
                        height: 48,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade300,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 20),
                              child: Row(
                                children: [
                                  Icon(Icons.check_circle, size: 20, color: Colors.green.shade700),
                                  const SizedBox(width: 8),
                                  Text(
                                    AppLocalizations.of(context)!.tripActionButton_start, // "Démarrer"
                                    style: TextStyle(
                                      fontSize: 13,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.grey.shade700,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                  ] else if (trip.status == TripStatus.inProgress) ...[
                    _buildLiveCostDisplay(context, trip),
                    const SizedBox(height: 16),
                    if (isFullDayTrip)
                      Text(AppLocalizations.of(context)!.mapScreen_enjoyFullDayRide) // "Enjoy your full day ride!"
                    else
                      Text(AppLocalizations.of(context)!.mapScreen_enjoyTheRide), // "Enjoy the ride!"
                  ] else if (trip.status == TripStatus.completed) ...[
                    // 🚫 DEPRECATED: Hide final cost display from passengers per new requirements
                    // _buildLiveCostDisplay(context, trip),
                    // const SizedBox(height: 16),
                    Text(AppLocalizations.of(context)!.mapScreen_thankYouForRiding), // "Thank you for riding with Fiaranow!"
                    const SizedBox(height: 16),
                    // Add feedback button for completed trips
                    TripActionButton(
                      label: AppLocalizations.of(context)!.trip_feedback_button, // "Give Feedback"
                      variant: ButtonVariant.warning,
                      icon: Icons.star,
                      onPressed: () {
                        FirebaseAnalytics.instance.logEvent(
                          name: 'trip_feedback_opened',
                          parameters: {
                            'widget_name': 'passenger_trip_control',
                            'trip_id': trip.id,
                            'trip_status': 'completed',
                          },
                        );
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => TripFeedbackScreen(trip: trip),
                          ),
                        );
                      },
                    ),
                  ] else if (trip.status == TripStatus.paid) ...[
                    const Icon(
                      Icons.check_circle,
                      size: 48,
                      color: Colors.green,
                    ),
                    const SizedBox(height: 8),
                    Text(AppLocalizations.of(context)!.mapScreen_tripPaid), // "Trip is paid."
                    // 🚫 DEPRECATED: Hide final cost display from passengers per new requirements
                    // const SizedBox(height: 16),
                    // _buildLiveCostDisplay(context, trip),
                    const SizedBox(height: 16),
                    // Add feedback button for paid trips
                    TripActionButton(
                      label: AppLocalizations.of(context)!.trip_feedback_button, // "Give Feedback"
                      variant: ButtonVariant.warning,
                      icon: Icons.star,
                      onPressed: () {
                        FirebaseAnalytics.instance.logEvent(
                          name: 'trip_feedback_opened',
                          parameters: {
                            'widget_name': 'passenger_trip_control',
                            'trip_id': trip.id,
                            'trip_status': 'paid',
                          },
                        );
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => TripFeedbackScreen(trip: trip),
                          ),
                        );
                      },
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  // Build control for viewing non-active trips
  Widget _buildViewingTripControl(BuildContext context, Trip trip) {
    final bool isFullDayTrip = trip.reservationType == ReservationType.fullDay;
    
    return Column(
      children: [
        // Header
        Row(
          children: [
            const SizedBox(width: 14),
            const Icon(Icons.visibility, size: 18),
            const SizedBox(width: 8),
            Text(
              AppLocalizations.of(context)!.mapScreen_tripHeader, // "Trip"
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
              const Spacer(),
              // Map centering button
              IconButton(
                icon: const Icon(Icons.center_focus_strong),
                onPressed: () {
                  if (trip.routeData?.bounds != null) {
                    widget.mapController?.animateCamera(
                      CameraUpdate.newLatLngBounds(trip.routeData!.bounds, 50),
                    );
                  }
                },
              ),
              // Close button
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  Get.find<NavigationState>().clearViewingTrip();
                },
              ),
            ],
          ),
        const Divider(height: 1),
        
        // Content - Show trip info in read-only mode
        Expanded(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status banner
                  Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            trip.status == TripStatus.completed || trip.status == TripStatus.paid
                                ? Icons.check_circle
                                : trip.status == TripStatus.cancelled
                                    ? Icons.cancel
                                    : trip.status == TripStatus.reserved
                                        ? Icons.schedule
                                        : Icons.visibility,
                            size: 20,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            trip.status.getLocalizedName(context),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  
                  // Trip details
                  if (trip.startLocationName != null) ...[
                    Row(
                      children: [
                        Icon(Icons.trip_origin, size: 20, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            trip.startLocationName!,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                  ],
                  
                  if (trip.arrivalLocationName != null) ...[
                    Row(
                      children: [
                        Icon(Icons.location_on, size: 20, color: Colors.green.shade700),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            trip.arrivalLocationName!,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                  ],
                  
                  // Cost display for completed/paid trips
                  if ((trip.status == TripStatus.completed || trip.status == TripStatus.paid) && trip.costTotal != null) ...[
                    const Divider(),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(Icons.monetization_on, size: 20),
                        const SizedBox(width: 8),
                        Text(AppLocalizations.of(context)!.mapScreen_finalTripCost),
                        const SizedBox(width: 8),
                        if (isFullDayTrip)
                          PriceDisplay.bold(
                            amount: trip.fullDayPriceType == FullDayPriceType.fixed ? 75 : 25,
                            currency: 'EUR',
                          )
                        else
                          PriceDisplay.bold(
                            amount: trip.costTotal,
                            currency: trip.realCostCurrency ?? 'MGA',
                          ),
                      ],
                    ),
                  ],
                  
                  // Reserved trip pickup time
                  if (trip.status == TripStatus.reserved && trip.pickupTime != null) ...[
                    const Divider(),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(Icons.schedule, size: 20),
                        const SizedBox(width: 8),
                        Text(AppLocalizations.of(context)!.mapScreen_pickupTime), // "Pickup Time"
                        const SizedBox(width: 8),
                        Text(
                          trip.pickupTime!.toLocal().toString().substring(0, 16),
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
