import 'dart:io' show Platform;

import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:fiaranow_flutter/models/Trip.dart';
import 'package:fiaranow_flutter/states/AppState.dart';
import 'package:fiaranow_flutter/states/AuthState.dart';
import 'package:fiaranow_flutter/states/NavigationState.dart';
import 'package:fiaranow_flutter/widgets/PassengerCountSlider.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class FullDayReservationSetup extends StatefulWidget {
  final void Function(dynamic index) onNavigateToTab;
  final GoogleMapController? mapController;

  const FullDayReservationSetup({
    super.key,
    required this.onNavigateToTab,
    required this.mapController,
  });

  @override
  State<FullDayReservationSetup> createState() => _FullDayReservationSetupState();
}

class _FullDayReservationSetupState extends State<FullDayReservationSetup> {
  final _reservationFormKey = GlobalKey<FormBuilderState>();
  final dateController = TextEditingController();
  final timeController = TextEditingController();
  DateTime selectedDate = DateTime.now();
  TimeOfDay selectedTime = TimeOfDay.now();
  FullDayPriceType selectedPriceType = FullDayPriceType.fixed;
  final passengerCount = 1.obs;

  final navigationState = Get.find<NavigationState>();
  final appState = Get.find<AppState>();
  final authState = Get.find<AuthState>();
  bool _initialized = false;

  @override
  void initState() {
    super.initState();
    // Initialize date and time with default values, but don't format them with context yet
    selectedDate = DateTime.now();
    selectedTime = TimeOfDay.now();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Only initialize once to avoid unnecessary updates
    if (!_initialized) {
      dateController.text = "${selectedDate.toLocal()}".split(' ')[0];
      timeController.text = selectedTime.format(context);
      _initialized = true;
    }
  }

  void _onConfirmFullDayReservation() async {
    FirebaseAnalytics.instance.logEvent(
      name: 'full_day_trip_reserved',
      parameters: {
        'date': selectedDate.toIso8601String(),
        'time': selectedTime.format(context),
        'price_type': selectedPriceType.name,
        'widget_name': 'full_day_reservation',
      },
    );

    if (_reservationFormKey.currentState?.saveAndValidate() ?? false) {
      final reservationDateTime = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        selectedTime.hour,
        selectedTime.minute,
      );

      if (reservationDateTime.isAfter(DateTime.now().add(const Duration(minutes: 15)))) {
        // Ensure we have a start position
        if (navigationState.startPosition.value == null) {
          Get.snackbar(
            AppLocalizations.of(context)!.mapScreen_error,
            AppLocalizations.of(context)!.mapScreen_pickupLocation, // "Please select a pickup location first"
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          return;
        }

        // Create the full day reservation
        final trip = await Trip.reserveFullDayTrip(
          uidPassenger: authState.currentMobileUser.value!.uid,
          startPosition: navigationState.startPosition.value!,
          user: authState.currentMobileUser.value!,
          reservationTime: reservationDateTime,
          priceType: selectedPriceType,
          startLocationName: navigationState.startAddress.value,
          tripConfiguration: appState.tripConfiguration.value,
          customerRequestedPaymentMethod: navigationState.currentRequestedPaymentMethod!,
          passengerCount: navigationState.passengerCount.value,
        );

        // Clear everything
        navigationState.reset();
        widget.mapController?.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: navigationState.currentPosition.value!,
              zoom: 16.0,
            ),
          ),
        );

        // Navigate to the History tab (index 1) and close this screen
        Get.back(); // Close the full day reservation screen
        widget.onNavigateToTab(1); // Navigate to history tab

        if (mounted) {
          Get.snackbar(
            AppLocalizations.of(context)!.mapScreen_success,
            AppLocalizations.of(context)!.mapScreen_fullDayReservationSuccess, // "Full day trip reserved successfully"
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
        }

        // Log analytics
        FirebaseAnalytics.instance.logEvent(
          name: 'full_day_trip_reserved_success',
          parameters: {
            'reservation_time': reservationDateTime.toIso8601String(),
            'price_type': selectedPriceType.name,
            'widget_name': 'full_day_reservation',
            'trip_id': trip.id,
          },
        );
      } else {
        Get.snackbar(
          AppLocalizations.of(context)!.mapScreen_error,
          AppLocalizations.of(context)!.mapScreen_selectFutureTime,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !Platform.isIOS,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop && Platform.isIOS) {
          Get.back();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(AppLocalizations.of(context)!.mapScreen_fullDayReservation), // "Full day reservation"
          leading: IconButton(
            icon: Icon(Platform.isIOS ? Icons.arrow_back_ios : Icons.arrow_back),
            onPressed: () => Get.back(),
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: FormBuilder(
              key: _reservationFormKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (navigationState.startPosition.value != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: Card(
                        elevation: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppLocalizations.of(context)!.mapScreen_pickupLocation, // "Pickup Location"
                                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                navigationState.startAddress.value ??
                                    AppLocalizations.of(context)!.mapScreen_pickupLocation, // "Selected location"
                                style: TextStyle(
                                  color: Theme.of(context).brightness == Brightness.dark ? Colors.white70 : Colors.black87,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  Card(
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context)!.mapScreen_reserveRide, // "Reservation Date & Time"
                            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: FormBuilderTextField(
                                  name: 'date',
                                  controller: dateController,
                                  decoration: InputDecoration(
                                    labelText: AppLocalizations.of(context)!.mapScreen_reservationDateLabel, // "Date"
                                    labelStyle: TextStyle(
                                      color: Theme.of(context).brightness == Brightness.dark ? Colors.white70 : Colors.black54,
                                    ),
                                    border: const OutlineInputBorder(),
                                    filled: true,
                                    fillColor: Theme.of(context).brightness == Brightness.dark ? Colors.black : Colors.white,
                                    prefixIcon: const Icon(Icons.calendar_today),
                                  ),
                                  style: TextStyle(
                                    color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                                  ),
                                  readOnly: true,
                                  onTap: () async {
                                    DateTime? pickedDate = await showDatePicker(
                                      context: context,
                                      initialDate: selectedDate,
                                      firstDate: DateTime.now(),
                                      lastDate: DateTime(2050),
                                    );
                                    if (pickedDate != null && pickedDate != selectedDate) {
                                      setState(() {
                                        selectedDate = pickedDate;
                                        dateController.text = "${selectedDate.toLocal()}".split(' ')[0];
                                      });
                                    }
                                  },
                                  validator: FormBuilderValidators.compose([
                                    FormBuilderValidators.required(),
                                  ]),
                                ),
                              ),
                              const SizedBox(width: 8.0),
                              Expanded(
                                child: FormBuilderTextField(
                                  name: 'time',
                                  controller: timeController,
                                  decoration: InputDecoration(
                                    labelText: AppLocalizations.of(context)!.mapScreen_reservationTimeLabel, // "Time"
                                    labelStyle: TextStyle(
                                      color: Theme.of(context).brightness == Brightness.dark ? Colors.white70 : Colors.black54,
                                    ),
                                    border: const OutlineInputBorder(),
                                    filled: true,
                                    fillColor: Theme.of(context).brightness == Brightness.dark ? Colors.black : Colors.white,
                                    prefixIcon: const Icon(Icons.access_time),
                                  ),
                                  style: TextStyle(
                                    color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                                  ),
                                  readOnly: true,
                                  onTap: () async {
                                    TimeOfDay? pickedTime = await showTimePicker(
                                      context: context,
                                      initialTime: selectedTime,
                                    );
                                    if (pickedTime != null && pickedTime != selectedTime) {
                                      setState(() {
                                        selectedTime = pickedTime;
                                        timeController.text = selectedTime.format(context);
                                      });
                                    }
                                  },
                                  validator: FormBuilderValidators.compose([
                                    FormBuilderValidators.required(),
                                  ]),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16.0),
                  Card(
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: PassengerCountSlider(
                        passengerCount: passengerCount,
                        maxPassengers: appState.tripConfiguration.value.maxPassengerCount,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16.0),
                  Card(
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context)!.mapScreen_fullDayPriceOptions, // "Pricing Option"
                            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                          ),
                          const SizedBox(height: 16),
                          FormBuilderRadioGroup<FullDayPriceType>(
                            name: 'price_type',
                            decoration: const InputDecoration(
                              border: InputBorder.none,
                            ),
                            initialValue: selectedPriceType,
                            options: [
                              FormBuilderFieldOption(
                                value: FullDayPriceType.fixed,
                                child: Row(
                                  children: [
                                    const Icon(Icons.check_circle_outline, color: Colors.green),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            AppLocalizations.of(context)!.mapScreen_fullDayFixedPrice, // "Fixed Price (€75)"
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color:
                                                  Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                                            ),
                                          ),
                                          Text(
                                            AppLocalizations.of(context)!.mapScreen_gasIncluded, // "Gas included in price"
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Theme.of(context).brightness == Brightness.dark
                                                  ? Colors.white70
                                                  : Colors.black54,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              FormBuilderFieldOption(
                                value: FullDayPriceType.gasExcluded,
                                child: Row(
                                  children: [
                                    const Icon(Icons.local_gas_station, color: Colors.orange),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            AppLocalizations.of(context)!.mapScreen_fullDayGasExcluded, // "Gas Excluded (€25)"
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color:
                                                  Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                                            ),
                                          ),
                                          Text(
                                            AppLocalizations.of(context)!
                                                .mapScreen_gasNotIncluded, // "You pay for gas separately"
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Theme.of(context).brightness == Brightness.dark
                                                  ? Colors.white70
                                                  : Colors.black54,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                            separator: const SizedBox(height: 8),
                            onChanged: (value) {
                              setState(() {
                                selectedPriceType = value as FullDayPriceType;
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24.0),
                  ElevatedButton(
                    onPressed: _onConfirmFullDayReservation,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    child: Text(AppLocalizations.of(context)!.mapScreen_confirmReservation), // "Confirm Full Day Reservation"
                  ),
                  const SizedBox(height: 16.0),
                  Card(
                    elevation: 1,
                    color: Theme.of(context).brightness == Brightness.dark ? Colors.grey[800] : Colors.grey[100],
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          Text(
                            AppLocalizations.of(context)!.mapScreen_fullDayReservation, // "About Full Day Reservations"
                            style: const TextStyle(fontWeight: FontWeight.bold),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            AppLocalizations.of(context)!
                                .mapScreen_fullDayReservationPrompt, // "A full day reservation allows you to have a driver at your disposal for the entire day..."
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
