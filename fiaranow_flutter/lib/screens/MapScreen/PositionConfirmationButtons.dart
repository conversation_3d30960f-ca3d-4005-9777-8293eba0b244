import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:fiaranow_flutter/states/NavigationState.dart';
import 'package:fiaranow_flutter/utils/payment_utils.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class PositionConfirmationButtons extends StatelessWidget {
  final double mapBottomPadding;
  final VoidCallback onConfirmBothPositions;
  final GoogleMapController? mapController;

  const PositionConfirmationButtons({
    super.key,
    required this.mapBottomPadding,
    required this.onConfirmBothPositions,
    required this.mapController,
  });

  @override
  Widget build(BuildContext context) {
    final navigationState = Get.find<NavigationState>();

    return Stack(
      children: [
        // Main content - crosshair and confirmation button
        Padding(
          padding: EdgeInsets.only(bottom: mapBottomPadding),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 80.0),
                Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      width: 2,
                      height: 20, // Half height since we only want bottom part
                      color: Colors.red,
                      margin: const EdgeInsets.only(top: 20), // Move down to align with center
                    ),
                    Container(
                      width: 40,
                      height: 2,
                      color: Colors.red,
                    ),
                  ],
                ),
                const SizedBox(height: 32.0),
                Obx(
                  () => ElevatedButton(
                    onPressed: () {
                      if (navigationState.currentMapPosition.value == null) return;

                      if (navigationState.isChoosingStartPosition.value) {
                        navigationState.setStartPosition(navigationState.currentMapPosition.value!);
                        navigationState.populateAddressNameFromPosition(navigationState.currentMapPosition.value!, true);
                        navigationState.isChoosingStartPosition.value = false;
                        navigationState.isChoosingDestinationPosition.value = false;
                        if (navigationState.destinationPosition.value == null) {
                          navigationState.isChoosingDestinationPosition.value = true;
                        }
                        navigationState.isStartAddressFocused.value = false;
                      } else if (navigationState.isChoosingDestinationPosition.value) {
                        navigationState.setDestinationPosition(navigationState.currentMapPosition.value!);
                        navigationState.populateAddressNameFromPosition(navigationState.currentMapPosition.value!, false);
                        navigationState.isChoosingDestinationPosition.value = false;
                        navigationState.isChoosingStartPosition.value = false;
                        if (navigationState.startPosition.value == null) {
                          navigationState.isChoosingStartPosition.value = true;
                        }
                        navigationState.isDestinationAddressFocused.value = false;
                      }

                      navigationState.setFollowingPosition(false);

                      if (!navigationState.isChoosingStartPosition.value &&
                          !navigationState.isChoosingDestinationPosition.value &&
                          navigationState.startPosition.value != null &&
                          navigationState.destinationPosition.value != null) {
                        // Zoom the map to fit both positions
                        onConfirmBothPositions();
                      }

                      FocusScope.of(context).unfocus();

                      if (navigationState.isChoosingStartPosition.value || navigationState.isChoosingDestinationPosition.value) {
                        // Move the map slightly to avoid overlapping start and destination positions
                        final zoomFactor = 0.0015 * (16.5 / navigationState.zoomLevel.value);
                        final newLng = navigationState.currentMapPosition.value!.longitude + zoomFactor;
                        final newPosition = LatLng(navigationState.currentMapPosition.value!.latitude, newLng);
                        mapController?.animateCamera(
                          CameraUpdate.newCameraPosition(
                            CameraPosition(
                              target: newPosition,
                              zoom: navigationState.zoomLevel.value,
                            ),
                          ),
                        );
                      }

                      final positionType = navigationState.isChoosingStartPosition.value ? 'start' : 'destination';
                      FirebaseAnalytics.instance.logEvent(
                        name: 'position_confirmed',
                        parameters: {
                          'type': positionType,
                          'source': 'map_pin',
                          'widget_name': 'position_confirmation',
                          'lat': navigationState.currentMapPosition.value?.latitude.toString() ?? '',
                          'lng': navigationState.currentMapPosition.value?.longitude.toString() ?? '',
                        },
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: navigationState.isChoosingStartPosition.value
                          ? Colors.blue.withAlpha(100)
                          : Colors.green.withAlpha(100),
                      foregroundColor: Colors.white,
                      textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: Text(
                      navigationState.isChoosingStartPosition.value
                          ? AppLocalizations.of(context)!.mapScreen_confirmPickupLocationButton // "Confirm Pick-up location?"
                          : AppLocalizations.of(context)!.mapScreen_confirmDestinationButton, // "Confirm destination?"
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // Full day reservation button positioned at the bottom
        Obx(() {
          // Only show the full day reservation button when the start position is chosen/confirmed
          if (navigationState.startPosition.value != null) {
            return Positioned(
              bottom: mapBottomPadding + 14, // Position above the bottom padding with some margin
              left: 16,
              right: 16,
              child: SafeArea(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 80),
                  child: ElevatedButton(
                    onPressed: () async {
                      FirebaseAnalytics.instance.logEvent(
                        name: 'full_day_reservation_button_tapped',
                        parameters: {
                          'widget_name': 'position_confirmation_buttons',
                        },
                      );

                      // Show payment method dialog first if not selected
                      if (navigationState.currentRequestedPaymentMethod == null) {
                        final paymentMethod =
                            await showPaymentMethodSelectionDialog(context, analyticsSource: 'position_confirmation_buttons');

                        if (paymentMethod != null && context.mounted) {
                          navigationState.currentRequestedPaymentMethod = paymentMethod;
                          _navigateToFullDayReservation(context);
                        }
                      } else {
                        _navigateToFullDayReservation(context);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.black,
                      textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      elevation: 4,
                    ),
                    child: Text(AppLocalizations.of(context)!.mapScreen_fullDayReservation), // "Full day reservation"
                  ),
                ),
              ),
            );
          }
          return const SizedBox.shrink();
        }),
      ],
    );
  }

  void _navigateToFullDayReservation(BuildContext context) {
    // Use Get.toNamed to navigate to the full day reservation page
    Get.toNamed('/full_day_reservation');
  }
}
