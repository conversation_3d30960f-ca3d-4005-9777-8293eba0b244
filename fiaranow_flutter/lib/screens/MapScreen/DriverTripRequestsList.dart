import 'package:cloud_functions/cloud_functions.dart';
import 'package:fiaranow_flutter/config/tenant_config.dart';
import 'package:fiaranow_flutter/fcm.dart';
import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:fiaranow_flutter/screens/TripRejectionScreen.dart';
import 'package:fiaranow_flutter/states/AuthState.dart';
import 'package:fiaranow_flutter/states/NavigationState.dart';
import 'package:fiaranow_flutter/widgets/ButtonGroup.dart';
import 'package:fiaranow_flutter/widgets/TripActionButton.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:logging/logging.dart';

class DriverTripRequestsList extends StatefulWidget {
  final String? selectedRequestTripId;
  final Function(String?) onTripSelected;
  final GoogleMapController? mapController;

  const DriverTripRequestsList({
    super.key,
    required this.selectedRequestTripId,
    required this.onTripSelected,
    required this.mapController,
  });

  @override
  State<DriverTripRequestsList> createState() => _DriverTripRequestsListState();
}

class _DriverTripRequestsListState extends State<DriverTripRequestsList> {
  final Logger _logger = Logger('DriverTripRequestsList');

  // Add loading states for each trip request
  final Map<String, bool> _acceptLoadingStates = {};
  final Map<String, bool> _rejectLoadingStates = {};

  @override
  Widget build(BuildContext context) {
    final navigationState = Get.find<NavigationState>();
    final authState = Get.find<AuthState>();

    return Container(
      color: Theme.of(context).cardColor,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Trip requests list
          Expanded(
            child: Obx(() {
              final tripRequests = navigationState.driverTripRequests;

              if (tripRequests.isEmpty) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.no_transfer, size: 48, color: Colors.grey),
                        const SizedBox(height: 16),
                        Text(
                          AppLocalizations.of(context)!.mapScreen_noTripRequests, // "No trip requests at this time."
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 16),
                        ),
                      ],
                    ),
                  ),
                );
              }

              return ListView.builder(
                itemCount: tripRequests.length,
                itemBuilder: (context, index) {
                  final trip = tripRequests[index];
                  final isSelected = widget.selectedRequestTripId == trip.id;

                  return Card(
                    margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    elevation: isSelected ? 4 : 1,
                    child: InkWell(
                      onTap: () {
                        widget.onTripSelected(isSelected ? null : trip.id);
                        if (!isSelected && trip.routeData?.bounds != null) {
                          widget.mapController?.animateCamera(
                            CameraUpdate.newLatLngBounds(trip.routeData!.bounds, 50),
                          );
                        }
                        navigationState.setFollowingPosition(false);
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Passenger info row
                            Row(
                              children: [
                                CircleAvatar(
                                  backgroundImage:
                                      trip.passenger['photoURL'] != null ? NetworkImage(trip.passenger['photoURL']) : null,
                                  child:
                                      trip.passenger['photoURL'] == null ? Text(trip.passenger['displayName']?[0] ?? 'P') : null,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    trip.passenger['displayName'] ?? 'Passenger',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),

                            // Location details
                            Row(
                              children: [
                                const Icon(Icons.location_on, color: Colors.blue, size: 20),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    trip.startLocationName ??
                                        AppLocalizations.of(context)!.mapScreen_pickupLocation, // "Pick-up location"
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                const Icon(Icons.location_on, color: Colors.green, size: 20),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    trip.arrivalLocationName ??
                                        AppLocalizations.of(context)!.mapScreen_destinationLocation, // "Destination location"
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                ),
                              ],
                            ),

                            // Trip details
                            if (trip.routeData != null || trip.driverRouteData != null) ...[
                              const SizedBox(height: 8),
                              const Divider(),
                              const SizedBox(height: 8),
                              Column(
                                children: [
                                  if (trip.routeData != null)
                                    Row(
                                      children: [
                                        const Icon(Icons.route, size: 16, color: Colors.grey),
                                        const SizedBox(width: 4),
                                        Text(
                                          AppLocalizations.of(context)!.mapScreen_tripDistanceLabel(
                                            trip.routeData!.distanceKm.toStringAsFixed(2),
                                            Duration(seconds: trip.routeData!.durationSec + 60).inMinutes.toString(),
                                          ),
                                          style: const TextStyle(fontSize: 12),
                                        ),
                                      ],
                                    ),
                                  if (trip.driverRouteData != null && trip.driverRouteData!.distanceKm > 0)
                                    Row(
                                      children: [
                                        const Icon(Icons.directions_car, size: 16, color: Colors.grey),
                                        const SizedBox(width: 4),
                                        Text(
                                          AppLocalizations.of(context)!.mapScreen_approachingDistance(
                                            trip.driverRouteData!.distanceKm.toStringAsFixed(1),
                                            Duration(seconds: trip.driverRouteData!.durationSec).inMinutes.toString(),
                                          ),
                                          style: const TextStyle(fontSize: 12),
                                        ),
                                      ],
                                    ),
                                ],
                              ),
                            ],

                            // Action buttons when selected
                            if (isSelected) ...[
                              const SizedBox(height: 16),
                              AcceptRejectButtons(
                                acceptButton: TripActionButton(
                                  label: AppLocalizations.of(context)!.mapScreen_accept, // "Accept"
                                  variant: ButtonVariant.success,
                                  icon: _acceptLoadingStates[trip.id] == true ? null : Icons.check,
                                  onPressed: _acceptLoadingStates[trip.id] == true
                                      ? null
                                      : () async {
                                          _logger.info('🔍 ACCEPT BUTTON PRESSED for trip: ${trip.id}');
                                          setState(() => _acceptLoadingStates[trip.id] = true);

                                          try {
                                            // Log navigation state details
                                            _logger.fine('🔍 NavigationState exists: $navigationState');
                                            _logger.fine('🔍 CurrentPosition exists: ${navigationState.currentPosition}');
                                            _logger.fine('🔍 CurrentPosition.value: ${navigationState.currentPosition.value}');

                                            // Check if driver's location is available
                                            final currentPosition = navigationState.currentPosition.value;
                                            if (currentPosition == null) {
                                              _logger.severe('❌ CurrentPosition is NULL!');
                                              _logger.info('🔍 Checking location permissions and service status...');

                                              if (context.mounted) {
                                                Get.snackbar(
                                                  AppLocalizations.of(context)!.mapScreen_error,
                                                  AppLocalizations.of(context)!
                                                      .mapScreen_locationNotAvailable, // "Your location is not available. Please enable location services and try again."
                                                  backgroundColor: Colors.red,
                                                  colorText: Colors.white,
                                                  duration: const Duration(seconds: 5),
                                                );
                                              }
                                              return;
                                            }

                                            _logger.info(
                                                '✅ CurrentPosition available: lat=${currentPosition.latitude}, lng=${currentPosition.longitude}');

                                            FirebaseAnalytics.instance.logEvent(
                                              name: 'trip_request_accepted',
                                              parameters: {'trip_id': trip.id},
                                            );
                                            cancelNotification();

                                            _logger.fine('🔍 About to call trip.driverAcceptRequest...');
                                            _logger.fine('🔍 Trip object exists: $trip');
                                            _logger.fine('🔍 Trip ID: ${trip.id}');

                                            await trip.driverAcceptRequest(currentPosition);

                                            _logger.info('✅ Trip accepted successfully!');
                                          } catch (e, stackTrace) {
                                            _logger.severe('❌ ERROR in accept button handler:', e, stackTrace);
                                            _logger.severe('❌ Error type: ${e.runtimeType}');
                                            _logger.severe('❌ Error message: $e');
                                            _logger.severe('❌ Stack trace: $stackTrace');

                                            if (context.mounted) {
                                              String errorMessage = AppLocalizations.of(context)!.mapScreen_error;
                                              String errorDetails;

                                              // ALWAYS show the actual error in console
                                              _logger.severe('🚨 ACTUAL ERROR: ${e.toString()}');

                                              // For null check errors, ALWAYS show the real error
                                              if (e is TypeError ||
                                                  e.toString().contains('Null check operator used on a null value')) {
                                                errorDetails =
                                                    'Internal error: ${e.toString()}\nStack trace:\n${stackTrace.toString().split('\n').take(5).join('\n')}';

                                                // Show error with full details
                                                Get.snackbar(
                                                  errorMessage,
                                                  errorDetails,
                                                  backgroundColor: Colors.red,
                                                  colorText: Colors.white,
                                                  duration: const Duration(seconds: 30),
                                                  isDismissible: true,
                                                  snackStyle: SnackStyle.GROUNDED,
                                                );
                                                return;
                                              }

                                              // For Firebase Functions errors, provide user-friendly messages
                                              if (e is FirebaseFunctionsException) {
                                                _logger.severe('🚨 Firebase Functions Error: ${e.code} - ${e.message}');
                                                _logger.severe('🚨 Error details: ${e.details}');

                                                if (e.code == 'failed-precondition') {
                                                  if (e.message?.contains('already assigned') ?? false) {
                                                    errorDetails = AppLocalizations.of(context)!
                                                        .mapScreen_driverAlreadyAssigned; // "You are already assigned to another trip. Please complete or cancel your current trip first."

                                                    // Show snackbar with action to clear stale assignment
                                                    Get.snackbar(
                                                      errorMessage,
                                                      errorDetails,
                                                      backgroundColor: Colors.red,
                                                      colorText: Colors.white,
                                                      duration: const Duration(seconds: 30),
                                                      mainButton: TextButton(
                                                        onPressed: () async {
                                                          Get.back(); // Close snackbar

                                                          // Show loading dialog
                                                          Get.dialog(
                                                            const Center(child: CircularProgressIndicator()),
                                                            barrierDismissible: false,
                                                          );

                                                          try {
                                                            final callable = FirebaseFunctions.instanceFor(region: 'europe-west3')
                                                                .httpsCallable('clearDriverStaleAssignment');
                                                            final response = await callable.call({
                                                              'driverUid': authState.uid,
                                                              'tenantId': TenantConfig.TENANT_ID,
                                                            });

                                                            Get.back(); // Close loading dialog

                                                            final data = response.data as Map<String, dynamic>;
                                                            if (data['wasCleared'] == true) {
                                                              Get.snackbar(
                                                                'Success',
                                                                data['message'] ?? 'Stale assignment cleared',
                                                                backgroundColor: Colors.green,
                                                                colorText: Colors.white,
                                                              );
                                                            } else {
                                                              Get.snackbar(
                                                                'Info',
                                                                data['message'] ?? 'No stale assignment found',
                                                                backgroundColor: Colors.blue,
                                                                colorText: Colors.white,
                                                              );
                                                            }
                                                          } catch (clearError) {
                                                            Get.back(); // Close loading dialog if still open
                                                            _logger.severe('Error clearing stale assignment', clearError);
                                                          }
                                                        },
                                                        child: const Text(
                                                          'Clear Stale Assignment',
                                                          style: TextStyle(color: Colors.white),
                                                        ),
                                                      ),
                                                    );
                                                    return; // Exit early to show custom snackbar
                                                  } else if (e.code == 'failed-precondition') {
                                                    // Use the localized error message directly from Firebase Functions
                                                    errorDetails = e.message ?? 'Failed to accept trip';
                                                  } else {
                                                    errorDetails = e.message ?? 'Failed to accept trip';
                                                  }
                                                } else if (e.code == 'not-found') {
                                                  errorDetails = AppLocalizations.of(context)!
                                                      .mapScreen_tripNotFound; // "This trip is no longer available."
                                                } else {
                                                  errorDetails = e.message ?? 'Failed to accept trip';
                                                }
                                              } else {
                                                // For any other error type, show the actual error
                                                errorDetails = 'Unexpected error: ${e.toString()}';
                                              }

                                              Get.snackbar(
                                                errorMessage,
                                                errorDetails,
                                                backgroundColor: Colors.red,
                                                colorText: Colors.white,
                                                duration: const Duration(seconds: 15),
                                              );
                                            }
                                          } finally {
                                            _logger.fine('🔍 Finally block - resetting loading state');
                                            if (mounted) setState(() => _acceptLoadingStates[trip.id] = false);
                                          }
                                        },
                                ),
                                rejectButton: TripActionButton(
                                  label: AppLocalizations.of(context)!.mapScreen_reject, // "Reject"
                                  variant: ButtonVariant.danger,
                                  icon: _rejectLoadingStates[trip.id] == true ? null : Icons.close,
                                  onPressed: _rejectLoadingStates[trip.id] == true
                                      ? null
                                      : () async {
                                          setState(() => _rejectLoadingStates[trip.id] = true);
                                          try {
                                            final rejectionUpdate =
                                                await Get.to<TripRejectionUpdate>(() => const TripRejectionScreen());
                                            FirebaseAnalytics.instance.logEvent(
                                              name: 'trip_request_rejected',
                                              parameters: {
                                                'trip_id': trip.id,
                                                'reason_type': rejectionUpdate?.reasonType.name ?? 'unknown',
                                                'widget_name': 'driver_trip_requests_list',
                                              },
                                            );
                                            if (rejectionUpdate != null) {
                                              await trip.driverRejectRequest(
                                                driverUid: authState.uid,
                                                reasonType: rejectionUpdate.reasonType,
                                                customReason: rejectionUpdate.customReason,
                                              );
                                            }
                                          } finally {
                                            if (mounted) setState(() => _rejectLoadingStates[trip.id] = false);
                                          }
                                        },
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            }),
          ),
        ],
      ),
    );
  }
}
