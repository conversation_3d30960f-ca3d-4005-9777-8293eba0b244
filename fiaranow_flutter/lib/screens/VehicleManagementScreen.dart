import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../config/tenant_config.dart';
import '../l10n/app_localizations.dart';
import '../models/MobileUserTenantState.dart';
import '../models/Vehicle.dart';
import '../models/VehicleLinking.dart';
import 'AddVehicleScreen.dart';

class VehicleManagementScreen extends StatelessWidget {
  VehicleManagementScreen({super.key});

  final String? currentUserId = FirebaseAuth.instance.currentUser?.uid;

  Stream<List<Vehicle>> _getUserVehicles() {
    if (currentUserId == null) return Stream.value([]);

    return Vehicle.vehiclesColl
        .where('ownerUID', isEqualTo: currentUserId)
        .where('isActive', isEqualTo: true)
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => doc.data()).toList());
  }

  Stream<MobileUserTenantState?> _getTenantState() {
    if (currentUserId == null) return Stream.value(null);

    return MobileUserTenantState.currentUserTenantStatesColl
        .doc(TenantConfig.TENANT_ID)
        .snapshots()
        .map((doc) => doc.exists ? doc.data() : null);
  }

  Stream<Vehicle?> _getAssignedVehicle(String? vehicleLinkingId) {
    if (vehicleLinkingId == null) return Stream.value(null);

    return VehicleLinking.vehicleLinkingColl.doc(vehicleLinkingId).snapshots().asyncMap((linkingDoc) async {
      if (!linkingDoc.exists) return null;
      final linking = linkingDoc.data()!;

      final vehicleDoc = await Vehicle.vehiclesColl.doc(linking.vehicleId).get();
      return vehicleDoc.exists ? vehicleDoc.data() : null;
    });
  }

  Widget _buildVehicleCard(Vehicle vehicle, bool isAssigned, BuildContext context) {
    return Card(
      elevation: isAssigned ? 4 : 1,
      child: Container(
        decoration: BoxDecoration(
          border: isAssigned ? Border.all(color: Theme.of(context).primaryColor, width: 2) : null,
          borderRadius: BorderRadius.circular(12),
        ),
        child: ListTile(
          leading: CircleAvatar(
            backgroundColor: isAssigned 
                ? Theme.of(context).primaryColor 
                : Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade700
                    : Colors.grey.shade300,
            child: Icon(
              Icons.directions_car,
              color: isAssigned 
                  ? Colors.white 
                  : Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey.shade300
                      : Colors.grey.shade600,
            ),
          ),
          title: Text(
            vehicle.displayName,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('${vehicle.color} • ${vehicle.registrationNumber}'),
              Text(AppLocalizations.of(context)!.vehicle_capacity_passengers(vehicle.maxPassengers)),
              if (isAssigned)
                Container(
                  margin: const EdgeInsets.only(top: 4),
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    AppLocalizations.of(context)!.vehicle_currentlyAssigned,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 11,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          isThreeLine: true,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.vehicleManagement_title), // "My Vehicles"
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Get.to(() => const AddVehicleScreen());
            },
          ),
        ],
      ),
      body: StreamBuilder<MobileUserTenantState?>(
        stream: _getTenantState(),
        builder: (context, tenantStateSnapshot) {
          final tenantState = tenantStateSnapshot.data;

          return StreamBuilder<List<Vehicle>>(
            stream: _getUserVehicles(),
            builder: (context, vehiclesSnapshot) {
              if (vehiclesSnapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              if (vehiclesSnapshot.hasError) {
                return Center(
                  child: Text(
                    AppLocalizations.of(context)!.vehicle_error(vehiclesSnapshot.error.toString()),
                  ),
                );
              }

              final vehicles = vehiclesSnapshot.data ?? [];

              if (vehicles.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.directions_car_outlined,
                        size: 64,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey.shade500
                            : Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        AppLocalizations.of(context)!.no_vehicles_added,
                        style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey.shade400
                              : Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: () {
                          Get.to(() => const AddVehicleScreen());
                        },
                        icon: const Icon(Icons.add),
                        label: Text(AppLocalizations.of(context)!.vehicleManagement_addButton), // "Add Vehicle"
                      ),
                    ],
                  ),
                );
              }

              return StreamBuilder<Vehicle?>(
                stream: _getAssignedVehicle(tenantState?.currentVehicleLinkingId),
                builder: (context, assignedVehicleSnapshot) {
                  final assignedVehicle = assignedVehicleSnapshot.data;

                  return ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: vehicles.length + 1,
                    itemBuilder: (context, index) {
                      if (index == 0 && assignedVehicle != null) {
                        // Show assigned vehicle at the top
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: Text(
                                AppLocalizations.of(context)!.vehicle_assignedVehicle,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                            ),
                            _buildVehicleCard(assignedVehicle, true, context),
                            const SizedBox(height: 24),
                            if (vehicles.isNotEmpty)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: Text(
                                  AppLocalizations.of(context)!.vehicle_myVehicles,
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                ),
                              ),
                          ],
                        );
                      }

                      final vehicleIndex = assignedVehicle != null ? index - 1 : index;
                      if (vehicleIndex >= vehicles.length) return null;

                      final vehicle = vehicles[vehicleIndex];
                      final isAssigned = assignedVehicle?.id == vehicle.id;

                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: _buildVehicleCard(vehicle, isAssigned, context),
                      );
                    },
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
