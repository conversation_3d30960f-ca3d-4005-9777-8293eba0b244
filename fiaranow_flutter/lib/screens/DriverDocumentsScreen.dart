import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../l10n/app_localizations.dart';
import '../models/DriverDocument.dart';
import 'DocumentDetailScreen.dart';
import 'DocumentUploadScreen.dart';

class DriverDocumentsScreen extends StatelessWidget {
  const DriverDocumentsScreen({super.key});

  Color _getStatusColor(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.approved:
        return Colors.green;
      case DocumentStatus.pendingReview:
        return Colors.orange;
      case DocumentStatus.rejected:
        return Colors.red;
      case DocumentStatus.expired:
        return Colors.red.shade900;
      case DocumentStatus.expiringSoon:
        return Colors.orange.shade700;
    }
  }

  IconData _getDocumentIcon(DocumentType type) {
    switch (type) {
      case DocumentType.license:
        return Icons.drive_eta;
      case DocumentType.insurance:
        return Icons.shield;
      case DocumentType.vehicleRegistration:
        return Icons.directions_car;
      case DocumentType.nationalId:
        return Icons.badge;
      case DocumentType.other:
        return Icons.description;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.driverDocuments_title), // "My Documents"
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Get.to(() => const DocumentUploadScreen());
            },
          ),
        ],
      ),
      body: StreamBuilder<QuerySnapshot<DriverDocument>>(
        stream: DriverDocument.driverDocumentsColl.orderBy('uploadedAt', descending: true).snapshots(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(
              child: Text('Error: ${snapshot.error}'),
            );
          }

          final documents = snapshot.data?.docs ?? [];
          if (documents.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.folder_open,
                    size: 64,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context)!.no_documents_uploaded,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {
                      Get.to(() => const DocumentUploadScreen());
                    },
                    icon: const Icon(Icons.upload_file),
                    label: Text(AppLocalizations.of(context)!.documentUpload_uploadButton), // "Upload Document"
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            itemCount: documents.length,
            itemBuilder: (context, index) {
              final docSnapshot = documents[index];
              final doc = docSnapshot.data();
              final documentId = docSnapshot.id;
              final daysUntilExpiry = doc.expiryDate.difference(DateTime.now()).inDays;

              return Card(
                elevation: 2,
                margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 6),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: _getStatusColor(doc.status).withValues(alpha: 0.2),
                    child: Icon(
                      _getDocumentIcon(doc.documentType),
                      color: _getStatusColor(doc.status),
                    ),
                  ),
                  title: Text(doc.documentName),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(doc.documentType.displayName),
                      if (daysUntilExpiry <= 30 && doc.status == DocumentStatus.approved)
                        Text(
                          AppLocalizations.of(context)!.document_expiresInDays(daysUntilExpiry),
                        )
                      else
                        Text(
                          AppLocalizations.of(context)!.document_expiresOn(DateFormat.yMMMd().format(doc.expiryDate)),
                        ),
                    ],
                  ),
                  trailing: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _getStatusColor(doc.status).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      doc.status.displayName,
                      style: TextStyle(
                        color: _getStatusColor(doc.status),
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  onTap: () {
                    if (documentId.isEmpty) {
                      Get.snackbar(
                        AppLocalizations.of(context)!.mainPage_error,
                        AppLocalizations.of(context)!.driverDocuments_unableToOpenDocument,
                        backgroundColor: Colors.red,
                        colorText: Colors.white,
                      );
                      return;
                    }

                    // Navigate only if we have a valid document ID
                    Get.to(() => DocumentDetailScreen(documentId: documentId));
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }
}
