import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fiaranow_flutter/widgets/ButtonGroup.dart';
import 'package:fiaranow_flutter/widgets/TripActionButton.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logging/logging.dart';
import 'package:path_provider/path_provider.dart';

import '../config/tenant_config.dart';
import '../l10n/app_localizations.dart';
import '../models/Feedback.dart' as feedback_model;
import '../states/AuthState.dart';

class ApplicationFeedbackScreen extends StatefulWidget {
  final GlobalKey? screenshotKey;

  const ApplicationFeedbackScreen({super.key, this.screenshotKey});

  @override
  State<ApplicationFeedbackScreen> createState() => _ApplicationFeedbackScreenState();
}

class _ApplicationFeedbackScreenState extends State<ApplicationFeedbackScreen> {
  final AuthState _authState = Get.find<AuthState>();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final ImagePicker _imagePicker = ImagePicker();
  final TextEditingController _messageController = TextEditingController();
  final Logger _logger = Logger('ApplicationFeedbackScreen');

  String _message = '';
  final List<File> _selectedImages = [];
  bool _isSubmitting = false;
  bool _includeScreenshot = true;
  File? _screenshotFile;

  @override
  void initState() {
    super.initState();
    if (widget.screenshotKey != null) {
      _captureScreenshot();
    }
  }

  Future<void> _captureScreenshot() async {
    if (widget.screenshotKey == null) return;

    try {
      await Future.delayed(const Duration(milliseconds: 100));

      RenderRepaintBoundary? boundary = widget.screenshotKey!.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary != null) {
        ui.Image image = await boundary.toImage(pixelRatio: 2.0);
        ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);

        if (byteData != null) {
          final tempDir = await getTemporaryDirectory();
          final file = File('${tempDir.path}/screenshot_${DateTime.now().millisecondsSinceEpoch}.png');
          await file.writeAsBytes(byteData.buffer.asUint8List());

          if (mounted) {
            setState(() {
              _screenshotFile = file;
            });
          }
        }
      }
    } catch (e) {
      _logger.warning('Error capturing screenshot: $e');
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    // Clean up screenshot file
    _screenshotFile?.deleteSync();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.app_feedback_title),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.app_feedback_description,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 24),
            TextField(
              controller: _messageController,
              maxLines: 6,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.feedback_message,
                hintText: AppLocalizations.of(context)!.app_feedback_hint,
                border: const OutlineInputBorder(),
              ),
              onChanged: (value) => _message = value,
            ),
            const SizedBox(height: 24),

            // Screenshot section
            if (_screenshotFile != null) ...[
              Text(
                AppLocalizations.of(context)!.current_screen_screenshot,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              CheckboxListTile(
                title: Text(AppLocalizations.of(context)!.include_screenshot),
                value: _includeScreenshot,
                onChanged: (value) {
                  setState(() {
                    _includeScreenshot = value ?? true;
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
              if (_includeScreenshot) ...[
                Container(
                  height: 200,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      _screenshotFile!,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ],

            Text(
              AppLocalizations.of(context)!.additional_images,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            MediaPickerButtons(
              cameraButton: TripActionButton(
                label: AppLocalizations.of(context)!.feedback_camera, // "Camera"
                variant: ButtonVariant.secondary,
                icon: Icons.camera_alt,
                onPressed: () => _pickImage(ImageSource.camera),
              ),
              galleryButton: TripActionButton(
                label: AppLocalizations.of(context)!.feedback_gallery, // "Gallery"
                variant: ButtonVariant.secondary,
                icon: Icons.photo_library,
                onPressed: () => _pickImage(ImageSource.gallery),
              ),
            ),
            const SizedBox(height: 16),

            if (_selectedImages.isNotEmpty) ...[
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _selectedImages.length,
                  itemBuilder: (context, index) {
                    return Stack(
                      children: [
                        Container(
                          margin: const EdgeInsets.only(right: 8),
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            image: DecorationImage(
                              image: FileImage(_selectedImages[index]),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 12,
                          child: InkWell(
                            onTap: () {
                              setState(() {
                                _selectedImages.removeAt(index);
                              });
                            },
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.close,
                                size: 16,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
              const SizedBox(height: 8),
            ],

            Text(
              AppLocalizations.of(context)!.max_5_images,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _message.isNotEmpty && !_isSubmitting ? _submitFeedback : null,
                child: _isSubmitting ? const CircularProgressIndicator() : Text(AppLocalizations.of(context)!.submit_feedback),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    if (_selectedImages.length >= 5) {
      if (mounted) {
        Get.snackbar(
          AppLocalizations.of(context)!.mainPage_error, // "Error"
          AppLocalizations.of(context)!.max_images_reached,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
      }
      return;
    }

    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        final file = File(image.path);
        // Check file size (5MB limit)
        final fileSize = await file.length();
        if (fileSize > 5 * 1024 * 1024) {
          if (mounted) {
            Get.snackbar(
              AppLocalizations.of(context)!.mainPage_error, // "Error"
              AppLocalizations.of(context)!.image_too_large,
              backgroundColor: Colors.red,
              colorText: Colors.white,
            );
          }
          return;
        }

        if (mounted) {
          setState(() {
            _selectedImages.add(file);
          });
        }
      }
    } catch (e) {
      if (mounted) {
        Get.snackbar(
          AppLocalizations.of(context)!.mainPage_error, // "Error"
          AppLocalizations.of(context)!.error_picking_image(e.toString()), // "Error picking image: $e"
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  Future<List<String>> _uploadImages() async {
    List<String> uploadedPaths = [];
    List<File> imagesToUpload = List.from(_selectedImages);

    // Add screenshot if included
    if (_includeScreenshot && _screenshotFile != null) {
      imagesToUpload.insert(0, _screenshotFile!);
    }

    for (int i = 0; i < imagesToUpload.length; i++) {
      final file = imagesToUpload[i];
      final fileName = 'feedback/app/${_authState.currentMobileUser.value!.uid}/${DateTime.now().millisecondsSinceEpoch}_$i.jpg';

      try {
        final ref = _storage.ref().child(fileName);
        await ref.putFile(file);
        // Store only the path, not the full URL
        uploadedPaths.add(fileName);
      } catch (e) {
        _logger.warning('Error uploading image: $e');
      }
    }

    return uploadedPaths;
  }

  Future<void> _submitFeedback() async {
    if (_message.isEmpty) {
      if (mounted) {
        Get.snackbar(
          AppLocalizations.of(context)!.mainPage_error, // "Error"
          AppLocalizations.of(context)!.please_enter_message,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final imagePaths = await _uploadImages();
      final currentUser = _authState.currentMobileUser.value!;

      await _firestore.collection(TenantConfig.getTenantPath('feedbacks')).add({
        'uid': currentUser.uid,
        'type': feedback_model.FeedbackType.application.toString().split('.').last,
        'message': _message,
        'images': imagePaths,
        'createdAt': FieldValue.serverTimestamp(),
        'status': feedback_model.FeedbackStatus.newFeedback.toString().split('.').last,
        'appVersion': '1.1.2', // You might want to get this dynamically
        'platform': Platform.isIOS ? 'iOS' : 'Android',
        'deviceInfo': {
          'os': Platform.operatingSystem,
          'version': Platform.operatingSystemVersion,
        },
      });

      if (mounted) {
        Get.snackbar(
          AppLocalizations.of(context)!.mapScreen_success, // "Success"
          AppLocalizations.of(context)!.feedback_submitted_success,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        Get.snackbar(
          AppLocalizations.of(context)!.mainPage_error, // "Error"
          AppLocalizations.of(context)!.error_submitting_feedback(e.toString()), // "Error submitting feedback: $e"
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}
