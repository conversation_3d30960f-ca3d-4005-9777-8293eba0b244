import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

import '../l10n/app_localizations.dart';
import '../models/DriverDocument.dart';
import '../utils/storage_utils.dart';
import '../widgets/StorageImage.dart';

class DocumentDetailScreen extends StatefulWidget {
  final String? documentId;

  const DocumentDetailScreen({super.key, required this.documentId});

  @override
  State<DocumentDetailScreen> createState() => _DocumentDetailScreenState();
}

class _DocumentDetailScreenState extends State<DocumentDetailScreen> {
  final Logger _logger = Logger('DocumentDetailScreen');
  String? _error;
  final GlobalKey<SfPdfViewerState> _pdfViewerKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    // Document will be loaded by the StreamBuilder
  }

  Color _getStatusColor(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.approved:
        return Colors.green;
      case DocumentStatus.pendingReview:
        return Colors.orange;
      case DocumentStatus.rejected:
        return Colors.red;
      case DocumentStatus.expired:
        return Colors.red.shade900;
      case DocumentStatus.expiringSoon:
        return Colors.orange.shade700;
    }
  }

  IconData _getDocumentIcon(DocumentType type) {
    switch (type) {
      case DocumentType.license:
        return Icons.drive_eta;
      case DocumentType.insurance:
        return Icons.shield;
      case DocumentType.vehicleRegistration:
        return Icons.directions_car;
      case DocumentType.nationalId:
        return Icons.badge;
      case DocumentType.other:
        return Icons.description;
    }
  }

  bool _isPdf(String url) {
    // Parse URL to handle Firebase Storage URLs with query parameters
    final uri = Uri.tryParse(url);
    if (uri == null) return false;

    // Get the path without query parameters
    final path = uri.path.toLowerCase();

    // Also check the raw URL in case it's encoded
    final decodedUrl = Uri.decodeFull(url).toLowerCase();

    return path.endsWith('.pdf') || path.contains('.pdf?') || decodedUrl.contains('.pdf?') || decodedUrl.contains('.pdf%3F');
  }

  bool _isImage(String url) {
    final imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];

    // Parse URL to handle Firebase Storage URLs with query parameters
    final uri = Uri.tryParse(url);
    if (uri == null) return false;

    // Get the path without query parameters
    final path = uri.path.toLowerCase();

    // Also check the raw URL in case it's encoded
    final decodedUrl = Uri.decodeFull(url).toLowerCase();

    return imageExtensions.any(
        (ext) => path.endsWith(ext) || path.contains('$ext?') || decodedUrl.contains('$ext?') || decodedUrl.contains('$ext%3F'));
  }

  Widget _buildDocumentViewer(BuildContext context, DriverDocument document) {
    if (document.fileURL.isEmpty) {
      return Container(
        height: 300,
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.grey),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context)!.documentDetail_notAvailable, // "Document not available"
                style: const TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    final pathOrUrl = document.fileURL;

    // Check if it's an image or PDF based on the path/URL
    if (_isPdf(pathOrUrl)) {
      // For PDFs, we need to get the download URL
      return Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: FutureBuilder<String>(
            future: StorageUtils.isFullUrl(pathOrUrl) ? Future.value(pathOrUrl) : StorageUtils.getDownloadUrl(pathOrUrl),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Container(
                  color: Colors.grey.shade100,
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              if (snapshot.hasError) {
                setState(() {
                  _error = snapshot.error.toString();
                });
                return Container(
                  color: Colors.grey.shade100,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error_outline, size: 48, color: Colors.red),
                        const SizedBox(height: 16),
                        Text(
                          AppLocalizations.of(context)!.documentDetail_previewNotAvailable,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ],
                    ),
                  ),
                );
              }

              return SfPdfViewer.network(
                snapshot.data!,
                key: _pdfViewerKey,
                onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
                  setState(() {
                    _error = '${details.error}: ${details.description}';
                  });
                },
              );
            },
          ),
        ),
      );
    } else if (_isImage(pathOrUrl)) {
      return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: StorageImage(
            pathOrUrl: pathOrUrl,
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                height: 300,
                color: Colors.grey.shade100,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(
                        AppLocalizations.of(context)!.documentDetail_failedToLoadImage, // "Failed to load image"
                        style: const TextStyle(color: Colors.red),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      );
    } else {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.insert_drive_file, size: 48, color: Colors.grey),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context)!.documentDetail_previewNotAvailable,
                style: const TextStyle(color: Colors.red),
              ),
              const SizedBox(height: 8),
              Text(
                AppLocalizations.of(context)!.documentDetail_unsupportedFormat, // "Unsupported file format"
                style: const TextStyle(color: Colors.grey, fontSize: 12),
              ),
            ],
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Debug print to trace the issue
    _logger.info('DocumentDetailScreen - documentId: ${widget.documentId}');

    // Add null safety check
    if (widget.documentId == null || widget.documentId!.isEmpty) {
      return Scaffold(
        appBar: AppBar(
          title: Text(AppLocalizations.of(context)!.documentDetail_title), // "Document Details"
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context)!.documentDetail_notFound, // "Document not found"
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
        ),
      );
    }

    return StreamBuilder<DocumentSnapshot<DriverDocument>>(
      stream: DriverDocument.driverDocumentsColl.doc(widget.documentId!).snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Scaffold(
            appBar: AppBar(
              title: Text(AppLocalizations.of(context)!.documentDetail_title), // "Document Details"
            ),
            body: const Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasError) {
          return Scaffold(
            appBar: AppBar(
              title: Text(AppLocalizations.of(context)!.documentDetail_title), // "Document Details"
            ),
            body: Center(
              child: Text('${AppLocalizations.of(context)!.mainPage_error}: ${snapshot.error}'),
            ),
          );
        }

        if (!snapshot.hasData || !snapshot.data!.exists) {
          return Scaffold(
            appBar: AppBar(
              title: Text(AppLocalizations.of(context)!.documentDetail_title), // "Document Details"
            ),
            body: Center(
              child: Text(AppLocalizations.of(context)!.documentDetail_notFound), // "Document not found"
            ),
          );
        }

        final document = snapshot.data!.data()!;
        final daysUntilExpiry = document.expiryDate.difference(DateTime.now()).inDays;
        final isExpiringSoon = daysUntilExpiry <= 30 && daysUntilExpiry > 0;
        final isExpired = daysUntilExpiry <= 0;

        return Scaffold(
          appBar: AppBar(
            title: Text(document.documentName),
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Status Card
                Card(
                  color: _getStatusColor(document.status).withValues(alpha: 0.1),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: _getStatusColor(document.status),
                          child: Icon(
                            _getDocumentIcon(document.documentType),
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                document.documentType.displayName,
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                              const SizedBox(height: 4),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: _getStatusColor(document.status),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  document.status.displayName,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Expiry Warning
                if (isExpired || isExpiringSoon)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isExpired ? Colors.red.shade50 : Colors.orange.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isExpired ? Colors.red : Colors.orange,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.warning,
                          color: isExpired ? Colors.red : Colors.orange,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            isExpired
                                ? AppLocalizations.of(context)!.documentDetail_expired // "This document has expired"
                                : AppLocalizations.of(context)!
                                    .document_expiresInDays(daysUntilExpiry), // "This document expires in X days"
                            style: TextStyle(
                              color: isExpired ? Colors.red : Colors.orange,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                const SizedBox(height: 24),

                // Document Details
                _buildDetailRow(
                  context,
                  AppLocalizations.of(context)!.documentDetail_documentName, // "Document Name"
                  document.documentName,
                ),
                _buildDetailRow(
                  context,
                  AppLocalizations.of(context)!.documentDetail_uploadDate, // "Upload Date"
                  DateFormat.yMMMd().add_jm().format(document.uploadedAt),
                ),
                _buildDetailRow(
                  context,
                  AppLocalizations.of(context)!.documentDetail_expiryDate, // "Expiry Date"
                  DateFormat.yMMMd().format(document.expiryDate),
                ),
                if (document.notes != null && document.notes!.isNotEmpty)
                  _buildDetailRow(
                    context,
                    AppLocalizations.of(context)!.documentDetail_notes, // "Notes"
                    document.notes!,
                  ),

                // Review Information
                if (document.reviewedAt != null) ...[
                  const SizedBox(height: 24),
                  Text(
                    AppLocalizations.of(context)!.documentDetail_reviewInformation, // "Review Information"
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  _buildDetailRow(
                    context,
                    AppLocalizations.of(context)!.documentDetail_reviewedDate, // "Reviewed Date"
                    DateFormat.yMMMd().add_jm().format(document.reviewedAt!),
                  ),
                  if (document.adminNotes != null && document.adminNotes!.isNotEmpty)
                    _buildDetailRow(
                      context,
                      AppLocalizations.of(context)!.documentDetail_adminNotes, // "Admin Notes"
                      document.adminNotes!,
                    ),
                ],

                const SizedBox(height: 24),

                // Document Preview Section
                Text(
                  AppLocalizations.of(context)!.documentDetail_documentPreview, // "Document Preview"
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey,
                      ),
                ),
                const SizedBox(height: 4),
                if (_error != null)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.error_outline, color: Colors.red),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _error!,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ),
                      ],
                    ),
                  )
                else
                  _buildDocumentViewer(context, document),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey,
                ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}
