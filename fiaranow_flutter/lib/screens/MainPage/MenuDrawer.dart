import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:restart/restart.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../l10n/app_localizations.dart';
import '../../models/MobileUser.dart';
import '../../states/AppState.dart';
import '../../states/AuthState.dart';
import '../../states/PermissionsState.dart';
import '../../states/ThemeState.dart';

class MenuDrawer extends StatelessWidget {
  const MenuDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final authState = Get.find<AuthState>();
    final appState = Get.find<AppState>();
    final themeState = Get.find<ThemeState>();

    return Drawer(
      child: SafeArea(
        bottom: true,
        left: false,
        top: false,
        right: false,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              SizedBox(
                width: double.infinity,
                child: DrawerHeader(
                  decoration: const BoxDecoration(
                    color: Color(0xFF0C2D48),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        authState.currentMobileUser.value?.ensuredDisplayName ?? AppLocalizations.of(context)!.menuDrawer_menu,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        'Version: ${appState.getAppVersion()}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              ListTile(
                leading: const Icon(Icons.language),
                title: Text(AppLocalizations.of(context)!.mainPage_chooseLanguage),
                onTap: () async {
                  FirebaseAnalytics.instance.logEvent(
                    name: 'select_language',
                    parameters: {'widget_name': 'menu_drawer'},
                  );
                  await Get.toNamed('/choose_language');
                },
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: Row(
                  children: [
                    Icon(Icons.palette, color: Theme.of(context).colorScheme.primary),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        AppLocalizations.of(context)!.menuDrawer_theme, // "Theme"
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Obx(() => Row(
                      children: [
                        Expanded(
                          child: ChoiceChip(
                            label: Text(AppLocalizations.of(context)!.menuDrawer_themeSystem), // "System"
                            selected: themeState.currentThemeMode.value == AppThemeMode.system,
                            onSelected: (selected) {
                              if (selected) {
                                FirebaseAnalytics.instance.logEvent(
                                  name: 'theme_changed',
                                  parameters: {'theme': 'system', 'widget_name': 'menu_drawer'},
                                );
                                themeState.setThemeMode(AppThemeMode.system);
                              }
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ChoiceChip(
                            label: Text(AppLocalizations.of(context)!.menuDrawer_themeLight), // "Light"
                            selected: themeState.currentThemeMode.value == AppThemeMode.light,
                            onSelected: (selected) {
                              if (selected) {
                                FirebaseAnalytics.instance.logEvent(
                                  name: 'theme_changed',
                                  parameters: {'theme': 'light', 'widget_name': 'menu_drawer'},
                                );
                                themeState.setThemeMode(AppThemeMode.light);
                              }
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ChoiceChip(
                            label: Text(AppLocalizations.of(context)!.menuDrawer_themeDark), // "Dark"
                            selected: themeState.currentThemeMode.value == AppThemeMode.dark,
                            onSelected: (selected) {
                              if (selected) {
                                FirebaseAnalytics.instance.logEvent(
                                  name: 'theme_changed',
                                  parameters: {'theme': 'dark', 'widget_name': 'menu_drawer'},
                                );
                                themeState.setThemeMode(AppThemeMode.dark);
                              }
                            },
                          ),
                        ),
                      ],
                    )),
              ),
              const Divider(),
              Padding(
                padding: const EdgeInsets.only(left: 16.0, top: 8.0),
                child: Text(
                  AppLocalizations.of(context)!.menuDrawer_profileSettings, // Profile Settings
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
              Obx(() {
                final userType = authState.currentMobileUser.value?.primaryUserType;
                final isSet = userType != null;
                return ListTile(
                  leading: Icon(
                    Icons.person,
                    color: isSet ? Colors.green : Colors.orange,
                  ),
                  title: Text(
                    AppLocalizations.of(context)!.menuDrawer_currentMode, // Current mode
                    style: TextStyle(
                      color: isSet ? Colors.green : Colors.orange,
                    ),
                  ),
                  subtitle: isSet
                      ? Text(
                          userType == UserType.driver
                              ? AppLocalizations.of(context)!.mainPage_driver
                              : AppLocalizations.of(context)!.mainPage_rider,
                          style: const TextStyle(color: Colors.green),
                        )
                      : null,
                  onTap: () async {
                    final currentType = authState.currentMobileUser.value?.primaryUserType;
                    FirebaseAnalytics.instance.logEvent(
                      name: 'change_user_type',
                      parameters: {'previous_type': currentType?.toString() ?? 'unknown', 'widget_name': 'menu_drawer'},
                    );
                    await Get.toNamed('/choose_user_type');
                  },
                );
              }),
              Obx(() {
                final phoneNumber = authState.currentMobileUser.value?.phoneNumber;
                final isSet = phoneNumber != null && phoneNumber.isNotEmpty;
                return ListTile(
                  leading: Icon(
                    Icons.phone,
                    color: isSet ? Colors.green : Colors.orange,
                  ),
                  title: Text(
                    AppLocalizations.of(context)!.menuDrawer_phoneNumber, // Phone number
                    style: TextStyle(
                      color: isSet ? Colors.green : Colors.orange,
                    ),
                  ),
                  subtitle: isSet
                      ? Text(
                          phoneNumber,
                          style: const TextStyle(color: Colors.green),
                        )
                      : null,
                  onTap: () {
                    FirebaseAnalytics.instance.logEvent(
                      name: 'set_phone_number',
                      parameters: {'widget_name': 'menu_drawer'},
                    );
                    Get.toNamed('/set_phone_number');
                  },
                );
              }),
              Obx(() {
                final userType = authState.currentMobileUser.value?.primaryUserType;
                if (userType != UserType.driver) return const SizedBox.shrink();

                return ListTile(
                  leading: const Icon(
                    Icons.folder_shared,
                  ),
                  title: Text(
                    AppLocalizations.of(context)!.menuDrawer_driverDocuments, // "My Documents"
                  ),
                  subtitle: Text(
                    AppLocalizations.of(context)!.menuDrawer_driverDocumentsDesc, // "Upload and manage your documents"
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                  onTap: () async {
                    FirebaseAnalytics.instance.logEvent(
                      name: 'access_driver_documents',
                      parameters: {'widget_name': 'menu_drawer'},
                    );
                    await Get.toNamed('/driver_documents');
                  },
                );
              }),
              ListTile(
                leading: const Icon(Icons.settings),
                title: Text(AppLocalizations.of(context)!.mainPage_settings),
                onTap: () {
                  FirebaseAnalytics.instance.logEvent(
                    name: 'open_settings',
                    parameters: {'widget_name': 'menu_drawer'},
                  );
                  Get.toNamed('/settings/notifications');
                },
              ),
              const Divider(),
              Padding(
                padding: const EdgeInsets.only(left: 16.0, top: 8.0),
                child: Text(
                  AppLocalizations.of(context)!.menuDrawer_permissions, // Permissions
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
              Builder(
                builder: (context) {
                  final permissionsState = Get.find<PermissionsState>();
                  return Obx(() {
                    final bool hasNotificationPerm = permissionsState.hasNotificationPermission();
                    final bool isPermanentlyDenied = permissionsState.isNotificationPermissionPermanentlyDenied();

                    return ListTile(
                      leading: Icon(
                        Icons.notifications,
                        color: hasNotificationPerm ? Colors.green : Colors.red,
                      ),
                      title: Text(
                        AppLocalizations.of(context)!.menuDrawer_pushNotifications, // Push Notifications
                        style: TextStyle(
                          color: hasNotificationPerm ? Colors.green : Colors.red,
                        ),
                      ),
                      subtitle: isPermanentlyDenied
                          ? Text(
                              AppLocalizations.of(context)!
                                  .menuDrawer_notificationPermanentlyDenied, // Permanently denied by User
                              style: TextStyle(color: Colors.grey[600]),
                            )
                          : null,
                      onTap: hasNotificationPerm
                          ? null
                          : () async {
                              // Refresh permissions
                              permissionsState.checkPermissions();

                              // Get current statuses at the moment of tap
                              final bool currentHasPermission = permissionsState.hasNotificationPermission();
                              final bool isPermanentlyDenied = permissionsState.isNotificationPermissionPermanentlyDenied();

                              if (isPermanentlyDenied) {
                                // Still, attempt to request permission, only if that fails that we open settings
                                FirebaseAnalytics.instance.logEvent(
                                    name: 'request_notification_permission', parameters: {'widget_name': 'menu_drawer'});
                                final granted = await permissionsState.requestNotificationPermission();
                                if (!granted) {
                                  // If still not granted after request, open settings
                                  FirebaseAnalytics.instance.logEvent(
                                      name: 'open_notification_settings_after_denied',
                                      parameters: {'widget_name': 'menu_drawer'});
                                  await permissionsState.openSettings();
                                }
                              } else if (!currentHasPermission) {
                                // If not granted (and not permanently denied), request permission
                                FirebaseAnalytics.instance.logEvent(
                                    name: 'request_notification_permission', parameters: {'widget_name': 'menu_drawer'});
                                await permissionsState.requestNotificationPermission();
                                // UI will update via Obx after permission request
                              } else {
                                // If granted (and not permanently denied), open settings (e.g., to allow disabling)
                                FirebaseAnalytics.instance.logEvent(
                                    name: 'open_notification_settings_from_granted', parameters: {'widget_name': 'menu_drawer'});
                                await permissionsState.openSettings();
                              }
                            },
                    );
                  });
                },
              ),
              Builder(
                builder: (context) {
                  final permissionsState = Get.find<PermissionsState>();
                  final authState = Get.find<AuthState>();
                  return Obx(() {
                    final isDriver = authState.currentMobileUser.value?.primaryUserType == UserType.driver;
                    final hasLocationPerm = permissionsState.hasLocationPermission(background: isDriver);
                    final locationPermission = permissionsState.locationPermission.value;

                    // Determine if subtitle should be shown
                    final bool showWhileInUseSubtitle = isDriver && locationPermission == LocationPermission.whileInUse;

                    return ListTile(
                      leading: Icon(
                        Icons.location_on,
                        color: hasLocationPerm ? Colors.green : Colors.red,
                      ),
                      title: Text(
                        isDriver
                            ? AppLocalizations.of(context)!.menuDrawer_gpsLocationBackground // GPS Location (Background)
                            : AppLocalizations.of(context)!.menuDrawer_gpsLocation, // GPS Location
                        style: TextStyle(
                          color: hasLocationPerm ? Colors.green : Colors.red,
                        ),
                      ),
                      subtitle: showWhileInUseSubtitle
                          ? Text(
                              AppLocalizations.of(context)!
                                  .menuDrawer_locationWhileInUseDriverSubtitle, // "Set to 'Always' for optimal driver experience."
                              style: TextStyle(color: Colors.orange[700]),
                            )
                          : null,
                      enabled: !hasLocationPerm || showWhileInUseSubtitle,
                      onTap: !hasLocationPerm || showWhileInUseSubtitle
                          ? () async {
                              if (isDriver && locationPermission == LocationPermission.whileInUse) {
                                FirebaseAnalytics.instance.logEvent(
                                  name: 'request_location_permission',
                                  parameters: {
                                    'is_driver': isDriver.toString(),
                                    'widget_name': 'menu_drawer',
                                    'current_permission': locationPermission.toString(),
                                    'request_strategy': 'open_settings'
                                  },
                                );
                                await permissionsState.openLocationSettings();
                              } else if (!hasLocationPerm) {
                                FirebaseAnalytics.instance.logEvent(
                                  name: 'request_location_permission',
                                  parameters: {
                                    'is_driver': isDriver.toString(),
                                    'widget_name': 'menu_drawer',
                                    'current_permission': locationPermission.toString(),
                                    'request_strategy': 'request_permission'
                                  },
                                );
                                await permissionsState.requestLocationPermission(background: isDriver);
                              } else {
                                FirebaseAnalytics.instance.logEvent(
                                  name: 'open_location_settings',
                                  parameters: {
                                    'is_driver': isDriver.toString(),
                                    'widget_name': 'menu_drawer',
                                    'current_permission': locationPermission.toString()
                                  },
                                );
                                await permissionsState.openLocationSettings();
                              }
                            }
                          : null,
                    );
                  });
                },
              ),
              const Divider(),
              Obx(() {
                const isDebugMode = !bool.fromEnvironment('dart.vm.product');
                final uid = authState.currentUser.value?.uid;
                final isSpecialUser = uid == 'c5YHX79mg8OgHoyF12JBEffYwnE3' || uid == 'oEay2mlxWgXAYRyKxvuIjL54pII3';

                if (isDebugMode || isSpecialUser) {
                  return FutureBuilder<bool>(
                    future: _isUsingEmulator(),
                    builder: (context, snapshot) {
                      final isUsingEmulator = snapshot.data ?? true;

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 16.0, top: 8.0, bottom: 8.0),
                            child: Text(
                              'Server',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16.0),
                            child: Row(
                              children: [
                                Expanded(
                                  child: ChoiceChip(
                                    label: const Text('Emulator'),
                                    selected: isUsingEmulator,
                                    onSelected: (selected) {
                                      if (selected) {
                                        _setServerMode(true);
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ChoiceChip(
                                    label: const Text('Live'),
                                    selected: !isUsingEmulator,
                                    onSelected: (selected) {
                                      if (selected) {
                                        _setServerMode(false);
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Divider(),
                        ],
                      );
                    },
                  );
                }
                return const SizedBox.shrink();
              }),
              ListTile(
                leading: const Icon(Icons.logout),
                title: Text(AppLocalizations.of(context)!.menuDrawer_logout),
                onTap: () {
                  FirebaseAnalytics.instance.logEvent(
                    name: 'logout_initiated',
                    parameters: {
                      'user_type': authState.currentMobileUser.value?.primaryUserType?.toString() ?? 'unknown',
                      'widget_name': 'menu_drawer'
                    },
                  );
                  Get.defaultDialog(
                    title: AppLocalizations.of(context)!.menuDrawer_logout,
                    middleText: AppLocalizations.of(context)!.dialog_logoutConfirmation,
                    textCancel: AppLocalizations.of(context)!.dialog_no,
                    textConfirm: AppLocalizations.of(context)!.dialog_yes,
                    onConfirm: () async {
                      await authState.logout();
                      Get.back();
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<bool> _isUsingEmulator() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('use_emulator') ?? false;
  }

  Future<void> _setServerMode(bool useEmulator) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('use_emulator', useEmulator);

    await Get.dialog(
      AlertDialog(
        title: const Text('Server Changed'),
        content: Text('The app will now restart to use the ${useEmulator ? 'emulator' : 'production'} server.'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              restart();
            },
            child: const Text('OK'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }
}
