import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../l10n/app_localizations.dart';
import '../../models/MobileUser.dart';
import '../../states/AppState.dart';
import '../../states/AuthState.dart';

class Home extends StatefulWidget {
  final void Function(int) onNavigateToTab;

  const Home({super.key, required this.onNavigateToTab});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final authState = Get.find<AuthState>();

    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Center(
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.home_welcomeText,
                      style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 20),

                    // Get a ride now or reserve a ride
                    Obx(() {
                      final userType = authState.currentMobileUser.value?.primaryUserType;
                      if (userType == UserType.rider) {
                        return Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ElevatedButton(
                              onPressed: () {
                                FirebaseAnalytics.instance.logEvent(
                                  name: 'select_ride_type',
                                  parameters: {
                                    'ride_type': 'now',
                                    'user_type': 'rider',
                                    'widget_name': 'home',
                                  },
                                );
                                Get.find<AppState>().setUserTendency(UserTendency.rideNow);
                                widget.onNavigateToTab(1);
                              },
                              child: Text(AppLocalizations.of(context)!.home_getRideNow), // Get a ride Now
                            ),
                            const SizedBox(height: 8),
                            TextButton(
                              onPressed: () {
                                FirebaseAnalytics.instance.logEvent(
                                  name: 'select_ride_type',
                                  parameters: {
                                    'ride_type': 'reserve',
                                    'user_type': 'rider',
                                    'widget_name': 'home',
                                  },
                                );
                                Get.find<AppState>().setUserTendency(UserTendency.reserve);
                                widget.onNavigateToTab(1);
                              },
                              child: Text(
                                AppLocalizations.of(context)!.home_reserveRide,
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ), // Reserve a ride
                            ),
                          ],
                        );
                      }
                      return const SizedBox.shrink();
                    }),
                    const SizedBox(height: 20),

                    // Special offer
                    Container(
                      padding: const EdgeInsets.only(left: 16, right: 10, top: 8, bottom: 16),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                AppLocalizations.of(context)!.home_specialOffer, // Special offer
                                style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                              ),
                              IconButton(
                                icon: const Icon(Icons.phone),
                                onPressed: () {
                                  FirebaseAnalytics.instance.logEvent(
                                    name: 'contact_special_offer',
                                    parameters: {
                                      'contact_method': 'phone',
                                      'widget_name': 'home',
                                    },
                                  );
                                  launchUrl(Uri.parse('tel:+261374609018'));
                                },
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Text(AppLocalizations.of(context)!
                              .home_reserveCarNoGas), // • Reserve a car for the whole day for €25 (no gas)
                          const SizedBox(height: 5),
                          Text(AppLocalizations.of(context)!
                              .home_reserveCarWithGas), // • Reserve for the whole day for €75 (including gas)
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
