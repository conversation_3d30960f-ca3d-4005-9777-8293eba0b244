import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fiaranow_flutter/models/MobileUser.dart';
import 'package:fiaranow_flutter/widgets/PriceDisplay.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../l10n/app_localizations.dart';
import '../models/Trip.dart';
import '../models/TripStatus.dart';
import '../states/AuthState.dart';
import '../states/NavigationState.dart';
import 'TripDetailsPage.dart';

class HistoryScreen extends StatefulWidget {
  final void Function(dynamic index) onNavigateToTab;

  const HistoryScreen({super.key, required this.onNavigateToTab});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  final _authState = Get.find<AuthState>();
  String? _selectedTripId;
  Stream<QuerySnapshot<Trip>>? _tripsStream;

  @override
  void initState() {
    super.initState();
    // Initialize the stream once
    _setupTripStream();

    // Listen to user type changes and update stream if needed
    ever(_authState.currentMobileUser, (_) {
      _setupTripStream();
    });
  }

  void _setupTripStream() {
    final userType = _authState.currentMobileUser.value?.primaryUserType;
    final isPassenger = userType == UserType.rider;

    _tripsStream = tripsColl
        .where(isPassenger ? 'uidPassenger' : 'uidChosenDriver', isEqualTo: _authState.uid)
        .orderBy('createdAt', descending: true)
        .limit(30)
        .snapshots();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      body: StreamBuilder<QuerySnapshot<Trip>>(
        stream: _tripsStream,
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.data!.docs.isEmpty) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.history,
                      size: 80,
                      color: Colors.grey[500],
                    ),
                    const SizedBox(height: 20),
                    Text(
                      AppLocalizations.of(context)!.history_title,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      AppLocalizations.of(context)!.history_noTripsYet,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            );
          }
          return CustomScrollView(
            slivers: [
              SliverAppBar(
                title: Text(
                  AppLocalizations.of(context)!.history_title,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
                floating: true,
                pinned: false,
                snap: true,
                backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                surfaceTintColor: Colors.transparent,
                elevation: 0,
              ),
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final trip = snapshot.data!.docs[index].data();
                    IconData icon;
                    Color color = Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black;
                    if (trip.status == TripStatus.completed || trip.status == TripStatus.paid) {
                      icon = Icons.check_circle;
                      color = Colors.green;
                    } else if (trip.status == TripStatus.cancelled) {
                      icon = Icons.cancel;
                      color = Colors.red;
                    } else if (trip.status == TripStatus.inProgress) {
                      icon = Icons.arrow_forward;
                    } else if (trip.status == TripStatus.reserved) {
                      icon = Icons.access_time;
                    } else {
                      icon = Icons.info;
                    }

                    // Handle full day trips which may not have route data
                    final bool isFullDayTrip = trip.reservationType == ReservationType.fullDay;
                    final String tripTitle = isFullDayTrip
                        ? (trip.startLocationName?.isNotEmpty == true
                            ? trip.startLocationName!
                            : AppLocalizations.of(context)!.mapScreen_fullDayReservation)
                        : (trip.arrivalLocationName ?? AppLocalizations.of(context)!.tripDetails_unknownDestination);

                    // Only use route data if it exists
                    final calculationData = trip.finalRouteData ?? trip.routeData;

                    return InkWell(
                      onTap: () {
                        FirebaseAnalytics.instance.logEvent(
                          name: 'view_trip_details',
                          parameters: {
                            'widget_name': 'history',
                            'trip_status': trip.status.toString(),
                            'trip_type': trip.pickupTime != null ? 'reserved' : 'immediate',
                            'trip_id': trip.id,
                          },
                        );
                        Get.find<NavigationState>().reset();
                        setState(() {
                          _selectedTripId = _selectedTripId == trip.id ? null : trip.id;
                        });
                        Get.to(
                          () => TripDetailsPage(
                            trip: trip,
                            onNavigateToTab: widget.onNavigateToTab,
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Card(
                        elevation: 2,
                        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: _selectedTripId == trip.id
                              ? BorderSide(color: Theme.of(context).primaryColor, width: 2)
                              : BorderSide.none,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Text(
                                      tripTitle,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      // Reservation indicator
                                      if (trip.inTakeSource == InTakeSource.reservation)
                                        Container(
                                          margin: const EdgeInsets.only(right: 6),
                                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                          decoration: BoxDecoration(
                                            color: Theme.of(context).brightness == Brightness.dark
                                                ? Colors.purple.withValues(alpha: 0.3)
                                                : Colors.purple.withValues(alpha: 0.15),
                                            borderRadius: BorderRadius.circular(12),
                                            border: Border.all(
                                              color: Theme.of(context).brightness == Brightness.dark
                                                  ? Colors.purple.withValues(alpha: 0.6)
                                                  : Colors.purple.withValues(alpha: 0.3),
                                              width: 1,
                                            ),
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(
                                                isFullDayTrip ? Icons.calendar_today : Icons.calendar_month,
                                                color: Theme.of(context).brightness == Brightness.dark
                                                    ? Colors.purple.shade200
                                                    : Colors.purple,
                                                size: 14,
                                              ),
                                            ],
                                          ),
                                        ),
                                      // Status indicator
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: color.withValues(alpha: 0.1),
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(icon, color: color, size: 16),
                                            const SizedBox(width: 4),
                                            Text(
                                              trip.status.getLocalizedName(context),
                                              style: TextStyle(
                                                color: Theme.of(context).brightness == Brightness.dark
                                                    ? color.withValues(alpha: 0.9)
                                                    : color,
                                                fontWeight: FontWeight.w500,
                                                fontSize: 12,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              if (trip.pickupTime != null)
                                Row(
                                  children: [
                                    const Icon(Icons.access_time, size: 16, color: Colors.grey),
                                    const SizedBox(width: 4),
                                    Text(
                                      DateFormat('yyyy-MM-dd HH:mm').format(trip.pickupTime!),
                                      style: const TextStyle(color: Colors.grey),
                                    ),
                                  ],
                                ),
                              const SizedBox(height: 4),
                              if (isFullDayTrip)
                                Row(
                                  children: [
                                    const Icon(Icons.money, size: 16, color: Colors.grey),
                                    const SizedBox(width: 4),
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: trip.fullDayPriceType == FullDayPriceType.fixed
                                            ? Colors.blue.withValues(alpha: 0.1)
                                            : Colors.orange.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Text(
                                        trip.fullDayPriceType == FullDayPriceType.fixed
                                            ? AppLocalizations.of(context)!.tripDetails_fixedPrice // "Fixed Price"
                                            : AppLocalizations.of(context)!.tripDetails_perHour, // "Per Hour"
                                        style: TextStyle(
                                          color: trip.fullDayPriceType == FullDayPriceType.fixed ? Colors.blue : Colors.orange,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    PriceDisplay.bold(
                                      amount: trip.costTotal,
                                      currency: trip.realCostCurrency ?? 'MGA',
                                      decimalPlaces: 2,
                                      color: Theme.of(context).textTheme.bodyLarge?.color,
                                    ),
                                  ],
                                )
                              else if (calculationData != null)
                                Row(
                                  children: [
                                    const Icon(Icons.route, size: 16, color: Colors.grey),
                                    const SizedBox(width: 4),
                                    Text(
                                      '${calculationData.distanceKm.toStringAsFixed(1)} km',
                                    ),
                                    const SizedBox(width: 12),
                                    const Icon(Icons.timer, size: 16, color: Colors.grey),
                                    const SizedBox(width: 4),
                                    Text(
                                      '${(calculationData.durationSec / 60).toStringAsFixed(0)} min',
                                    ),
                                    const Spacer(),
                                    PriceDisplay.bold(
                                      amount: trip.costTotal,
                                      currency: trip.realCostCurrency ?? 'MGA',
                                      decimalPlaces: 2,
                                      color: Theme.of(context).textTheme.bodyLarge?.color,
                                    ),
                                  ],
                                )
                              else if (trip.costTotal != null)
                                // Fallback for trips with cost but no route data
                                Row(
                                  children: [
                                    const Spacer(),
                                    PriceDisplay.bold(
                                      amount: trip.costTotal,
                                      currency: trip.realCostCurrency ?? 'MGA',
                                      decimalPlaces: 2,
                                      color: Theme.of(context).textTheme.bodyLarge?.color,
                                    ),
                                  ],
                                ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                  childCount: snapshot.data!.docs.length,
                ),
              )
            ],
          );
        },
      ),
    );
  }
}
