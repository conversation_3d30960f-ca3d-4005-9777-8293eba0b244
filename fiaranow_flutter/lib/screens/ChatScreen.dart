import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logging/logging.dart';

import '../config/tenant_config.dart';
import '../l10n/app_localizations.dart';
import '../models/ChatMessage.dart';
import '../models/Feedback.dart' as feedback_model;
import '../models/Trip.dart';
import '../services/ChatPresenceManager.dart';
import '../states/AuthState.dart';
import '../states/ChatState.dart';
import '../widgets/StorageImage.dart';
import 'TripDetailsPage.dart';

class ChatScreen extends StatefulWidget {
  final String sessionId;
  final String sessionTitle;

  const ChatScreen({
    super.key,
    required this.sessionId,
    required this.sessionTitle,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with WidgetsBindingObserver {
  final Logger _logger = Logger('ChatScreen');
  final AuthState _authState = Get.find<AuthState>();
  late final ChatState _chatState;
  late final ChatPresenceManager _presenceManager;
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ImagePicker _imagePicker = ImagePicker();
  final FirebaseStorage _storage = FirebaseStorage.instance;

  feedback_model.Feedback? _linkedFeedback;
  Trip? _linkedTrip;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _chatState = Get.put(ChatState(sessionId: widget.sessionId));

    // Initialize presence manager
    final currentUser = _authState.currentMobileUser.value;
    if (currentUser != null) {
      _presenceManager = ChatPresenceManager(
        userId: currentUser.uid,
        userType: 'user',
      );
      // Enter chat session for presence tracking
      _presenceManager.enterChatSession(widget.sessionId);
    }

    _loadLinkedFeedback();
  }

  Future<void> _loadLinkedFeedback() async {
    try {
      final sessionDoc =
          await FirebaseFirestore.instance.collection(TenantConfig.getTenantPath('chat_sessions')).doc(widget.sessionId).get();

      if (sessionDoc.exists && sessionDoc.data()?['feedbackId'] != null) {
        final feedbackDoc = await FirebaseFirestore.instance
            .collection(TenantConfig.getTenantPath('feedbacks'))
            .doc(sessionDoc.data()!['feedbackId'])
            .get();

        if (feedbackDoc.exists) {
          final feedback = feedback_model.Feedback.fromFirestore(feedbackDoc);

          // Load the associated trip if it exists
          Trip? loadedTrip;
          if (feedback.tripId != null) {
            final tripDoc = await tripsColl.doc(feedback.tripId).get();
            if (tripDoc.exists) {
              loadedTrip = tripDoc.data();
            }
          }

          if (mounted) {
            setState(() {
              _linkedFeedback = feedback;
              _linkedTrip = loadedTrip;
            });
          }
        }
      }
    } catch (e) {
      _logger.severe('Error loading linked feedback', e);
    }
  }

  @override
  void dispose() {
    // Leave chat session immediately when disposing
    _presenceManager.leaveChatSession();
    _presenceManager.dispose();

    WidgetsBinding.instance.removeObserver(this);
    _messageController.dispose();
    _scrollController.dispose();
    Get.delete<ChatState>();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Update presence based on app lifecycle
    switch (state) {
      case AppLifecycleState.resumed:
        _presenceManager.updatePresenceForLifecycle(isActive: true);
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        _presenceManager.updatePresenceForLifecycle(isActive: false);
        break;
      case AppLifecycleState.hidden:
        // Handle hidden state if needed
        _presenceManager.updatePresenceForLifecycle(isActive: false);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(widget.sessionTitle),
            Text(
              AppLocalizations.of(context)!.chat_supportTeam, // "Support Team"
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          if (_linkedFeedback != null)
            Container(
              padding: const EdgeInsets.all(12),
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context)!.related_feedback,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 4),
                        if (_linkedFeedback!.rating != null)
                          Row(
                            children: List.generate(5, (index) {
                              return Icon(
                                index < _linkedFeedback!.rating! ? Icons.star : Icons.star_border,
                                size: 16,
                                color: Colors.amber,
                              );
                            }),
                          ),
                        if (_linkedFeedback!.message.isNotEmpty)
                          Text(
                            _linkedFeedback!.message,
                            style: const TextStyle(fontSize: 14),
                          ),
                      ],
                    ),
                  ),
                  if (_linkedTrip != null)
                    TextButton(
                      onPressed: () {
                        Get.to(
                          () => TripDetailsPage(
                            trip: _linkedTrip!,
                            onNavigateToTab: (index) {
                              // Navigate back and potentially switch tabs if needed
                              Get.back();
                            },
                          ),
                        );
                      },
                      child: Text(
                        AppLocalizations.of(context)!.chat_trip_button, // "Trip"
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          Expanded(
            child: Obx(() {
              final messages = _chatState.messages;

              if (_chatState.isLoading.value && messages.isEmpty) {
                return const Center(child: CircularProgressIndicator());
              }

              return ListView.builder(
                controller: _scrollController,
                reverse: true,
                padding: const EdgeInsets.all(16),
                itemCount: messages.length,
                itemBuilder: (context, index) {
                  final message = messages[index];
                  final isMe = message.senderUid == _authState.currentMobileUser.value!.uid;

                  return Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
                      children: [
                        Container(
                          constraints: BoxConstraints(
                            maxWidth: MediaQuery.of(context).size.width * 0.7,
                          ),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: isMe ? Theme.of(context).primaryColor : Colors.grey[200],
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Column(
                            crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                            children: [
                              if (message.messageType == MessageType.image && message.imageUrl != null)
                                GestureDetector(
                                  onTap: () => _showFullImage(message.imageUrl!),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: StorageImage(
                                      pathOrUrl: message.imageUrl!,
                                      width: 200,
                                      height: 200,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              if (message.message.isNotEmpty)
                                Text(
                                  message.message,
                                  style: TextStyle(
                                    color: isMe ? Colors.white : Colors.black,
                                  ),
                                ),
                              const SizedBox(height: 4),
                              Text(
                                _formatTime(message.timestamp),
                                style: TextStyle(
                                  fontSize: 11,
                                  color: isMe ? Colors.white70 : Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            }),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.camera_alt),
                  onPressed: () => _pickAndSendImage(ImageSource.camera),
                ),
                IconButton(
                  icon: const Icon(Icons.photo),
                  onPressed: () => _pickAndSendImage(ImageSource.gallery),
                ),
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: AppLocalizations.of(context)!.chat_typeMessage, // "Type a message..."
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.white,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                const SizedBox(width: 8),
                CircleAvatar(
                  backgroundColor: Theme.of(context).primaryColor,
                  child: IconButton(
                    icon: const Icon(Icons.send, color: Colors.white),
                    onPressed: _sendMessage,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    _messageController.clear();
    await _chatState.sendMessage(message);
  }

  Future<void> _pickAndSendImage(ImageSource source) async {
    // Extract localized string before async operations
    final errorSendingImage = AppLocalizations.of(context)!.error_sending_image;

    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        // Show loading indicator
        Get.dialog(
          const Center(
            child: CircularProgressIndicator(),
          ),
          barrierDismissible: false,
        );

        // Upload image
        final file = File(image.path);
        final fileName = 'chat/${widget.sessionId}/${DateTime.now().millisecondsSinceEpoch}.jpg';
        final ref = _storage.ref().child(fileName);
        await ref.putFile(file);

        // Store only the path, not the full URL
        await _chatState.sendMessage('', imagePath: fileName);

        if (mounted) {
          Get.back(); // Close loading dialog
        }
      }
    } catch (e) {
      if (mounted) {
        Get.back(); // Close loading dialog if open
        Get.snackbar(
          'Error',
          errorSendingImage(e.toString()),
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  void _showFullImage(String pathOrUrl) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Stack(
          children: [
            StorageImage(
              pathOrUrl: pathOrUrl,
              fit: BoxFit.contain,
            ),
            Positioned(
              top: 8,
              right: 8,
              child: IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: () => Navigator.pop(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
