import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:permission_handler/permission_handler.dart';

import '../config/tenant_config.dart';
import '../l10n/app_localizations.dart';
import '../models/DriverDocument.dart';

class DocumentUploadScreen extends StatefulWidget {
  const DocumentUploadScreen({super.key});

  @override
  State<DocumentUploadScreen> createState() => _DocumentUploadScreenState();
}

class _DocumentUploadScreenState extends State<DocumentUploadScreen> {
  final Logger _logger = Logger('DocumentUploadScreen');
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final _formKey = GlobalKey<FormState>();

  DocumentType _selectedType = DocumentType.license;
  String _documentName = '';
  DateTime? _expiryDate;
  String? _notes;
  File? _selectedFile;
  bool _isUploading = false;

  Future<bool> _checkAndRequestPermissions(ImageSource source) async {
    // Extract localized strings before any async operations
    final permissionDenied = AppLocalizations.of(context)!.documentUpload_permissionDenied; // "Permission Denied"
    final cameraPermissionRequired =
        AppLocalizations.of(context)!.documentUpload_cameraPermissionRequired; // "Camera permission is required to take photos"
    final photoLibraryPermissionRequired = AppLocalizations.of(context)!
        .documentUpload_photoLibraryPermissionRequired; // "Photo library permission is required to select photos"
    final storagePermissionRequiredPhotos = AppLocalizations.of(context)!
        .documentUpload_storagePermissionRequiredPhotos; // "Storage permission is required to select photos"

    if (source == ImageSource.camera) {
      final status = await Permission.camera.request();
      if (!status.isGranted) {
        Get.snackbar(
          permissionDenied,
          cameraPermissionRequired,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } else {
      // For gallery on Android 13+
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        if (androidInfo.version.sdkInt >= 33) {
          final status = await Permission.photos.request();
          if (!status.isGranted) {
            Get.snackbar(
              permissionDenied,
              photoLibraryPermissionRequired,
              backgroundColor: Colors.red,
              colorText: Colors.white,
            );
            return false;
          }
        } else {
          final status = await Permission.storage.request();
          if (!status.isGranted) {
            Get.snackbar(
              permissionDenied,
              storagePermissionRequiredPhotos,
              backgroundColor: Colors.red,
              colorText: Colors.white,
            );
            return false;
          }
        }
      }
    }
    return true;
  }

  Future<void> _pickFile() async {
    // Extract localized strings before any async operations
    final permissionDenied = AppLocalizations.of(context)!.documentUpload_permissionDenied; // "Permission Denied"
    final storagePermissionRequiredFiles = AppLocalizations.of(context)!
        .documentUpload_storagePermissionRequiredFiles; // "Storage permission is required to select files"
    final fileTooLarge = AppLocalizations.of(context)!.documentUpload_fileTooLarge; // "File Too Large"
    final fileSizeLimit = AppLocalizations.of(context)!.documentUpload_fileSizeLimit; // "Please select a file smaller than 5MB"
    final platformError = AppLocalizations.of(context)!.documentUpload_platformError; // "Platform Error"
    final mainPageError = AppLocalizations.of(context)!.mainPage_error; // "Error"
    final failedToPickFile =
        AppLocalizations.of(context)!.documentUpload_failedToPickFile; // "Failed to pick file. Please try again."

    try {
      // Check storage permissions for older Android versions
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        if (androidInfo.version.sdkInt < 33) {
          // Android 12 and below
          final status = await Permission.storage.request();
          if (!status.isGranted) {
            Get.snackbar(
              permissionDenied,
              storagePermissionRequiredFiles,
              backgroundColor: Colors.red,
              colorText: Colors.white,
            );
            return;
          }
        }
      }

      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
        allowMultiple: false, // Explicitly set to false
        withData: false, // Use file path instead of bytes to save memory
        withReadStream: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final fileSize = await file.length();

        // Check file size (5MB limit)
        if (fileSize > 5 * 1024 * 1024) {
          Get.snackbar(
            fileTooLarge,
            fileSizeLimit,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          return;
        }

        setState(() {
          _selectedFile = file;
          if (_documentName.isEmpty) {
            _documentName = result.files.single.name;
          }
        });
      }
    } on PlatformException catch (e) {
      _logger.severe('Platform error in file picker: ${e.code} - ${e.message}');
      Get.snackbar(
        platformError,
        'Device error: ${e.message ?? 'Unknown error'}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } catch (e, stackTrace) {
      _logger.severe('Unexpected error in file picker', e, stackTrace);
      _logger.fine('Stack trace: $stackTrace');
      Get.snackbar(
        mainPageError,
        failedToPickFile,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _pickFromCamera() async {
    // Extract localized strings before any async operations
    final cameraError = AppLocalizations.of(context)!.documentUpload_cameraError; // "Camera Error"
    final mainPageError = AppLocalizations.of(context)!.mainPage_error; // "Error"
    final failedToCaptureImage =
        AppLocalizations.of(context)!.documentUpload_failedToCaptureImage; // "Failed to capture image. Please try again."

    try {
      // Check permissions first
      if (!await _checkAndRequestPermissions(ImageSource.camera)) {
        return;
      }

      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
        maxWidth: 1920, // Add max dimensions to prevent memory issues
        maxHeight: 1920,
      );

      if (pickedFile != null) {
        setState(() {
          _selectedFile = File(pickedFile.path);
          if (_documentName.isEmpty) {
            _documentName = '${_selectedType.displayName}_${DateTime.now().millisecondsSinceEpoch}.jpg';
          }
        });
      }
    } on PlatformException catch (e) {
      _logger.severe('Platform error in camera picker: ${e.code} - ${e.message}');
      Get.snackbar(
        cameraError,
        'Device error: ${e.message ?? 'Unknown error'}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } catch (e, stackTrace) {
      _logger.severe('Unexpected error in camera picker', e, stackTrace);
      _logger.fine('Stack trace: $stackTrace');
      Get.snackbar(
        mainPageError,
        failedToCaptureImage,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _selectExpiryDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 3650)), // 10 years
    );

    if (picked != null) {
      setState(() {
        _expiryDate = picked;
      });
    }
  }

  Future<void> _uploadDocument() async {
    // Extract localized strings before any async operations
    final noFileSelected = AppLocalizations.of(context)!.documentUpload_noFileSelected; // "No File Selected"
    final selectDocumentPrompt =
        AppLocalizations.of(context)!.documentUpload_selectDocumentPrompt; // "Please select a document to upload"
    final noExpiryDate = AppLocalizations.of(context)!.documentUpload_noExpiryDate; // "No Expiry Date"
    final selectExpiryDatePrompt =
        AppLocalizations.of(context)!.documentUpload_selectExpiryDatePrompt; // "Please select an expiry date for the document"
    final mapScreenSuccess = AppLocalizations.of(context)!.mapScreen_success; // "Success"
    final uploadSuccess = AppLocalizations.of(context)!.documentUpload_uploadSuccess; // "Document uploaded successfully"
    final uploadFailed = AppLocalizations.of(context)!.documentUpload_uploadFailed; // "Upload Failed"
    final unexpectedError =
        AppLocalizations.of(context)!.documentUpload_unexpectedError; // "An unexpected error occurred. Please try again."

    if (!_formKey.currentState!.validate()) return;
    if (_selectedFile == null) {
      Get.snackbar(
        noFileSelected,
        selectDocumentPrompt,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }
    if (_expiryDate == null) {
      Get.snackbar(
        noExpiryDate,
        selectExpiryDatePrompt,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    setState(() {
      _isUploading = true;
    });

    try {
      final userId = FirebaseAuth.instance.currentUser!.uid;
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = _selectedFile!.path.split('.').last;
      final fileName = '${timestamp}_${_selectedType.name}.$fileExtension';

      // Upload to Firebase Storage
      final ref = _storage.ref().child('documents/$userId/$fileName');
      await ref.putFile(_selectedFile!);
      // Store only the path, not the full URL
      final storagePath = 'documents/$userId/$fileName';

      // Create document in Firestore
      final document = DriverDocument(
        documentType: _selectedType,
        documentName: _documentName,
        fileURL: storagePath, // Store path instead of URL
        expiryDate: _expiryDate!,
        status: DocumentStatus.pendingReview,
        notes: _notes,
        uploadedAt: DateTime.now(),
        tenantIDs: [TenantConfig.TENANT_ID], // Add current tenant
      );

      await DriverDocument.driverDocumentsColl.add(document);

      Get.back();
      Get.snackbar(
        mapScreenSuccess,
        uploadSuccess,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } on FirebaseException catch (e) {
      _logger.severe('Firebase error during upload: ${e.code} - ${e.message}');
      Get.snackbar(
        uploadFailed,
        'Firebase error: ${e.message ?? 'Unknown error'}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } catch (e, stackTrace) {
      _logger.severe('Unexpected error during upload', e, stackTrace);
      _logger.fine('Stack trace: $stackTrace');
      Get.snackbar(
        uploadFailed,
        unexpectedError,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.documentUpload_title), // "Upload Document"
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Document Type
              Text(
                AppLocalizations.of(context)!.documentUpload_documentType, // "Document Type"
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<DocumentType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                ),
                items: DocumentType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.displayName),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),

              // Document Name
              TextFormField(
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context)!.documentUpload_documentName, // "Document Name"
                  border: const OutlineInputBorder(),
                  hintText:
                      AppLocalizations.of(context)!.documentUpload_examplePrefix(_selectedType.displayName), // "e.g., {example}"
                ),
                onChanged: (value) => _documentName = value,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppLocalizations.of(context)!.documentUpload_enterDocumentName; // "Please enter a document name"
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Expiry Date
              InkWell(
                onTap: _selectExpiryDate,
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context)!.documentUpload_expiryDate, // "Expiry Date"
                    border: const OutlineInputBorder(),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    _expiryDate != null
                        ? DateFormat.yMMMd().format(_expiryDate!)
                        : AppLocalizations.of(context)!.documentUpload_selectExpiryDate, // "Select expiry date"
                    style: TextStyle(
                      color: _expiryDate != null ? null : Colors.grey,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Notes (Optional)
              TextFormField(
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context)!.documentUpload_notesOptional, // "Notes (Optional)"
                  border: const OutlineInputBorder(),
                  hintText: AppLocalizations.of(context)!.documentUpload_additionalInfoHint, // "Any additional information"
                ),
                maxLines: 3,
                onChanged: (value) => _notes = value,
              ),
              const SizedBox(height: 24),

              // File Selection
              Text(
                AppLocalizations.of(context)!.documentUpload_selectDocument, // "Select Document"
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),

              if (_selectedFile != null) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.green),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _selectedFile!.path.split('/').last,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () {
                          setState(() {
                            _selectedFile = null;
                          });
                        },
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],

              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _pickFile,
                      icon: const Icon(Icons.folder_open),
                      label: Text(AppLocalizations.of(context)!.documentUpload_chooseFile), // "Choose File"
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _pickFromCamera,
                      icon: const Icon(Icons.camera_alt),
                      label: Text(AppLocalizations.of(context)!.documentUpload_takePhoto), // "Take Photo"
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                AppLocalizations.of(context)!.documentUpload_acceptedFormats, // "Accepted formats: PDF, JPG, PNG (Max 5MB)"
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey,
                    ),
              ),
              const SizedBox(height: 32),

              // Upload Button
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: _isUploading ? null : _uploadDocument,
                  child: _isUploading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(AppLocalizations.of(context)!.documentUpload_uploadButton), // "Upload Document"
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
