import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fiaranow_flutter/widgets/ButtonGroup.dart';
import 'package:fiaranow_flutter/widgets/TripActionButton.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logging/logging.dart';

import '../config/tenant_config.dart';
import '../l10n/app_localizations.dart';
import '../models/Feedback.dart' as feedback_model;
import '../models/Trip.dart';
import '../states/AuthState.dart';

class TripFeedbackScreen extends StatefulWidget {
  final Trip trip;

  const TripFeedbackScreen({super.key, required this.trip});

  @override
  State<TripFeedbackScreen> createState() => _TripFeedbackScreenState();
}

class _TripFeedbackScreenState extends State<TripFeedbackScreen> {
  final AuthState _authState = Get.find<AuthState>();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final ImagePicker _imagePicker = ImagePicker();
  final TextEditingController _messageController = TextEditingController();
  final Logger _logger = Logger('TripFeedbackScreen');

  int _rating = 0;
  String _message = '';
  final List<File> _selectedImages = [];
  bool _isSubmitting = false;
  feedback_model.Feedback? _existingFeedback;

  @override
  void initState() {
    super.initState();
    _checkExistingFeedback();
  }

  Future<void> _checkExistingFeedback() async {
    try {
      final querySnapshot = await _firestore
          .collection(TenantConfig.getTenantPath('feedbacks'))
          .where('tripId', isEqualTo: widget.trip.id)
          .where('uid', isEqualTo: _authState.currentMobileUser.value!.uid)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        if (mounted) {
          setState(() {
            _existingFeedback = feedback_model.Feedback.fromFirestore(querySnapshot.docs.first);
            _rating = _existingFeedback!.rating ?? 0;
            _messageController.text = _existingFeedback!.message;
          });
        }
      }
    } catch (e) {
      _logger.warning('Error checking existing feedback: $e');
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.trip_feedback_title),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.trip_details,
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            '${widget.trip.startLocationName ?? AppLocalizations.of(context)!.tripDetails_locationUnknown} → ${widget.trip.arrivalLocationName ?? AppLocalizations.of(context)!.tripDetails_locationUnknown}', // "Unknown"
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          _formatDate(widget.trip.createdAt),
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              AppLocalizations.of(context)!.rate_your_trip,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Center(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: List.generate(5, (index) {
                  return IconButton(
                    icon: Icon(
                      index < _rating ? Icons.star : Icons.star_border,
                      size: 40,
                      color: index < _rating ? Colors.amber : Colors.grey,
                    ),
                    onPressed: _existingFeedback == null
                        ? () {
                            setState(() {
                              _rating = index + 1;
                            });
                          }
                        : null,
                  );
                }),
              ),
            ),
            const SizedBox(height: 24),
            TextField(
              controller: _messageController,
              maxLines: 4,
              enabled: _existingFeedback == null,
              textAlignVertical: TextAlignVertical.top,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.feedback_message,
                hintText: AppLocalizations.of(context)!.feedback_message_hint,
                alignLabelWithHint: true,
                border: const OutlineInputBorder(),
              ),
              onChanged: (value) => _message = value,
            ),
            const SizedBox(height: 16),
            if (_existingFeedback == null) ...[
              Text(
                AppLocalizations.of(context)!.add_photos,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              MediaPickerButtons(
                cameraButton: TripActionButton(
                  label: AppLocalizations.of(context)!.feedback_camera, // "Camera"
                  variant: ButtonVariant.secondary,
                  icon: Icons.camera_alt,
                  onPressed: () => _pickImage(ImageSource.camera),
                ),
                galleryButton: TripActionButton(
                  label: AppLocalizations.of(context)!.feedback_gallery, // "Gallery"
                  variant: ButtonVariant.secondary,
                  icon: Icons.photo_library,
                  onPressed: () => _pickImage(ImageSource.gallery),
                ),
              ),
              const SizedBox(height: 16),
              if (_selectedImages.isNotEmpty) ...[
                SizedBox(
                  height: 100,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _selectedImages.length,
                    itemBuilder: (context, index) {
                      return Stack(
                        children: [
                          Container(
                            margin: const EdgeInsets.only(right: 8),
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              image: DecorationImage(
                                image: FileImage(_selectedImages[index]),
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          Positioned(
                            top: 4,
                            right: 12,
                            child: InkWell(
                              onTap: () {
                                setState(() {
                                  _selectedImages.removeAt(index);
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  size: 16,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ],
            if (_existingFeedback != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.check_circle, color: Colors.green),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        AppLocalizations.of(context)!.feedback_already_submitted,
                        style: const TextStyle(color: Colors.green),
                      ),
                    ),
                  ],
                ),
              ),
            ] else ...[
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _rating > 0 && !_isSubmitting ? _submitFeedback : null,
                  child: _isSubmitting ? const CircularProgressIndicator() : Text(AppLocalizations.of(context)!.submit_feedback),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    if (_selectedImages.length >= 5) {
      if (mounted) {
        Get.snackbar(
          AppLocalizations.of(context)!.mainPage_error, // "Error"
          AppLocalizations.of(context)!.max_images_reached,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
      }
      return;
    }

    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        if (mounted) {
          setState(() {
            _selectedImages.add(File(image.path));
          });
        }
      }
    } catch (e) {
      if (mounted) {
        Get.snackbar(
          AppLocalizations.of(context)!.mainPage_error, // "Error"
          AppLocalizations.of(context)!.error_picking_image(e.toString()), // "Error picking image: $e"
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  Future<List<String>> _uploadImages() async {
    List<String> uploadedPaths = [];

    for (int i = 0; i < _selectedImages.length; i++) {
      final file = _selectedImages[i];
      final fileName = 'feedback/trip/${widget.trip.id}/${DateTime.now().millisecondsSinceEpoch}_$i.jpg';

      try {
        final ref = _storage.ref().child(fileName);
        await ref.putFile(file);
        uploadedPaths.add(fileName);
      } catch (e) {
        _logger.warning('Error uploading image: $e');
      }
    }

    return uploadedPaths;
  }

  Future<void> _submitFeedback() async {
    if (_rating == 0) {
      if (mounted) {
        Get.snackbar(
          AppLocalizations.of(context)!.mainPage_error, // "Error"
          AppLocalizations.of(context)!.please_rate_trip,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final imagePaths = await _uploadImages();
      final currentUser = _authState.currentMobileUser.value!;

      await _firestore.collection(TenantConfig.getTenantPath('feedbacks')).add({
        'uid': currentUser.uid,
        'type': feedback_model.FeedbackType.trip.toString().split('.').last,
        'tripId': widget.trip.id,
        'rating': _rating,
        'message': _message,
        'images': imagePaths,
        'createdAt': FieldValue.serverTimestamp(),
        'status': feedback_model.FeedbackStatus.newFeedback.toString().split('.').last,
      });

      if (mounted) {
        Get.snackbar(
          AppLocalizations.of(context)!.mapScreen_success, // "Success"
          AppLocalizations.of(context)!.feedback_submitted_success,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        Get.snackbar(
          AppLocalizations.of(context)!.mainPage_error, // "Error"
          AppLocalizations.of(context)!.error_submitting_feedback(e.toString()), // "Error submitting feedback: $e"
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}
