import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:fiaranow_flutter/models/Trip.dart';
import 'package:fiaranow_flutter/models/TripStatus.dart';
import 'package:fiaranow_flutter/states/AuthState.dart';
import 'package:fiaranow_flutter/widgets/ButtonGroup.dart';
import 'package:fiaranow_flutter/widgets/PriceDisplay.dart';
import 'package:fiaranow_flutter/widgets/TripActionButton.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../models/MobileUser.dart';
import '../states/NavigationState.dart';
import 'TripFeedbackScreen.dart';

class TripDetailsPage extends StatefulWidget {
  final Trip trip;
  final void Function(dynamic index) onNavigateToTab;

  const TripDetailsPage({
    super.key,
    required this.trip,
    required this.onNavigateToTab,
  });

  @override
  State<TripDetailsPage> createState() => _TripDetailsPageState();
}

class _TripDetailsPageState extends State<TripDetailsPage> {
  late final AuthState authState = Get.find<AuthState>();

  @override
  Widget build(BuildContext context) {
    final bool isFullDayTrip = widget.trip.reservationType == ReservationType.fullDay;
    final calculationData = widget.trip.finalRouteData ?? widget.trip.routeData;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.tripDetails_title),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with status
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _getStatusColor(widget.trip.status).withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getStatusIcon(widget.trip.status),
                        color: _getStatusColor(widget.trip.status),
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        child: Text(
                          widget.trip.status.getLocalizedName(context), // "Preparing", "Reserved", "In Progress", etc.
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: _getStatusColor(widget.trip.status),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      if (widget.trip.inTakeSource == InTakeSource.reservation)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: isDarkMode ? Colors.purple.withValues(alpha: 0.3) : Colors.purple.withValues(alpha: 0.15),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: isDarkMode ? Colors.purple.withValues(alpha: 0.6) : Colors.purple.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                isFullDayTrip ? Icons.calendar_today : Icons.calendar_month,
                                color: isDarkMode ? Colors.purple.shade200 : Colors.purple,
                                size: 14,
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    isFullDayTrip
                        ? (widget.trip.startLocationName?.isNotEmpty == true
                            ? widget.trip.startLocationName!
                            : AppLocalizations.of(context)!.mapScreen_fullDayReservation) // "Full Day Reservation"
                        : (widget.trip.arrivalLocationName ??
                            AppLocalizations.of(context)!.tripDetails_unknownDestination), // "Unknown destination"
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (widget.trip.pickupTime != null) ...[
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(Icons.access_time, size: 16, color: Colors.grey),
                        const SizedBox(width: 8),
                        Text(
                          DateFormat('yyyy-MM-dd HH:mm').format(widget.trip.pickupTime!),
                          style: const TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Price section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Card(
                elevation: 2,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocalizations.of(context)!.tripDetails_priceDetails, // "Price Details"
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      if (isFullDayTrip) ...[
                        _buildDetailRow(
                          icon: Icons.euro,
                          title: AppLocalizations.of(context)!.tripDetails_pricingOption, // "Pricing Option"
                          value: widget.trip.fullDayPriceType == FullDayPriceType.fixed
                              ? AppLocalizations.of(context)!.mapScreen_gasIncluded // "Gas included in price"
                              : AppLocalizations.of(context)!.mapScreen_gasNotIncluded, // "You pay for gas separately"
                        ),
                        if (widget.trip.distanceTotalMeters != null)
                          _buildDetailRow(
                            icon: Icons.straighten,
                            title: AppLocalizations.of(context)!.tripDetails_distance,
                            value: '${(widget.trip.distanceTotalMeters! / 1000).toStringAsFixed(2)} km',
                          ),
                        _buildDetailRow(
                          icon: Icons.payments,
                          title: AppLocalizations.of(context)!.tripDetails_cost,
                          value: widget.trip.fullDayPriceType == FullDayPriceType.fixed
                              ? '€75'
                              : widget.trip.costDistance != null
                                  ? '' // Empty value because we'll use valueWidget
                                  : '€25',
                          valueWidget: widget.trip.fullDayPriceType != FullDayPriceType.fixed && widget.trip.costDistance != null
                              ? Row(
                                  children: [
                                    const Text('€25 + ', style: TextStyle(fontWeight: FontWeight.bold)),
                                    PriceDisplay.bold(
                                      amount: widget.trip.costDistance,
                                      currency: widget.trip.realCostCurrency ?? 'MGA',
                                    ),
                                  ],
                                )
                              : null,
                          valueColor: Theme.of(context).brightness == Brightness.dark
                              ? Colors.greenAccent
                              : Theme.of(context).primaryColor,
                          valueBold: true,
                        ),
                      ] else if (calculationData != null) ...[
                        _buildDetailRow(
                          icon: Icons.straighten,
                          title: AppLocalizations.of(context)!.tripDetails_distance,
                          value: '${calculationData.distanceKm.toStringAsFixed(1)} km',
                        ),
                        _buildDetailRow(
                          icon: Icons.timer,
                          title: AppLocalizations.of(context)!.tripDetails_duration,
                          value: '${(calculationData.durationSec / 60).toStringAsFixed(0)} min',
                        ),
                        _buildDetailRow(
                          icon: Icons.payments,
                          title: AppLocalizations.of(context)!.tripDetails_cost,
                          value: widget.trip.costTotal != null ? '' : 'Calculating...',
                          valueWidget: widget.trip.costTotal != null
                              ? PriceDisplay.bold(
                                  amount: widget.trip.costTotal,
                                  currency: widget.trip.realCostCurrency ?? 'MGA',
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.greenAccent
                                      : Theme.of(context).primaryColor,
                                )
                              : null,
                          valueColor: Theme.of(context).brightness == Brightness.dark
                              ? Colors.greenAccent
                              : Theme.of(context).primaryColor,
                          valueBold: true,
                        ),
                      ] else if (widget.trip.costTotal != null) ...[
                        _buildDetailRow(
                          icon: Icons.payments,
                          title: AppLocalizations.of(context)!.tripDetails_cost,
                          value: '',
                          valueWidget: PriceDisplay.bold(
                            amount: widget.trip.costTotal,
                            currency: widget.trip.realCostCurrency ?? 'MGA',
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.greenAccent
                                : Theme.of(context).primaryColor,
                          ),
                          valueColor: Theme.of(context).brightness == Brightness.dark
                              ? Colors.greenAccent
                              : Theme.of(context).primaryColor,
                          valueBold: true,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Trip details section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Card(
                elevation: 2,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocalizations.of(context)!.tripDetails_title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildDetailRow(
                        icon: Icons.person,
                        title: AppLocalizations.of(context)!.tripDetails_passenger,
                        value: widget.trip.passenger['displayName'] ?? 'No Name',
                      ),
                      _buildDetailRow(
                        icon: Icons.group,
                        title: AppLocalizations.of(context)!.tripDetails_passengerCount, // "Passenger Count"
                        value: widget.trip.passengerCount.toString(),
                      ),
                      if (widget.trip.driver != null)
                        _buildDetailRow(
                          icon: Icons.drive_eta,
                          title: AppLocalizations.of(context)!.mainPage_driver,
                          value: widget.trip.driver!['displayName'] ?? 'No Name',
                        ),
                      _buildDetailRow(
                        icon: Icons.calendar_today,
                        title: AppLocalizations.of(context)!.tripDetails_createdAt,
                        value: DateFormat('yyyy-MM-dd HH:mm').format(widget.trip.createdAt),
                      ),
                      if (widget.trip.completedAt != null)
                        _buildDetailRow(
                          icon: Icons.check_circle,
                          title: AppLocalizations.of(context)!.tripDetails_completedAt,
                          value: DateFormat('yyyy-MM-dd HH:mm').format(widget.trip.completedAt!),
                        ),
                      if (widget.trip.cancelledAt != null)
                        _buildDetailRow(
                          icon: Icons.cancel,
                          title: AppLocalizations.of(context)!.tripDetails_cancelledAt,
                          value: DateFormat('yyyy-MM-dd HH:mm').format(widget.trip.cancelledAt!),
                        ),
                    ],
                  ),
                ),
              ),
            ),

            if (!isFullDayTrip && (widget.trip.startLocation != null || widget.trip.arrivalLocation != null)) ...[
              const SizedBox(height: 16),

              // Location details section
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context)!.tripDetails_locationDetails,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        if (widget.trip.startLocation != null)
                          _buildDetailRow(
                            icon: Icons.location_on,
                            title: AppLocalizations.of(context)!.tripDetails_startLocation,
                            value: widget.trip.startLocationName ??
                                'Lat: ${widget.trip.startLocation!.lat.toStringAsFixed(6)}, Lon: ${widget.trip.startLocation!.lon.toStringAsFixed(6)}',
                          ),
                        if (widget.trip.arrivalLocation != null)
                          _buildDetailRow(
                            icon: Icons.location_on,
                            title: AppLocalizations.of(context)!.tripDetails_arrivalLocation,
                            value: widget.trip.arrivalLocationName ??
                                'Lat: ${widget.trip.arrivalLocation!.lat.toStringAsFixed(6)}, Lon: ${widget.trip.arrivalLocation!.lon.toStringAsFixed(6)}',
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ],

            const SizedBox(height: 24),

            // Action buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  // Primary action buttons row
                  if (widget.trip.status == TripStatus.preparing ||
                      (widget.trip.status != TripStatus.cancelled &&
                          widget.trip.status != TripStatus.preparing &&
                          widget.trip.startLocation != null))
                    ButtonGroup.horizontal(
                      children: [
                        // Delete button for preparing trips
                        if (widget.trip.status == TripStatus.preparing)
                          TripActionButton(
                            label: AppLocalizations.of(context)!.tripDetails_deleteTrip,
                            variant: ButtonVariant.danger,
                            icon: Icons.delete,
                            onPressed: () async {
                              FirebaseAnalytics.instance.logEvent(
                                name: 'delete_trip_initiated',
                                parameters: {'widget_name': 'trip_details'},
                              );
                              final confirm = await Get.dialog<bool>(
                                AlertDialog(
                                  title: Text(AppLocalizations.of(context)!.tripDetails_confirmDelete), // "Confirm Delete"
                                  content: Text(AppLocalizations.of(context)!.tripDetails_confirmDeleteMessage), // "Are you sure you want to delete this trip?"
                                  actions: [
                                    TextButton(
                                      onPressed: () => Get.back(result: false),
                                      child: Text(AppLocalizations.of(context)!.tripDetails_cancelButton), // "Cancel"
                                    ),
                                    TextButton(
                                      onPressed: () => Get.back(result: true),
                                      child: Text(AppLocalizations.of(context)!.tripDetails_deleteButton), // "Delete"
                                    ),
                                  ],
                                ),
                                barrierDismissible: false,
                              );
                              if (confirm == true) {
                                FirebaseAnalytics.instance.logEvent(
                                  name: 'delete_trip_confirmed',
                                  parameters: {'widget_name': 'trip_details'},
                                );
                                widget.trip.deleteTrip();
                                Get.back(); // Return to previous screen
                              }
                            },
                          ),

                        // Show on map button
                        if ((isFullDayTrip && widget.trip.startLocation != null) ||
                            (!isFullDayTrip &&
                                widget.trip.status != TripStatus.cancelled &&
                                widget.trip.status != TripStatus.preparing &&
                                widget.trip.startLocation != null &&
                                (widget.trip.arrivalLocation != null || widget.trip.status == TripStatus.reserved)))
                          TripActionButton(
                            label: AppLocalizations.of(context)!.tripDetails_showOnMap,
                            variant: ButtonVariant.primary,
                            icon: Icons.map,
                            onPressed: () {
                              FirebaseAnalytics.instance.logEvent(
                                name: 'show_trip_on_map',
                                parameters: {
                                  'widget_name': 'trip_details',
                                  'trip_status': widget.trip.status.toString(),
                                  'trip_type': widget.trip.pickupTime != null ? 'reserved' : 'immediate',
                                  'is_full_day':
                                      widget.trip.reservationType == ReservationType.fullDay ? 'true' : 'false',
                                },
                              );

                              // For on-demand viewing, directly show the trip without any state changes
                              Get.back();
                              Get.find<NavigationState>().showTripOnMap(widget.trip);
                              widget.onNavigateToTab(0);
                            },
                          ),
                      ],
                    ),

                  // Feedback button for completed trips (only for passengers)
                  if ((widget.trip.status == TripStatus.completed || widget.trip.status == TripStatus.paid) &&
                      authState.currentMobileUser.value?.primaryUserType == UserType.rider) ...[
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: TripActionButton(
                        label: AppLocalizations.of(context)!.trip_feedback_button, // "Give Feedback"
                        variant: ButtonVariant.warning,
                        icon: Icons.star,
                        onPressed: () {
                          FirebaseAnalytics.instance.logEvent(
                            name: 'trip_feedback_opened',
                            parameters: {
                              'widget_name': 'trip_details',
                              'trip_id': widget.trip.id,
                            },
                          );
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => TripFeedbackScreen(trip: widget.trip),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String title,
    required String value,
    Widget? valueWidget,
    Color? valueColor,
    bool valueBold = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 18, color: Colors.grey),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                if (valueWidget != null)
                  valueWidget
                else
                  SelectableText(
                    value,
                    style: TextStyle(
                      fontSize: 16,
                      color: valueColor,
                      fontWeight: valueBold ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(TripStatus status) {
    if (status == TripStatus.completed || status == TripStatus.paid) {
      return Colors.green;
    } else if (status == TripStatus.cancelled) {
      return Colors.red;
    } else if (status == TripStatus.inProgress) {
      return Colors.blue;
    } else if (status == TripStatus.reserved) {
      return Colors.purple;
    }
    return Colors.grey;
  }

  IconData _getStatusIcon(TripStatus status) {
    if (status == TripStatus.completed || status == TripStatus.paid) {
      return Icons.check_circle;
    } else if (status == TripStatus.cancelled) {
      return Icons.cancel;
    } else if (status == TripStatus.inProgress) {
      return Icons.directions_car;
    } else if (status == TripStatus.reserved) {
      return Icons.access_time;
    } else if (status == TripStatus.driverAwaiting) {
      return Icons.person_pin_circle;
    } else if (status == TripStatus.driverApproaching) {
      return Icons.near_me;
    }
    return Icons.info;
  }
}
