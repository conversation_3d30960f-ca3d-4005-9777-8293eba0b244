import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

import '../l10n/app_localizations.dart';
import '../services/NotificationStateService.dart';
import '../widgets/NotificationListItem.dart';

class NotificationsListScreen extends StatelessWidget {
  NotificationsListScreen({super.key});

  final Logger _logger = Logger('NotificationsListScreen');
  final NotificationStateService _notificationService = Get.find<NotificationStateService>();
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final AppLocalizations localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.notification_title),
        actions: [
          Obx(() {
            if (_notificationService.unreadCount.value > 0) {
              return TextButton(
                onPressed: () async {
                  await _notificationService.markAllAsRead();
                },
                child: Text(
                  localizations.notification_markAllAsRead,
                  style: const TextStyle(color: Colors.white),
                ),
              );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              onChanged: (value) {
                _notificationService.searchQuery.value = value;
              },
              decoration: InputDecoration(
                hintText: localizations.notification_searchPlaceholder,
                prefixIcon: const Icon(Icons.search),
                suffixIcon: Obx(() {
                  if (_notificationService.searchQuery.value.isNotEmpty) {
                    return IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _notificationService.clearSearch();
                      },
                    );
                  }
                  return const SizedBox.shrink();
                }),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          
          // Notifications list
          Expanded(
            child: Obx(() {
              if (_notificationService.isLoading.value) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }
              
              if (_notificationService.hasError.value) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        localizations.notification_errorLoading,
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () {
                          _notificationService.refreshNotifications();
                        },
                        child: Text(localizations.notification_retry),
                      ),
                    ],
                  ),
                );
              }
              
              final notifications = _notificationService.filteredNotifications;
              
              if (notifications.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.notifications_none,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _notificationService.searchQuery.value.isNotEmpty
                            ? localizations.notification_noResultsFound
                            : localizations.notification_empty,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                );
              }
              
              return RefreshIndicator(
                onRefresh: () async {
                  _notificationService.refreshNotifications();
                },
                child: ListView.builder(
                  itemCount: notifications.length,
                  itemBuilder: (context, index) {
                    final notification = notifications[index];
                    return NotificationListItem(
                      notification: notification,
                      onTap: () {
                        _logger.info('Tapped notification: ${notification.id}');
                        _notificationService.markAsReadAndNavigate(notification.id);
                      },
                    );
                  },
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}