import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

import '../l10n/app_localizations.dart';
import '../models/MobileUserNotification.dart';
import '../services/NotificationStateService.dart';

class NotificationDetailScreen extends StatefulWidget {
  const NotificationDetailScreen({super.key});

  @override
  State<NotificationDetailScreen> createState() => _NotificationDetailScreenState();
}

class _NotificationDetailScreenState extends State<NotificationDetailScreen> {
  final Logger _logger = Logger('NotificationDetailScreen');
  final NotificationStateService _notificationService = Get.find<NotificationStateService>();
  late MobileUserNotification notification;
  bool _markedAsRead = false;

  @override
  void initState() {
    super.initState();
    
    // Get notification from arguments
    final args = Get.arguments;
    if (args is MobileUserNotification) {
      notification = args;
    } else if (args is String) {
      // If passed a notification ID, get it from service
      final notificationFromService = _notificationService.getNotificationById(args);
      if (notificationFromService != null) {
        notification = notificationFromService;
      } else {
        // Handle error - notification not found
        Get.back();
        return;
      }
    } else {
      // Handle error - invalid arguments
      Get.back();
      return;
    }
    
    // Auto-mark as read
    _markAsReadIfNeeded();
  }

  Future<void> _markAsReadIfNeeded() async {
    if (!notification.isRead && !_markedAsRead) {
      _markedAsRead = true;
      try {
        await _notificationService.markAsRead(notification.id);
        _logger.info('Marked notification as read: ${notification.id}');
      } catch (e) {
        _logger.severe('Error marking notification as read', e);
      }
    }
  }

  void _handleAction() {
    if (notification.clickAction != null && notification.clickAction!.isNotEmpty) {
      _logger.info('Navigating to: ${notification.clickAction}');
      
      // Handle different action types
      if (notification.clickAction!.startsWith('/trip/')) {
        // Extract trip ID and navigate
        final tripId = notification.clickAction!.replaceFirst('/trip/', '');
        Get.toNamed('/trip-details', arguments: {'tripId': tripId});
      } else {
        // Generic navigation
        Get.toNamed(notification.clickAction!);
      }
    } else if (notification.isTripRelated && notification.tripId != null) {
      // Navigate to trip details
      Get.toNamed('/trip-details', arguments: {'tripId': notification.tripId!});
    }
  }

  @override
  Widget build(BuildContext context) {
    final AppLocalizations localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.notificationDetail_title),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Notification icon and type
              Center(
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: _getIconBackgroundColor(notification.type),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getNotificationIcon(notification.type),
                    color: Colors.white,
                    size: 40,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              
              // Title
              Text(
                notification.title,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              
              // Time
              Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      notification.displayTime,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              
              // Body
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  notification.body,
                  style: const TextStyle(
                    fontSize: 16,
                    height: 1.5,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              
              // Additional info
              if (notification.data.isNotEmpty) ...[
                Text(
                  localizations.notificationDetail_additionalInfo,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                ..._buildAdditionalInfo(context),
                const SizedBox(height: 24),
              ],
              
              // Action button
              if (notification.clickAction != null || notification.isTripRelated) ...[
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _handleAction,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      _getActionButtonText(localizations),
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildAdditionalInfo(BuildContext context) {
    final List<Widget> widgets = [];
    
    notification.data.forEach((key, value) {
      // Skip internal fields
      if (key.startsWith('_') || key == 'notificationId' || key == 'systemNotificationId') {
        return;
      }
      
      widgets.add(
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${_formatKey(key)}:',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  value.toString(),
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ),
            ],
          ),
        ),
      );
    });
    
    return widgets;
  }

  String _formatKey(String key) {
    // Convert camelCase to Title Case
    return key
        .replaceAllMapped(
          RegExp(r'([A-Z])'),
          (match) => ' ${match.group(1)}',
        )
        .trim()
        .split(' ')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  String _getActionButtonText(AppLocalizations localizations) {
    if (notification.isTripRelated) {
      return localizations.notificationDetail_viewTrip;
    }
    
    switch (notification.type) {
      case 'document_expiry':
        return localizations.notificationDetail_updateDocuments;
      case 'reservation_reminder':
        return localizations.notificationDetail_viewReservation;
      default:
        return localizations.notificationDetail_viewDetails;
    }
  }

  Color _getIconBackgroundColor(String type) {
    switch (type) {
      case 'driver_moving':
      case 'driver_arrived':
        return Colors.blue;
      case 'trip_paid':
        return Colors.green;
      case 'reservation_reminder':
        return Colors.orange;
      case 'driver_timeout':
        return Colors.red;
      case 'document_expiry':
        return Colors.deepOrange;
      case 'trip_request':
        return Colors.indigo;
      case 'trip_cancelled':
        return Colors.red;
      case 'trip_completed':
        return Colors.green;
      case 'system_update':
        return Colors.purple;
      case 'general':
      default:
        return Colors.grey;
    }
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'driver_moving':
        return Icons.directions_car;
      case 'driver_arrived':
        return Icons.location_on;
      case 'trip_paid':
        return Icons.payment;
      case 'reservation_reminder':
        return Icons.alarm;
      case 'driver_timeout':
        return Icons.timer_off;
      case 'document_expiry':
        return Icons.warning;
      case 'trip_request':
        return Icons.notifications_active;
      case 'trip_cancelled':
        return Icons.cancel;
      case 'trip_completed':
        return Icons.check_circle;
      case 'system_update':
        return Icons.system_update;
      case 'general':
      default:
        return Icons.notifications;
    }
  }
}