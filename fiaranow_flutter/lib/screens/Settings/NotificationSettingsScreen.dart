import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../l10n/app_localizations.dart';
import '../../models/UserPreferences.dart';
import '../../services/UserPreferencesService.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  final UserPreferencesService preferencesService = Get.find<UserPreferencesService>();
  bool _hasTimedOut = false;

  @override
  void initState() {
    super.initState();
    // Add a timeout to prevent infinite loading
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted && preferencesService.userPreferences.value == null) {
        setState(() {
          _hasTimedOut = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.notificationSettings),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: Obx(() {
        final preferences = preferencesService.userPreferences.value;

        // Show error state if timed out
        if (_hasTimedOut && preferences == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red[300],
                ),
                const SizedBox(height: 16),
                Text(
                  AppLocalizations.of(context)!.notificationSettings_failedToLoadPreferences,
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.red[700],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  AppLocalizations.of(context)!.notificationSettings_checkConnectionAndRetry,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      _hasTimedOut = false;
                    });
                    // Try to reload preferences
                    preferencesService.onInit();
                  },
                  icon: const Icon(Icons.refresh),
                  label: Text(AppLocalizations.of(context)!.notificationSettings_retry),
                ),
              ],
            ),
          );
        }

        // Show loading while preferences are null
        if (preferences == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  AppLocalizations.of(context)!.notificationSettings_loadingPreferences,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        }

        final settings = preferences.notificationSettings;

        return ListView(
          children: [
            _buildSectionHeader(AppLocalizations.of(context)!.notificationSettings_general),
            _buildRingtoneTile(context, settings),
            const Divider(),
            _buildSectionHeader(AppLocalizations.of(context)!.notificationSettings_tripNotifications),
            _buildNotificationTile(
              context,
              title: AppLocalizations.of(context)!.notificationSettings_driverMoving,
              subtitle: AppLocalizations.of(context)!.notificationSettings_driverMovingDesc,
              value: settings.enableDriverMovingNotification,
              onChanged: (value) => _updateSetting(
                settings,
                enableDriverMoving: value,
              ),
            ),
            _buildNotificationTile(
              context,
              title: AppLocalizations.of(context)!.notificationSettings_driverArrived,
              subtitle: AppLocalizations.of(context)!.notificationSettings_driverArrivedDesc,
              value: settings.enableDriverArrivedNotification,
              onChanged: (value) => _updateSetting(
                settings,
                enableDriverArrived: value,
              ),
            ),
            _buildNotificationTile(
              context,
              title: AppLocalizations.of(context)!.notificationSettings_paymentCompleted,
              subtitle: AppLocalizations.of(context)!.notificationSettings_paymentCompletedDesc,
              value: settings.enableTripPaidNotification,
              onChanged: (value) => _updateSetting(
                settings,
                enableTripPaid: value,
              ),
            ),
            const Divider(),
            _buildSectionHeader(AppLocalizations.of(context)!.notificationSettings_reservations),
            _buildNotificationTile(
              context,
              title: AppLocalizations.of(context)!.notificationSettings_reservationReminders,
              subtitle: AppLocalizations.of(context)!.notificationSettings_reservationRemindersDesc,
              value: settings.enableReservationReminders,
              onChanged: (value) => _updateSetting(
                settings,
                enableReservationReminders: value,
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.grey[600],
        ),
      ),
    );
  }

  Widget _buildRingtoneTile(BuildContext context, NotificationSettings settings) {
    return Obx(() {
      final adminDefault = preferencesService.adminRingtoneDefault.value ?? true;
      final effectiveSetting = settings.enableRingtone ?? adminDefault;

      return ListTile(
        title: Text(AppLocalizations.of(context)!.ringtoneNotifications),
        subtitle: Text(AppLocalizations.of(context)!.ringtoneNotificationsDesc),
        trailing: Switch(
          value: effectiveSetting,
          onChanged: (value) {
            // If switching to the same as default, set to null
            final shouldUseDefault = value == adminDefault;
            preferencesService.updateRingtoneSetting(
              shouldUseDefault ? null : value,
            );
          },
        ),
      );
    });
  }

  Widget _buildNotificationTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
      ),
    );
  }

  void _updateSetting(
    NotificationSettings currentSettings, {
    bool? enableDriverMoving,
    bool? enableDriverArrived,
    bool? enableTripPaid,
    bool? enableReservationReminders,
  }) {
    final updatedSettings = NotificationSettings(
      enableRingtone: currentSettings.enableRingtone,
      enableDriverMovingNotification: enableDriverMoving ?? currentSettings.enableDriverMovingNotification,
      enableDriverArrivedNotification: enableDriverArrived ?? currentSettings.enableDriverArrivedNotification,
      enableTripPaidNotification: enableTripPaid ?? currentSettings.enableTripPaidNotification,
      enableReservationReminders: enableReservationReminders ?? currentSettings.enableReservationReminders,
    );

    preferencesService.updateNotificationSettings(updatedSettings);
  }
}
