import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../services/UserPreferencesService.dart';

class RingtonePermissionDialog extends StatelessWidget {
  final UserPreferencesService preferencesService = Get.find<UserPreferencesService>();

  RingtonePermissionDialog({super.key});

  static Future<void> showIfNeeded() async {
    final preferencesService = Get.find<UserPreferencesService>();
    final prefs = preferencesService.userPreferences.value;

    // Only show if user hasn't set their preference yet
    if (prefs != null && prefs.notificationSettings.enableRingtone == null) {
      // Check if admin has disabled ringtone by default
      final effectiveSetting = await preferencesService.getEffectiveRingtoneSetting();
      if (!effectiveSetting) {
        // Admin has disabled ringtone by default, ask user if they want to enable it
        await Get.dialog(
          RingtonePermissionDialog(),
          barrierDismissible: false,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('ringtonePermissionTitle'.tr),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('ringtonePermissionMessage'.tr),
          const SizedBox(height: 16),
          Text(
            'ringtonePermissionDescription'.tr,
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () async {
            // Keep the default (no ringtone)
            await preferencesService.updateRingtoneSetting(false);
            Get.back();
          },
          child: Text('noThanks'.tr),
        ),
        ElevatedButton(
          onPressed: () async {
            // Enable ringtone for this user
            await preferencesService.updateRingtoneSetting(true);
            Get.back();
          },
          child: Text('enableRingtone'.tr),
        ),
      ],
    );
  }
}
