import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

enum UserType { rider, driver }

class MobileUser {
  final String uid;
  final String? email;
  final String? displayName;
  final String? phoneNumber;
  final String? photoURL;
  final DateTime? lastSeen;
  final UserType? primaryUserType;
  final Locale? primaryLanguage;
  final Map<String, bool>? isServiceActiveByTenant;
  final DocumentReference ref;
  final String? isDriverConfirmed;
  final double? lat;
  final double? lon;
  final String? deviceId;

  // occupiedByTripId is only set when the Driver has accepted a Trip. This is what allows the Driver to be excluded from the list
  // of available Drivers, with `isServiceActive` still being true.
  final String? occupiedByTripId;

  // List of tags assigned to this driver (e.g., "external", "truck", "premium")
  final List<String>? driverTags;

  // Array of tenant IDs for performance optimization
  final List<String>? tenantIDs;

  MobileUser({
    required this.uid,
    this.email,
    this.displayName,
    this.phoneNumber,
    this.photoURL,
    this.primaryUserType,
    this.primaryLanguage,
    this.lastSeen,
    this.isServiceActiveByTenant,
    required this.ref,
    this.isDriverConfirmed,
    this.lat,
    this.lon,
    this.occupiedByTripId,
    this.deviceId,
    this.driverTags,
    this.tenantIDs,
  });

  factory MobileUser.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;

    // Ensure certain fields exist
    if (!data.containsKey('occupiedByTripId')) {
      doc.reference.update({'occupiedByTripId': null});
    }

    return MobileUser(
      uid: data['uid'],
      email: data['email'],
      displayName: data['displayName'],
      phoneNumber: data['phoneNumber'],
      photoURL: data['photoURL'],
      primaryUserType: data['primaryUserType'] != null ? UserType.values[data['primaryUserType']] : null,
      primaryLanguage: data['primaryLanguage'] != null
          ? Locale(data['primaryLanguage']['languageCode'], data['primaryLanguage']['countryCode'])
          : null,
      lastSeen: data['lastSeen'] != null ? (data['lastSeen'] as Timestamp).toDate() : null,
      isServiceActiveByTenant:
          data['isServiceActiveByTenant'] != null ? Map<String, bool>.from(data['isServiceActiveByTenant']) : null,
      ref: doc.reference,
      isDriverConfirmed: data['isDriverConfirmed'],
      lat: data['lat'],
      lon: data['lon'],
      occupiedByTripId: data['occupiedByTripId'],
      deviceId: data['deviceId'],
      driverTags: data['driverTags'] != null ? List<String>.from(data['driverTags']) : null,
      tenantIDs: data['tenantIDs'] != null ? List<String>.from(data['tenantIDs']) : null,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'uid': uid,
      'email': email,
      'displayName': displayName,
      'photoURL': photoURL,
      'primaryUserType': primaryUserType?.index,
      'primaryLanguage': primaryLanguage != null
          ? {'languageCode': primaryLanguage!.languageCode, 'countryCode': primaryLanguage!.countryCode}
          : null,
      'lastSeen': lastSeen,
      'isServiceActiveByTenant': isServiceActiveByTenant,
      'occupiedByTripId': occupiedByTripId,
      'deviceId': deviceId,
      'driverTags': driverTags,
      'tenantIDs': tenantIDs,
    };
  }

  String get ensuredDisplayName => displayName ?? email ?? 'Unknown User';

  /// Helper method to check if service is active for a specific tenant
  bool isServiceActiveForTenant(String tenantId) {
    return isServiceActiveByTenant?[tenantId] ?? false;
  }
}

final mobileUsersColl = FirebaseFirestore.instance.collection('mobile_users').withConverter<MobileUser>(
      fromFirestore: (snapshot, _) => MobileUser.fromFirestore(snapshot),
      toFirestore: (user, _) => user.toFirestore(),
    );
