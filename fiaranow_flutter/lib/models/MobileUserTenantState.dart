import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

// 📊 Rating statistics for drivers
class RatingStats {
  final int totalTrips;
  final double totalRating;
  final double averageRating;
  final DateTime? lastRatingDate;
  final Map<int, int> distribution;

  RatingStats({
    required this.totalTrips,
    required this.totalRating,
    required this.averageRating,
    this.lastRatingDate,
    required this.distribution,
  });

  factory RatingStats.fromMap(Map<String, dynamic> data) {
    // Handle totalTrips which might be stored as String or int
    int parsedTotalTrips = 0;
    if (data['totalTrips'] != null) {
      if (data['totalTrips'] is int) {
        parsedTotalTrips = data['totalTrips'];
      } else if (data['totalTrips'] is String) {
        parsedTotalTrips = int.tryParse(data['totalTrips']) ?? 0;
      }
    }

    // Parse distribution map with string keys to int keys
    final Map<int, int> parsedDistribution = {};
    if (data['distribution'] != null && data['distribution'] is Map) {
      (data['distribution'] as Map).forEach((key, value) {
        int? intKey;
        if (key is int) {
          intKey = key;
        } else if (key is String) {
          intKey = int.tryParse(key);
        }

        if (intKey != null) {
          if (value is int) {
            parsedDistribution[intKey] = value;
          } else if (value is String) {
            parsedDistribution[intKey] = int.tryParse(value) ?? 0;
          } else {
            parsedDistribution[intKey] = 0;
          }
        }
      });
    }

    // Ensure all rating keys 1-5 exist
    for (int i = 1; i <= 5; i++) {
      parsedDistribution.putIfAbsent(i, () => 0);
    }

    return RatingStats(
      totalTrips: parsedTotalTrips,
      totalRating: (data['totalRating'] ?? 0).toDouble(),
      averageRating: (data['averageRating'] ?? 0).toDouble(),
      lastRatingDate: data['lastRatingDate'] != null ? (data['lastRatingDate'] as Timestamp).toDate() : null,
      distribution: parsedDistribution,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'totalTrips': totalTrips,
      'totalRating': totalRating,
      'averageRating': averageRating,
      'lastRatingDate': lastRatingDate != null ? Timestamp.fromDate(lastRatingDate!) : null,
      'distribution': distribution.map((key, value) => MapEntry(key.toString(), value)),
    };
  }
}

class MobileUserTenantState {
  final String uid;
  final String tenantId;
  final bool isActive;
  final String? currentVehicleLinkingId;
  final List<String> driverTags;
  final DateTime joinedAt;
  final DateTime lastActiveAt;

  // Driver-specific state for this tenant
  final String? isDriverConfirmed;
  final bool isServiceActive;

  // 🌟 Driver rating information
  final RatingStats? ratingStats;

  MobileUserTenantState({
    required this.uid,
    required this.tenantId,
    required this.isActive,
    this.currentVehicleLinkingId,
    required this.driverTags,
    required this.joinedAt,
    required this.lastActiveAt,
    this.isDriverConfirmed,
    required this.isServiceActive,
    this.ratingStats,
  });

  factory MobileUserTenantState.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    return MobileUserTenantState(
      uid: data['uid'] ?? '',
      tenantId: data['tenantId'] ?? '',
      isActive: data['isActive'] ?? false,
      currentVehicleLinkingId: data['currentVehicleLinkingId'],
      driverTags: List<String>.from(data['driverTags'] ?? []),
      joinedAt: (data['joinedAt'] as Timestamp).toDate(),
      lastActiveAt: (data['lastActiveAt'] as Timestamp).toDate(),
      isDriverConfirmed: data['isDriverConfirmed'],
      isServiceActive: data['isServiceActive'] ?? false,
      ratingStats: data['ratingStats'] != null ? RatingStats.fromMap(data['ratingStats']) : null,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'uid': uid,
      'tenantId': tenantId,
      'isActive': isActive,
      'currentVehicleLinkingId': currentVehicleLinkingId,
      'driverTags': driverTags,
      'joinedAt': Timestamp.fromDate(joinedAt),
      'lastActiveAt': Timestamp.fromDate(lastActiveAt),
      'isDriverConfirmed': isDriverConfirmed,
      'isServiceActive': isServiceActive,
      'ratingStats': ratingStats?.toMap(),
    };
  }

  static CollectionReference<MobileUserTenantState> getTenantStatesColl(String userId) {
    return FirebaseFirestore.instance
        .collection('mobile_users')
        .doc(userId)
        .collection('tenant_states')
        .withConverter<MobileUserTenantState>(
          fromFirestore: (snapshot, _) => MobileUserTenantState.fromFirestore(snapshot),
          toFirestore: (state, _) => state.toFirestore(),
        );
  }

  static CollectionReference<MobileUserTenantState> get currentUserTenantStatesColl {
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;
    if (currentUserId == null) {
      throw Exception('No authenticated user');
    }

    return getTenantStatesColl(currentUserId);
  }
}
