import 'package:cloud_firestore/cloud_firestore.dart';
import '../config/tenant_config.dart';

enum EventLogType {
  driverServiceStatusUpdate,
  driverTripRejected,
}

// Please also update the `ServiceStatusUpdateScreen.dart` file when updating this enum.
enum ServiceStatusReasonType {
  morningServiceStart,
  eveningServiceStart,
  lunchBreak,
  prayerBreak,
  fuelRefill,
  vehicleMaintenance,
  endOfShift,
  emergencyStop,
  switchActivity,
  appRelaunch,
  custom,
}

enum TripRejectionReasonType {
  vehicleMalfunction,
  tooFarPickup,
  heavyTraffic,
  unsafeArea,
  endingShiftSoon,
  vehicleCleaning,
  passengerCapacityFull,
  batteryLow,
  weatherConditions,
  custom,
}

class EventLog {
  final String id;
  final String uid;
  final EventLogType type;
  final DateTime timestamp;
  final DateTime timestampDT;
  final Map<String, dynamic>? driver;
  final Map<String, dynamic>? trip;
  final String? reason;
  final ServiceStatusReasonType? serviceStatusReasonType;
  final TripRejectionReasonType? tripRejectionReasonType;
  final Map<String, dynamic>? metadata;

  EventLog({
    required this.id,
    required this.uid,
    required this.type,
    required this.timestamp,
    required this.timestampDT,
    this.driver,
    this.trip,
    this.reason,
    this.serviceStatusReasonType,
    this.tripRejectionReasonType,
    this.metadata,
  });

  static String convertToLowerCamelCase(String screamingSnakeCase) {
    return screamingSnakeCase
        .toLowerCase()
        .split('_')
        .indexed
        .map((pair) => pair.$1 == 0 ? pair.$2 : pair.$2[0].toUpperCase() + pair.$2.substring(1))
        .join('');
  }

  static String convertToScreamingSnakeCase(String lowerCamelCase) {
    return lowerCamelCase.replaceAllMapped(RegExp(r'[A-Z]'), (match) => '_${match.group(0)}').toUpperCase();
  }

  factory EventLog.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    final typeStr = data['type'] as String;
    final normalizedType = convertToLowerCamelCase(typeStr);

    ServiceStatusReasonType? serviceStatusReasonType;
    TripRejectionReasonType? tripRejectionReasonType;

    if (data['serviceStatusReasonType'] != null) {
      final reasonTypeStr = data['serviceStatusReasonType'] as String;
      serviceStatusReasonType = ServiceStatusReasonType.values.firstWhere(
        (e) => e.name == convertToLowerCamelCase(reasonTypeStr),
      );
    }

    if (data['tripRejectionReasonType'] != null) {
      final reasonTypeStr = data['tripRejectionReasonType'] as String;
      tripRejectionReasonType = TripRejectionReasonType.values.firstWhere(
        (e) => e.name == convertToLowerCamelCase(reasonTypeStr),
      );
    }

    return EventLog(
      id: doc.id,
      uid: data['uid'] as String,
      type: EventLogType.values.firstWhere(
        (e) => e.name == normalizedType,
      ),
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      timestampDT:
          data['timestampDT'] != null ? (data['timestampDT'] as Timestamp).toDate() : (data['timestamp'] as Timestamp).toDate(),
      driver: data['driver'] as Map<String, dynamic>?,
      trip: data['trip'] as Map<String, dynamic>?,
      reason: data['reason'] as String?,
      serviceStatusReasonType: serviceStatusReasonType,
      tripRejectionReasonType: tripRejectionReasonType,
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toFirestore() {
    final typeStr = convertToScreamingSnakeCase(type.name);

    return {
      'uid': uid,
      'type': typeStr,
      'timestamp': FieldValue.serverTimestamp(),
      'timestampDT': timestampDT,
      if (driver != null) 'driver': driver,
      if (trip != null) 'trip': trip,
      if (reason != null) 'reason': reason,
      if (serviceStatusReasonType != null) 'serviceStatusReasonType': convertToScreamingSnakeCase(serviceStatusReasonType!.name),
      if (tripRejectionReasonType != null) 'tripRejectionReasonType': convertToScreamingSnakeCase(tripRejectionReasonType!.name),
      if (metadata != null) 'metadata': metadata,
    };
  }
}

final eventLogsColl = FirebaseFirestore.instance.collection(TenantConfig.getTenantPath('event_logs')).withConverter<EventLog>(
      fromFirestore: (snapshot, _) => EventLog.fromFirestore(snapshot),
      toFirestore: (eventLog, _) => eventLog.toFirestore(),
    );
