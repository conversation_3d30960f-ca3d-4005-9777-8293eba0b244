import 'package:cloud_firestore/cloud_firestore.dart';

import '../config/tenant_config.dart';
import 'ConfigurationModel/ChatConfigurationModel.dart';
import 'ConfigurationModel/TripConfigurationModel.dart';

class ConfigurationModel {
  final String key;
  final dynamic value;

  ConfigurationModel({
    required this.key,
    required this.value,
  });

  factory ConfigurationModel.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    dynamic decodedValue = data['value'];

    if (doc.id == TripConfigurationModel.id) {
      decodedValue = TripConfigurationModel.fromMap(decodedValue);
    } else if (doc.id == ChatConfigurationModel.id) {
      decodedValue = ChatConfigurationModel.fromMap(decodedValue);
    }
    return ConfigurationModel(
      key: doc.id,
      value: decodedValue,
    );
  }

  Map<String, dynamic> toFirestore() {
    if (value is TripConfigurationModel) {
      return value.toMap();
    } else if (value is ChatConfigurationModel) {
      return value.toMap();
    }
    return {
      'value': value,
    };
  }
}

final configurationsColl =
    FirebaseFirestore.instance.collection(TenantConfig.getTenantPath('configurations')).withConverter<ConfigurationModel>(
          fromFirestore: (snapshot, _) => ConfigurationModel.fromFirestore(snapshot),
          toFirestore: (config, _) => config.toFirestore(),
        );
