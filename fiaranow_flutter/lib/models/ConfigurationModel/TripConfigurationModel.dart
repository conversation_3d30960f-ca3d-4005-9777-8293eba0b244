class TripConfigurationModel {
  static const String id = 'tripConfiguration';

  final double costPerKilometer;
  final double costPerHour;
  final double minimumTripCost;
  final int waitTimeAfterExtraPayment;
  final double costPerExtraWaitChunk;
  final double cancelCostPreStart;
  final double nearbyDriverListedRadiusMeters;
  final int maxPassengerCount;
  final bool? hideInProgressCosts;

  TripConfigurationModel({
    required this.costPerKilometer,
    required this.costPerHour,
    required this.minimumTripCost,
    required this.waitTimeAfterExtraPayment,
    required this.costPerExtraWaitChunk,
    required this.cancelCostPreStart,
    required this.nearbyDriverListedRadiusMeters,
    this.maxPassengerCount = 8,
    this.hideInProgressCosts,
  });

  factory TripConfigurationModel.fromMap(Map<String, dynamic>? data) {
    if (data == null) {
      return getDefault();
    }
    return TripConfigurationModel(
      costPerKilometer: (data['costPerKilometer'] as num).toDouble(),
      costPerHour: (data['costPerHour'] as num).toDouble(),
      minimumTripCost: (data['minimumTripCost'] as num).toDouble(),
      waitTimeAfterExtraPayment: data['waitTimeAfterExtraPayment'] as int,
      costPerExtraWaitChunk: (data['costPerExtraWaitChunk'] as num).toDouble(),
      cancelCostPreStart: (data['cancelCostPreStart'] as num).toDouble(),
      nearbyDriverListedRadiusMeters: (data['nearbyDriverListedRadiusMeters'] as num?)?.toDouble() ?? 1700.0,
      maxPassengerCount: (data['maxPassengerCount'] as num?)?.toInt() ?? 8,
      hideInProgressCosts: data['hideInProgressCosts'] as bool?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'costPerKilometer': costPerKilometer,
      'costPerHour': costPerHour,
      'minimumTripCost': minimumTripCost,
      'waitTimeAfterExtraPayment': waitTimeAfterExtraPayment,
      'costPerExtraWaitChunk': costPerExtraWaitChunk,
      'cancelCostPreStart': cancelCostPreStart,
      'nearbyDriverListedRadiusMeters': nearbyDriverListedRadiusMeters,
      'maxPassengerCount': maxPassengerCount,
      'hideInProgressCosts': hideInProgressCosts,
    };
  }

  Map<String, dynamic> toJson() => toMap();

  static TripConfigurationModel getDefault() {
    return TripConfigurationModel(
      costPerKilometer: 4000.0,
      costPerHour: 25000.0,
      minimumTripCost: 15000.0,
      waitTimeAfterExtraPayment: 10,
      costPerExtraWaitChunk: 5000.0,
      cancelCostPreStart: 10000.0,
      nearbyDriverListedRadiusMeters: 1700.0,
      maxPassengerCount: 8,
      hideInProgressCosts: false,
    );
  }
}
