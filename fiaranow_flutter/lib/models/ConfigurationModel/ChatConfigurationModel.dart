class ChatConfigurationModel {
  static const String id = 'chat';

  final bool showAdminNamesInChat;
  final List<String> defaultChatCategories;
  final int autoArchiveChatAfterDays;
  final bool enableChatNotifications;
  final bool enableAutoReplyForOffHours;
  final String autoReplyMessage;
  final List<int> supportHours; // [startHour, endHour] in 24h format
  final List<int> supportDays; // [0-6] where 0 is Sunday
  final int maxImagesPerMessage;
  final int maxImageSizeMB;

  ChatConfigurationModel({
    required this.showAdminNamesInChat,
    required this.defaultChatCategories,
    required this.autoArchiveChatAfterDays,
    required this.enableChatNotifications,
    required this.enableAutoReplyForOffHours,
    required this.autoReplyMessage,
    required this.supportHours,
    required this.supportDays,
    required this.maxImagesPerMessage,
    required this.maxImageSizeMB,
  });

  factory ChatConfigurationModel.fromMap(Map<String, dynamic> map) {
    return ChatConfigurationModel(
      showAdminNamesInChat: map['showAdminNamesInChat'] ?? false,
      defaultChatCategories: List<String>.from(map['defaultChatCategories'] ?? [
        'supportGeneral',
        'supportTrip',
        'supportPayment',
        'supportTechnical',
        'feedbackFollowup'
      ]),
      autoArchiveChatAfterDays: map['autoArchiveChatAfterDays'] ?? 30,
      enableChatNotifications: map['enableChatNotifications'] ?? true,
      enableAutoReplyForOffHours: map['enableAutoReplyForOffHours'] ?? false,
      autoReplyMessage: map['autoReplyMessage'] ?? 'Thank you for contacting us. Our support team is currently offline. We will respond to your message during our business hours.',
      supportHours: List<int>.from(map['supportHours'] ?? [8, 18]), // 8 AM to 6 PM
      supportDays: List<int>.from(map['supportDays'] ?? [1, 2, 3, 4, 5]), // Monday to Friday
      maxImagesPerMessage: map['maxImagesPerMessage'] ?? 5,
      maxImageSizeMB: map['maxImageSizeMB'] ?? 5,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'showAdminNamesInChat': showAdminNamesInChat,
      'defaultChatCategories': defaultChatCategories,
      'autoArchiveChatAfterDays': autoArchiveChatAfterDays,
      'enableChatNotifications': enableChatNotifications,
      'enableAutoReplyForOffHours': enableAutoReplyForOffHours,
      'autoReplyMessage': autoReplyMessage,
      'supportHours': supportHours,
      'supportDays': supportDays,
      'maxImagesPerMessage': maxImagesPerMessage,
      'maxImageSizeMB': maxImageSizeMB,
    };
  }

  bool isSupportAvailable() {
    final now = DateTime.now();
    final currentHour = now.hour;
    final currentDay = now.weekday == 7 ? 0 : now.weekday; // Convert to 0-6 format

    // Check if current day is in support days
    if (!supportDays.contains(currentDay)) {
      return false;
    }

    // Check if current hour is within support hours
    if (supportHours.length >= 2) {
      final startHour = supportHours[0];
      final endHour = supportHours[1];
      return currentHour >= startHour && currentHour < endHour;
    }

    return true; // Default to available if hours not configured properly
  }
}