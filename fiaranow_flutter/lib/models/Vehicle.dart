import 'package:cloud_firestore/cloud_firestore.dart';

class Vehicle {
  final String? id;
  final String brand;
  final String model;
  final String color;
  final int year;
  final String registrationNumber;
  final int maxPassengers;
  final bool isActive;
  final String? ownerUID;
  
  // Metadata
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;

  Vehicle({
    this.id,
    required this.brand,
    required this.model,
    required this.color,
    required this.year,
    required this.registrationNumber,
    this.maxPassengers = 4,
    this.isActive = true,
    this.ownerUID,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
  });

  factory Vehicle.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    return Vehicle(
      id: doc.id,
      brand: data['brand'] ?? '',
      model: data['model'] ?? '',
      color: data['color'] ?? '',
      year: data['year'] ?? 0,
      registrationNumber: data['registrationNumber'] ?? '',
      maxPassengers: data['maxPassengers'] ?? 4,
      isActive: data['isActive'] ?? true,
      ownerUID: data['ownerUID'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'brand': brand,
      'model': model,
      'color': color,
      'year': year,
      'registrationNumber': registrationNumber,
      'maxPassengers': maxPassengers,
      'isActive': isActive,
      'ownerUID': ownerUID,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
    };
  }

  String get displayName => '$brand $model ($year)';

  static CollectionReference<Vehicle> get vehiclesColl {
    return FirebaseFirestore.instance
        .collection('vehicles')
        .withConverter<Vehicle>(
          fromFirestore: (snapshot, _) => Vehicle.fromFirestore(snapshot),
          toFirestore: (vehicle, _) => vehicle.toFirestore(),
        );
  }
}