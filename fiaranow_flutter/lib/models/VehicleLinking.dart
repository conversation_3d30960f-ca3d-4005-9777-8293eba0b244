import 'package:cloud_firestore/cloud_firestore.dart';
import '../config/tenant_config.dart';

class VehicleLinking {
  final String? id;
  final String vehicleId;
  final String tenantId;
  
  // Tenant-specific vehicle state
  final String? tenantRemark;
  final bool? tenantApproved;
  final String? currentDriverId;
  
  // Linking metadata
  final DateTime linkedAt;
  final String linkedBy;
  final bool isActive;
  
  // For tenant-owned vehicles
  final bool isOwnedByTenant;

  VehicleLinking({
    this.id,
    required this.vehicleId,
    required this.tenantId,
    this.tenantRemark,
    this.tenantApproved,
    this.currentDriverId,
    required this.linkedAt,
    required this.linkedBy,
    this.isActive = true,
    required this.isOwnedByTenant,
  });

  factory VehicleLinking.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    return VehicleLinking(
      id: doc.id,
      vehicleId: data['vehicleId'] ?? '',
      tenantId: data['tenantId'] ?? '',
      tenantRemark: data['tenantRemark'],
      tenantApproved: data['tenantApproved'],
      currentDriverId: data['currentDriverId'],
      linkedAt: (data['linkedAt'] as Timestamp).toDate(),
      linkedBy: data['linkedBy'] ?? '',
      isActive: data['isActive'] ?? true,
      isOwnedByTenant: data['isOwnedByTenant'] ?? false,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'vehicleId': vehicleId,
      'tenantId': tenantId,
      'tenantRemark': tenantRemark,
      'tenantApproved': tenantApproved,
      'currentDriverId': currentDriverId,
      'linkedAt': Timestamp.fromDate(linkedAt),
      'linkedBy': linkedBy,
      'isActive': isActive,
      'isOwnedByTenant': isOwnedByTenant,
    };
  }

  static CollectionReference<VehicleLinking> get vehicleLinkingColl {
    return FirebaseFirestore.instance
        .collection('tenants/${TenantConfig.TENANT_ID}/vehicles_linking')
        .withConverter<VehicleLinking>(
          fromFirestore: (snapshot, _) => VehicleLinking.fromFirestore(snapshot),
          toFirestore: (linking, _) => linking.toFirestore(),
        );
  }
}