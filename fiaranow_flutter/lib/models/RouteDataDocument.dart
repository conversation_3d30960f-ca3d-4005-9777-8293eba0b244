import 'package:cloud_firestore/cloud_firestore.dart';

import '../config/tenant_config.dart';
import 'Trip.dart'; // Import Trip.dart which contains RouteData

enum RouteType { main, driver, finalRoute, overview }

class RouteDataDocument {
  final String tripId;
  final RouteType routeType;
  final int? routeIndex;
  final bool? isSelected;
  final RouteData routeData;
  final DateTime createdAt;

  RouteDataDocument({
    required this.tripId,
    required this.routeType,
    this.routeIndex,
    this.isSelected,
    required this.routeData,
    required this.createdAt,
  });

  factory RouteDataDocument.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return RouteDataDocument(
      tripId: data['tripId'] as String,
      routeType: RouteType.values.firstWhere(
        (e) => e.name == data['routeType'],
      ),
      routeIndex: data['routeIndex'] as int?,
      isSelected: data['isSelected'] as bool?,
      routeData: RouteData.fromMap(data['routeData'] as Map<String, dynamic>),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'tripId': tripId,
      'routeType': routeType.name,
      'routeIndex': routeIndex,
      'isSelected': isSelected,
      'routeData': routeData.toMap(),
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }
}

final routeDataColl =
    FirebaseFirestore.instance.collection(TenantConfig.getTenantPath('route_data')).withConverter<RouteDataDocument>(
          fromFirestore: (snapshot, _) => RouteDataDocument.fromFirestore(snapshot),
          toFirestore: (routeData, _) => routeData.toFirestore(),
        );
