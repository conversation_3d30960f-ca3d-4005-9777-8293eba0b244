import 'package:cloud_firestore/cloud_firestore.dart';

import '../config/tenant_config.dart';

enum FeedbackType { trip, application }

enum FeedbackStatus { newFeedback, seen, addressed, archived }

class Feedback {
  final String id;
  final String uid;
  final FeedbackType type;
  final String? tripId;
  final int? rating;
  final String message;
  final List<String> images;
  final DateTime createdAt;
  final FeedbackStatus status;
  final String? chatSessionId;

  Feedback({
    required this.id,
    required this.uid,
    required this.type,
    this.tripId,
    this.rating,
    required this.message,
    required this.images,
    required this.createdAt,
    required this.status,
    this.chatSessionId,
  });

  factory Feedback.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return Feedback(
      id: doc.id,
      uid: data['uid'] ?? '',
      type: FeedbackType.values.firstWhere(
        (e) => e.toString() == 'FeedbackType.${data['type']}',
        orElse: () => FeedbackType.trip,
      ),
      tripId: data['tripId'],
      rating: data['rating'],
      message: data['message'] ?? '',
      images: List<String>.from(data['images'] ?? []),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      status: FeedbackStatus.values.firstWhere(
        (e) => e.toString() == 'FeedbackStatus.${data['status']}',
        orElse: () => FeedbackStatus.newFeedback,
      ),
      chatSessionId: data['chatSessionId'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'type': type.toString().split('.').last,
      if (tripId != null) 'tripId': tripId,
      if (rating != null) 'rating': rating,
      'message': message,
      'images': images,
      'createdAt': Timestamp.fromDate(createdAt),
      'status': status.toString().split('.').last,
      if (chatSessionId != null) 'chatSessionId': chatSessionId,
    };
  }
}

final feedbacksColl = FirebaseFirestore.instance.collection(TenantConfig.getTenantPath('feedbacks')).withConverter<Feedback>(
      fromFirestore: (snapshot, _) => Feedback.fromFirestore(snapshot),
      toFirestore: (feedback, _) => feedback.toMap(),
    );
