import 'package:cloud_firestore/cloud_firestore.dart';

class MobileUserNotification {
  final String id;
  final String recipientUID;
  final String type;
  final String title;
  final String body;
  final DateTime createdAt;
  final DateTime? readAt;
  final bool isRead;
  final Map<String, dynamic> data;
  final String? tripId;
  final String? clickAction;
  final String? fcmMessageId;
  final bool isDismissed;

  MobileUserNotification({
    required this.id,
    required this.recipientUID,
    required this.type,
    required this.title,
    required this.body,
    required this.createdAt,
    this.readAt,
    required this.isRead,
    required this.data,
    this.tripId,
    this.clickAction,
    this.fcmMessageId,
    this.isDismissed = false,
  });

  factory MobileUserNotification.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return MobileUserNotification(
      id: doc.id,
      recipientUID: data['recipientUID'] ?? '',
      type: data['type'] ?? 'general',
      title: data['title'] ?? '',
      body: data['body'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      readAt: (data['readAt'] as Timestamp?)?.toDate(),
      isRead: data['isRead'] ?? false,
      data: data['data'] ?? {},
      tripId: data['tripId'],
      clickAction: data['clickAction'],
      fcmMessageId: data['fcmMessageId'],
      isDismissed: data['isDismissed'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'recipientUID': recipientUID,
      'type': type,
      'title': title,
      'body': body,
      'createdAt': FieldValue.serverTimestamp(),
      'readAt': readAt != null ? Timestamp.fromDate(readAt!) : null,
      'isRead': isRead,
      'data': data,
      'tripId': tripId,
      'clickAction': clickAction,
      'fcmMessageId': fcmMessageId,
      'isDismissed': isDismissed,
    };
  }

  MobileUserNotification copyWith({
    String? id,
    String? recipientUID,
    String? type,
    String? title,
    String? body,
    DateTime? createdAt,
    DateTime? readAt,
    bool? isRead,
    Map<String, dynamic>? data,
    String? tripId,
    String? clickAction,
    String? fcmMessageId,
    bool? isDismissed,
  }) {
    return MobileUserNotification(
      id: id ?? this.id,
      recipientUID: recipientUID ?? this.recipientUID,
      type: type ?? this.type,
      title: title ?? this.title,
      body: body ?? this.body,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
      tripId: tripId ?? this.tripId,
      clickAction: clickAction ?? this.clickAction,
      fcmMessageId: fcmMessageId ?? this.fcmMessageId,
      isDismissed: isDismissed ?? this.isDismissed,
    );
  }

  String get displayTime {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }

  bool get isTripRelated {
    return tripId != null && tripId!.isNotEmpty;
  }

  bool get isDriverNotification {
    return type == 'driver_moving' || 
           type == 'driver_arrived' || 
           type == 'driver_timeout' ||
           type == 'trip_request' ||
           type == 'trip_assignment';
  }

  bool get isPassengerNotification {
    return type == 'trip_paid' || 
           type == 'reservation_reminder' ||
           type == 'trip_cancelled' ||
           type == 'trip_completed';
  }

  bool get isSystemNotification {
    return type == 'document_expiry' || 
           type == 'general' ||
           type == 'system_update';
  }
}

enum NotificationType {
  driverMoving('driver_moving'),
  driverArrived('driver_arrived'),
  tripPaid('trip_paid'),
  reservationReminder('reservation_reminder'),
  driverTimeout('driver_timeout'),
  documentExpiry('document_expiry'),
  general('general'),
  tripRequest('trip_request'),
  tripAssignment('trip_assignment'),
  tripCancelled('trip_cancelled'),
  tripCompleted('trip_completed'),
  systemUpdate('system_update');

  final String value;
  const NotificationType(this.value);

  static NotificationType fromString(String value) {
    return NotificationType.values.firstWhere(
      (e) => e.value == value,
      orElse: () => NotificationType.general,
    );
  }
}