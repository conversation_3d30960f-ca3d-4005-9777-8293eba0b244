import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

enum DocumentType {
  license,
  insurance,
  vehicleRegistration,
  nationalId,
  other;

  String get displayName {
    switch (this) {
      case DocumentType.license:
        return 'Driver License';
      case DocumentType.insurance:
        return 'Insurance';
      case DocumentType.vehicleRegistration:
        return 'Vehicle Registration';
      case DocumentType.nationalId:
        return 'National ID';
      case DocumentType.other:
        return 'Other';
    }
  }
}

enum DocumentStatus {
  pendingReview,
  approved,
  rejected,
  expired,
  expiringSoon;

  String get displayName {
    switch (this) {
      case DocumentStatus.pendingReview:
        return 'Pending Review';
      case DocumentStatus.approved:
        return 'Approved';
      case DocumentStatus.rejected:
        return 'Rejected';
      case DocumentStatus.expired:
        return 'Expired';
      case DocumentStatus.expiringSoon:
        return 'Expiring Soon';
    }
  }
}

class DriverDocument {
  final String? id;
  final DocumentType documentType;
  final String documentName;
  final String fileURL;
  final DateTime expiryDate;
  final DocumentStatus status;
  final String? notes;
  final DateTime uploadedAt;
  final DateTime? reviewedAt;
  final String? reviewedBy;
  final String? adminNotes;
  final List<String> tenantIDs;

  DriverDocument({
    this.id,
    required this.documentType,
    required this.documentName,
    required this.fileURL,
    required this.expiryDate,
    required this.status,
    this.notes,
    required this.uploadedAt,
    this.reviewedAt,
    this.reviewedBy,
    this.adminNotes,
    required this.tenantIDs,
  });

  factory DriverDocument.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    return DriverDocument(
      id: doc.id,
      documentType: DocumentType.values.firstWhere(
        (e) => e.name == data['documentType'],
        orElse: () => DocumentType.other,
      ),
      documentName: data['documentName'] ?? '',
      fileURL: data['fileURL'] ?? '',
      expiryDate: (data['expiryDate'] as Timestamp).toDate(),
      status: DocumentStatus.values.firstWhere(
        (e) => e.name == data['status'],
        orElse: () => DocumentStatus.pendingReview,
      ),
      notes: data['notes'],
      uploadedAt: (data['uploadedAt'] as Timestamp).toDate(),
      reviewedAt: data['reviewedAt'] != null 
          ? (data['reviewedAt'] as Timestamp).toDate() 
          : null,
      reviewedBy: data['reviewedBy'],
      adminNotes: data['adminNotes'],
      tenantIDs: List<String>.from(data['tenantIDs'] ?? []),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'documentType': documentType.name,
      'documentName': documentName,
      'fileURL': fileURL,
      'expiryDate': Timestamp.fromDate(expiryDate),
      'status': status.name,
      'notes': notes,
      'uploadedAt': Timestamp.fromDate(uploadedAt),
      'reviewedAt': reviewedAt != null ? Timestamp.fromDate(reviewedAt!) : null,
      'reviewedBy': reviewedBy,
      'adminNotes': adminNotes,
      'tenantIDs': tenantIDs,
    };
  }

  static CollectionReference<DriverDocument> get driverDocumentsColl {
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;
    if (currentUserId == null) {
      throw Exception('No authenticated user');
    }
    
    return FirebaseFirestore.instance
        .collection('mobile_users')
        .doc(currentUserId)
        .collection('driver_documents')
        .withConverter<DriverDocument>(
          fromFirestore: (snapshot, _) => DriverDocument.fromFirestore(snapshot),
          toFirestore: (doc, _) => doc.toFirestore(),
        );
  }

  static CollectionReference<DriverDocument> getVehicleDocumentsColl(String vehicleId) {
    return FirebaseFirestore.instance
        .collection('vehicles')
        .doc(vehicleId)
        .collection('vehicle_documents')
        .withConverter<DriverDocument>(
          fromFirestore: (snapshot, _) => DriverDocument.fromFirestore(snapshot),
          toFirestore: (doc, _) => doc.toFirestore(),
        );
  }
}