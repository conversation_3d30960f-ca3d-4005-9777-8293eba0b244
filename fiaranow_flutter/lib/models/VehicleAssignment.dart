import 'package:cloud_firestore/cloud_firestore.dart';

enum AssignmentReason {
  adminAssignment,
  driverSwitch,
  vehicleMaintenance,
  driverUnavailable;

  String get displayName {
    switch (this) {
      case AssignmentReason.adminAssignment:
        return 'Admin Assignment';
      case AssignmentReason.driverSwitch:
        return 'Driver Switch';
      case AssignmentReason.vehicleMaintenance:
        return 'Vehicle Maintenance';
      case AssignmentReason.driverUnavailable:
        return 'Driver Unavailable';
    }
  }
}

class VehicleAssignment {
  final String? id;
  final String vehicleId;
  final String vehicleLinkingId;
  final String driverUID;
  final String tenantId;
  
  final DateTime createdAt;
  final DateTime assignedAt;
  final String assignedBy;
  final DateTime? unassignedAt;
  final String? unassignedBy;
  
  final AssignmentReason reason;
  final String? notes;
  
  final bool isActive;

  VehicleAssignment({
    this.id,
    required this.vehicleId,
    required this.vehicleLinkingId,
    required this.driverUID,
    required this.tenantId,
    required this.createdAt,
    required this.assignedAt,
    required this.assignedBy,
    this.unassignedAt,
    this.unassignedBy,
    required this.reason,
    this.notes,
    required this.isActive,
  });

  factory VehicleAssignment.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    return VehicleAssignment(
      id: doc.id,
      vehicleId: data['vehicleId'] ?? '',
      vehicleLinkingId: data['vehicleLinkingId'] ?? '',
      driverUID: data['driverUID'] ?? '',
      tenantId: data['tenantId'] ?? '',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      assignedAt: (data['assignedAt'] as Timestamp).toDate(),
      assignedBy: data['assignedBy'] ?? '',
      unassignedAt: data['unassignedAt'] != null 
          ? (data['unassignedAt'] as Timestamp).toDate() 
          : null,
      unassignedBy: data['unassignedBy'],
      reason: AssignmentReason.values.firstWhere(
        (e) => e.name == data['reason'],
        orElse: () => AssignmentReason.adminAssignment,
      ),
      notes: data['notes'],
      isActive: data['isActive'] ?? false,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'vehicleId': vehicleId,
      'vehicleLinkingId': vehicleLinkingId,
      'driverUID': driverUID,
      'tenantId': tenantId,
      'createdAt': Timestamp.fromDate(createdAt),
      'assignedAt': Timestamp.fromDate(assignedAt),
      'assignedBy': assignedBy,
      'unassignedAt': unassignedAt != null ? Timestamp.fromDate(unassignedAt!) : null,
      'unassignedBy': unassignedBy,
      'reason': reason.name,
      'notes': notes,
      'isActive': isActive,
    };
  }

  static CollectionReference<VehicleAssignment> get vehicleAssignmentsColl {
    return FirebaseFirestore.instance
        .collection('vehicle_assignments')
        .withConverter<VehicleAssignment>(
          fromFirestore: (snapshot, _) => VehicleAssignment.fromFirestore(snapshot),
          toFirestore: (assignment, _) => assignment.toFirestore(),
        );
  }
}