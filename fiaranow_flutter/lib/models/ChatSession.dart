import 'package:cloud_firestore/cloud_firestore.dart';

import '../config/tenant_config.dart';

enum ChatCategory { supportGeneral, supportTrip, supportPayment, supportTechnical, feedbackFollowup }

enum ChatStatus { active, resolved, archived }

class ChatSession {
  final String id;
  final List<String> participantUids;
  final String title;
  final ChatCategory category;
  final ChatStatus status;
  final DateTime createdAt;
  final DateTime lastMessageAt;
  final String? feedbackId;
  final bool isAdminInitiated;
  final int? unreadCount;
  final String? lastMessage;

  ChatSession({
    required this.id,
    required this.participantUids,
    required this.title,
    required this.category,
    required this.status,
    required this.createdAt,
    required this.lastMessageAt,
    this.feedbackId,
    required this.isAdminInitiated,
    this.unreadCount,
    this.lastMessage,
  });

  factory ChatSession.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ChatSession(
      id: doc.id,
      participantUids: List<String>.from(data['participantUids'] ?? []),
      title: data['title'] ?? '',
      category: ChatCategory.values.firstWhere(
        (e) => e.toString().split('.').last == data['category'],
        orElse: () => ChatCategory.supportGeneral,
      ),
      status: ChatStatus.values.firstWhere(
        (e) => e.toString().split('.').last == data['status'],
        orElse: () => ChatStatus.active,
      ),
      createdAt: data['createdAt'] != null ? (data['createdAt'] as Timestamp).toDate() : DateTime.now(),
      lastMessageAt: data['lastMessageAt'] != null ? (data['lastMessageAt'] as Timestamp).toDate() : DateTime.now(),
      feedbackId: data['feedbackId'],
      isAdminInitiated: data['isAdminInitiated'] ?? false,
      unreadCount: data['unreadCount'],
      lastMessage: data['lastMessage'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'participantUids': participantUids,
      'title': title,
      'category': category.toString().split('.').last,
      'status': status.toString().split('.').last,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastMessageAt': Timestamp.fromDate(lastMessageAt),
      if (feedbackId != null) 'feedbackId': feedbackId,
      'isAdminInitiated': isAdminInitiated,
      if (unreadCount != null) 'unreadCount': unreadCount,
      if (lastMessage != null) 'lastMessage': lastMessage,
    };
  }

  String getCategoryDisplayName() {
    switch (category) {
      case ChatCategory.supportGeneral:
        return 'General Support';
      case ChatCategory.supportTrip:
        return 'Trip Support';
      case ChatCategory.supportPayment:
        return 'Payment Support';
      case ChatCategory.supportTechnical:
        return 'Technical Support';
      case ChatCategory.feedbackFollowup:
        return 'Feedback Follow-up';
    }
  }
}

final chatSessionsColl =
    FirebaseFirestore.instance.collection(TenantConfig.getTenantPath('chat_sessions')).withConverter<ChatSession>(
          fromFirestore: (snapshot, _) => ChatSession.fromFirestore(snapshot),
          toFirestore: (chatSession, _) => chatSession.toFirestore(),
        );
