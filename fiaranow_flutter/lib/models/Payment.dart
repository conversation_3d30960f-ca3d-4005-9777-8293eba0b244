import 'package:cloud_firestore/cloud_firestore.dart';
import '../config/tenant_config.dart';

enum PaymentMethod {
  cash,
  mobile,
  card,
  other;

  static PaymentMethod fromString(String value) {
    return PaymentMethod.values.firstWhere(
      (e) => e.toString() == 'PaymentMethod.$value',
      orElse: () => PaymentMethod.other,
    );
  }
}

enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  refunded,
  partiallyRefunded,
  disputed;

  static PaymentStatus fromString(String value) {
    return PaymentStatus.values.firstWhere(
      (e) => e.toString() == 'PaymentStatus.$value',
      orElse: () => PaymentStatus.failed,
    );
  }
}

class Payment {
  final String id;
  final String tripId;
  final String customerId;
  final String? driverId;
  final PaymentMethod customerRequestedPaymentMethod;
  final String? customerRequestedPaymentMethodRemark;
  final PaymentMethod? finalPaymentMethod;
  final String? finalPaymentMethodRemark;
  final double totalCost;
  final double? totalCostDue;
  final double? discount;
  final String? discountReason;
  final String? remark;
  final PaymentStatus status;
  final DateTime createdAt;
  final DateTime? processedAt;
  final DateTime? completedAt;
  final String? processedByUid;
  final String? completedByUid;
  final String? transactionId; // External payment system's transaction ID
  final String? receiptNumber;
  final Map<String, dynamic>? metadata; // Additional payment-specific data

  Payment({
    required this.id,
    required this.tripId,
    required this.customerId,
    this.driverId,
    required this.customerRequestedPaymentMethod,
    this.customerRequestedPaymentMethodRemark,
    this.finalPaymentMethod,
    this.finalPaymentMethodRemark,
    required this.totalCost,
    this.totalCostDue,
    this.discount,
    this.discountReason,
    this.remark,
    required this.status,
    required this.createdAt,
    this.processedAt,
    this.completedAt,
    this.processedByUid,
    this.completedByUid,
    this.transactionId,
    this.receiptNumber,
    this.metadata,
  });

  factory Payment.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;

    return Payment(
      id: doc.id,
      tripId: data['tripId'],
      customerId: data['customerId'],
      driverId: data['driverId'],
      customerRequestedPaymentMethod: PaymentMethod.fromString(data['customerRequestedPaymentMethod']),
      customerRequestedPaymentMethodRemark: data['customerRequestedPaymentMethodRemark'],
      finalPaymentMethod: data['finalPaymentMethod'] != null ? PaymentMethod.fromString(data['finalPaymentMethod']) : null,
      finalPaymentMethodRemark: data['finalPaymentMethodRemark'],
      totalCost: data['totalCost'],
      totalCostDue: data['totalCostActuallyReceived'],
      discount: data['discount'],
      discountReason: data['discountReason'],
      remark: data['remark'],
      status: PaymentStatus.fromString(data['status']),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      processedAt: data['processedAt'] != null ? (data['processedAt'] as Timestamp).toDate() : null,
      completedAt: data['completedAt'] != null ? (data['completedAt'] as Timestamp).toDate() : null,
      processedByUid: data['processedByUid'],
      completedByUid: data['completedByUid'],
      transactionId: data['transactionId'],
      receiptNumber: data['receiptNumber'],
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'tripId': tripId,
      'customerId': customerId,
      'driverId': driverId,
      'customerRequestedPaymentMethod': customerRequestedPaymentMethod.name,
      'customerRequestedPaymentMethodRemark': customerRequestedPaymentMethodRemark,
      'finalPaymentMethod': finalPaymentMethod?.name,
      'finalPaymentMethodRemark': finalPaymentMethodRemark,
      'totalCost': totalCost,
      'totalCostActuallyReceived': totalCostDue,
      'discount': discount,
      'discountReason': discountReason,
      'remark': remark,
      'status': status.name,
      'createdAt': createdAt,
      'processedAt': processedAt,
      'completedAt': completedAt,
      'processedByUid': processedByUid,
      'completedByUid': completedByUid,
      'transactionId': transactionId,
      'receiptNumber': receiptNumber,
      'metadata': metadata,
    };
  }

  static DocumentReference<Payment> createPayment({
    required String tripId,
    required String customerId,
    String? driverId,
    required PaymentMethod requestedMethod,
    String? requestedMethodRemark,
    required double totalCost,
    String? remark,
    Map<String, dynamic>? metadata,
  }) {
    final ref = paymentsColl.doc();
    final payment = Payment(
      id: ref.id,
      tripId: tripId,
      customerId: customerId,
      driverId: driverId,
      customerRequestedPaymentMethod: requestedMethod,
      customerRequestedPaymentMethodRemark: requestedMethodRemark,
      totalCost: totalCost,
      remark: remark,
      status: PaymentStatus.pending,
      createdAt: DateTime.now(),
      metadata: metadata,
    );

    ref.set(payment);
    return ref;
  }

  Payment copyWith({
    String? tripId,
    String? customerId,
    String? driverId,
    PaymentMethod? customerRequestedPaymentMethod,
    String? customerRequestedPaymentMethodRemark,
    PaymentMethod? finalPaymentMethod,
    String? finalPaymentMethodRemark,
    double? totalCost,
    double? totalCostActuallyReceived,
    double? discount,
    String? discountReason,
    String? remark,
    PaymentStatus? status,
    DateTime? createdAt,
    DateTime? processedAt,
    DateTime? completedAt,
    String? processedByUid,
    String? completedByUid,
    String? transactionId,
    String? receiptNumber,
    Map<String, dynamic>? metadata,
  }) {
    return Payment(
      id: id,
      tripId: tripId ?? this.tripId,
      customerId: customerId ?? this.customerId,
      driverId: driverId ?? this.driverId,
      customerRequestedPaymentMethod: customerRequestedPaymentMethod ?? this.customerRequestedPaymentMethod,
      customerRequestedPaymentMethodRemark: customerRequestedPaymentMethodRemark ?? this.customerRequestedPaymentMethodRemark,
      finalPaymentMethod: finalPaymentMethod ?? this.finalPaymentMethod,
      finalPaymentMethodRemark: finalPaymentMethodRemark ?? this.finalPaymentMethodRemark,
      totalCost: totalCost ?? this.totalCost,
      totalCostDue: totalCostActuallyReceived ?? totalCostDue,
      discount: discount ?? this.discount,
      discountReason: discountReason ?? this.discountReason,
      remark: remark ?? this.remark,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      processedAt: processedAt ?? this.processedAt,
      completedAt: completedAt ?? this.completedAt,
      processedByUid: processedByUid ?? this.processedByUid,
      completedByUid: completedByUid ?? this.completedByUid,
      transactionId: transactionId ?? this.transactionId,
      receiptNumber: receiptNumber ?? this.receiptNumber,
      metadata: metadata ?? this.metadata,
    );
  }
}

final paymentsColl = FirebaseFirestore.instance.collection(TenantConfig.getTenantPath('payments')).withConverter<Payment>(
      fromFirestore: (snapshot, _) => Payment.fromFirestore(snapshot),
      toFirestore: (payment, _) => payment.toFirestore(),
    );
