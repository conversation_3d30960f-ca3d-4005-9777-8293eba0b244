import 'package:cloud_firestore/cloud_firestore.dart';

enum SenderType { passenger, admin }

enum MessageType { text, image, system }

class ChatMessage {
  final String id;
  final String senderUid;
  final SenderType senderType;
  final String message;
  final DateTime timestamp;
  final Map<String, DateTime> readBy;
  final MessageType messageType;
  final String? imageUrl;

  ChatMessage({
    required this.id,
    required this.senderUid,
    required this.senderType,
    required this.message,
    required this.timestamp,
    required this.readBy,
    required this.messageType,
    this.imageUrl,
  });

  factory ChatMessage.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    Map<String, DateTime> readByMap = {};
    if (data['readBy'] != null) {
      (data['readBy'] as Map<String, dynamic>).forEach((key, value) {
        readByMap[key] = (value as Timestamp).toDate();
      });
    }

    return ChatMessage(
      id: doc.id,
      senderUid: data['senderUid'] ?? '',
      senderType: SenderType.values.firstWhere(
        (e) => e.toString().split('.').last == data['senderType'],
        orElse: () => SenderType.passenger,
      ),
      message: data['message'] ?? '',
      timestamp: data['timestamp'] != null ? (data['timestamp'] as Timestamp).toDate() : DateTime.now(),
      readBy: readByMap,
      messageType: MessageType.values.firstWhere(
        (e) => e.toString().split('.').last == data['messageType'],
        orElse: () => MessageType.text,
      ),
      imageUrl: data['imageUrl'],
    );
  }

  Map<String, dynamic> toFirestore() {
    Map<String, dynamic> readByFirestore = {};
    readBy.forEach((key, value) {
      readByFirestore[key] = Timestamp.fromDate(value);
    });

    return {
      'senderUid': senderUid,
      'senderType': senderType.toString().split('.').last,
      'message': message,
      'timestamp': Timestamp.fromDate(timestamp),
      'readBy': readByFirestore,
      'messageType': messageType.toString().split('.').last,
      if (imageUrl != null) 'imageUrl': imageUrl,
    };
  }

  bool isRead(String uid) {
    return readBy.containsKey(uid);
  }
}
