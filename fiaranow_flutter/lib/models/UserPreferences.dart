import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationSettings {
  bool? enableRingtone; // null = use admin default
  bool enableDriverMovingNotification;
  bool enableDriverArrivedNotification;
  bool enableTripPaidNotification;
  bool enableReservationReminders;

  NotificationSettings({
    this.enableRingtone,
    this.enableDriverMovingNotification = true,
    this.enableDriverArrivedNotification = true,
    this.enableTripPaidNotification = true,
    this.enableReservationReminders = true,
  });

  factory NotificationSettings.fromMap(Map<String, dynamic> map) {
    return NotificationSettings(
      enableRingtone: map['enableRingtone'],
      enableDriverMovingNotification: map['enableDriverMovingNotification'] ?? true,
      enableDriverArrivedNotification: map['enableDriverArrivedNotification'] ?? true,
      enableTripPaidNotification: map['enableTripPaidNotification'] ?? true,
      enableReservationReminders: map['enableReservationReminders'] ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'enableRingtone': enableRingtone,
      'enableDriverMovingNotification': enableDriverMovingNotification,
      'enableDriverArrivedNotification': enableDriverArrivedNotification,
      'enableTripPaidNotification': enableTripPaidNotification,
      'enableReservationReminders': enableReservationReminders,
    };
  }

  factory NotificationSettings.defaultSettings() {
    return NotificationSettings();
  }
}

class UserPreferences {
  final String userId;
  final NotificationSettings notificationSettings;

  UserPreferences({
    required this.userId,
    required this.notificationSettings,
  });

  factory UserPreferences.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    if (data == null) {
      return UserPreferences(
        userId: doc.id,
        notificationSettings: NotificationSettings.defaultSettings(),
      );
    }

    return UserPreferences(
      userId: doc.id,
      notificationSettings: NotificationSettings.fromMap(
        data['notificationSettings'] ?? {},
      ),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'notificationSettings': notificationSettings.toMap(),
    };
  }
}