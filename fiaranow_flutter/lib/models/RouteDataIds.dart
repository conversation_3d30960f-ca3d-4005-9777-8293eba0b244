class RouteDataIds {
  final String? main;
  final String? driver;
  final String? finalRoute;
  final List<String>? overviews;
  final String? selectedOverviewId;

  RouteDataIds({
    this.main,
    this.driver,
    this.finalRoute,
    this.overviews,
    this.selectedOverviewId,
  });

  factory RouteDataIds.fromMap(Map<String, dynamic> map) {
    return RouteDataIds(
      main: map['main'] as String?,
      driver: map['driver'] as String?,
      finalRoute: map['final'] as String?,
      overviews: map['overviews'] != null
          ? List<String>.from(map['overviews'] as List)
          : null,
      selectedOverviewId: map['selectedOverviewId'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'main': main,
      'driver': driver,
      'final': finalRoute,
      'overviews': overviews,
      'selectedOverviewId': selectedOverviewId,
    };
  }
}