name: fiaranow_flutter
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.3.1+28

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: any

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  firebase_core: ^3.7.0
  firebase_auth: ^5.3.2
  cloud_firestore: ^5.5.0
  firebase_database: ^11.1.6
  firebase_messaging: ^15.1.5
  cloud_functions: ^5.1.5
  firebase_analytics: ^11.4.1
  firebase_crashlytics: ^4.3.9
  firebase_app_check: ^0.3.2+10

  shared_preferences: ^2.5.2

  firebase_ui_auth: ^1.16.0
  google_sign_in: ^6.2.2
  firebase_ui_oauth_google: ^1.4.0

  get: ^4.6.6
  logging: ^1.3.0
  device_info_plus: ^11.3.0

  #google_maps_flutter: ^2.9.0
  google_maps_flutter: # because of this bug: https://github.com/flutter/flutter/issues/60695
    git:
      url: https://github.com/bndos/flutter-packages.git
      path: packages/google_maps_flutter/google_maps_flutter
      ref: feature/point-of-interest
  flutter_google_places_sdk: ^0.3.10
  geolocator: ^13.0.2
  geoflutterfire_plus: ^0.0.31
  flutter_form_builder: ^9.5.0
  form_builder_validators: ^11.0.0
  geocoding: ^3.0.0
  permission_handler: ^12.0.0+1
  flutter_native_splash: ^2.4.3
  flutter_launcher_icons: ^0.14.1
  flutter_polyline_points: ^2.1.0

  package_info_plus: ^8.1.1
  in_app_review: ^2.0.10
  flutter_local_notifications: ^18.0.1
  url_launcher: ^6.3.1

  google_fonts: ^6.2.1
  restart: ^1.0.0+1
  wakelock_plus: ^1.2.11
  flutter_foreground_task: ^9.1.0

  image_picker: ^1.1.2
  file_picker: ^10.2.0
  firebase_storage: ^12.4.7
  path_provider: ^2.1.5
  syncfusion_flutter_pdfviewer: ^29.1.38
  cached_network_image: ^3.4.1
  workmanager: ^0.7.0

dependency_overrides:
  google_maps_flutter_android:
    git:
      url: https://github.com/bndos/flutter-packages.git
      path: packages/google_maps_flutter/google_maps_flutter_android
      ref: feature/point-of-interest

  google_maps_flutter_ios:
    git:
      url: https://github.com/bndos/flutter-packages.git
      path: packages/google_maps_flutter/google_maps_flutter_ios
      ref: feature/point-of-interest

  google_maps_flutter_platform_interface:
    git:
      url: https://github.com/bndos/flutter-packages.git
      path: packages/google_maps_flutter/google_maps_flutter_platform_interface
      ref: feature/point-of-interest

dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.5
  fake_cloud_firestore: ^3.1.0

  intl_translation: ^0.20.1
  vm_service: ^14.3.0

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/json/map_dark_style.json

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
