<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<array>
			<string>com.pravera.flutter_foreground_task.refresh</string>
		</array>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Fiaranow</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>fiaranow_flutter</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.1034858234595-si16m58aial4rg6vaa2dv31dunku0huj</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>GMSApiKey</key>
		<string>AIzaSyCYv7VWSjBh8_XziLq9QqwrjEygnA9uNmE</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true/>
		</dict>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>This app needs access to location when in the background to show your position on the map.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>This app needs access to location when open to show your position on the map.</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Always-on location access is only required for drivers, so they can continue to receive location updates even when the app is in the background. Passengers do not need to grant this permission.</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
		</array>
		<key>UIDeviceFamily</key>
		<array>
			<integer>1</integer>
			<integer>2</integer>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>io.flutter.embedded_views_preview</key>
		<true/>
		<key>NSUserNotificationsUsageDescription</key>
		<string>Our app uses notifications to keep you updated on your trip status, new ride requests, and other important alerts related to your account and activity.</string>
		<key>NSCameraUsageDescription</key>
		<string>This app needs camera access to take photos of documents</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>This app needs photo library access to upload documents</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>This app needs photo library access to save documents</string>
	</dict>
</plist>
