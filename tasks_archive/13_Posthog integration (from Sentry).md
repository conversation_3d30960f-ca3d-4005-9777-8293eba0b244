# PostHog Integration Migration Plan (from Sentry)

## Executive Summary
This document outlines the complete migration from Sentry to PostHog for error tracking, session replay, and analytics in the Fiaranow Flutter application. The migration maintains existing logging patterns while leveraging PostHog's superior free tier (5,000 sessions vs Sentry's 50) and comprehensive analytics capabilities.

## Current State Analysis

### Existing Sentry Implementation
- **Dependencies**: `sentry_flutter: ^8.14.1`, `sentry_logging: ^8.14.1`
- **Initialization**: Only in release mode (`main.dart:180-218`)
- **Configuration**: DSN-based, session replay enabled, privacy settings configured
- **User Context**: Set via `Sentry.configureScope` in `AuthState.dart:70-72`
- **Logging Integration**: Uses `LoggingIntegration` for breadcrumbs
- **Coverage**: 27 files with logging patterns (print statements, Logger instances)

### Current Logging Architecture
- **Core Logger**: `logging: ^1.3.0` package with hierarchical logging
- **Custom Service**: `AgentLoggingService` in `states/DevToolsService.dart`
- **Structure**: JSON-formatted events with dart:developer integration
- **Patterns**: Logger instances per class, structured event logging

## Firebase Crashlytics vs PostHog Decision

### Analysis Summary
Based on 2025 research, **PostHog is recommended** over Firebase Crashlytics for the following reasons:

**PostHog Advantages:**
- **Comprehensive platform**: Error tracking + analytics + session replay in one
- **Cost effective**: 5,000 free sessions vs limited crash reports
- **Open source**: Future flexibility and data control
- **No Google Play Services dependency**: Critical for Madagascar market
- **Session replay**: Superior debugging capabilities

**Firebase Crashlytics Strengths:**
- **Specialized crash reporting**: More detailed crash analysis
- **Google reliability**: Enterprise-grade stability
- **Firebase ecosystem**: Deep integration if using other Firebase services

**Decision**: Use PostHog as primary solution but **keep abstraction layer** to easily add Firebase Crashlytics back if needed for specialized crash scenarios.

## Analytics Abstraction Design

### Simple Abstraction Principles
- **Single interface**: One class to rule all analytics calls
- **Minimal overhead**: Simple method delegation pattern
- **Easy switching**: Change implementation in one place
- **No complexity**: Avoid over-engineering, keep it simple

### Abstraction Structure
```dart
abstract class AnalyticsBackend {
  void trackEvent(String event, Map<String, dynamic> properties);
  void identifyUser(String userId, Map<String, dynamic> properties);
  void captureException(dynamic exception, StackTrace? stackTrace);
  void setUserProperty(String key, dynamic value);
}

class AnalyticsService {
  static AnalyticsBackend _backend = PostHogBackend(); // Default
  static void setBackend(AnalyticsBackend backend) => _backend = backend;
  static void trackEvent(String event, [Map<String, dynamic>? props]) => 
    _backend.trackEvent(event, props ?? {});
  // ... other methods
}
```

## Migration Strategy

### Phase 1: Dependencies and Configuration

#### 1.1 Update pubspec.yaml
**File**: `/fiaranow_flutter/pubspec.yaml`
**Action**: Replace Sentry dependencies with PostHog
```yaml
# Remove:
# sentry_flutter: ^8.14.1
# sentry_logging: ^8.14.1

# Add:
posthog_flutter: ^5.0.0
```

#### 1.2 Analytics Abstraction Layer
**File**: `/fiaranow_flutter/lib/services/AnalyticsService.dart` (NEW)
**Purpose**: Simple abstraction layer for easy backend switching
**Implementation**:
```dart
// Core abstraction - keep it simple
abstract class AnalyticsBackend {
  Future<void> initialize();
  void trackEvent(String event, Map<String, dynamic> properties);
  void identifyUser(String userId, Map<String, dynamic> properties);
  void captureException(dynamic exception, StackTrace? stackTrace);
  void setUserProperty(String key, dynamic value);
}

// Main service - static methods for simplicity
class AnalyticsService {
  static AnalyticsBackend _backend = PostHogBackend();
  
  static Future<void> initialize([AnalyticsBackend? backend]) async {
    if (backend != null) _backend = backend;
    await _backend.initialize();
  }
  
  static void trackEvent(String event, [Map<String, dynamic>? properties]) =>
    _backend.trackEvent(event, properties ?? {});
    
  static void identifyUser(String userId, [Map<String, dynamic>? properties]) =>
    _backend.identifyUser(userId, properties ?? {});
    
  static void captureException(dynamic exception, [StackTrace? stackTrace]) =>
    _backend.captureException(exception, stackTrace);
    
  static void setUserProperty(String key, dynamic value) =>
    _backend.setUserProperty(key, value);
}
```

**Additional Files**:
- `/lib/services/backends/PostHogBackend.dart` (NEW) - PostHog implementation
- `/lib/services/backends/FirebaseCrashlyticsBackend.dart` (NEW) - Optional for future
- `/lib/services/backends/SentryBackend.dart` (NEW) - Optional fallback

#### 1.3 PostHog Configuration Setup
**File**: `/fiaranow_flutter/lib/config/posthog_config.dart` (NEW)
**Purpose**: Centralized PostHog configuration
**Contents**:
- PostHog API key constants (dev/prod)
- Host configuration (EU instance: `https://eu.posthog.com`)
- Feature flags and session replay settings
- Privacy and sampling configurations

### Phase 2: Core Integration

#### 2.1 Main Application Integration
**File**: `/fiaranow_flutter/lib/main.dart`
**Lines to Modify**: 27, 28, 180-218, 221-231
**Actions**:
1. Replace Sentry imports with AnalyticsService import
2. Replace `SentryFlutter.init()` with `AnalyticsService.initialize()`
3. Remove `SentryWidget` wrapper (PostHog doesn't need widget wrapper)
4. Maintain existing logging setup but integrate with AnalyticsService
5. Preserve environment-specific behavior (debug vs release)
6. **Critical**: All analytics calls go through AnalyticsService, never direct PostHog

**Key Requirements**:
- Use PostHog API key: `phc_PROJECT_API_KEY` (to be obtained from PostHog EU dashboard)
- Configure session replay with 100% sampling in debug, 10% in production
- **MANDATORY**: Set host to EU instance (`https://eu.posthog.com`) for optimal latency to Madagascar
- Maintain hierarchical logging structure

#### 2.2 User Context Migration
**File**: `/fiaranow_flutter/lib/states/AuthState.dart`
**Lines to Modify**: 17, 70-72
**Actions**:
1. Replace `sentry_flutter` import with AnalyticsService import
2. Replace `Sentry.configureScope()` with `AnalyticsService.identifyUser()`
3. Maintain user property structure (id, email, name)
4. Use abstraction layer for all user identification
5. **No direct PostHog calls** - everything through AnalyticsService

### Phase 3: Logging Service Enhancement

#### 3.1 AgentLoggingService Integration
**File**: `/fiaranow_flutter/lib/states/DevToolsService.dart`
**Lines to Modify**: All methods (17-72)
**Actions**:
1. Add AnalyticsService injection instead of direct PostHog imports
2. Enhance existing methods to use AnalyticsService abstraction
3. Maintain dart:developer logging for DevTools
4. Route all analytics calls through the abstraction layer
5. Preserve existing method signatures for minimal breaking changes

**Integration Pattern**:
- Replace direct analytics calls with `AnalyticsService.instance.method()`
- Maintain existing AgentLoggingService API unchanged
- Add analytics backend through dependency injection

#### 3.2 Error Handling Enhancement
**Files**: All 27 files identified with logging patterns
**Actions**:
1. Review each Logger instance usage
2. Add AnalyticsService error capture for severe/warning logs
3. Maintain existing print statements for development
4. Add structured error context through abstraction layer
5. No direct PostHog calls - all through AnalyticsService

### Phase 4: Session Replay Configuration

#### 4.1 Flutter Web Configuration
**Files**: 
- `/fiaranow_flutter/web/index.html` (if exists)
- `/fiaranow_flutter/lib/main.dart`
**Actions**:
1. Configure PostHog for Flutter Web session replay
2. Enable canvas capture for Flutter Web rendering
3. Set appropriate privacy settings for text/image masking
4. Configure recording duration and quality

#### 4.2 Mobile Session Replay
**Files**:
- `/fiaranow_flutter/android/app/src/main/AndroidManifest.xml`
- `/fiaranow_flutter/ios/Runner/Info.plist`
**Actions**:
1. Add required permissions for screen recording
2. Configure privacy settings for mobile replay
3. Set sampling rates for mobile vs web
4. Ensure compliance with app store policies

### Phase 5: Analytics Integration

#### 5.1 Custom Event Tracking
**Files**: All state management files
**Actions**:
1. Replace Firebase Analytics events with PostHog events
2. Maintain existing event structure and naming
3. Add PostHog-specific user journey tracking
4. Implement feature flag integration

#### 5.2 Performance Monitoring
**Files**: Navigation and performance-critical files
**Actions**:
1. Add PostHog performance event tracking
2. Monitor app startup time, navigation performance
3. Track user engagement metrics
4. Implement custom metrics for business KPIs

### Phase 6: Testing and Validation

#### 6.1 Development Testing
**Requirements**:
1. Verify PostHog events in PostHog dashboard
2. Test session replay functionality on all platforms
3. Validate error tracking and reporting
4. Ensure user identification works correctly
5. Test feature flags if implemented

#### 6.2 Production Validation
**Requirements**:
1. Gradual rollout with sampling
2. Monitor PostHog event volume and costs
3. Compare error detection with previous Sentry data
4. Validate session replay quality and privacy
5. Monitor app performance impact

## Implementation Conditions and Behaviors

### Environment-Specific Behavior
- **Debug Mode**: PostHog with full logging, 100% session replay sampling
- **Release Mode**: PostHog with optimized settings, 10% session replay sampling
- **Emulator Mode**: PostHog disabled or pointed to test project

### User Privacy and Configuration
- **Regional Optimization**: **MANDATORY** - Use EU PostHog instance (`https://eu.posthog.com`) for optimal performance from Madagascar
- **Data Masking**: Configure text/image masking for sensitive data
- **User Consent**: Implement user consent mechanism if required
- **Data Retention**: Configure appropriate data retention policies

### Performance Requirements
- **Bundle Size**: Monitor app size increase (PostHog SDK ~2MB)
- **Runtime Performance**: Ensure <100ms impact on app startup
- **Network Usage**: Monitor PostHog API call frequency
- **Battery Impact**: Validate session replay battery consumption

### Error Handling and Fallbacks
- **PostHog Unavailable**: Graceful degradation to local logging
- **Network Issues**: Queue events for later transmission
- **Storage Limits**: Implement event queue size limits
- **Rate Limiting**: Handle PostHog rate limiting gracefully

## Migration Timeline

### Week 1: Setup and Configuration
- Install PostHog SDK and configure basic settings
- Set up PostHog project and obtain API keys
- Configure development environment

### Week 2: Core Integration
- Migrate main.dart initialization
- Update AuthState user context
- Test basic event tracking

### Week 3: Logging Enhancement
- Enhance AgentLoggingService with PostHog integration
- Update error handling across all files
- Implement session replay

### Week 4: Testing and Validation
- Comprehensive testing on all platforms
- Performance testing and optimization
- Production deployment preparation

## Abstraction Benefits and Future Flexibility

### Easy Backend Switching Examples

**Switch to Firebase Crashlytics (if PostHog fails):**
```dart
// In main.dart - only change this line:
await AnalyticsService.initialize(FirebaseCrashlyticsBackend());
```

**Add dual tracking (PostHog + Crashlytics):**
```dart
// Create CompositeBackend.dart with multiple backends
await AnalyticsService.initialize(CompositeBackend([
  PostHogBackend(),
  FirebaseCrashlyticsBackend(),
]));
```

**Disable analytics in testing:**
```dart
// Use NoOpBackend for tests
await AnalyticsService.initialize(NoOpBackend());
```

### Migration Simplicity
- **One file change**: Switch entire analytics backend by changing one line
- **Zero refactoring**: All existing `AnalyticsService.trackEvent()` calls work unchanged
- **Gradual transition**: Can run multiple backends simultaneously during migration
- **Easy rollback**: Revert to previous backend in minutes, not hours

### Testing Benefits
- **Mock analytics**: Easy to test without real analytics calls
- **Verify tracking**: Simple to verify correct events are sent
- **A/B test backends**: Compare PostHog vs Crashlytics performance side-by-side

## Success Metrics

### Functional Metrics
- **Error Detection**: 100% of critical errors captured
- **Session Replay**: 90% of sessions recordable
- **User Identification**: 100% of authenticated users tracked
- **Event Tracking**: All custom events successfully transmitted

### Performance Metrics
- **App Startup**: <100ms additional delay
- **Memory Usage**: <50MB additional memory
- **Network Usage**: <1MB per session for events
- **Battery Impact**: <5% additional battery usage

### Business Metrics
- **Cost Reduction**: 90% reduction in monitoring costs
- **Data Quality**: Improved error context and user journey data
- **Feature Usage**: Enhanced feature adoption tracking
- **User Experience**: Improved issue resolution time

## Rollback Plan

### Immediate Rollback (if critical issues)
1. Revert to previous version with Sentry
2. Disable PostHog initialization
3. Restore Sentry configuration
4. Deploy emergency hotfix

### Gradual Rollback (if performance issues)
1. Reduce PostHog sampling rates
2. Disable session replay temporarily
3. Optimize PostHog configuration
4. Plan phased re-deployment

## Dependencies and Prerequisites

### External Dependencies
- PostHog EU project setup and API key from `https://eu.posthog.com`
- **CRITICAL**: EU PostHog instance configuration for optimal Madagascar connectivity
- Development and production environment separation (both on EU instance)

### Internal Dependencies
- Firebase Analytics integration review
- Custom event tracking audit
- User privacy policy updates
- App store submission considerations

## Risk Assessment

### High Risk
- **Session Replay Privacy**: Ensure no sensitive data capture
- **Performance Impact**: Monitor app performance degradation
- **Data Migration**: Ensure no loss of historical error data

### Medium Risk
- **Integration Complexity**: Multiple file modifications required
- **Testing Coverage**: Comprehensive testing across platforms
- **User Experience**: Minimal disruption to existing functionality

### Low Risk
- **SDK Stability**: PostHog Flutter SDK is mature
- **Documentation**: Comprehensive PostHog documentation available
- **Community Support**: Active PostHog community and support

## Conclusion

This migration from Sentry to PostHog will provide significant cost savings (5,000 vs 50 free sessions), enhanced analytics capabilities, and improved user experience monitoring. The phased approach ensures minimal disruption while maintaining the existing logging architecture and patterns.

The key to success is maintaining the current logging structure while enhancing it with PostHog's comprehensive tracking capabilities, ensuring proper testing across all platforms, and implementing appropriate privacy and performance safeguards.