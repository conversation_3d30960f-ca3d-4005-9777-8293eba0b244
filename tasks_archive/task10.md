# Driver Management System Implementation Plan

## Overview
Implementation of comprehensive driver management system with tag categorization, document management, and admin oversight capabilities. The system integrates with existing multi-tenant architecture and leverages current Firebase Storage infrastructure.

## Data Model Architecture Tree

```
Firebase Collections
├── GLOBAL COLLECTIONS
│   ├── /mobile_users/{uid}
│   │   ├── [existing fields...]
│   │   ├── tenantIDs: string[]                    // NEW: Performance optimization array
│   │   ├── occupiedByTripId?: string              // EXISTING: Trip occupation indicator
│   │   ├── /driver_documents/{docId}              // NEW: Personal documents subcollection
│   │   │   ├── documentType: string
│   │   │   ├── documentName: string
│   │   │   ├── fileURL: string
│   │   │   ├── expiryDate: Timestamp              // REQUIRED for all documents
│   │   │   ├── status: string
│   │   │   ├── tenantIDs: string[]                // NEW: Tenant access array
│   │   │   └── [metadata fields...]
│   │   └── /tenant_states/{tenantId}              // NEW: Per-tenant state subcollection
│   │       ├── uid: string                        // For grouped collection queries
│   │       ├── tenantId: string
│   │       ├── isActive: boolean
│   │       ├── currentVehicleLinkingId?: string   // Currently assigned vehicle linking
│   │       ├── driverTags: string[]               // Tenant-specific tags
│   │       ├── isDriverConfirmed?: string         // Admin UID who confirmed
│   │       ├── isServiceActive: boolean           // Service status in tenant
│   │       └── [timestamp fields...]
│   │
│   ├── /vehicles/{vehicleId}                      // NEW: Global vehicle collection (base data)
│   │   ├── [vehicle details: brand, model, etc.]
│   │   ├── ownerUID?: string                      // For user-owned vehicles
│   │   ├── createdBy: string                      // Admin UID or mobile user UID
│   │   └── [metadata fields...]
│   │
│   └── /vehicle_assignments/{assignmentId}        // NEW: Assignment history tracking
│       ├── vehicleId: string
│       ├── vehicleLinkingId: string               // Reference to tenant linking
│       ├── driverUID: string
│       ├── tenantId: string                       // Always present (from linking)
│       ├── createdAt: Timestamp                   // For efficient sorting
│       ├── assignedAt: Timestamp
│       ├── assignedBy: string
│       ├── reason: string
│       ├── isActive: boolean
│       └── [assignment metadata...]
│
└── TENANT-SPECIFIC COLLECTIONS
    └── /tenants/{tenantId}/
        ├── /driver_tags/{tagId}                   // NEW: Tenant-specific driver tags
        │   ├── name: string
        │   ├── color: string
        │   ├── description?: string
        │   ├── isActive: boolean
        │   ├── usageCount: number
        │   └── [metadata fields...]
        │
        └── /vehicles_linking/{linkingId}          // NEW: Tenant-vehicle associations
            ├── vehicleId: string                  // Reference to global vehicle
            ├── tenantId: string
            ├── tenantRemark?: string              // Admin notes
            ├── tenantApproved?: boolean           // Approval status
            ├── currentDriverId?: string           // Current assignment in tenant
            ├── linkedAt: Timestamp
            ├── linkedBy: string                   // Admin UID who linked
            ├── isActive: boolean
            ├── isOwnedByTenant: boolean           // Internal fleet vs external
            └── [metadata fields...]
```

## 1. Driver Tag System

### 1.1 Data Model
**New Collection**: `/tenants/{tenantId}/driver_tags`
- **Document Structure**:
  - `name`: string (e.g., "external", "truck", "premium")
  - `color`: string (hex color for UI display)
  - `description`: string (optional)
  - `isActive`: boolean
  - `createdAt`: Timestamp
  - `createdBy`: string (admin UID)
  - `usageCount`: number (auto-updated)

### 1.2 Driver Model Extensions
**Modify**: `MobileUser` model in both Flutter and Admin
- **Add field**: `driverTags`: array of strings (tag names)
- **Files to modify**:
  - `fiaranow_flutter/lib/models/MobileUser.dart` - Add driverTags field to DriverProfile class
  - `admin_sveltekit/src/lib/stores/mobile_users.svelte.ts` - Add driverTags to DriverProfile interface
  - Update `toMap()` and `fromMap()` methods in both files

### 1.3 Admin Interface Implementation
**New Components**:
- `admin_sveltekit/src/lib/components/DriverTagSelector.svelte` - Multi-select tag component with create-on-demand
- `admin_sveltekit/src/lib/components/DriverTagBadge.svelte` - Display component for tags

**Modify Existing Pages**:
- `admin_sveltekit/src/routes/rides/drivers/[key]/edit/+page.svelte` - Add tag selector
- `admin_sveltekit/src/routes/rides/drivers/+page.svelte` - Add tag filtering and display
- `admin_sveltekit/src/routes/rides/MobileUserListItem.svelte` - Display driver tags as badges

**New Store**: `admin_sveltekit/src/lib/stores/driver_tags.svelte.ts`
- CRUD operations for driver tags **within current tenant context**
- Use `tenantStore.getTenantPath('driver_tags')` for tenant-specific operations
- Auto-creation logic when new tag is used (tenant-scoped)
- Usage count tracking per tenant
- Import and use existing `callAdminFunction()` for tenant-validated operations

## 2. Document Management System

### 2.1 Data Model
**New Subcollections** (following multi-tenant architecture):
- `/mobile_users/{uid}/driver_documents/{docId}` - Personal documents (license, ID) - **GLOBAL** per MULTI-TENANT.md
- `/tenants/{tenantId}/vehicles/{vehicleId}/vehicle_documents/{docId}` - Vehicle documents (insurance, registration) - **TENANT-SPECIFIC**

**Document Structure** (based on existing MODELS.md):
```typescript
interface DriverDocument {
  documentType: 'license' | 'insurance' | 'vehicle_registration' | 'national_id' | 'other';
  documentName: string;
  fileURL: string;
  expiryDate: Timestamp;       // Required for all documents
  status: 'pending_review' | 'approved' | 'rejected' | 'expired' | 'expiring_soon';
  notes: string;
  uploadedAt: Timestamp;
  reviewedAt?: Timestamp;
  reviewedBy?: string;
  adminNotes?: string;
  tenantIDs: string[];         // Array of tenant IDs that can view this document
}
```

### 2.2 Mobile Implementation
**New Screens**:
- `fiaranow_flutter/lib/screens/DriverDocumentsScreen.dart` - List all documents
- `fiaranow_flutter/lib/screens/DocumentUploadScreen.dart` - Upload new document
- `fiaranow_flutter/lib/screens/DocumentDetailScreen.dart` - View/edit document details
- `fiaranow_flutter/lib/screens/VehicleManagementScreen.dart` - Manage personal vehicles
- `fiaranow_flutter/lib/screens/AddVehicleScreen.dart` - Add new personal vehicle

**New Models**:
- `fiaranow_flutter/lib/models/DriverDocument.dart` - Document model with Firebase converters
- `fiaranow_flutter/lib/models/Vehicle.dart` - Vehicle model with ownership patterns
- `fiaranow_flutter/lib/models/MobileUserTenantState.dart` - Tenant state model

**Integration Points**:
- Leverage existing Firebase Storage pattern from `ApplicationFeedbackScreen.dart` and `TripFeedbackScreen.dart`
- Use similar file upload logic with path: `documents/{uid}/{timestamp}_{documentType}.{ext}`
- Implement file size validation (5MB limit) and format restrictions (PDF, JPG, PNG)
- **Multi-tenant Integration**: Use `TenantConfig.TENANT_ID` for tenant context
- **Function Calls**: Always include `tenantId: TenantConfig.TENANT_ID` in cloud function calls
- **Vehicle Creation**: Mobile drivers can create personal vehicles with `ownerUID` set

### 2.3 Admin Document Management
**New Admin Section**: Add "Documents" to sidebar navigation in `admin_sveltekit/src/routes/Sidebar.svelte`

**New Pages**:
- `admin_sveltekit/src/routes/documents/+page.svelte` - Document overview dashboard
- `admin_sveltekit/src/routes/documents/expiring/+page.svelte` - Expiring documents view
- `admin_sveltekit/src/routes/documents/[docId]/+page.svelte` - Document review interface

**New Store**: `admin_sveltekit/src/lib/stores/driver_documents.svelte.ts`
- Use collection group queries: `collectionGroup(fdb, 'driver_documents')`
- Filter by `tenantIds` array containing current tenant
- Filter by expiry status, document type, driver
- Document approval/rejection workflows
- **Tenant-aware queries**: Respect admin tenant access from `tenant.svelte.ts`

## 3. Vehicle Management Enhancement

### 3.1 Global Vehicle Collection Implementation
**New Collection**: `/vehicles/{vehicleId}` - Global collection for vehicle base data
- **Document Structure**:
```typescript
interface Vehicle {
  id: string;
  brand: string;
  model: string;
  color: string;
  year: number;
  registrationNumber: string;
  maxPassengers: number;
  isActive: boolean;
  ownerUID?: string;        // For user-owned vehicles (external drivers)

  // Metadata
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy: string;        // Admin UID or mobile user UID
}
```

### 3.2 Tenant Vehicle Linking Collection
**New Collection**: `/tenants/{tenantId}/vehicles_linking/{linkingId}` - Tenant-specific vehicle associations
- **Document Structure**:
```typescript
interface VehicleLinking {
  id: string;               // linkingId
  vehicleId: string;        // Reference to global vehicle
  tenantId: string;         // Current tenant

  // Tenant-specific vehicle state
  tenantRemark?: string;    // Admin notes about the vehicle
  tenantApproved?: boolean; // Whether tenant has approved this vehicle for use
  currentDriverId?: string; // Who is currently driving this vehicle in this tenant

  // Linking metadata
  linkedAt: Timestamp;
  linkedBy: string;         // Admin UID who linked the vehicle
  isActive: boolean;        // Whether this linking is active

  // For tenant-owned vehicles
  isOwnedByTenant: boolean; // true for internal fleet, false for external vehicles
}
```

### 3.3 Mobile User Tenant State Management
**New Subcollection**: `/mobile_users/{uid}/tenant_states/{tenantId}`
- Following existing admin user pattern for tenant-scoped state
- **Document Structure**:
```typescript
interface MobileUserTenantState {
  uid: string;                 // User UID for grouped collection queries
  tenantId: string;
  isActive: boolean;           // Is user active in this tenant
  currentVehicleLinkingId?: string; // Currently assigned vehicle linking in this tenant
  driverTags: string[];        // Tags assigned in this tenant
  joinedAt: Timestamp;
  lastActiveAt: Timestamp;

  // Driver-specific state for this tenant
  isDriverConfirmed?: string;  // Admin UID who confirmed driver in this tenant
  isServiceActive: boolean;    // Service status in this tenant
}
```

### 3.4 Enhanced MobileUser Model
**Modify**: `MobileUser` model in both Flutter and Admin
- **Add field**: `tenantIDs: string[]` - Array of tenant IDs for performance optimization
- **Note**: `occupiedByTripId` already exists - indicates driver is currently occupied with this specific Trip ID (not a linking mechanism)
- **Files to modify**:
  - `fiaranow_flutter/lib/models/MobileUser.dart` - Add tenantIDs field
  - `admin_sveltekit/src/lib/stores/mobile_users.svelte.ts` - Add tenantIDs to interface
  - Update `toMap()` and `fromMap()` methods in both files

### 3.5 Vehicle Assignment History
**New Collection**: `/vehicle_assignments/{assignmentId}`
- **Document Structure**:
```typescript
interface VehicleAssignment {
  id: string;
  vehicleId: string;
  vehicleLinkingId: string;    // Reference to tenant vehicle linking
  driverUID: string;
  tenantId: string;            // Always present now (from linking)

  createdAt: Timestamp;        // For efficient sorting
  assignedAt: Timestamp;
  assignedBy: string;          // Admin UID or 'system'
  unassignedAt?: Timestamp;
  unassignedBy?: string;

  reason: 'admin_assignment' | 'driver_switch' | 'vehicle_maintenance' | 'driver_unavailable';
  notes?: string;

  isActive: boolean;           // Current assignment
}
```

### 3.6 Migration Strategy - Non-Destructive Approach
**Phase 1 Function**: `migrateToVehicleSystem` (Transactional)
- Create Vehicle documents from existing `driverProfile` data
- Create VehicleLinking documents in tenant collections
- Create MobileUserTenantState documents with `currentVehicleLinkingId`
- Populate `tenantIDs` array in MobileUser
- Create initial vehicle assignments with linking references
- Does NOT delete old data

**Phase 2 Function**: `cleanupOldVehicleData` (Transactional)
- Remove `driverProfile` from MobileUser documents
- Remove old vehicle-related fields
- Only runs after verification
- **Manual execution only** - not automated

## 4. Admin Vehicle Management

### 4.1 New Admin Pages
**Vehicle Management Section**: Add "Vehicles" to sidebar navigation

**New Pages**:
- `admin_sveltekit/src/routes/vehicles/+page.svelte` - Global vehicle list with tenant/owner filtering
- `admin_sveltekit/src/routes/vehicles/[vehicleId]/+page.svelte` - Vehicle details with assignment history
- `admin_sveltekit/src/routes/vehicles/assignments/+page.svelte` - Vehicle assignment history dashboard
- `admin_sveltekit/src/routes/vehicles/assignments/[vehicleId]/+page.svelte` - Specific vehicle assignment history

**Vehicle Approval Workflow**:
- Display user-owned vehicles requiring tenant approval
- Admin can set `tenantApproved` status and `tenantRemark`
- Filter vehicles by approval status

**New Stores**:
- `admin_sveltekit/src/lib/stores/vehicles.svelte.ts` - Global vehicles collection management
- `admin_sveltekit/src/lib/stores/vehicle_linking.svelte.ts` - Tenant-specific vehicle linking management
  - Query `/tenants/{tenantId}/vehicles_linking` with real-time updates
  - Vehicle approval/rejection workflows
  - Driver assignment management within tenant context

## 5. Technical Implementation Details

### 5.1 Firebase Storage Structure
```
documents/
├── {uid}/
│   ├── {timestamp}_license.pdf
│   ├── {timestamp}_national_id.jpg
│   └── ...
└── vehicles/
    └── {vehicleId}/
        ├── {timestamp}_insurance.pdf
        └── {timestamp}_registration.pdf
```

### 5.2 Security Rules Updates
**File**: `firebase/firestore.rules`

**Driver Tags** (tenant-specific):
```javascript
// /tenants/{tenantId}/driver_tags/{tagId}
match /tenants/{tenantId}/driver_tags/{tagId} {
  allow read: if hasActiveTenantAccess(request.auth.uid, tenantId);
  allow write: if hasMinimumTenantRole(request.auth.uid, tenantId, 1); // ADMIN required
}
```

**Personal Driver Documents** (global with tenant access):
```javascript
// /mobile_users/{userId}/driver_documents/{docId}
match /mobile_users/{userId}/driver_documents/{docId} {
  // Owner can read/write their own documents
  allow read, write: if request.auth.uid == userId;

  // Admins can read/write if they have access to any tenant in tenantIds array
  allow read, write: if isActiveAdmin(request.auth.uid) &&
    hasAnyTenantAccess(request.auth.uid, resource.data.tenantIds);
}
```

**Global Vehicles Collection**:
```javascript
// /vehicles/{vehicleId}
match /vehicles/{vehicleId} {
  // Owner can read/write their own vehicle
  allow read, write: if request.auth.uid == resource.data.ownerUID;

  // Tenant admins can read/write tenant vehicles
  allow read, write: if resource.data.tenantId != null &&
    hasActiveTenantAccess(request.auth.uid, resource.data.tenantId);

  // Assigned driver can read vehicle details
  allow read: if request.auth.uid == resource.data.currentDriverId;
}
```

**Mobile User Tenant States**:
```javascript
// /mobile_users/{userId}/tenant_states/{tenantId}
match /mobile_users/{userId}/tenant_states/{tenantId} {
  allow read: if request.auth.uid == userId || hasActiveTenantAccess(request.auth.uid, tenantId);
  allow write: if hasActiveTenantAccess(request.auth.uid, tenantId);
}
```

**Vehicle Assignments**:
```javascript
// /vehicle_assignments/{assignmentId}
match /vehicle_assignments/{assignmentId} {
  allow read: if request.auth.uid == resource.data.driverUID ||
                 hasActiveTenantAccess(request.auth.uid, resource.data.tenantId);
  allow write: if hasActiveTenantAccess(request.auth.uid, resource.data.tenantId);
}
```

### 5.3 Cloud Functions (All Transactional)
**New Functions** (following multi-tenant patterns):

**Document Management**:
- `notifyExpiringDocuments` - Scheduled function (weekdays 10am GMT+3) to send notifications:
  - **Driver notifications**: Push notifications that open document view when clicked
  - **Admin notifications**: Push notifications that open document details when clicked
  - **Timing**: 1 month, 1 week, and 24 hours before expiry
  - **Schedule**: `0 7 * * 1-5` (10am GMT+3, Monday-Friday, excludes weekends)
- `updateDocumentStatus` - Admin function to approve/reject documents with tenant validation

**Vehicle Management**:
- `createVehicle` - Create new vehicle (mobile user or admin initiated)
- `linkVehicleToTenant` - Create vehicle linking in tenant collection
- `assignVehicleToDriver` - Assign vehicle linking to driver within tenant (reject if existing active assignment)
- `unassignVehicleFromDriver` - Remove vehicle assignment from linking
- `approveUserVehicle` - Admin approval of user-owned vehicles via linking
- `updateVehicleStatus` - Update vehicle active status
- `unlinkVehicleFromTenant` - Remove vehicle from tenant (deactivate linking)

**Migration Functions**:
- `migrateToVehicleSystem` - Phase 1: Non-destructive migration to new structure with vehicle linking
- `cleanupOldVehicleData` - Phase 2: Remove old driverProfile data after verification (manual execution)

**Implementation Requirements**:
- **All functions must be transactional** using `db.runTransaction()`
- Use `getTenantCollection(effectiveTenantId, 'collection')` from `tenant_utils.ts`
- Include `ensureTenantId(tenantId)` validation in all functions
- Follow existing pattern from `driver_operations.ts` for tenant-aware operations
- Update `tenantIDs` array in MobileUser when tenant state changes
- Maintain vehicle assignment history for all changes

### 5.4 Multi-tenant Architecture Compliance

**Collection Architecture** (per MULTI-TENANT.md):
- **GLOBAL**: `/mobile_users/{uid}/driver_documents/{docId}` - Personal documents with `tenantIDs` array
- **GLOBAL**: `/mobile_users/{uid}/tenant_states/{tenantId}` - User state per tenant (following admin pattern)
- **GLOBAL**: `/vehicles/{vehicleId}` - Base vehicle data with optional `ownerUID`
- **GLOBAL**: `/vehicle_assignments/{assignmentId}` - Assignment history tracking
- **TENANT-SPECIFIC**: `/tenants/{tenantId}/driver_tags/{tagId}` - Tags are tenant-isolated
- **TENANT-SPECIFIC**: `/tenants/{tenantId}/vehicles_linking/{linkingId}` - Vehicle-tenant associations

**Performance Optimization**:
- `tenantIDs` array in MobileUser for fast tenant membership queries
- Reflects entries in `/mobile_users/{uid}/tenant_states/{tenantId}`
- Enables efficient filtering without subcollection queries

**Admin Access Control**:
- Respect existing role hierarchy: MANAGER (0), ADMIN (1), SUPER_ADMIN (2)
- SUPER_ADMIN (2) role only available in 'fiaranow' tenant
- Vehicle management requires minimum ADMIN (1) role
- Cross-tenant vehicle access only for SUPER_ADMIN from 'fiaranow' tenant

**Security Rules Integration**:
- Use existing `hasActiveTenantAccess(uid, tenantId)` function
- Use existing `hasMinimumTenantRole(uid, tenantId, minRole)` function
- Personal documents accessible by owner + admins with tenant access via `tenantIds` array
- Vehicle access based on ownership (`tenantId` or `ownerUID`) and assignment

**Function Implementation**:
- **All functions must be transactional**
- All functions must accept `tenantId` parameter
- Use `ensureTenantId(tenantId)` for validation (defaults to 'fiaranow')
- Update `tenantIDs` array when tenant state changes
- Follow existing patterns from `driver_operations.ts` and `trip_operations.ts`

## 5. Multi-Tenant Implementation Patterns

### 5.1 Mobile App Integration (Flutter)
**File**: `fiaranow_flutter/lib/models/DriverDocument.dart`
```dart
// Use existing TenantConfig pattern
static CollectionReference<DriverDocument> get driverDocumentsColl {
  return FirebaseFirestore.instance
    .collection('mobile_users')
    .doc(currentUserId)
    .collection('driver_documents')
    .withConverter<DriverDocument>(...);
}

// For vehicle documents - tenant-specific
static CollectionReference<DriverDocument> getVehicleDocumentsColl(String vehicleId) {
  return FirebaseFirestore.instance
    .collection(TenantConfig.getTenantPath('vehicles'))
    .doc(vehicleId)
    .collection('vehicle_documents')
    .withConverter<DriverDocument>(...);
}
```

**Cloud Function Calls**:
```dart
// Always include tenantId in function calls
final response = await callable.call({
  'tenantId': TenantConfig.TENANT_ID,
  'documentId': documentId,
  'status': 'approved',
});
```

### 5.2 Admin Panel Integration (SvelteKit)
**File**: `admin_sveltekit/src/lib/stores/driver_tags.svelte.ts`
```typescript
// Use existing tenant store pattern
import { tenantStore } from './tenant.svelte';
import { callAdminFunction } from '$lib/firebase.client';

async function createDriverTag(tagData: Partial<DriverTag>) {
  return callAdminFunction('createDriverTag', {
    tenantId: tenantStore.currentId,
    ...tagData
  }, 1); // Requires ADMIN role
}

// Query tenant-specific tags
const tagsRef = collection(fdb, tenantStore.getTenantPath('driver_tags'));
```

**File**: `admin_sveltekit/src/lib/stores/driver_documents.svelte.ts`
```typescript
// Cross-tenant document queries for SUPER_ADMIN
function getDocumentsQuery() {
  if (tenantStore.hasMinimumRole(2)) { // SUPER_ADMIN
    // Can query across tenants
    return collectionGroup(fdb, 'driver_documents');
  } else {
    // Tenant-specific query
    return collection(fdb, tenantStore.getTenantPath('vehicles'))
      .where('ownerId', 'in', driverIds);
  }
}
```

### 5.3 Firebase Functions Implementation
**File**: `firebase/functions/src/document_operations.ts`
```typescript
import { getTenantCollection, ensureTenantId } from './tenant_utils';

export const updateDocumentStatus = onCall(async (request) => {
  const { tenantId, documentId, status, adminNotes } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  // Validate admin has access to this tenant
  const adminAccess = await validateAdminTenantAccess(
    request.auth!.uid,
    effectiveTenantId
  );

  if (!adminAccess || adminAccess.role < 1) { // Requires ADMIN
    throw new functions.HttpsError('permission-denied', 'Insufficient permissions');
  }

  // Use tenant-aware collection reference
  const vehiclesRef = getTenantCollection(effectiveTenantId, 'vehicles');
  // ... rest of function
});
```

## 6. Enhanced Driver Availability Logic

### 6.1 Current vs Enhanced Logic
**Current Logic**:
```typescript
// Driver available if: occupiedByTripId == null (not occupied with a trip)
```

**Enhanced Logic**:
```typescript
// Driver available if:
// 1. occupiedByTripId == null (not occupied with a trip)
// 2. tenantIDs array contains current tenant
// 3. Has active tenant state for current tenant (isActive && isServiceActive)
// 4. Has assigned vehicle OR owns a personal vehicle
// 5. Assigned/owned vehicle is active and tenant-approved (if user-owned)
```

### 6.2 Query Performance Optimization
**Step 1**: Basic availability filter using `tenantIDs` array
```typescript
const availableDrivers = await db.collection('mobile_users')
  .where('occupiedByTripId', '==', null)
  .where('primaryUserType', '==', UserType.driver.index)
  .where('tenantIDs', 'array-contains', tenantId)
  .get();
```

**Step 2**: Validate tenant state and vehicle availability (transactional)
- Check `/mobile_users/{uid}/tenant_states/{tenantId}` for active status
- Verify vehicle linking assignment and approval status via `/tenants/{tenantId}/vehicles_linking`
- Ensure vehicle capacity meets trip requirements

## 7. Integration with Existing Systems

### 7.1 Driver Confirmation Process
**Enhance**: `admin_sveltekit/src/routes/rides/MobileUserDetails.svelte`
- Add document status check before allowing driver confirmation
- Display document compliance status in driver details
- Show vehicle assignment and approval status

### 7.2 Trip Assignment Logic
**Modify**: `firebase/functions/src/driver_operations.ts`
- Implement enhanced driver availability logic
- Consider document status and vehicle availability in driver eligibility
- Add vehicle capacity validation in `adminAssignDriver` function
- **All operations must be transactional**

### 7.3 Notification System
**Leverage**: Existing admin notification system from `admin_sveltekit/src/routes/admin/notifications/+page.svelte`
- Add document expiry notification types
- Add vehicle approval request notifications
- Integrate with existing notification infrastructure

## 8. Risk Assessment

**HIGHEST RISK**: Enhanced driver availability logic affects core trip assignment functionality across Firebase Functions, Admin Panel, and Mobile App. The new logic requires vehicle availability checks in addition to driver availability.

**HIGH RISK**:
- Data migration from embedded `driverProfile` to global Vehicle collection
- Transactional requirements for all vehicle and tenant state operations
- Performance impact of enhanced driver queries with vehicle joins

**MITIGATION**:
- Implement feature flags for gradual rollout of enhanced availability logic
- Non-destructive migration with verification and rollback capability
- Use `tenantIDs` array for performance optimization
- Extensive testing with both old and new logic running in parallel

## 9. File Modifications Summary

### New Files (25+):
- Driver tag management components and store
- Document management screens (mobile) with `tenantIds` support
- Document management pages (admin) with collection group queries
- Vehicle management screens (mobile) for personal vehicle creation
- Vehicle management pages (admin) with approval workflow
- Vehicle assignment history tracking
- Migration functions (phase 1 & 2)
- Vehicle and tenant state models
- Enhanced security rules for global collections

### Modified Files (12+):
- MobileUser model (Flutter + Admin) - Add `tenantIDs` array
- Sidebar navigation - Add Vehicles and Documents sections
- Driver edit/list pages - Add vehicle assignment and tag management
- Driver operations functions - Enhanced availability logic (transactional)
- Security rules - Global vehicle and assignment collections
- Trip assignment logic - Vehicle availability validation
