# Push Notifications Performance & Reliability Improvements

## Executive Summary
This document outlines critical improvements to the Firebase Cloud Messaging (FCM) implementation to align with **Firebase 2025 best practices**. The current implementation violates key performance guidelines that can cause delayed, missed, or failed notifications. This task implements WorkManager for background processing, configures high-priority messaging, and optimizes the background handler for reliable notification delivery.

## Problem Statement 

### Current Implementation Issues

Based on **Firebase's April 17, 2025 official guidance** and analysis of the existing codebase, the current FCM implementation has several critical issues:

#### 1. **Background Handler Performance Violations**
**Location**: `fiaranow_flutter/lib/main.dart:57-67`

**Current Code Issues**:
```dart
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(...);           // ⚠️ Heavy operation
  await initializeFlutterNotifications();      // ⚠️ Heavy operation  
  await showNotification(message);             // ⚠️ Heavy operation
}
```

**Firebase 2025 Guidance**: 
> "Several seconds are given to process the message payload in the onMessageReceived handler... This time is only expected to be long enough to immediately render a notification."

**Impact**: Heavy operations in background handler cause:
- Delayed/missing notifications
- Process lifecycle violations
- Battery optimization interference
- Unreliable notification delivery

#### 2. **Missing Message Priority Configuration**
**Issue**: No server-side priority configuration for critical notifications
**Impact**: Normal priority messages affected by Android Doze mode
**Required**: High priority for driver requests, trip updates, chat messages

#### 3. **No WorkManager Implementation**
**Issue**: Firebase 2025 recommends WorkManager for any processing beyond immediate notification display
**Impact**: Complex background processing should be offloaded to WorkManager

#### 4. **Insufficient Error Handling**
**Issue**: No error boundaries in critical notification paths
**Impact**: Silent failures in notification delivery

### Transport App Requirements

For Fiaranow's transport use case, notifications are **mission-critical**:
- **Driver notifications**: Trip requests, passenger updates (high priority)
- **Passenger notifications**: Driver status, trip updates (high priority)
- **Chat messages**: Real-time communication (high priority)
- **General updates**: Marketing, announcements (normal priority)

## Solution Architecture

### Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                        FCM Message Flow                        │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────▼───────────────┐
                    │    Background Handler         │
                    │  (Minimal Processing Only)    │
                    └───────────────┬───────────────┘
                                    │
                    ┌───────────────▼───────────────┐
                    │     WorkManager Task          │
                    │  (Heavy Processing & Logic)   │
                    └───────────────┬───────────────┘
                                    │
                    ┌───────────────▼───────────────┐
                    │    Local Notification         │
                    │     Display to User           │
                    └───────────────────────────────┘
```

### Implementation Strategy

1. **Streamlined Background Handler**: Minimal processing, immediate WorkManager scheduling
2. **WorkManager Integration**: Heavy operations moved to background workers
3. **High-Priority Messaging**: Server-side configuration for critical notifications
4. **Comprehensive Error Handling**: Fallback mechanisms and logging
5. **Doze Mode Testing**: Validation strategy for battery optimization scenarios

## Implementation Plan

### Phase 1: WorkManager Integration

#### 1.1 Add WorkManager Dependency
**File**: `fiaranow_flutter/pubspec.yaml`
**Action**: Add latest WorkManager package

**Current Version Research** (July 2025):
- **Latest Version**: `workmanager: ^0.8.0` (Published 6 days ago)
- **Platform Support**: Android & iOS
- **Dart SDK**: 3.5+ required
- **Architecture**: Federated plugin with platform-specific implementations

```yaml
dependencies:
  # Existing dependencies...
  workmanager: ^0.8.0  # Latest stable version as of July 2025
```

#### 1.2 WorkManager Service Implementation
**File**: `fiaranow_flutter/lib/services/NotificationWorkManager.dart` (NEW)
**Purpose**: Dedicated service for notification background processing

```dart
import 'package:workmanager/workmanager.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:logging/logging.dart';
import '../fcm.dart';

const String NOTIFICATION_TASK = 'notification_processing_task';
const String NOTIFICATION_WORK_TAG = 'fcm_notifications';

class NotificationWorkManager {
  static final Logger _logger = Logger('NotificationWorkManager');

  /// Initialize WorkManager for notification processing
  static Future<void> initialize() async {
    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: true, // Set to false in production
    );
    _logger.info('WorkManager initialized for notifications');
  }

  /// Schedule notification processing work
  static Future<void> scheduleNotificationWork(RemoteMessage message) async {
    try {
      await Workmanager().registerOneOffTask(
        'notification_${message.messageId}',
        NOTIFICATION_TASK,
        tag: NOTIFICATION_WORK_TAG,
        inputData: {
          'messageId': message.messageId ?? '',
          'title': message.notification?.title ?? '',
          'body': message.notification?.body ?? '',
          'data': message.data,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
        constraints: Constraints(
          networkType: NetworkType.connected, // Require network for data processing
        ),
      );
      _logger.info('Scheduled notification work for message: ${message.messageId}');
    } catch (e) {
      _logger.severe('Failed to schedule notification work: $e');
      // Fallback: show notification immediately
      await _showFallbackNotification(message);
    }
  }

  /// Cancel all pending notification work
  static Future<void> cancelAllWork() async {
    await Workmanager().cancelByTag(NOTIFICATION_WORK_TAG);
  }

  /// Fallback notification for WorkManager failures
  static Future<void> _showFallbackNotification(RemoteMessage message) async {
    try {
      // Initialize minimal notification display
      await initializeFlutterNotifications();
      await showNotification(message);
    } catch (e) {
      _logger.severe('Fallback notification also failed: $e');
    }
  }
}

/// WorkManager callback dispatcher
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    final Logger logger = Logger('WorkManager');
    
    try {
      logger.info('Executing background task: $task');
      
      if (task == NOTIFICATION_TASK) {
        await _processNotification(inputData);
        return Future.value(true);
      }
      
      logger.warning('Unknown task: $task');
      return Future.value(false);
    } catch (e) {
      logger.severe('WorkManager task failed: $e');
      return Future.value(false);
    }
  });
}

/// Process notification in WorkManager context
Future<void> _processNotification(Map<String, dynamic>? inputData) async {
  final Logger logger = Logger('NotificationProcessor');
  
  if (inputData == null) {
    logger.severe('No input data for notification processing');
    return;
  }

  try {
    // Initialize Firebase if needed (this is now in WorkManager context)
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }

    // Initialize notification system
    await initializeFlutterNotifications();

    // Reconstruct message from input data
    final messageData = Map<String, dynamic>.from(inputData['data'] ?? {});
    final title = inputData['title'] as String? ?? 'Notification';
    final body = inputData['body'] as String? ?? '';

    // Create notification details based on message type
    await _processSpecificNotification(title, body, messageData);
    
    logger.info('Successfully processed notification in WorkManager');
  } catch (e) {
    logger.severe('Failed to process notification in WorkManager: $e');
    throw e; // Let WorkManager handle retry
  }
}

/// Process specific notification types with enhanced logic
Future<void> _processSpecificNotification(
  String title, 
  String body, 
  Map<String, dynamic> data
) async {
  final Logger logger = Logger('NotificationProcessor');
  
  try {
    // Determine notification type and channel
    final notificationType = data['notificationType'] as String?;
    final isDriverNotification = data['isDriverNotification'] == 'true';
    
    String channelId;
    AndroidNotificationDetails androidDetails;

    // Configure notification based on type
    if (isDriverNotification || 
        ['trip_request', 'trip_cancelled', 'passenger_arrived'].contains(notificationType)) {
      // High-priority driver notifications with special ringtone
      channelId = 'driver_channel';
      androidDetails = AndroidNotificationDetails(
        channelId,
        'Driver Notifications',
        importance: Importance.max,
        priority: Priority.high,
        sound: const RawResourceAndroidNotificationSound('phone_ringtone_ultra'),
        additionalFlags: Int32List.fromList(<int>[4]), // FLAG_INSISTENT
        playSound: true,
        enableVibration: true,
        timeoutAfter: 60000, // 60 seconds
      );
    } else if (['driver_moving', 'driver_arrived', 'trip_paid'].contains(notificationType)) {
      // Silent notifications for status updates
      channelId = 'silent_notification';
      androidDetails = const AndroidNotificationDetails(
        'silent_notification',
        'Silent Notifications',
        importance: Importance.high,
        priority: Priority.high,
        playSound: false,
        enableVibration: false,
        enableLights: false,
        showBadge: true,
      );
    } else {
      // Default passenger notifications
      channelId = 'passenger_channel';
      androidDetails = const AndroidNotificationDetails(
        channelId,
        'Passenger Notifications',
        importance: Importance.high,
        priority: Priority.high,
        playSound: true,
        enableVibration: true,
      );
    }

    // Show the notification
    await flutterLocalNotificationsPlugin.show(
      data['notificationId']?.hashCode ?? DateTime.now().millisecondsSinceEpoch,
      title,
      body,
      NotificationDetails(android: androidDetails),
      payload: data.isNotEmpty ? data.entries.map((e) => '${e.key}=${e.value}').join('&') : null,
    );

    logger.info('Displayed notification: $title');
  } catch (e) {
    logger.severe('Failed to display notification: $e');
    // Try basic notification as fallback
    await flutterLocalNotificationsPlugin.show(
      DateTime.now().millisecondsSinceEpoch,
      title,
      body,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'default_channel',
          'Default Notifications',
          importance: Importance.high,
          priority: Priority.high,
        ),
      ),
    );
  }
}
```

### Phase 2: Streamlined Background Handler

#### 2.1 Optimized Background Handler Implementation
**File**: `fiaranow_flutter/lib/main.dart`
**Lines**: 57-67
**Action**: Replace current heavy background handler with minimal processing

```dart
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  final Logger logger = Logger('FCM_Background');
  
  try {
    logger.info('Received background message: ${message.messageId}');
    
    // MINIMAL processing only - Firebase 2025 best practice
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }
    
    // Schedule heavy work with WorkManager
    await NotificationWorkManager.scheduleNotificationWork(message);
    
    logger.info('Background message processing delegated to WorkManager');
  } catch (e) {
    logger.severe('Background handler error: $e');
    
    // Critical fallback: try immediate notification
    try {
      await initializeFlutterNotifications();
      await showNotification(message);
    } catch (fallbackError) {
      logger.severe('Fallback notification failed: $fallbackError');
    }
  }
}
```

#### 2.2 Main App Initialization Updates
**File**: `fiaranow_flutter/lib/main.dart`
**Lines**: Around 150-180 (in runner function)
**Action**: Add WorkManager initialization

```dart
void main() async {
  runner() async {
    // ... existing code until Firebase initialization ...
    
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Register the background message handler
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    
    // Initialize WorkManager for notification processing
    await NotificationWorkManager.initialize();
    
    // ... rest of existing initialization ...
  }
  
  await runner();
}
```

### Phase 3: High-Priority Message Configuration

#### 3.1 Server-Side Priority Configuration
**File**: `firebase/functions/src/notification_helpers.ts` (NEW)
**Purpose**: Utility for configuring high-priority messages

```typescript
import { Messaging } from 'firebase-admin/messaging';

export interface NotificationPriority {
  android: 'normal' | 'high';
  ios: '5' | '10'; // APNs priority
}

export interface NotificationConfig {
  title: string;
  body: string;
  data?: Record<string, string>;
  priority: NotificationPriority;
  channelId?: string;
  sound?: string;
  timeoutMs?: number;
}

// High-priority notification types for transport app
export const HIGH_PRIORITY_TYPES = [
  'trip_request',
  'trip_cancelled', 
  'driver_moving',
  'driver_arrived',
  'chat_message',
  'emergency',
  'payment_required',
] as const;

export function createHighPriorityMessage(
  tokens: string | string[],
  config: NotificationConfig
): Messaging.MulticastMessage | Messaging.Message {
  const baseMessage = {
    notification: {
      title: config.title,
      body: config.body,
    },
    data: {
      ...config.data,
      notificationId: Date.now().toString(),
      timestamp: new Date().toISOString(),
    },
    android: {
      priority: config.priority.android as 'high' | 'normal',
      notification: {
        channelId: config.channelId || 'default_channel',
        sound: config.sound || 'default',
        priority: config.priority.android === 'high' ? 'max' : 'default',
        ...(config.timeoutMs && { timeoutAfter: config.timeoutMs }),
      },
    },
    apns: {
      headers: {
        'apns-priority': config.priority.ios,
        'apns-push-type': 'alert',
      },
      payload: {
        aps: {
          sound: config.sound || 'default',
          badge: 1,
          'content-available': 1,
        },
      },
    },
    webpush: {
      headers: {
        Urgency: config.priority.android === 'high' ? 'high' : 'normal',
      },
    },
  };

  if (Array.isArray(tokens)) {
    return {
      ...baseMessage,
      tokens,
    } as Messaging.MulticastMessage;
  } else {
    return {
      ...baseMessage,
      token: tokens,
    } as Messaging.Message;
  }
}

// Predefined configurations for common notification types
export const NOTIFICATION_CONFIGS = {
  trip_request: {
    priority: { android: 'high', ios: '10' },
    channelId: 'driver_channel',
    sound: 'phone_ringtone_ultra',
    timeoutMs: 60000,
  },
  driver_status: {
    priority: { android: 'high', ios: '10' },
    channelId: 'passenger_channel', 
    sound: 'default',
  },
  chat_message: {
    priority: { android: 'high', ios: '10' },
    channelId: 'silent_notification',
    sound: 'default',
  },
  general_update: {
    priority: { android: 'normal', ios: '5' },
    channelId: 'default_channel',
    sound: 'default',
  },
} as const;
```

#### 3.2 Enhanced Notification Sending Function
**File**: `firebase/functions/src/send_notifications.ts`
**Action**: Update existing notification functions to use high priority

```typescript
import { logger } from 'firebase-functions/v2';
import { getMessaging } from 'firebase-admin/messaging';
import { 
  createHighPriorityMessage, 
  NOTIFICATION_CONFIGS, 
  HIGH_PRIORITY_TYPES,
  NotificationConfig 
} from './notification_helpers';

export async function sendTripRequestNotification(
  driverTokens: string[],
  tripData: {
    tripId: string;
    passengerName: string;
    pickupLocation: string;
    estimatedDistance: string;
  }
): Promise<void> {
  const config: NotificationConfig = {
    title: 'New Trip Request',
    body: `${tripData.passengerName} needs a ride from ${tripData.pickupLocation}`,
    data: {
      type: 'trip_request',
      tripId: tripData.tripId,
      passengerName: tripData.passengerName,
      pickupLocation: tripData.pickupLocation,
      estimatedDistance: tripData.estimatedDistance,
      isDriverNotification: 'true',
    },
    ...NOTIFICATION_CONFIGS.trip_request,
  };

  try {
    const message = createHighPriorityMessage(driverTokens, config);
    const response = await getMessaging().sendEachForMulticast(message);
    
    logger.info(`Trip request notification sent to ${driverTokens.length} drivers`, {
      successCount: response.successCount,
      failureCount: response.failureCount,
      tripId: tripData.tripId,
    });

    // Log failures for debugging
    response.responses.forEach((resp, idx) => {
      if (!resp.success) {
        logger.warn(`Failed to send to token ${idx}:`, resp.error);
      }
    });
  } catch (error) {
    logger.error('Failed to send trip request notification:', error);
    throw error;
  }
}

export async function sendDriverStatusNotification(
  passengerTokens: string[],
  statusData: {
    tripId: string;
    driverName: string;
    status: 'moving' | 'arrived' | 'completed';
    estimatedArrival?: string;
  }
): Promise<void> {
  let title: string;
  let body: string;
  let notificationType: string;

  switch (statusData.status) {
    case 'moving':
      title = 'Driver On The Way';
      body = `${statusData.driverName} is coming to pick you up`;
      notificationType = 'driver_moving';
      break;
    case 'arrived':
      title = 'Driver Arrived';
      body = `${statusData.driverName} has arrived at your location`;
      notificationType = 'driver_arrived';
      break;
    case 'completed':
      title = 'Trip Completed';
      body = `Your trip with ${statusData.driverName} is complete`;
      notificationType = 'trip_completed';
      break;
  }

  const config: NotificationConfig = {
    title,
    body,
    data: {
      type: notificationType,
      tripId: statusData.tripId,
      driverName: statusData.driverName,
      status: statusData.status,
      ...(statusData.estimatedArrival && { estimatedArrival: statusData.estimatedArrival }),
    },
    ...NOTIFICATION_CONFIGS.driver_status,
  };

  try {
    const message = createHighPriorityMessage(passengerTokens, config);
    const response = await getMessaging().sendEachForMulticast(message);
    
    logger.info(`Driver status notification sent`, {
      status: statusData.status,
      successCount: response.successCount,
      failureCount: response.failureCount,
      tripId: statusData.tripId,
    });
  } catch (error) {
    logger.error('Failed to send driver status notification:', error);
    throw error;
  }
}
```

### Phase 4: Comprehensive Error Handling

#### 4.1 Enhanced Error Handling in Message Listeners
**File**: `fiaranow_flutter/lib/main.dart`
**Lines**: ~240-280 (in _MyAppState.initState)
**Action**: Add comprehensive error handling

```dart
@override
void initState() {
  super.initState();

  // Initialize flutter_local_notifications with error handling
  _initializeNotifications();

  // Enhanced foreground message handler
  FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    _logger.info('Foreground message received: ${message.messageId}');
    
    try {
      _handleForegroundMessage(message);
    } catch (e) {
      _logger.severe('Foreground message handling failed: $e');
      _showFallbackNotification(message);
    }
  });

  // Enhanced message opened app handler
  FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
    _logger.info('Message opened app: ${message.messageId}');
    
    try {
      _handleFirebaseMessageNavigation(message);
    } catch (e) {
      _logger.severe('Message navigation failed: $e');
      // Continue app execution, don't crash
    }
  });

  // Handle initial message with error handling
  _handleInitialMessage();

  // ... rest of existing initState code ...
}

Future<void> _initializeNotifications() async {
  try {
    await initializeFlutterNotifications();
    _logger.info('Notifications initialized successfully');
  } catch (e) {
    _logger.severe('Failed to initialize notifications: $e');
    // Continue app execution - notifications are not critical for app startup
  }
}

Future<void> _handleForegroundMessage(RemoteMessage message) async {
  try {
    await showNotification(message);
  } catch (e) {
    _logger.severe('Failed to show foreground notification: $e');
    // Try fallback notification
    _showFallbackNotification(message);
  }
}

Future<void> _showFallbackNotification(RemoteMessage message) async {
  try {
    // Minimal fallback notification
    final title = message.notification?.title ?? 'Notification';
    final body = message.notification?.body ?? 'You have a new notification';
    
    await flutterLocalNotificationsPlugin.show(
      DateTime.now().millisecondsSinceEpoch,
      title,
      body,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'fallback_channel',
          'Fallback Notifications',
          importance: Importance.high,
          priority: Priority.high,
        ),
      ),
    );
  } catch (e) {
    _logger.severe('Even fallback notification failed: $e');
    // At this point, just log and continue - don't crash the app
  }
}

Future<void> _handleInitialMessage() async {
  try {
    RemoteMessage? initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      _logger.info('Handling initial message: ${initialMessage.messageId}');
      _handleFirebaseMessageNavigation(initialMessage);
    }
  } catch (e) {
    _logger.severe('Failed to handle initial message: $e');
    // Continue app execution
  }
}
```

#### 4.2 FCM Error Monitoring
**File**: `fiaranow_flutter/lib/services/FCMErrorMonitor.dart` (NEW)
**Purpose**: Centralized FCM error monitoring and reporting

```dart
import 'package:logging/logging.dart';
import '../services/AnalyticsService.dart';

class FCMErrorMonitor {
  static final Logger _logger = Logger('FCMErrorMonitor');
  static int _errorCount = 0;
  static DateTime? _lastErrorTime;
  
  static void reportError(String operation, dynamic error, StackTrace? stackTrace) {
    _errorCount++;
    _lastErrorTime = DateTime.now();
    
    _logger.severe('FCM Error in $operation: $error', error, stackTrace);
    
    // Report to analytics for monitoring
    AnalyticsService.captureException(error, stackTrace);
    AnalyticsService.trackEvent('fcm_error', {
      'operation': operation,
      'error_type': error.runtimeType.toString(),
      'error_count': _errorCount,
      'timestamp': _lastErrorTime!.toIso8601String(),
    });
    
    // Critical: if too many errors, disable notifications temporarily
    if (_errorCount > 10) {
      _logger.warning('Too many FCM errors, consider implementing circuit breaker');
    }
  }
  
  static void reportSuccess(String operation) {
    _logger.info('FCM Success: $operation');
    
    // Reset error count on success
    if (_errorCount > 0) {
      _logger.info('FCM recovered after $_errorCount errors');
      _errorCount = 0;
    }
    
    AnalyticsService.trackEvent('fcm_success', {
      'operation': operation,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  static Map<String, dynamic> getErrorStats() {
    return {
      'error_count': _errorCount,
      'last_error_time': _lastErrorTime?.toIso8601String(),
      'is_healthy': _errorCount < 5,
    };
  }
}
```

### Phase 5: Doze Mode Testing Strategy

#### 5.1 Testing Documentation
**File**: `docs/fcm_testing_guide.md` (NEW)
**Purpose**: Comprehensive testing strategy for notifications

```markdown
# FCM Testing Guide for Fiaranow

## Doze Mode Testing (Critical for Android)

### Test Environment Setup
1. **Device Requirements**: Physical Android device (emulator cannot test Doze mode)
2. **Android Version**: Android 6.0+ (when Doze mode was introduced)
3. **App Build**: Use release or profile build (debug may behave differently)

### Enable Doze Mode for Testing
```bash
# Force device into Doze mode immediately
adb shell dumpsys deviceidle force-idle

# Check Doze mode status  
adb shell dumpsys deviceidle

# Exit Doze mode
adb shell dumpsys deviceidle unforce
```

### Test Scenarios

#### High Priority Notifications (Should Wake Device)
- Trip request notifications
- Driver arrival notifications  
- Chat messages
- Emergency notifications

**Expected Behavior**: Device wakes up, notification displayed immediately

#### Normal Priority Notifications (May Be Delayed)
- General app updates
- Marketing notifications
- Non-urgent status updates

**Expected Behavior**: May be batched and delivered when device exits Doze mode

### Test Protocol
1. Send high-priority notification to device in Doze mode
2. Verify notification appears within 30 seconds
3. Check notification sound/vibration works
4. Verify tapping notification opens correct app screen
5. Monitor device battery usage during testing

### Network Testing
Test notifications under various network conditions:
- WiFi only
- Mobile data only  
- Poor signal conditions
- Network switching scenarios

### Performance Testing
Monitor these metrics during notification testing:
- Battery usage increase
- Memory consumption
- App startup time after notification
- Notification display latency
```

#### 5.2 Automated Testing Setup
**File**: `fiaranow_flutter/test/fcm_test.dart` (NEW)
**Purpose**: Unit tests for FCM functionality

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:mocktail/mocktail.dart';
import '../lib/services/NotificationWorkManager.dart';

class MockFirebaseMessaging extends Mock implements FirebaseMessaging {}
class MockRemoteMessage extends Mock implements RemoteMessage {}

void main() {
  group('FCM Integration Tests', () {
    late MockFirebaseMessaging mockMessaging;
    late MockRemoteMessage mockMessage;

    setUp(() {
      mockMessaging = MockFirebaseMessaging();
      mockMessage = MockRemoteMessage();
    });

    testWidgets('Background handler processes messages correctly', (tester) async {
      // Arrange
      when(() => mockMessage.messageId).thenReturn('test_message_123');
      when(() => mockMessage.data).thenReturn({
        'type': 'trip_request',
        'tripId': 'trip_123',
      });

      // Act
      await NotificationWorkManager.scheduleNotificationWork(mockMessage);

      // Assert
      // Verify WorkManager task was scheduled
      // This would require WorkManager mock setup
    });

    test('High priority message configuration is correct', () {
      // Test priority configuration for different message types
      final configs = NOTIFICATION_CONFIGS;
      
      expect(configs['trip_request']!.priority.android, equals('high'));
      expect(configs['trip_request']!.priority.ios, equals('10'));
      expect(configs['general_update']!.priority.android, equals('normal'));
    });

    test('Error handling works correctly', () {
      // Test error scenarios and fallback behavior
    });
  });
}
```

## Migration Timeline

### Week 1: Foundation (WorkManager Integration)
**Duration**: 5 business days
**Deliverables**:
- [ ] Add WorkManager dependency (Day 1)
- [ ] Implement NotificationWorkManager service (Days 2-3)
- [ ] Create callback dispatcher and task processing (Days 4-5)
- [ ] Unit tests for WorkManager integration (Day 5)

### Week 2: Background Handler Optimization  
**Duration**: 3 business days
**Deliverables**:
- [ ] Refactor background handler for minimal processing (Day 1)
- [ ] Update main app initialization (Day 1)
- [ ] Implement comprehensive error handling (Day 2)
- [ ] Test background processing flow (Day 3)

### Week 3: Server-Side Priority Configuration
**Duration**: 4 business days  
**Deliverables**:
- [ ] Create notification helper utilities (Day 1)
- [ ] Update Firebase Functions for high priority (Days 2-3)
- [ ] Test high vs normal priority delivery (Day 4)
- [ ] Deploy Functions to staging environment (Day 4)

### Week 4: Testing and Validation
**Duration**: 5 business days
**Deliverables**:
- [ ] Doze mode testing on physical devices (Days 1-2)
- [ ] Performance testing and optimization (Days 3-4)
- [ ] User acceptance testing (Day 5)
- [ ] Production deployment preparation (Day 5)

## Implementation Conditions and Requirements

### Development Environment
- **Flutter SDK**: Latest stable (3.5+)
- **Dart SDK**: 3.5+ (required for WorkManager 0.8.0)
- **Firebase CLI**: Latest version for Functions deployment
- **Test Devices**: Physical Android and iOS devices for Doze mode testing

### Platform Configurations

#### Android Requirements
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

#### iOS Requirements  
```xml
<!-- ios/Runner/Info.plist -->
<key>UIBackgroundModes</key>
<array>
    <string>fetch</string>
    <string>background-processing</string>
</array>
```

#### Server-Side Requirements
- **Firebase Project**: EU or closest region to Madagascar
- **Cloud Functions**: Node.js 18+ runtime
- **FCM Quotas**: Verify limits for high-priority messages

### Performance Benchmarks
- **Background Handler**: <500ms execution time
- **WorkManager Task**: <5 seconds for notification processing
- **Notification Display**: <1 second latency after FCM delivery
- **Battery Impact**: <5% additional usage during testing
- **Memory Usage**: <50MB additional for WorkManager

### Error Handling Requirements
- **Graceful Degradation**: App continues functioning if notifications fail
- **Fallback Mechanisms**: Multiple levels of notification fallbacks
- **Error Monitoring**: All FCM errors tracked and reported
- **Circuit Breaker**: Disable notifications temporarily if too many failures

## Success Metrics

### Functional Metrics
- [ ] **Notification Delivery**: 99%+ delivery rate for high-priority messages
- [ ] **Doze Mode Compatibility**: High-priority messages wake device consistently
- [ ] **Error Rate**: <1% notification processing failures
- [ ] **Background Processing**: 100% of heavy operations moved to WorkManager

### Performance Metrics
- [ ] **Background Handler Speed**: <500ms average execution time
- [ ] **Memory Efficiency**: <50MB additional RAM usage
- [ ] **Battery Impact**: <5% increase in battery consumption
- [ ] **Startup Performance**: No impact on app startup time

### User Experience Metrics
- [ ] **Sound/Vibration**: Correct notification behavior for each type
- [ ] **Navigation**: Tapping notifications opens correct app screens
- [ ] **Reliability**: Users report receiving critical notifications consistently

## Risk Assessment and Mitigation

### High Risk Items
1. **WorkManager Learning Curve**
   - *Risk*: Team unfamiliar with WorkManager implementation
   - *Mitigation*: Dedicated study time, simple implementation first

2. **Doze Mode Compatibility**
   - *Risk*: High-priority messages still affected by battery optimization
   - *Mitigation*: Extensive testing on multiple devices, fallback mechanisms

3. **Breaking Changes**
   - *Risk*: Notification system breaks during migration
   - *Mitigation*: Feature flag implementation, gradual rollout

### Medium Risk Items
1. **Performance Impact**
   - *Risk*: WorkManager adds overhead
   - *Mitigation*: Performance monitoring, optimization iterations

2. **Server Configuration**
   - *Risk*: Incorrect priority configuration
   - *Mitigation*: Thorough testing, staging environment validation

### Rollback Plan
1. **Immediate Rollback**: Revert background handler to previous implementation
2. **Gradual Rollback**: Disable WorkManager, use direct processing
3. **Feature Flag**: Toggle between old and new notification systems
4. **Monitoring**: Real-time dashboards for notification delivery rates

## Conclusion

This implementation brings the Fiaranow FCM system into full compliance with **Firebase 2025 best practices**. By implementing WorkManager for background processing, configuring high-priority messaging, and optimizing the background handler, the app will achieve:

1. **Reliable Notification Delivery**: Critical transport notifications delivered consistently
2. **Battery Optimization Compliance**: Works correctly with Android Doze mode
3. **Performance Optimization**: Background operations don't impact UI responsiveness  
4. **Scalable Architecture**: Foundation for future notification enhancements

The phased implementation approach ensures minimal disruption while providing comprehensive testing and validation at each stage. The success of this implementation is critical for user safety and experience in the transport domain. 