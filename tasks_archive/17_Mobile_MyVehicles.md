# Execution Plan: Mobile "My Vehicles" Feature

## Overview

Implement a comprehensive "My Vehicles" feature for drivers in the Flutter mobile app, allowing them to manage their personal vehicles and select which vehicle to drive at any given time. This feature will integrate with the existing Firebase backend and mirror the admin panel's vehicle management capabilities while providing a mobile-optimized user experience.

## Analysis

### Current State

**Admin System**: 
- Full vehicle management through SvelteKit admin panel at `/admin_sveltekit/src/routes/vehicles/`
- Vehicle model with comprehensive attributes (brand, model, color, year, registration, capacity, ownership)
- Vehicle linking system connecting vehicles to tenants
- Assignment system for driver-vehicle relationships
- Approval workflow for user-owned vehicles

**Flutter App**:
- Basic vehicle display screens already exist but are incomplete
- Models: `Vehicle.dart`, `VehicleAssignment.dart`, `VehicleLinking.dart`
- Screens: `VehicleManagementScreen.dart`, `AddVehicleScreen.dart`
- Current implementation shows user's vehicles and assigned vehicle status
- Limited functionality - no vehicle selection or comprehensive management

**Firebase Backend**:
- Complete vehicle operations in `firebase/functions/src/vehicle_operations.ts`
- Functions: `createVehicle`, `assignVehicleToDriver`, `updateVehicleStatus`
- Global vehicles collection with tenant-specific linking system
- Approval workflow for user-owned vehicles

### Requirements

1. **Vehicle Management**: Drivers must be able to add, view, edit, and deactivate their own vehicles
2. **Vehicle Selection**: Drivers must be able to choose which vehicle to drive from their available vehicles
3. **Status Visibility**: Clear indication of currently active/assigned vehicle
4. **Approval Process**: Integration with existing admin approval workflow
5. **Multi-Vehicle Support**: Handle drivers with multiple vehicles seamlessly
6. **Tenant Integration**: Proper integration with tenant-specific vehicle linking
7. **Real-time Updates**: Immediate UI updates when vehicle status changes
8. **Validation Workflow**: Vehicle information edits must trigger admin re-validation when needed
9. **Trip Engagement Check**: Prevent vehicle editing when vehicle is engaged in active trips
10. **Service Status Check**: Prevent vehicle editing when driver's service is active while assigned to the vehicle
11. **Admin-Only Assignment**: Only admins can assign vehicles to drivers, even for driver-owned vehicles
12. **Assignment Notifications**: Push notifications to drivers when admins assign vehicles to them
13. **Admin Notification System**: Automatic push notifications to admin users for vehicle re-validation
14. **Validation Status Tracking**: Clear indicators for pending, approved, and rejected validation states

### Research Findings

**GetX Best Practices for 2025**:
- Lightweight, performance-focused state management ideal for vehicle apps
- Reactive programming with `.obs` variables for real-time updates
- Built-in dependency injection and navigation management
- Minimal resource consumption crucial for mobile performance

**Mobile UI Patterns for Vehicle Selection**:
- Color-coded visual systems for quick vehicle identification
- Personalized interfaces with smart filtering and selection
- Split-screen workflows for multi-vehicle management
- Gesture-based controls for efficient navigation
- Safety-focused, intuitive design patterns

## Entity Relationship Diagram

The following diagram shows the key entities and relationships involved in the vehicle management system:

```mermaid
erDiagram
    %% FIRESTORE COLLECTION STRUCTURE
    %% Global Root Collections
    MOBILE_USERS ||--o{ MOBILE_USER_TENANT_STATES : "subcollection: /mobile_users/{uid}/tenant_states/{tenantId}"
    MOBILE_USERS ||--o{ MOBILE_USER_DRIVER_DOCS : "subcollection: /mobile_users/{uid}/driver_documents/{docId}"
    MOBILE_USERS }o--|| VEHICLES : "references via ownerUID field"
    
    ADMIN_USERS ||--o{ ADMIN_USER_TENANT_ACCESS : "subcollection: /admin_users/{uid}/tenants/{tenantId}"
    
    VEHICLES ||--o{ VEHICLE_DOCUMENTS : "subcollection: /vehicles/{vehicleId}/vehicle_documents/{docId}"
    VEHICLES }o--o{ VEHICLE_ASSIGNMENTS : "references via vehicleId field"
    
    TENANTS ||--o{ TENANT_VEHICLES_LINKING : "subcollection: /tenants/{tenantId}/vehicles_linking/{linkingId}"
    TENANTS ||--o{ TENANT_TRIPS : "subcollection: /tenants/{tenantId}/trips/{tripId}"
    TENANTS ||--o{ TENANT_ADMIN_NOTIFICATIONS : "subcollection: /tenants/{tenantId}/admin_notifications/{notificationId}"
    TENANTS ||--o{ TENANT_CONFIGURATIONS : "subcollection: /tenants/{tenantId}/configurations/{configId}"
    
    %% Document References (not foreign keys)
    TENANT_VEHICLES_LINKING }o--|| VEHICLES : "vehicleId reference"
    TENANT_VEHICLES_LINKING }o--o| MOBILE_USER_TENANT_STATES : "currentDriverId reference"
    
    VEHICLE_ASSIGNMENTS }o--|| VEHICLES : "vehicleId reference"
    VEHICLE_ASSIGNMENTS }o--|| TENANT_VEHICLES_LINKING : "vehicleLinkingId reference"
    VEHICLE_ASSIGNMENTS }o--|| MOBILE_USERS : "driverUID reference"
    
    MOBILE_USER_TENANT_STATES }o--o| TENANT_VEHICLES_LINKING : "currentVehicleLinkingId reference"
    
    TENANT_TRIPS }o--|| MOBILE_USERS : "uidPassenger reference"
    TENANT_TRIPS }o--o| MOBILE_USERS : "uidChosenDriver reference"
    
    TENANT_ADMIN_NOTIFICATIONS }o--|| ADMIN_USERS : "recipientUid reference"
    
    %% FIRESTORE DOCUMENT STRUCTURES
    MOBILE_USERS {
        string uid "Document ID"
        string email
        string displayName
        string phoneNumber
        boolean phoneNumberVerified
        string primaryUserType "rider|driver"
        object primaryLanguage
        number lat "Last known location"
        number lon "Last known location"
        object position "geohash + geopoint"
        string occupiedByTripId "Global trip engagement"
        map isServiceActiveByTenant "Denormalized per-tenant status"
        string fcmToken
        string deviceId
        array tenantIDs "Performance optimization"
        object notificationPreferences
    }
    
    MOBILE_USER_TENANT_STATES {
        string tenantId "Document ID"
        string uid "Parent document reference"
        boolean isActive
        string currentVehicleLinkingId "Document reference"
        array driverTags
        string isDriverConfirmed "Admin UID who confirmed"
        boolean isServiceActive "Source of truth"
        timestamp joinedAt
        timestamp lastActiveAt
    }
    
    VEHICLES {
        string vehicleId "Document ID"
        string brand
        string model
        string color
        number year
        string registrationNumber
        number maxPassengers "Source of truth for capacity"
        boolean isActive
        string ownerUID "NULL for tenant-owned, references MOBILE_USERS"
        string photoURL
        timestamp createdAt
        timestamp updatedAt
        string createdBy "Admin or mobile user UID"
    }
    
    TENANT_VEHICLES_LINKING {
        string linkingId "Document ID"
        string vehicleId "Document reference to VEHICLES"
        string tenantId "Parent collection context"
        string tenantRemark
        boolean tenantApproved "Required for user-owned vehicles"
        string currentDriverId "Document reference to MOBILE_USERS"
        timestamp linkedAt
        string linkedBy "Admin UID"
        boolean isActive
        boolean isOwnedByTenant "Internal vs external fleet"
        timestamp reviewedAt "Validation tracking"
        string reviewedBy "Admin who validated"
    }
    
    VEHICLE_ASSIGNMENTS {
        string assignmentId "Document ID"
        string vehicleId "Document reference"
        string vehicleLinkingId "Document reference"
        string driverUID "Document reference"
        string tenantId "Tenant context"
        timestamp createdAt "For sorting"
        timestamp assignedAt
        string assignedBy "Admin UID or system"
        timestamp unassignedAt
        string unassignedBy
        string reason "admin_assignment|driver_switch|maintenance|driver_unavailable"
        string notes
        boolean isActive "Only one active per vehicle"
    }
    
    TENANT_TRIPS {
        string tripId "Document ID"
        string uidPassenger "Document reference to MOBILE_USERS"
        string uidChosenDriver "Document reference to MOBILE_USERS"
        string status "requestingDriver|driverApproaching|driverAwaiting|inProgress|completed|cancelled"
        timestamp createdAt
        object startLocation "lat, lon, name"
        object arrivalLocation "lat, lon, name"
        object passenger "Embedded user data snapshot"
        object driver "Embedded user data snapshot"
        object tripConfiguration "Embedded pricing config"
        number estimatedCost
        number costTotal
        string paymentId "Document reference to payments subcollection"
    }
    
    TENANT_ADMIN_NOTIFICATIONS {
        string notificationId "Document ID"
        string recipientUid "Document reference to ADMIN_USERS"
        string recipientType "admin|mobile_user"
        string type "vehicle_revalidation_required|vehicle_approval|trip_reserved_admin"
        string title
        string body
        map data "vehicleId, tripId, reason, etc"
        timestamp createdAt
        timestamp sentAt
        timestamp readAt
        string status "pending|sent|delivered|read|failed"
        string channel "fcm|whatsapp|in_app"
        string errorMessage
    }
    
    MOBILE_USER_DRIVER_DOCS {
        string docId "Document ID"
        string tenantId "Multi-tenant scope"
        string documentType "license|insurance|vehicle_registration|national_id"
        string documentName
        string fileURL "Firebase Storage reference"
        timestamp issueDate
        timestamp expiryDate
        string notes
        string status "pending_review|approved|rejected|expired"
        timestamp uploadedAt
        string reviewedBy "Admin UID"
        timestamp reviewedAt
        string rejectionReason
    }
```

## Vehicle Management Operations Flow

The following sequence diagram shows the complete workflow for vehicle management operations including validation, editing, approval, and notifications:

```mermaid
sequenceDiagram
    participant Driver as Mobile Driver
    participant MobileApp as Flutter App
    participant Firebase as Firebase Functions
    participant Firestore as Firestore Database
    participant Admin as Admin Panel
    participant AdminUser as Admin User
    participant FCM as Push Notifications

    Note over Driver, FCM: Vehicle Addition Flow
    Driver->>MobileApp: Add new vehicle
    MobileApp->>MobileApp: Validate form data
    MobileApp->>Firebase: createVehicle(vehicleData, tenantId)
    
    Firebase->>Firestore: Create /vehicles/{vehicleId}
    Note right of Firestore: Global vehicle document<br/>ownerUID = driverUID
    
    Firebase->>Firestore: Create /tenants/{tenantId}/vehicles_linking/{linkingId}
    Note right of Firestore: tenantApproved = false<br/>isOwnedByTenant = false<br/>isActive = true
    
    Firebase->>Firestore: Create /tenants/{tenantId}/admin_notifications/{notificationId}
    Note right of Firestore: type = "vehicle_approval_required"
    
    Firebase->>FCM: Send notification to tenant admins
    FCM-->>AdminUser: Push notification: "New vehicle requires approval"
    
    Firebase-->>MobileApp: {success: true, vehicleId, linkingId}
    MobileApp-->>Driver: "Vehicle added, pending approval"

    Note over Driver, FCM: Vehicle Approval Flow
    AdminUser->>Admin: Review pending vehicle
    Admin->>Firebase: approveUserVehicle(linkingId, approved=true)
    
    Firebase->>Firestore: Update vehicles_linking
    Note right of Firestore: tenantApproved = true<br/>reviewedAt = timestamp<br/>reviewedBy = adminUID
    
    Firebase->>FCM: Send approval notification
    FCM-->>Driver: Push notification: "Vehicle approved"

    Note over Driver, FCM: Vehicle Assignment Request Flow (Driver-Initiated)
    Driver->>MobileApp: Request assignment for approved vehicle
    MobileApp->>Firebase: requestVehicleAssignment(vehicleId, driverUID, tenantId)
    
    Firebase->>Firestore: Query vehicles_linking
    Note right of Firestore: Check tenantApproved = true
    
    alt Vehicle Approved
        Firebase->>Firestore: Create /tenants/{tenantId}/admin_notifications/{notificationId}
        Note right of Firestore: type = "vehicle_assignment_request"
        
        Firebase->>FCM: Notify tenant admins
        FCM-->>AdminUser: "Driver requests vehicle assignment"
        
        Firebase-->>MobileApp: {success: true}
        MobileApp-->>Driver: "Assignment request sent to admin"
    else Vehicle Not Approved
        Firebase-->>MobileApp: {error: "Vehicle not approved"}
        MobileApp-->>Driver: "Vehicle pending admin approval"
    end

    Note over Driver, FCM: Admin Vehicle Assignment Flow
    AdminUser->>Admin: Review assignment request
    Admin->>Firebase: assignVehicleToDriver(vehicleId, driverUID, tenantId)
    
    Firebase->>Firestore: Update vehicles_linking.currentDriverId
    Firebase->>Firestore: Update mobile_users/{uid}/tenant_states/{tenantId}
    Note right of Firestore: currentVehicleLinkingId = linkingId
    
    Firebase->>Firestore: Create vehicle_assignments record
    Note right of Firestore: Global assignment history<br/>isActive = true
    
    Firebase->>FCM: Send assignment notification to driver
    FCM-->>Driver: "You have been assigned to {vehicleBrand} {vehicleModel}"
    
    Firebase-->>Admin: {success: true}
    Admin-->>AdminUser: "Vehicle assigned successfully"

    Note over Driver, FCM: Vehicle Editing with Validation
    Driver->>MobileApp: Edit vehicle details
    MobileApp->>Firebase: checkVehicleEditability(vehicleId, driverUID)
    
    Firebase->>Firestore: Query active trips
    Note right of Firestore: Check if vehicle in active trip<br/>status in [requestingDriver, driverApproaching,<br/>driverAwaiting, inProgress]
    
    Firebase->>Firestore: Query driver service status
    Note right of Firestore: Check MobileUserTenantState.isServiceActive<br/>AND currentVehicleLinkingId matches vehicle
    
    alt Vehicle in Active Trip
        Firebase-->>MobileApp: {editable: false, reason: "vehicle_in_trip"}
        MobileApp-->>Driver: "Cannot edit: Vehicle in active trip"
    else Driver Service Active with This Vehicle
        Firebase-->>MobileApp: {editable: false, reason: "service_active"}
        MobileApp-->>Driver: "Cannot edit: Service is active with this vehicle"
    else Vehicle Available for Edit
        Firebase->>Firestore: Check vehicles_linking.tenantApproved
        
        alt Previously Validated Vehicle
            Firebase-->>MobileApp: {editable: true, requiresRevalidation: true}
            MobileApp-->>Driver: "⚠️ Editing will require admin re-approval"
            
            Driver->>MobileApp: Confirm edit
            MobileApp->>Firebase: updateVehicle(vehicleId, updates)
            
            Firebase->>Firestore: Update /vehicles/{vehicleId}
            Firebase->>Firestore: Update vehicles_linking
            Note right of Firestore: tenantApproved = false<br/>reviewedAt = null<br/>requiresRevalidation = true
            
            Firebase->>Firestore: Create admin notification
            Note right of Firestore: type = "vehicle_revalidation_required"
            
            Firebase->>FCM: Notify admins of revalidation need
            FCM-->>AdminUser: "Vehicle requires re-validation"
            
            Firebase-->>MobileApp: {success: true, requiresApproval: true}
            MobileApp-->>Driver: "Changes saved. Awaiting admin approval."
            
        else Never Validated Vehicle
            Firebase-->>MobileApp: {editable: true, requiresRevalidation: false}
            
            Driver->>MobileApp: Save changes
            MobileApp->>Firebase: updateVehicle(vehicleId, updates)
            
            Firebase->>Firestore: Update /vehicles/{vehicleId}
            Firebase-->>MobileApp: {success: true}
            MobileApp-->>Driver: "Vehicle updated successfully"
        end
    end

    Note over Driver, FCM: Admin Re-validation Flow
    AdminUser->>Admin: Review revalidation request
    Admin->>Firebase: approveUserVehicle(linkingId, approved=true/false, remark)
    
    alt Approved
        Firebase->>Firestore: Update vehicles_linking
        Note right of Firestore: tenantApproved = true<br/>reviewedAt = timestamp
        
        Firebase->>FCM: Send approval notification
        FCM-->>Driver: "Vehicle changes approved"
        
    else Rejected
        Firebase->>Firestore: Update vehicles_linking
        Note right of Firestore: tenantApproved = false<br/>tenantRemark = rejection reason
        
        Firebase->>FCM: Send rejection notification
        FCM-->>Driver: "Vehicle changes rejected: {reason}"
    end

    Note over Driver, FCM: Admin Vehicle Reassignment Flow
    AdminUser->>Admin: Reassign driver to different vehicle
    Admin->>Firebase: reassignVehicleToDriver(newVehicleId, driverUID, tenantId)
    
    Firebase->>Firestore: Check active trips for current vehicle
    Firebase->>Firestore: Check driver service status
    
    alt Active Trips or Service Active
        Firebase-->>Admin: {error: "Cannot reassign: Active trip or service"}
        Admin-->>AdminUser: "Complete trip/stop service before reassigning"
    else Available for Reassignment
        Firebase->>Firestore: Unassign current vehicle
        Note right of Firestore: Update vehicle_assignments.isActive = false<br/>vehicles_linking.currentDriverId = null
        
        Firebase->>Firestore: Assign new vehicle
        Note right of Firestore: Create new vehicle_assignments record<br/>Update vehicles_linking.currentDriverId
        
        Firebase->>Firestore: Update driver tenant state
        Note right of Firestore: currentVehicleLinkingId = newLinkingId
        
        Firebase->>FCM: Send reassignment notification to driver
        FCM-->>Driver: "You have been reassigned to {newVehicle}"
        
        Firebase->>FCM: Notify admins of change
        FCM-->>AdminUser: "Driver reassigned successfully"
    end

    Note over Driver, FCM: Real-time Status Updates
    loop Status Monitoring
        MobileApp->>Firestore: Listen to vehicles_linking changes
        Firestore-->>MobileApp: Real-time approval status updates
        
        MobileApp->>Firestore: Listen to vehicle_assignments changes
        Firestore-->>MobileApp: Real-time assignment updates
        
        alt Status Changed
            MobileApp-->>Driver: Update UI with new status
        end
    end
```

## Task List

### Task 1: Enhance Vehicle State Management

- **Objective**: Create comprehensive GetX state management for vehicles
- **Actions**: 
  - Create `VehicleController` extending GetxController
  - Implement reactive state for user vehicles list, assigned vehicle, loading states
  - Add methods for vehicle CRUD operations
  - Integrate with Firebase Firestore streams for real-time updates
  - Handle tenant-specific vehicle filtering and assignments
- **Files**: 
  - `/fiaranow_flutter/lib/states/VehicleController.dart` (new)
- **Validation**: Controller properly manages vehicle state with real-time Firebase synchronization

### Task 2: Implement Vehicle Management Interface

- **Objective**: Allow drivers to view their vehicles and request assignments through admin
- **Actions**:
  - Create vehicle display functionality in VehicleController
  - Show vehicle approval status and assignment status
  - Add "Request Assignment" functionality that notifies admins
  - Display currently assigned vehicle prominently
  - Handle real-time updates when admin assigns/unassigns vehicles
  - Add optimistic UI updates for better user experience
- **Files**:
  - `/fiaranow_flutter/lib/states/VehicleController.dart`
  - `/fiaranow_flutter/lib/services/VehicleService.dart` (new)
- **Validation**: Drivers can view their vehicles and see assignment status with clear admin-managed workflow

### Task 3: Redesign Vehicle Management Screen

- **Objective**: Create modern, intuitive vehicle management interface
- **Actions**:
  - Redesign VehicleManagementScreen with 2025 UI patterns  
  - Implement color-coded vehicle cards for quick identification
  - Add prominent "Currently Assigned" vehicle section at top (admin-assigned)
  - Create "My Vehicles" section showing approval and assignment status
  - Add "Request Assignment" buttons for approved vehicles
  - Add floating action button for adding new vehicles
  - Implement pull-to-refresh for real-time updates
  - Add empty state with compelling call-to-action
- **Files**:
  - `/fiaranow_flutter/lib/screens/VehicleManagementScreen.dart`
- **Validation**: Intuitive interface clearly shows assigned vehicle and admin-managed assignment workflow

### Task 4: Enhanced Vehicle Addition Flow

- **Objective**: Improve the add vehicle experience with better validation and UX
- **Actions**:
  - Enhance AddVehicleScreen with improved form validation
  - Add vehicle brand/model dropdown suggestions
  - Implement color picker with visual color swatches
  - Add photo capture capability for vehicle documentation
  - Create progress indicator for multi-step form
  - Add approval status explanation and tracking
  - Implement form auto-save to prevent data loss
  - Add validation warning when vehicle requires admin approval
  - Display clear messaging about approval workflow for user-owned vehicles
- **Files**:
  - `/fiaranow_flutter/lib/screens/AddVehicleScreen.dart`
  - `/fiaranow_flutter/lib/widgets/VehicleFormWidgets.dart` (new)
- **Validation**: Streamlined vehicle addition with clear approval status feedback and workflow explanation

### Task 5: Vehicle Details and Edit Screen

- **Objective**: Provide detailed vehicle information and editing capabilities
- **Actions**:
  - Create VehicleDetailsScreen for comprehensive vehicle information
  - Add edit mode for updating vehicle details
  - Implement vehicle status management (active/inactive)
  - Add vehicle history and assignment tracking
  - Create delete/remove vehicle functionality
  - Add sharing capability for vehicle information
  - Add trip engagement validation - prevent editing if vehicle is in active trip
  - Add service status validation - prevent editing if driver service is active while assigned
  - Display warning when editing validated vehicle (will require re-approval)
  - Add Firebase function call to validate vehicle availability for editing
  - Show clear validation status indicators (pending, approved, rejected)
- **Files**:
  - `/fiaranow_flutter/lib/screens/VehicleDetailsScreen.dart` (new)
  - `/fiaranow_flutter/lib/screens/EditVehicleScreen.dart` (new)
  - `/fiaranow_flutter/lib/services/VehicleValidationService.dart` (new)
- **Validation**: Complete vehicle information management with proper validation and approval workflow

### Task 6: Vehicle Display and Status Widgets

- **Objective**: Create reusable vehicle display components for use across the app
- **Actions**:
  - Design VehicleStatusWidget showing assignment status and approval state
  - Implement compact vehicle display for driver dashboard
  - Add vehicle status indicators (assigned by admin, available, pending approval)
  - Create "Request Assignment" action buttons
  - Add quick actions (call admin, view details, request assignment)
  - Show edit restrictions when service is active
- **Files**:
  - `/fiaranow_flutter/lib/widgets/VehicleStatusWidget.dart` (new)
  - `/fiaranow_flutter/lib/widgets/VehicleStatusCard.dart` (new)
- **Validation**: Reusable components work consistently and show proper admin-managed workflow

### Task 7: Navigation Integration

- **Objective**: Integrate vehicle management into app navigation and routing
- **Actions**:
  - Add "My Vehicles" to main navigation drawer/bottom bar
  - Update routing to include all vehicle-related screens
  - Add deep linking support for vehicle management
  - Create navigation shortcuts from driver dashboard
  - Integrate with existing GetX navigation patterns
- **Files**:
  - `/fiaranow_flutter/lib/routes/AppRoutes.dart`
  - `/fiaranow_flutter/lib/widgets/NavigationDrawer.dart` or similar
- **Validation**: Vehicle management is accessible through intuitive navigation paths

### Task 8: Localization and Accessibility

- **Objective**: Ensure proper localization and accessibility support
- **Actions**:
  - Add all vehicle management strings to localization files
  - Implement accessibility labels and semantic descriptions
  - Add support for screen readers and assistive technologies
  - Test with different language settings
- **Files**:
  - `/fiaranow_flutter/lib/l10n/app_en.arb`
  - `/fiaranow_flutter/lib/l10n/app_fr.arb` 
  - All vehicle-related screen files (accessibility updates)
- **Validation**: Full localization support with proper accessibility features

### Task 9: Error Handling and Edge Cases

- **Objective**: Implement comprehensive error handling and edge case management
- **Actions**:
  - Add network connectivity handling for offline scenarios
  - Implement retry mechanisms for failed operations
  - Create informative error messages and recovery options
  - Handle edge cases (no vehicles, pending approvals, assignment conflicts)
  - Add logging for debugging and monitoring
  - Implement loading states and skeleton screens
- **Files**:
  - All vehicle-related screen and controller files
  - `/fiaranow_flutter/lib/utils/ErrorHandler.dart` (updates)
- **Validation**: Robust error handling with clear user feedback and recovery options

### Task 10: Firebase Security Rules Updates

- **Objective**: Ensure proper security rules for vehicle operations
- **Actions**:
  - Review and update Firestore security rules for vehicle operations
  - Ensure users can only access their own vehicles
  - Verify tenant-based access controls are properly implemented
  - Test security rules with various user scenarios
  - Document security rule changes
- **Files**:
  - `/firebase/firestore.rules`
- **Validation**: Security rules properly restrict access while allowing legitimate operations

### Task 11: Integration Testing

- **Objective**: Ensure seamless integration between mobile app and backend
- **Actions**:
  - Test vehicle creation flow from mobile to admin approval
  - Verify real-time updates when admin approves/rejects vehicles  
  - Test vehicle assignment/unassignment workflows
  - Validate multi-vehicle scenarios with proper state management
  - Test offline/online synchronization
  - Perform cross-platform testing (iOS/Android)
- **Files**:
  - Test files and validation scripts
- **Validation**: All vehicle operations work seamlessly across mobile app and admin system

### Task 12: Firebase Backend Enhancements for Vehicle Validation

- **Objective**: Extend Firebase functions to support vehicle validation and admin notifications
- **Actions**:
  - Create `updateVehicle` Firebase function for editing vehicle information
  - Add trip engagement validation in vehicle update function
  - Add service status validation - prevent editing if driver service active while assigned
  - Implement automatic admin notification when vehicle needs re-validation
  - Add `checkVehicleEditability` Firebase function to validate edit permissions
  - Create `requestVehicleAssignment` Firebase function for assignment requests
  - Extend existing notification system to support vehicle re-validation alerts
  - Update admin notification types to include `vehicle_revalidation_required` and `vehicle_assignment_request`
  - Add validation workflow tracking in vehicle linking documents
  - Ensure vehicle assignment notifications are sent to drivers when admin assigns vehicles
- **Files**:
  - `/firebase/functions/src/vehicle_operations.ts` (updates)
  - `/firebase/functions/src/notification_operations.ts` (updates)
  - `/firebase/functions/src/index.ts` (function exports)
- **Validation**: Complete backend support for vehicle validation workflow with admin notifications

### Task 13: Performance Optimization

- **Objective**: Optimize performance for smooth user experience
- **Actions**:
  - Implement efficient image loading and caching for vehicle photos
  - Optimize Firebase queries to minimize bandwidth usage
  - Add pagination for large vehicle lists
  - Implement lazy loading for vehicle details
  - Optimize state management to prevent unnecessary rebuilds
  - Add performance monitoring and analytics
- **Files**:
  - All vehicle-related files (optimization updates)
- **Validation**: App performs smoothly with fast loading times and minimal resource usage