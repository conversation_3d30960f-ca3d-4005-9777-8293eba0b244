# Denormalizing `isServiceActiveByTenant` for Efficient Filtering
Completed on 2025-07-03

This document outlines the strategy for denormalizing the `isServiceActive` status from the `MobileUserTenantState` sub-collection to the parent `MobileUser` document. This is designed to enable efficient querying and filtering of users based on their service status within a specific tenant.

⚠️ **IMPORTANT**: This migration will completely remove the top-level `isServiceActive` field from the `mobile_users` collection and replace it with `isServiceActiveByTenant` map.

## 1. Problem Statement

The application's data model was refactored to be fully multi-tenant aware. As part of this, a user's state, which can differ between tenants, was moved into a dedicated sub-collection: `/mobile_users/{uid}/tenant_states/{tenantId}`. This sub-collection now holds the `isServiceActive` boolean, which indicates if a driver is on duty for a particular tenant.

While this normalization is architecturally sound, it introduces a significant challenge for reading and filtering data. For instance, to display a list of all *active* drivers for a given tenant, the application would need to:

1.  Query the `tenant_states` collection group to find all states where `isServiceActive` is `true`.
2.  Extract the `uid` from each of these state documents.
3.  Perform a second query on the `mobile_users` collection to fetch the full profile for each `uid`.

This two-step process is inefficient, especially with real-time listeners, and can lead to increased client-side complexity and UI lag.

## 2. Proposed Solution: Denormalization

To solve this, we will denormalize the `isServiceActive` status by adding a map to the parent `MobileUser` document and completely remove the old top-level `isServiceActive` field.

The `MobileUser` model will be updated to:
- Remove the `isServiceActive` field completely
- Add a new field: `isServiceActiveByTenant`

### `MobileUser` Model Update

```json
// Path: /mobile_users/{uid}
{
  "uid": "...",
  "displayName": "...",
  "photoURL": "...",
  // ... other existing fields

  // ---- NEW FIELD ----
  "isServiceActiveByTenant": {
    "fiaranow": true,       // User is active in the 'fiaranow' tenant
    "tenant_b": false,      // User is inactive in 'tenant_b'
    "tenant_c": true        // User is active in 'tenant_c'
  }
}
```

## 3. Implementation

### Reading Data: Simplified Queries

With the new `isServiceActiveByTenant` map, querying for all active drivers for a specific tenant becomes a single, highly efficient query.

**Example: Get all active drivers for `fiaranow`**
```javascript
import { collection, query, where, getDocs } from "firebase/firestore";

const db = getFirestore();
const tenantId = "fiaranow";

const usersRef = collection(db, "mobile_users");

// The query uses dot notation to access the nested field in the map
const q = query(usersRef, where(`isServiceActiveByTenant.${tenantId}`, "==", true));

const querySnapshot = await getDocs(q);
// querySnapshot.docs now contains the full MobileUser documents for all active drivers.
```

This approach is significantly faster and simpler, as it requires only one round trip to the database and eliminates any need for client-side data joining.

### Writing Data: Maintaining Integrity with Firebase Functions

To ensure the denormalized `isServiceActiveByTenant` map remains synchronized with the source of truth (`MobileUserTenantState` document), we will use a **Firebase Function (v2)**.

This function will be triggered whenever a `MobileUserTenantState` document is created or updated. It will use a transaction to guarantee atomicity, following the "reads-before-writes" pattern.

**Trigger:** `onDocumentWritten("/mobile_users/{uid}/tenant_states/{tenantId}")`

**Function Logic:**

1.  The function is triggered by a write event on a `tenant_states` document.
2.  It extracts the `uid` and `tenantId` from the event parameters.
3.  It checks if the `isServiceActive` field was actually part of the change to avoid unnecessary writes.
4.  It initiates a Firestore transaction.
5.  **Read Phase:** Inside the transaction, it reads the parent `MobileUser` document at `/mobile_users/{uid}`.
6.  **Write Phase:** It updates the `isServiceActiveByTenant` map on the `MobileUser` document, setting the value for the specific `tenantId` from the trigger data.
7.  The transaction is committed.

**Example Firebase Function (v2 - TypeScript):**

```typescript
import { onDocumentWritten } from "firebase-functions/v2/firestore";
import { getFirestore, FieldValue } from "firebase-admin/firestore";
import { logger } from "firebase-functions/v2";

const db = getFirestore();

export const onTenantStateChangeUpdateUser = onDocumentWritten({
  document: "mobile_users/{uid}/tenant_states/{tenantId}",
  // Use standard project configuration (see firebase-functions.mdc rule)
}, async (event) => {
  const { uid, tenantId } = event.params;
  const change = event.data;

  // Guard against deletions - remove tenant from map
  if (!change || !change.after.exists) {
    logger.info(`Tenant state for ${uid} in ${tenantId} was deleted. Removing from map.`);
    const userRef = db.collection("mobile_users").doc(uid);
    await userRef.update({
      [`isServiceActiveByTenant.${tenantId}`]: FieldValue.delete(),
    });
    return;
  }

  const beforeData = change.before.data();
  const afterData = change.after.data();

  // Only proceed if isServiceActive has changed
  if (beforeData?.isServiceActive === afterData?.isServiceActive) {
    logger.info("No change in isServiceActive, skipping update.");
    return;
  }

  const isServiceActive = afterData.isServiceActive;
  const userRef = db.collection("mobile_users").doc(uid);

  logger.info(
    `Updating isServiceActiveByTenant for user ${uid}, tenant ${tenantId} to ${isServiceActive}`
  );

  try {
    await userRef.update({
      [`isServiceActiveByTenant.${tenantId}`]: isServiceActive,
    });
    logger.info("Successfully updated MobileUser document.");
  } catch (error) {
    logger.error(
      `Failed to update isServiceActiveByTenant for user ${uid}`,
      error
    );
  }
});
```

**Note**: Function configuration follows the patterns defined in [firebase-functions.mdc](mdc:.cursor/rules/firebase-functions.mdc).

## 4. Code Changes Required

### 4.1 Flutter App Changes

**MobileUser Model** (`fiaranow_flutter/lib/models/MobileUser.dart`):
- Remove `isServiceActive` field completely
- Add `isServiceActiveByTenant` map field
- Update `fromFirestore` and `toFirestore` methods

**AuthState** (`fiaranow_flutter/lib/states/AuthState.dart`):
- Update `setServiceActive` method to write to the tenant state sub-collection only
- Implement automatic tenant state creation during login (see section 4.4 below)

**UI Components**:
- Update `DriverModeWidget` and other components that check `isServiceActive` to use `isServiceActiveByTenant[TenantConfig.TENANT_ID]`

### 4.2 Admin Panel Changes

**Mobile Users Store** (`admin_sveltekit/src/lib/stores/mobile_users.svelte.ts`):
- Remove `isServiceActive` from the `MobileUser` interface
- Add `isServiceActiveByTenant?: Record<string, boolean>` field
- Update the store to use the new field structure

### 4.3 Firebase Functions Changes

**Driver Availability** (`firebase/functions/src/driver_availability.ts`):
- Already checks tenant state sub-collection, no changes needed

**Navigation Handlers** (`firebase/functions/src/navigation_handlers.ts`):
- Update to check `isServiceActiveByTenant[tenantId]` instead of `isServiceActive`

**Trip State Operations** (`firebase/functions/src/trip_state_operations.ts`):
- Update to check `isServiceActiveByTenant[tenantId]` instead of `isServiceActive`

### 4.4 Tenant State Creation During Login

**Critical Implementation Requirements**: Update the mobile app login flow to ensure tenant states are created automatically.

**Location**: `fiaranow_flutter/lib/states/AuthState.dart` in the `_verifyDeviceAndCompleteLogin` method

**Important Points to Cover**:

1. **Timing**: After the user document is created/updated during login
2. **Tenant Detection**: Use `TenantConfig.TENANT_ID` to identify current tenant
3. **State Creation**: Create tenant state document if it doesn't exist with:
   - `uid`, `tenantId`, `isActive: true`, `isServiceActive: false`
   - `driverTags: []`, `joinedAt`, `lastActiveAt` timestamps
4. **Tenant Array**: Ensure `tenantIDs` array in mobile_users includes current tenant
5. **Existing Users**: Update `lastActiveAt` for users who already have tenant state
6. **Error Handling**: Proper logging and error management
7. **Performance**: Non-blocking operation that doesn't affect login flow

**Benefits**:
- **Automatic Enrollment**: Every user gets enrolled in current tenant on login
- **Sync Function Ready**: Firebase function will populate `isServiceActiveByTenant` automatically
- **Data Integrity**: Ensures all users have proper tenant states
- **Login Performance**: Creates required data structure immediately

## 5. Migration Implementation

### 5.1 Migration Script Requirements

Following the [Migration flow.md](Migration%20flow.md) standards, the migration must cover:

**Migration ID**: `002__denormalize-service-active-by-tenant`

**Key Implementation Points**:

1. **Pre-Validation**:
   - Verify `onTenantStateChangeUpdateUser` function is deployed
   - Confirm mobile app login flow has been updated

2. **Data Population**:
   - Find all users with existing tenant states
   - Build `isServiceActiveByTenant` map from tenant state data
   - Use batch operations (max 500 per batch)
   - Handle users with no tenant states appropriately

3. **Field Removal**:
   - Remove the old `isServiceActive` field from all mobile_users documents
   - Ensure no code references remain to the old field

4. **Tracking & Logging**:
   - Count users processed, populated, skipped
   - Log critical steps and any errors
   - Include verification steps in migration metadata

5. **Rollback Considerations**:
   - Mark as non-rollbackable due to code deployment requirements
   - Document recovery procedures if needed

### 5.2 Pre-Migration Requirements

1. **Deploy Firebase Function**: The `onTenantStateChangeUpdateUser` function
2. **Update Mobile App**: Implement tenant state creation in login flow
3. **Test Function**: Verify sync function works correctly
4. **Backup Data**: Ensure recent backups are available

### 5.3 Post-Migration Verification

1. **Test Sync**: Verify service status changes sync to `isServiceActiveByTenant`
2. **Test Login**: Confirm new logins create tenant states automatically
3. **Query Performance**: Validate new query patterns work efficiently
4. **Monitor Logs**: Watch for sync errors in Firebase function logs
5. **Field Removal**: Confirm old `isServiceActive` field is completely removed

## 6. Backward Compatibility Pattern

During the transition period, implement this pattern for reading service status:

```typescript
// TypeScript/JavaScript pattern
function isDriverServiceActive(userData: any, tenantId: string): boolean {
  // Use the new field structure
  if (userData.isServiceActiveByTenant && 
      userData.isServiceActiveByTenant.hasOwnProperty(tenantId)) {
    return userData.isServiceActiveByTenant[tenantId];
  }
  
  // Return false if no tenant-specific data exists
  return false;
}
```

```dart
// Dart/Flutter pattern
bool isDriverServiceActive(MobileUser user, String tenantId) {
  // Use the new field structure
  if (user.isServiceActiveByTenant != null && 
      user.isServiceActiveByTenant!.containsKey(tenantId)) {
    return user.isServiceActiveByTenant![tenantId] ?? false;
  }
  
  // Return false if no tenant-specific data exists
  return false;
}
```

## 7. Implications & Trade-offs

*   **Pros:**
    *   **High Read Performance:** Queries are fast, simple, and scalable.
    *   **Simplified Client Logic:** Clean, purpose-built data structure.
    *   **Improved User Experience:** Faster data loading leads to a more responsive UI.
    *   **Multi-tenant Ready:** Native support for multiple tenants.
    *   **Data Consistency:** Single source of truth with automatic sync.
    *   **Automatic Enrollment:** New users get proper tenant states immediately.

*   **Cons:**
    *   **Breaking Change:** Complete removal of old field requires careful migration.
    *   **Increased Write Complexity:** Firebase Functions required for sync.
    *   **Data Duplication:** Service status stored in both tenant states and mobile_users.
    *   **Migration Complexity:** Requires coordinated deployment of multiple components.
    *   **Login Complexity:** Additional logic required in mobile app login flow.

## 8. Conclusion

This migration completely modernizes the service status data structure to be fully multi-tenant aware. By removing the old `isServiceActive` field and implementing `isServiceActiveByTenant`, the system gains significant performance improvements and architectural clarity.

The automatic tenant state creation during login ensures seamless user enrollment, while the Firebase function sync maintains data consistency across the denormalized structure. This approach provides a clean, scalable foundation for multi-tenant service status management. 