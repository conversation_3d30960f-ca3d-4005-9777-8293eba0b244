# 15_Algolia_Search Execution Plan

⚠️ **Development Only — no remote deployments; focus on local implementation**

A step-by-step guide for integrating Algolia search in local development and planning for production.

---

🔍 1. Inventory Collections

- Identify “large” data sets to index:
  - trips (per tenant)
  - mobile_users
  - vehicles
  - payments
  - chat_sessions (ordered by lastMessageAt)
- Skip small/static entities (configs, admin_users, event_logs, etc.)
- Plan tenant isolation: include `tenantId` in each record and/or use per-tenant index suffixes; always filter by `tenantId` to prevent cross-tenant visibility.

⚙️ 2. **FOR HUMAN**: Algolia Account Setup

- Sign up or log in at Algolia (algolia.com).
- Obtain:
  - Application ID
  - Admin API Key
  - Search-Only API Key
- In the dashboard, plan two index prefixes:
  - `dev_` for local/emulator
  - `prod_` for future production

🛠️ 3. Configure Environments (Development Only)

- Local (emulator):
  - Ensure Firebase CLI points to your local emulator project (e.g. `firebase use dev`).
  - Use `firebase functions:config:set` to store Algolia keys and `index_prefix=dev`.
  - Restart compose to pick up new config; never run `firebase deploy` now.
- Production (Outline Only):
  - Plan to repeat config with `index_prefix=prod` on the live Firebase project, but skip execution in this phase.

🔄 4. Implement Firestore → Algolia Sync

- For each target collection, add Firestore triggers (onWrite, onDelete).
- In each trigger:
  - Derive the correct Algolia index via `index_prefix` and optionally tenant suffix.
  - Map document fields (must include `tenantId` and `objectID`).
  - Include related entity fields in the record (e.g. `passengerName`, `driverName`) so search covers associated users by default.
  - Ensure mapping supports toggling: default index `searchableAttributes` includes both core and related fields; plan a way to restrict mapping or query to core fields only (e.g. location) when desired.
  - Save or delete the record in Algolia.

💻 5. Integrate Search in Admin App

- Install Algolia client & InstantSearch libraries.
- Expose env vars in `admin_sveltekit/.env` only:
  - `VITE_ALGOLIA_APP_ID`
  - `VITE_ALGOLIA_SEARCH_KEY`
  - `VITE_ALGOLIA_INDEX_PREFIX`
  - `VITE_TENANT_ID`
- Add InstantSearch components (SearchBox, Hits) on relevant pages (e.g., trips list).
- Apply tenant filter in UI (e.g. `searchParameters={{ filters: 'tenantId:' + VITE_TENANT_ID }}`).
- Provide a UI toggle (e.g. checkbox) for including/excluding related fields in the search; toggle should update `searchableAttributes` or `searchParameters` to restrict search to core fields (e.g. location) when turned off.

📋 6. Local Testing

- Start services via `docker-compose -f compose.yaml up`.
- Confirm `firebase use` is set to dev before any CLI operations.
- Create/update Firestore docs via emulator or Admin UI.
- Verify records appear in Algolia “dev_” indices.
- Test search UI in browser for expected, tenant-isolated results.

🔜 Next Steps

- Define field mappings, facets, typo-tolerance, and custom ranking in Algolia dashboard.
- Monitor record usage, quotas, and performance.
- Iterate based on internal testing and user feedback. 