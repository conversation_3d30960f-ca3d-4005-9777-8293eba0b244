# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Priorities

- ALWAYS run `fvm flutter analyze` after editing Dart files
- ALWAYS check `if (mounted)` before `setState` in Flutter to prevent state updates on unmounted widgets
- ALWAYS check collections before access: `if (myList.isNotEmpty)` to prevent null/index errors
- ALWAYS follow [docs/flutter_styling_guide.md](docs/flutter_styling_guide.md) when working with selectable UI components, dark mode support, or visual styling
- NEVER remove existing features without explicit user confirmation
- NEVER create files unless explicitly required by the task
- NEVER run deployment commands (e.g., `firebase deploy`, `npm run deploy`, etc.) - only remind the user to run them
- Never deploy anything to production. That task is strictly reserved for the user

### Firestore Index Management
- ALWAYS check `/firebase/firestore.indexes.json` when:
  - Adding new Firestore queries in Flutter (especially with `.where()` clauses)
  - Creating new listeners with complex queries
  - Modifying Firebase functions that query Firestore
- REQUIRED indexes for compound queries:
  - Multiple `.where()` clauses need composite indexes
  - `whereIn` queries need indexes on other fields (not the whereIn field)
  - Queries with `.orderBy()` combined with `.where()` need indexes
- After updating indexes: REMIND user to run `firebase deploy --only firestore:indexes` (NEVER run this command yourself)
- Common query patterns that need indexes:
  - `where('field1', '==', value).where('field2', '==', value)` 
  - `where('field1', '==', value).orderBy('field2')`
  - `where('field1', 'in', [...]).where('field2', '==', value)`

## Logging Guidelines

For comprehensive logging documentation, refer to platform-specific guides:
- **Flutter App**: See [docs/logging/flutter-logging.md](docs/logging/flutter-logging.md) for hierarchical logging, Crashlytics integration, and error handling patterns
- **Admin Panel**: See [docs/logging/admin-logging.md](docs/logging/admin-logging.md) for toast notifications, console logging, and Firebase integration
- **Firebase Functions**: See [docs/logging/functions-logging.md](docs/logging/functions-logging.md) for structured logging, audit trails, and performance monitoring

### Critical Logging Requirements:
- **ALWAYS surface real errors**: Never hide actual runtime errors behind generic messages
- **Use structured logging**: Include relevant context (user ID, trip ID, tenant ID, operation)
- **Log both success and failure**: Track operation outcomes for debugging
- **Follow environment patterns**: Detailed logs in development, essential logs in production

## Preferences

### Workflow Preferences

### Flutter Mobile App (`/fiaranow_flutter/`)
- Use GetX: Obx widgets, Rx variables, non-const stateless widgets with GetX
- File names: `NameOfModel.dart` not `name_of_model.dart`
- States in `/lib/states/`, Models in `/lib/models/`
- **UI Styling**: Follow [docs/flutter_styling_guide.md](docs/flutter_styling_guide.md) for selection states, dark mode support, and visual consistency

### Admin Dashboard (`/admin_sveltekit/`)
- Use Svelte 5 runes: `$state`, `$props`, not `export let`
- Event handlers: `onclick` not `on:click`
- Toast: `toast.success()` from "svelte-sonner"
- Spinner: `import Spinner from "$lib/components/ui/Spinner.svelte"`
- Firebase calls: Use `callFunction` from `firebase.client.ts`
- Date formatting: Use `datetime.ts` utils only
- Translations: Import `* as m from '$lib/paraglide/messages'`, use `{m.keyName()}`
- Translation files: `messages/en-us.json` and `messages/fr-fr.json`
- Language switching: `switchToLanguage()` function in layout

### Firebase Backend (`/firebase/`)
- Keep functions modular, not all in index.ts
- Region: europe-west3
- Node.js 22, TypeScript
- No need to do an `npm run build` for Firebase Functions. @compose.yaml already has a build:watch that does that automatically.

### Display Conventions
- Currency: "XXXXX Ar" (no decimals, no "MGA")
- Card scrolls: `h-[calc(100vh-Xpx)]` (140-152px offset)

## Reference Commands (for user to run)

**Flutter**: `fvm flutter analyze` | `fvm flutter run` | `./build-appbundle.sh` | `./build-ipa.sh`

**Admin**: `npm run dev` | `npm run build` | `npm run check` | `npm test`

**Firebase**: `npm run build` | `npm run serve` | `npm run deploy` | `npm run spin` | `firebase deploy --only firestore:indexes`

**Git**: `git update-index --skip-worktree admin_sveltekit/src/lib/firebase.client.ts`

Note: I can run development/analysis commands (like `fvm flutter analyze`, `npm run check`) but NEVER:
- Deployment commands (`firebase deploy`, `npm run deploy`)
- Production build commands (`./build-appbundle.sh`, `./build-ipa.sh`)
- Any command that affects production systems or creates production artifacts

## Development Utilities
- Use Docker Compose v2 to manage the development environment:
  - `docker compose up -d` - Start all development services
  - `docker compose down` - Stop all services
  - `docker compose logs -f` - View logs
  - `docker compose ps` - Check service status
- See README-docker.md for complete Docker Compose documentation
- **Important Docker Guideline**:
  - Don't do a `docker compose down` unless it is really necessary and truly disturbs ongoing investigation/debugging

## Linguistic Guidelines
- For french wordings, never use "prise en charge" for trips, use "récupération". For example, "Rappel de récupération" instead of "Rappel de prise en charge"

## Ethical Guidelines
- Don't digress, always keep the standards high.