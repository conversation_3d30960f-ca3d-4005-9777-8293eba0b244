{"name": "functions", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "spin": "firebase emulators:start --only functions,firestore,pubsub --inspect-functions 9229 --import emulators_data --export-on-exit"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"@google-cloud/tasks": "^6.1.0", "algoliasearch": "^5.34.0", "axios": "^1.7.9", "firebase-admin": "^13.4.0", "firebase-functions": "^6.4.0", "xstate": "^5.19.4"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "prettier": "^3.6.2", "typescript": "^5.8.3"}, "private": true}