import { logger } from "firebase-functions/v2";
import { Timestamp, GeoPoint, FieldValue } from "firebase-admin/firestore";

/**
 * Recursively removes undefined properties from an object or array and logs a warning
 * if any undefined properties are found. This is to make undefined value handling
 * explicit before sending data to Firestore.
 *
 * @param data The object or array to normalize.
 * @param operationContext A string describing the operation for logging purposes (e.g., "fileName:functionName:variableName").
 * @param parentPath The current path for nested objects/arrays, used for logging.
 * @returns A new object or array with undefined properties/elements removed.
 *          Returns the original primitive value if data is not an object or array.
 */
export function normalizeDataForFirestore<T>(
  data: T,
  operationContext: string,
  parentPath: string = ""
): T {
  if (typeof data !== 'object' || data === null) {
    return data; // Return primitives, null, or functions as is
  }

  // Handle arrays: recursively normalize each element, filtering out undefined results from recursion if needed.
  if (Array.isArray(data)) {
    return data
      .map((item, index) => {
        const currentPath = `${parentPath}[${index}]`;
        if (item === undefined) {
          logger.warn(
            `Undefined value found and removed directly in array at path '${currentPath}' during operation: '${operationContext}'.`
          );
          return undefined; // Mark for removal
        }
        return normalizeDataForFirestore(item, operationContext, currentPath);
      })
      .filter(item => item !== undefined) as unknown as T;
  }

  // Handle objects (including Firestore specific types that should not be deeply traversed)
  try {
    if (
      data instanceof Timestamp ||
      data instanceof GeoPoint ||
      data instanceof FieldValue ||
      data instanceof Date
    ) {
      return data; // Return Firestore-specific types and Date objects as is
    }
  } catch (error) {
    // If instanceof checks fail (e.g., types not loaded), continue with normal object processing
    logger.warn(`Error checking Firestore types in normalizeDataForFirestore: ${error}`, {
      operationContext,
      errorMessage: error instanceof Error ? error.message : String(error)
    });
  }

  const cleanedData = {} as T;
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      const value = data[key];
      const currentPath = parentPath ? `${parentPath}.${key}` : key;

      if (value === undefined) {
        logger.warn(
          `Undefined value found and removed for key '${currentPath}' during operation: '${operationContext}'.`
        );
        // Key is skipped, effectively removing it from cleanedData
      } else {
        cleanedData[key as keyof T] = normalizeDataForFirestore(value, operationContext, currentPath);
      }
    }
  }
  return cleanedData;
} 