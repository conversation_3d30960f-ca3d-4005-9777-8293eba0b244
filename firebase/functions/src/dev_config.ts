import { isDevelopment } from "./environment";

/**
 * Development mode configuration for push notifications
 * 
 * IMPORTANT: UIDs are shared across admin_users and mobile_users collections.
 * The same person can exist in both collections with the same UID:
 * - mobile_users/{uid} - Mobile app access
 * - admin_users/{uid} - Admin dashboard access
 * 
 * This dev mode filtering applies uniformly to all notification types.
 */

/**
 * User IDs allowed to receive push notifications in development mode
 * These UIDs work for both mobile_users and admin_users collections
 */
const DEV_ALLOWED_USER_IDS = [
  'c5YHX79mg8OgHoyF12JBEffYwnE3',
  'oEay2mlxWgXAYRyKxvuIjL54pII3', 
  'zH5hYbVBkwPUc99Q2asR5TIXnks1'
];

/**
 * Check if a user is allowed to receive push notifications in development mode
 * 
 * @param userId - The user's UID (same across admin_users and mobile_users collections)
 * @returns true if user should receive notifications in dev mode, false otherwise
 */
export function isDevNotificationAllowedForUser(userId: string): boolean {
  if (!isDevelopment()) {
    // In production, all users receive notifications
    return true;
  }
  
  // In development, only allow specific test user IDs
  return DEV_ALLOWED_USER_IDS.includes(userId);
}

/**
 * Get the list of allowed user IDs for development mode (for debugging/logging)
 * @returns Array of allowed user IDs
 */
export function getDevAllowedUserIds(): readonly string[] {
  return DEV_ALLOWED_USER_IDS;
}