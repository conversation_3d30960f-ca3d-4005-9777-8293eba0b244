import { logger } from "firebase-functions/v2";

/**
 * Check if we're running in production environment
 * In Firebase Functions, production is detected by checking if we're not in the emulator
 */
export function isProduction(): boolean {
  // Firebase emulator sets FUNCTIONS_EMULATOR to true
  return process.env.FUNCTIONS_EMULATOR !== "true";
}

/**
 * Check if we're running in development environment (Firebase emulator)
 */
export function isDevelopment(): boolean {
  return !isProduction();
}

/**
 * Get the current environment name
 */
export function getEnvironment(): "production" | "development" {
  return isProduction() ? "production" : "development";
}

/**
 * Log the current environment on startup
 */
export function logEnvironment(): void {
  const env = getEnvironment();
  const emoji = env === "production" ? "🚀" : "🔧";
  logger.info(`${emoji} Running in ${env.toUpperCase()} mode`, {
    environment: env,
    functionsEmulator: process.env.FUNCTIONS_EMULATOR,
  });
} 