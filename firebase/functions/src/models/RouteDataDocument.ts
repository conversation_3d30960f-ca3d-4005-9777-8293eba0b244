import { Timestamp } from 'firebase-admin/firestore';
import { RouteData } from '../navigation';

export type RouteType = 'main' | 'driver' | 'final' | 'overview';

export interface RouteDataDocument {
  tripId: string;
  routeType: RouteType;
  routeIndex?: number;
  isSelected?: boolean;
  routeData: RouteData;
  createdAt: Timestamp;
}

export interface RouteDataIds {
  main?: string;
  driver?: string;
  final?: string;
  overviews?: string[];
  selectedOverviewId?: string;
}