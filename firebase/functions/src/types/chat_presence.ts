import { Timestamp } from "firebase-admin/firestore";

/**
 * Represents a user's active presence in a chat session
 */
export interface ChatPresence {
  /** The user or admin ID */
  userId: string;
  
  /** The chat session ID they're currently viewing */
  sessionId: string;
  
  /** Type of user - affects notification grouping for admins */
  userType: "admin" | "user";
  
  /** Last heartbeat timestamp - updated every 10 seconds while active */
  lastHeartbeat: Timestamp;
  
  /** Whether the user is actively viewing the chat */
  isActive: boolean;
  
  /** TTL field - document will be auto-deleted by Firestore after this time */
  expiresAt: Timestamp;
}

/**
 * Threshold in seconds for considering a presence as active
 * If lastHeartbeat is older than this, presence is considered stale
 */
export const PRESENCE_ACTIVE_THRESHOLD_SECONDS = 15;

/**
 * TTL expiration buffer in seconds
 * Presence documents will expire this many seconds after last heartbeat
 * Should be slightly larger than PRESENCE_ACTIVE_THRESHOLD_SECONDS to account for TTL delay
 */
export const PRESENCE_TTL_BUFFER_SECONDS = 60; // 1 minute expiration

/**
 * Heartbeat interval in milliseconds for updating presence
 */
export const PRESENCE_HEARTBEAT_INTERVAL_MS = 10000; // 10 seconds