import { db } from './config';

/**
 * User notification preferences interface
 * Matches the structure used in Flutter NotificationSettingsScreen
 */
export interface UserNotificationPreferences {
  notificationSettings: {
    enableRingtone: boolean | null; // null means use admin default
    enableDriverMovingNotification: boolean;
    enableDriverArrivedNotification: boolean;
    enableTripPaidNotification: boolean;
    enableReservationReminders: boolean;
  };
}

/**
 * Get user notification preferences from Firestore
 * Note: mobile_users collection is global (shared across all tenants)
 * 
 * @param userId - The user's UID
 * @returns User preferences or null if not found
 */
export async function getUserPreferences(userId: string): Promise<UserNotificationPreferences | null> {
  try {
    const preferencesDoc = await db
      .collection('mobile_users')
      .doc(userId)
      .collection('preferences')
      .doc('notification_settings')
      .get();

    if (!preferencesDoc.exists) {
      return null;
    }

    return preferencesDoc.data() as UserNotificationPreferences;
  } catch (error) {
    console.error('Error getting user notification preferences:', error);
    return null;
  }
}

/**
 * Update user notification preferences in Firestore
 * Note: mobile_users collection is global (shared across all tenants)
 * 
 * @param userId - The user's UID
 * @param preferences - Partial or complete preferences to update
 * @returns Success status
 */
export async function updateUserPreferences(
  userId: string,
  preferences: Partial<UserNotificationPreferences>
): Promise<boolean> {
  try {
    await db
      .collection('mobile_users')
      .doc(userId)
      .collection('preferences')
      .doc('notification_settings')
      .set(preferences, { merge: true });

    return true;
  } catch (error) {
    console.error('Error updating user notification preferences:', error);
    return false;
  }
}

/**
 * Create default user notification preferences
 * This ensures users have proper default settings when they first access notifications
 * 
 * @param userId - The user's UID
 * @returns Success status
 */
export async function createDefaultUserPreferences(userId: string): Promise<boolean> {
  try {
    const defaultPreferences: UserNotificationPreferences = {
      notificationSettings: {
        enableRingtone: null, // Use admin default
        enableDriverMovingNotification: true,
        enableDriverArrivedNotification: true,
        enableTripPaidNotification: true,
        enableReservationReminders: true,
      },
    };

    await db
      .collection('mobile_users')
      .doc(userId)
      .collection('preferences')
      .doc('notification_settings')
      .set(defaultPreferences);

    return true;
  } catch (error) {
    console.error('Error creating default user notification preferences:', error);
    return false;
  }
}

/**
 * Check if user has notification preferences set up
 * Useful for determining if we need to create defaults
 * 
 * @param userId - The user's UID
 * @returns True if preferences exist, false otherwise
 */
export async function hasUserPreferences(userId: string): Promise<boolean> {
  try {
    const preferencesDoc = await db
      .collection('mobile_users')
      .doc(userId)
      .collection('preferences')
      .doc('notification_settings')
      .get();

    return preferencesDoc.exists;
  } catch (error) {
    console.error('Error checking user notification preferences:', error);
    return false;
  }
} 