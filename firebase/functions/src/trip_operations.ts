import * as functions from "firebase-functions/v2/https";
import { logger } from "firebase-functions/v2";
import { onDocumentDeleted, onDocumentWritten } from "firebase-functions/v2/firestore";
import { normalizeDataForFirestore } from "./utils";
import { cancelTripTransition } from "./trip_state_operations";
import { getTenantCollection, ensureTenantId } from "./tenant_utils";
import { handlePreparingStatus, handleRequestingDriverStatus, handleDriverApproachingStatus, handleDriverAwaitingStatus, handlePaidStatus, handleTripDeletion } from "./navigation_handlers";
import { Trip } from "./navigation";
import { calculateTripEstimate } from "./trip_cost_calculator";
import { createRemindersForReservedTrip } from "./reservation_notifications";
import { sendAdminNotification } from "./chat_notifications";
import { db } from "./config";

// Function to cancel a trip - now uses state machine for consistency
export const cancelTrip = functions.onCall({
  region: "europe-west3",
  timeoutSeconds: 5,
  memory: "512MiB",
  concurrency: 1000,
}, async (request) => {
  if (!request.auth) {
    throw new functions.HttpsError("unauthenticated", "Authentication is required.");
  }

  const { tripId, uidCancelledBy, tenantId } = request.data;
  if (!tripId || !uidCancelledBy) {
    throw new functions.HttpsError("invalid-argument", "Trip ID and canceller UID are required.");
  }

  try {
    // Use the state machine cancellation function
    const result = await cancelTripTransition.run({
      auth: request.auth,
      data: {
        tripId,
        tenantId,
        reason: "Cancelled via trip operations"
      },
      rawRequest: request.rawRequest,
      acceptsStreaming: false
    });

    // The state machine handles all cleanup including driver release
    logger.info("Trip cancelled successfully via state machine", { tripId, uidCancelledBy });
    return result;
  } catch (error) {
    logger.error("Error cancelling trip", { tripId, error });
    throw error; // Re-throw the error from state machine
  }
});

// Function to update skipped drivers
export const updateSkippedDrivers = functions.onCall({
  region: "europe-west3",
  timeoutSeconds: 5,
  memory: "512MiB",
  concurrency: 1000,
}, async (request) => {
  if (!request.auth) {
    throw new functions.HttpsError("unauthenticated", "Authentication is required.");
  }

  const { tripId, skippedDriverIds, operation, tenantId } = request.data;
  if (!tripId || !skippedDriverIds || !Array.isArray(skippedDriverIds)) {
    throw new functions.HttpsError("invalid-argument", "Trip ID and skipped driver IDs array are required.");
  }

  const validOperation = operation === 'set' || operation === 'remove';
  if (!validOperation) {
    throw new functions.HttpsError("invalid-argument", "Operation must be 'set' or 'remove'.");
  }

  try {
    const effectiveTenantId = ensureTenantId(tenantId);
    const tripRef = getTenantCollection(effectiveTenantId, 'trips').doc(tripId);

    await db.runTransaction(async (transaction) => {
      const tripDoc = await transaction.get(tripRef);
      if (!tripDoc.exists) {
        throw new functions.HttpsError("not-found", "Trip not found");
      }

      const currentSkippedDrivers = tripDoc.data()?.skippedDriverIds || [];
      const updatedSkippedDrivers = operation === 'remove'
        ? currentSkippedDrivers.filter((id: string) => !skippedDriverIds.includes(id))
        : skippedDriverIds;

      const skippedDriversUpdateData = normalizeDataForFirestore(
        { skippedDriverIds: updatedSkippedDrivers },
        `updateSkippedDrivers:tripRef:update:${tripId}`
      );
      if (Object.keys(skippedDriversUpdateData).length > 0) {
        transaction.update(tripRef, skippedDriversUpdateData);
      }
    });

    logger.info("Skipped drivers updated", { tripId, operation });
    return { success: true };
  } catch (error) {
    logger.error("Error updating skipped drivers", { tripId, error });
    throw new functions.HttpsError("internal", "Error updating skipped drivers", { originalError: error });
  }
});

// Function to handle trip deletion and cleanup of logs
export const onTripDeleted = onDocumentDeleted({
  document: "/tenants/{tenantId}/trips/{tripId}",
  region: "europe-west3",
  timeoutSeconds: 5,
  memory: "512MiB",
  concurrency: 1000,
  maxInstances: 1,
}, async (event) => {
  const tripId = event.params.tripId;
  // tenantId is available in event.params but not needed as we use event.data.ref directly

  if (!event.data) {
    logger.warn(`No data found for deleted trip ${tripId}`);
    return;
  }

  try {
    // Get reference to logs subcollection
    const logsRef = event.data.ref.collection('logs');

    // Get all documents in the logs subcollection
    const logsSnapshot = await logsRef.get();

    // Delete each log document
    const deletionPromises = logsSnapshot.docs.map(doc => doc.ref.delete());
    await Promise.all(deletionPromises);

    logger.info(`Successfully deleted ${logsSnapshot.size} logs for trip ${tripId}`);
  } catch (error) {
    logger.error(`Error deleting logs for trip ${tripId}:`, error);
    throw error;
  }
});

// Main trip mutations handler
export const onTripMutations = onDocumentWritten({
  document: "/tenants/{tenantId}/trips/{tripId}",
  region: "europe-west3",
  timeoutSeconds: 5,
  memory: "512MiB",
  concurrency: 1000,
  maxInstances: 1,
}, async (event) => {
  const tenantId = event.params.tenantId;
  const tripId = event.params.tripId;


  // Handle trip deletion first
  if (!event.data?.after.exists) {
    const tripBeforeDeletion = event.data?.before.data() as Trip;
    if (tripBeforeDeletion) {
      await handleTripDeletion(tripBeforeDeletion, event.params.tripId, tenantId);
    }
    return;
  }

  const tripBefore = event.data?.before.data();
  const tripAfter = event.data?.after.data();

  // Do nothing if trip data is missing
  if (!tripAfter) return;

  // --- COST ESTIMATION LOGIC ---
  const isNewTrip = !tripBefore;
  // Using JSON.stringify for a simple deep comparison of the routeDataIds object
  const routeDataIdsChanged = JSON.stringify(tripBefore?.routeDataIds) !== JSON.stringify(tripAfter.routeDataIds);
  const hasRouteData = tripAfter.routeData || (tripAfter.routeDataIds && tripAfter.routeDataIds.length > 0);
  const isPreFinalStatus = !['completed', 'cancelled', 'paid'].includes(tripAfter.status);

  const shouldCalculateEstimate = (isNewTrip && hasRouteData) || (routeDataIdsChanged && isPreFinalStatus);

  if (shouldCalculateEstimate) {
    try {
      await calculateTripEstimate(tripId, tenantId);
      logger.info(`Successfully triggered estimate calculation for trip ${tripId}`);
    } catch (error) {
      logger.error(`Error triggering estimate calculation for trip ${tripId}`, { error });
    }
  }
  // --- END COST ESTIMATION LOGIC ---

  const trip = event.data?.after.data() as Trip | undefined;
  // Do nothing if trip has been deleted
  if (!trip) return;

  const { startLocation, arrivalLocation } = trip;
  if (!startLocation || !arrivalLocation) return;

  // Check if trip transitioned to 'reserved' status (new booking)
  const tripData = event.data?.after.data() as any;
  const previousStatus = event.data?.before.data()?.status;
  if (previousStatus !== 'reserved' && tripData?.status === 'reserved') {
    // Send notification to all admins about new trip booking
    try {
      // Get passenger information
      const passengerDoc = await db.collection('mobile_users').doc(trip.uidPassenger).get();
      const passengerName = passengerDoc.exists
        ? passengerDoc.data()?.displayName || passengerDoc.data()?.fullName || "Passenger"
        : "Passenger";

      // Prepare notification details
      const pickupAddress = tripData.startAddress || "Pickup location";
      const destinationAddress = tripData.arrivalAddress || "Destination";
      const reservationTime = tripData.reservationTime
        ? new Date(tripData.reservationTime).toLocaleString()
        : "Immediate";

      const title = "New Trip Booked";
      const body = `${passengerName} booked a trip from ${pickupAddress} to ${destinationAddress} for ${reservationTime}`;

      await sendAdminNotification(
        "trip_booking",
        title,
        body,
        {
          type: "trip_booking",
          tripId: event.params.tripId
        },
        tenantId
      );

      logger.info("Trip booking notification sent to admins", {
        tripId: event.params.tripId,
        passengerName,
        status: tripData.status
      });

      // Create reminder documents for the reserved trip
      if (tripData.pickupTime) {
        await createRemindersForReservedTrip(event.params.tripId, tripData.pickupTime, tenantId);
      }
    } catch (error) {
      logger.error("Error sending trip booking notification", {
        tripId: event.params.tripId,
        error
      });
    }
  }

  switch (trip.status) {
    case 'preparing':
      // Calculates route and handles driver cleanup if returning from another status
      await handlePreparingStatus(trip, event, tenantId);
      break;
    case 'requestingDriver':
      // Calculates driver route to pickup and sends notification to chosen driver
      await handleRequestingDriverStatus(trip, event, tenantId);
      break;
    case 'driverApproaching':
      // Prevent repeated execution if it's already the same status
      if (event.data?.before.data()?.status === 'driverApproaching') return;

      // Marks driver as occupied and cancels their other pending requests
      await handleDriverApproachingStatus(trip, event, tenantId);
      break;
    case 'driverAwaiting':
      // Prevent repeated execution if it's already the same status
      if (event.data?.before.data()?.status === 'driverAwaiting') return;

      // Sends notification to passenger that driver has arrived
      await handleDriverAwaitingStatus(trip, event, tenantId);
      break;
    case 'paid':
      // Prevent repeated execution if it's already the same status
      if (event.data?.before.data()?.status === 'paid') return;

      // Cleanup: removes trip occupation if driver was dismissed
      await handlePaidStatus(trip, event, tenantId);
      break;
  }
}); 