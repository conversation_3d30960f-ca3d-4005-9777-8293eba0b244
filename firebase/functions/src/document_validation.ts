import { logger } from "firebase-functions/v2";
import { db } from "./config";

export interface DocumentStatus {
  hasRequiredDocuments: boolean;
  hasExpiringDocuments: boolean;
  missingDocuments: string[];
  expiringDocuments: string[];
  expiredDocuments: string[];
}

/**
 * Check if a driver has all required documents and they are valid
 */
export async function validateDriverDocuments(
  driverUID: string,
  tenantId: string
): Promise<DocumentStatus> {
  const result: DocumentStatus = {
    hasRequiredDocuments: true,
    hasExpiringDocuments: false,
    missingDocuments: [],
    expiringDocuments: [],
    expiredDocuments: [],
  };

  try {
    // Define required document types
    const requiredDocumentTypes = [
      "national_id",
      "drivers_license",
    ];

    // Get driver's documents
    const documentsSnapshot = await db
      .collection("mobile_users")
      .doc(driverUID)
      .collection("driver_documents")
      .where("tenantIDs", "array-contains", tenantId)
      .get();

    const documentsByType = new Map<string, any>();
    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

    // Process each document
    documentsSnapshot.forEach((doc) => {
      const data = doc.data();

      // Skip rejected documents
      if (data.status === "rejected") {
        return;
      }

      // Check if document is expired
      if (data.expiryDate) {
        const expiryDate = data.expiryDate.toDate();

        if (expiryDate < now) {
          result.expiredDocuments.push(data.documentType);
          return;
        }

        // Check if expiring soon (within 30 days)
        if (expiryDate < thirtyDaysFromNow) {
          result.hasExpiringDocuments = true;
          result.expiringDocuments.push(data.documentType);
        }
      }

      // Store the document if it's approved or pending
      if (data.status === "approved" || data.status === "pending") {
        documentsByType.set(data.documentType, data);
      }
    });

    // Check for missing required documents
    for (const docType of requiredDocumentTypes) {
      if (!documentsByType.has(docType)) {
        result.missingDocuments.push(docType);
        result.hasRequiredDocuments = false;
      } else {
        const doc = documentsByType.get(docType);
        // If document is pending, consider it as not fully valid
        if (doc.status === "pending") {
          result.hasRequiredDocuments = false;
        }
      }
    }

    // If any required documents are expired, mark as not having required documents
    if (result.expiredDocuments.some(docType => requiredDocumentTypes.includes(docType))) {
      result.hasRequiredDocuments = false;
    }

    logger.debug("Document validation result", {
      driverUID,
      tenantId,
      result,
    });

    return result;
  } catch (error) {
    logger.error("Error validating driver documents", { driverUID, tenantId, error });
    // Return safe default - driver doesn't have valid documents
    return {
      hasRequiredDocuments: false,
      hasExpiringDocuments: false,
      missingDocuments: ["error_checking_documents"],
      expiringDocuments: [],
      expiredDocuments: [],
    };
  }
}

/**
 * Check if a vehicle has all required documents
 */
export async function validateVehicleDocuments(
  vehicleId: string,
  tenantId: string
): Promise<DocumentStatus> {
  const result: DocumentStatus = {
    hasRequiredDocuments: true,
    hasExpiringDocuments: false,
    missingDocuments: [],
    expiringDocuments: [],
    expiredDocuments: [],
  };

  try {
    // Define required vehicle document types
    const requiredDocumentTypes = [
      "vehicle_registration",
      "vehicle_insurance",
    ];

    // Get vehicle's documents
    const documentsSnapshot = await db
      .collection("vehicles")
      .doc(vehicleId)
      .collection("vehicle_documents")
      .where("tenantIDs", "array-contains", tenantId)
      .get();

    const documentsByType = new Map<string, any>();
    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

    // Process each document
    documentsSnapshot.forEach((doc) => {
      const data = doc.data();

      // Skip rejected documents
      if (data.status === "rejected") {
        return;
      }

      // Check if document is expired
      if (data.expiryDate) {
        const expiryDate = data.expiryDate.toDate();

        if (expiryDate < now) {
          result.expiredDocuments.push(data.documentType);
          return;
        }

        // Check if expiring soon (within 30 days)
        if (expiryDate < thirtyDaysFromNow) {
          result.hasExpiringDocuments = true;
          result.expiringDocuments.push(data.documentType);
        }
      }

      // Store the document if it's approved or pending
      if (data.status === "approved" || data.status === "pending") {
        documentsByType.set(data.documentType, data);
      }
    });

    // Check for missing required documents
    for (const docType of requiredDocumentTypes) {
      if (!documentsByType.has(docType)) {
        result.missingDocuments.push(docType);
        result.hasRequiredDocuments = false;
      } else {
        const doc = documentsByType.get(docType);
        // If document is pending, consider it as not fully valid
        if (doc.status === "pending") {
          result.hasRequiredDocuments = false;
        }
      }
    }

    // If any required documents are expired, mark as not having required documents
    if (result.expiredDocuments.some(docType => requiredDocumentTypes.includes(docType))) {
      result.hasRequiredDocuments = false;
    }

    return result;
  } catch (error) {
    logger.error("Error validating vehicle documents", { vehicleId, tenantId, error });
    // Return safe default - vehicle doesn't have valid documents
    return {
      hasRequiredDocuments: false,
      hasExpiringDocuments: false,
      missingDocuments: ["error_checking_documents"],
      expiringDocuments: [],
      expiredDocuments: [],
    };
  }
}