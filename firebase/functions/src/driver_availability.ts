import { getTenantCollection, ensureTenantId } from "./tenant_utils";
import { logger } from "firebase-functions/v2";
import { validateDriverDocuments, validateVehicleDocuments } from "./document_validation";
import { db } from "./config";

export interface AvailableDriver {
  uid: string;
  displayName?: string;
  photoURL?: string;
  phoneNumber?: string;
  lat?: number;
  lon?: number;
  maxPassengers: number;
  vehicleId?: string;
  vehicleLinkingId?: string;
  vehicleInfo?: {
    brand: string;
    model: string;
    color: string;
    registrationNumber: string;
  };
  hasValidDocuments?: boolean;
  documentsExpiryWarning?: boolean;
}

/**
 * Get available drivers for a tenant based on enhanced criteria:
 * 1. Not occupied by a trip
 * 2. Has tenant access (tenantIDs array contains tenant)
 * 3. Has active tenant state
 * 4. Has assigned vehicle OR owns a personal vehicle
 * 5. Vehicle is active and approved
 */
export async function getAvailableDrivers(
  tenantId: string,
  requiredCapacity: number = 1
): Promise<AvailableDriver[]> {
  const effectiveTenantId = ensureTenantId(tenantId);

  try {
    // Step 1: Basic availability filter using tenantIDs array
    const driversSnapshot = await db.collection("mobile_users")
      .where("occupiedByTripId", "==", null)
      .where("primaryUserType", "==", 1) // Driver type
      .where("tenantIDs", "array-contains", effectiveTenantId)
      .get();

    logger.info(`Found ${driversSnapshot.size} drivers with basic availability in tenant ${effectiveTenantId}`);

    const availableDrivers: AvailableDriver[] = [];

    // Step 2: Validate tenant state and vehicle availability
    await db.runTransaction(async (transaction) => {
      for (const driverDoc of driversSnapshot.docs) {
        const driverData = driverDoc.data();
        const driverUID = driverDoc.id;

        // Check tenant state
        const tenantStateRef = driverDoc.ref
          .collection("tenant_states")
          .doc(effectiveTenantId);
        const tenantStateDoc = await transaction.get(tenantStateRef);

        if (!tenantStateDoc.exists) {
          logger.debug(`Driver ${driverUID} has no tenant state for ${effectiveTenantId}`);
          continue;
        }

        const tenantState = tenantStateDoc.data()!;

        // Check if driver is active in this tenant
        if (!tenantState.isActive || !tenantState.isServiceActive) {
          logger.debug(`Driver ${driverUID} is not active in tenant ${effectiveTenantId}`);
          continue;
        }

        // Check if driver is confirmed
        if (!tenantState.isDriverConfirmed) {
          logger.debug(`Driver ${driverUID} is not confirmed in tenant ${effectiveTenantId}`);
          continue;
        }

        let vehicleCapacity = 4; // Default capacity
        let vehicleInfo = null;
        let hasValidDocuments = false;
        let documentsExpiryWarning = false;

        // Check vehicle assignment
        if (tenantState.currentVehicleLinkingId) {
          // Driver has assigned vehicle
          const linkingRef = getTenantCollection(effectiveTenantId, "vehicles_linking")
            .doc(tenantState.currentVehicleLinkingId);
          const linkingDoc = await transaction.get(linkingRef);

          if (!linkingDoc.exists) {
            logger.warn(`Vehicle linking ${tenantState.currentVehicleLinkingId} not found`);
            continue;
          }

          const linkingData = linkingDoc.data()!;

          // Get vehicle details
          const vehicleRef = db.collection("vehicles").doc(linkingData.vehicleId);
          const vehicleDoc = await transaction.get(vehicleRef);

          if (!vehicleDoc.exists) {
            logger.warn(`Vehicle ${linkingData.vehicleId} not found`);
            continue;
          }

          const vehicleData = vehicleDoc.data()!;

          // Check if vehicle is active
          if (!vehicleData.isActive) {
            logger.debug(`Vehicle ${linkingData.vehicleId} is not active`);
            continue;
          }

          vehicleCapacity = vehicleData.maxPassengers || 4;
          vehicleInfo = {
            brand: vehicleData.brand,
            model: vehicleData.model,
            color: vehicleData.color,
            registrationNumber: vehicleData.registrationNumber,
          };

          // Check capacity requirement
          if (vehicleCapacity < requiredCapacity) {
            logger.debug(`Driver ${driverUID} vehicle capacity ${vehicleCapacity} < required ${requiredCapacity}`);
            continue;
          }

          // IDEAL PATH: Check documents first
          const driverDocStatus = await validateDriverDocuments(driverUID, effectiveTenantId);
          const vehicleDocStatus = await validateVehicleDocuments(linkingData.vehicleId, effectiveTenantId);

          if (driverDocStatus.hasRequiredDocuments && vehicleDocStatus.hasRequiredDocuments) {
            // IDEAL: Driver has all required documents
            hasValidDocuments = true;
            documentsExpiryWarning = driverDocStatus.hasExpiringDocuments || vehicleDocStatus.hasExpiringDocuments;
          } else if (linkingData.tenantApproved) {
            // FALLBACK: Vehicle is tenant-approved, allow driver without documents
            logger.info(`Driver ${driverUID} available via activated vehicle (documents pending)`, {
              missingDriverDocs: driverDocStatus.missingDocuments,
              missingVehicleDocs: vehicleDocStatus.missingDocuments,
            });
            hasValidDocuments = false;
            documentsExpiryWarning = false;
          } else {
            // Neither documents nor vehicle approval - skip this driver
            logger.debug(`Driver ${driverUID} not available - missing documents and vehicle not approved`);
            continue;
          }

          availableDrivers.push({
            uid: driverUID,
            displayName: driverData.displayName,
            photoURL: driverData.photoURL,
            phoneNumber: driverData.phoneNumber,
            lat: driverData.lat,
            lon: driverData.lon,
            maxPassengers: vehicleCapacity,
            vehicleId: linkingData.vehicleId,
            vehicleLinkingId: tenantState.currentVehicleLinkingId,
            vehicleInfo,
            hasValidDocuments,
            documentsExpiryWarning,
          });
        } else {
          // Check if driver owns a personal vehicle that might be available
          const personalVehiclesSnapshot = await transaction.get(
            db.collection("vehicles")
              .where("ownerUID", "==", driverUID)
              .where("isActive", "==", true)
          );

          if (personalVehiclesSnapshot.empty) {
            logger.debug(`Driver ${driverUID} has no vehicle assigned or owned`);
            continue;
          }

          // For now, skip drivers without assigned vehicles
          // In future, could implement logic to auto-assign personal vehicle
          logger.debug(`Driver ${driverUID} has personal vehicles but none assigned`);
          continue;
        }
      }
    });

    logger.info(`Found ${availableDrivers.length} fully available drivers with vehicles`);
    return availableDrivers;

  } catch (error) {
    logger.error("Error getting available drivers", { tenantId, error });
    throw error;
  }
}

export interface DriverAvailabilityResult {
  isAvailable: boolean;
  reason?: string;
  details?: string;
}


/**
 * Check if a specific driver is available with detailed error information
 */
export async function checkDriverAvailabilityDetailed(
  driverUID: string,
  tenantId: string,
  requiredCapacity: number = 1
): Promise<DriverAvailabilityResult> {
  const effectiveTenantId = ensureTenantId(tenantId);

  logger.info("🔍 Checking driver availability", {
    driverUID,
    tenantId: effectiveTenantId,
    requiredCapacity
  });

  try {
    return await db.runTransaction(async (transaction) => {
      // Get driver document
      const driverRef = db.collection("mobile_users").doc(driverUID);
      const driverDoc = await transaction.get(driverRef);

      if (!driverDoc.exists) {
        logger.warn("❌ Driver document not found", { driverUID });
        return {
          isAvailable: false,
          reason: "Driver account not found",
          details: "The driver profile could not be found in the system"
        };
      }

      const driverData = driverDoc.data()!;

      // Check basic availability
      if (driverData.occupiedByTripId) {
        logger.warn("❌ Driver already occupied", {
          driverUID,
          occupiedByTripId: driverData.occupiedByTripId
        });
        return {
          isAvailable: false,
          reason: "Driver is currently on another trip",
          details: `Driver is assigned to trip ${driverData.occupiedByTripId}`
        };
      }

      if (driverData.primaryUserType !== 1) {
        logger.warn("❌ User is not a driver", {
          driverUID,
          primaryUserType: driverData.primaryUserType
        });
        return {
          isAvailable: false,
          reason: "Account is not configured as a driver",
          details: "This user account is not set up for driver operations"
        };
      }

      // Check tenant access
      const tenantIDs = driverData.tenantIDs || [];
      if (!tenantIDs.includes(effectiveTenantId)) {
        logger.warn("❌ Driver does not have access to tenant", {
          driverUID,
          tenantId: effectiveTenantId,
          driverTenantIDs: tenantIDs
        });
        return {
          isAvailable: false,
          reason: "Driver does not have access to this service area",
          details: "Driver is not authorized to operate in this tenant/region"
        };
      }

      // Check tenant state
      const tenantStateRef = driverRef
        .collection("tenant_states")
        .doc(effectiveTenantId);
      const tenantStateDoc = await transaction.get(tenantStateRef);

      if (!tenantStateDoc.exists) {
        logger.warn("❌ Driver has no tenant state", {
          driverUID,
          tenantId: effectiveTenantId
        });
        return {
          isAvailable: false,
          reason: "Driver profile is not set up for this service area",
          details: "Driver needs to complete registration for this tenant"
        };
      }

      const tenantState = tenantStateDoc.data()!;

      if (!tenantState.isActive) {
        logger.warn("❌ Driver is not active", {
          driverUID,
          tenantId: effectiveTenantId,
          isActive: tenantState.isActive
        });
        return {
          isAvailable: false,
          reason: "Driver profile is deactivated",
          details: "Driver account has been temporarily disabled"
        };
      }

      if (!tenantState.isServiceActive) {
        logger.warn("❌ Driver service is not active", {
          driverUID,
          tenantId: effectiveTenantId,
          isServiceActive: tenantState.isServiceActive
        });
        return {
          isAvailable: false,
          reason: "Driver is currently offline",
          details: "Driver has turned off their availability status"
        };
      }

      if (!tenantState.isDriverConfirmed) {
        logger.warn("❌ Driver is not confirmed", {
          driverUID,
          tenantId: effectiveTenantId,
          isDriverConfirmed: tenantState.isDriverConfirmed
        });
        return {
          isAvailable: false,
          reason: "Driver registration is pending approval",
          details: "Driver account is waiting for admin confirmation"
        };
      }

      // Check vehicle
      if (!tenantState.currentVehicleLinkingId) {
        logger.warn("❌ Driver has no vehicle assigned", {
          driverUID,
          tenantId: effectiveTenantId
        });
        return {
          isAvailable: false,
          reason: "No vehicle assigned to driver",
          details: "Driver needs to have a vehicle assigned before accepting trips"
        };
      }

      const linkingRef = getTenantCollection(effectiveTenantId, "vehicles_linking")
        .doc(tenantState.currentVehicleLinkingId);
      const linkingDoc = await transaction.get(linkingRef);

      if (!linkingDoc.exists) {
        logger.warn("❌ Vehicle linking not found", {
          driverUID,
          vehicleLinkingId: tenantState.currentVehicleLinkingId
        });
        return {
          isAvailable: false,
          reason: "Vehicle assignment is invalid",
          details: "The vehicle linked to this driver could not be found"
        };
      }

      const linkingData = linkingDoc.data()!;

      // Get vehicle details for capacity check
      const vehicleRef = db.collection("vehicles").doc(linkingData.vehicleId);
      const vehicleDoc = await transaction.get(vehicleRef);

      if (!vehicleDoc.exists) {
        logger.warn("❌ Vehicle not found", {
          driverUID,
          vehicleId: linkingData.vehicleId
        });
        return {
          isAvailable: false,
          reason: "Assigned vehicle not found",
          details: "The vehicle assigned to this driver does not exist in the system"
        };
      }

      const vehicleData = vehicleDoc.data()!;
      if (!vehicleData.isActive) {
        logger.warn("❌ Vehicle is not active", {
          driverUID,
          vehicleId: linkingData.vehicleId,
          isActive: vehicleData.isActive
        });
        return {
          isAvailable: false,
          reason: "Assigned vehicle is deactivated",
          details: "The vehicle assigned to this driver has been disabled"
        };
      }

      const vehicleCapacity = vehicleData.maxPassengers || 4;
      if (vehicleCapacity < requiredCapacity) {
        logger.warn("❌ Vehicle capacity insufficient", {
          driverUID,
          vehicleId: linkingData.vehicleId,
          vehicleCapacity,
          requiredCapacity
        });
        return {
          isAvailable: false,
          reason: "Vehicle capacity is insufficient",
          details: `Vehicle can only carry ${vehicleCapacity} passengers, but ${requiredCapacity} needed`
        };
      }

      // IDEAL PATH: Check if driver has valid documents
      const driverDocStatus = await validateDriverDocuments(driverUID, effectiveTenantId);
      const vehicleDocStatus = await validateVehicleDocuments(linkingData.vehicleId, effectiveTenantId);

      if (driverDocStatus.hasRequiredDocuments && vehicleDocStatus.hasRequiredDocuments) {
        // IDEAL: Driver has all required documents
        logger.info("✅ Driver approved with valid documents", {
          driverUID,
          tenantId: effectiveTenantId
        });
        return { isAvailable: true };
      }

      // FALLBACK PATH: Check if vehicle is tenant-approved
      // This allows operations to continue while documents are being collected
      if (linkingData.tenantApproved) {
        logger.info("⚠️ Driver approved via activated vehicle (documents pending)", {
          driverUID,
          tenantId: effectiveTenantId,
          missingDriverDocs: driverDocStatus.missingDocuments,
          missingVehicleDocs: vehicleDocStatus.missingDocuments,
        });
        return { isAvailable: true };
      }

      // Neither documents nor vehicle approval - driver not available
      const missingDocs = [
        ...(driverDocStatus.missingDocuments || []),
        ...(vehicleDocStatus.missingDocuments || [])
      ];
      
      logger.warn("❌ Driver not available - missing documents and vehicle not approved", {
        driverUID,
        tenantId: effectiveTenantId,
        tenantApproved: linkingData.tenantApproved,
        missingDriverDocs: driverDocStatus.missingDocuments,
        missingVehicleDocs: vehicleDocStatus.missingDocuments
      });
      
      return {
        isAvailable: false,
        reason: "Missing required documents and vehicle not activated",
        details: `Missing documents: ${missingDocs.join(', ')}. Vehicle needs admin activation.`
      };
    });
  } catch (error) {
    logger.error("❌ Error checking driver availability", {
      driverUID,
      tenantId,
      error: error instanceof Error ? error.message : error
    });
    return {
      isAvailable: false,
      reason: "System error while checking availability",
      details: "An unexpected error occurred during the availability check"
    };
  }
}