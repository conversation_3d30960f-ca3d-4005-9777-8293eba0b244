import { firestore } from "firebase-admin";
import axios from "axios";
import { onCall, HttpsError } from "firebase-functions/v2/https";
import { logger } from "firebase-functions/v2";

export interface LatLng {
  lat: number;
  lon: number;
}

export interface RouteData {
  distanceMeters: number;
  durationSeconds: number;
  polyline: string;
  bounds: {
    northeast: {
      lat: number;
      lng: number;
    };
    southwest: {
      lat: number;
      lng: number;
    };
  };
}

export interface Trip {
  startLocation: LatLng;
  arrivalLocation: LatLng;
  driverLocation?: LatLng;
  distance?: number;
  status:
    | "preparing"
    | "requestingDriver"
    | "driverApproaching"
    | "driverAwaiting"
    | "inProgress"
    | "completed"
    | "cancelled"
    | "paid"
    | "reserved";
  driverAwaitingTime?: firestore.Timestamp;
  driverStartTime?: firestore.Timestamp;
  passengerStartTime?: firestore.Timestamp;
  pickupTime?: firestore.Timestamp;
  paymentMethod?: string;
  routeData?: RouteData;
  driverRouteData?: RouteData;
  uidChosenDriver?: string;
  uidPassenger: string;
  driverNotificationSent?: boolean;
  driverDismissed: boolean;
  routeDataIds?: {
    main?: string;
    driver?: string;
    final?: string;
    overviews?: string[];
    selectedOverviewId?: string;
  };
  passengerCount?: number;
  startLocationName?: string;
  arrivalLocationName?: string;
}

const apiKey = "AIzaSyC5gn-4e1bZvCv4MVkopVvZAS642Up2EK8";

export async function createRouteData(
  startLocation: LatLng,
  arrivalLocation: LatLng,
  polylineQuality: "HIGH_QUALITY" | "OVERVIEW" = "HIGH_QUALITY"
): Promise<RouteData> {
  const response = await axios.post(
    `https://routes.googleapis.com/directions/v2:computeRoutes`,
    {
      origin: {
        location: {
          latLng: {
            latitude: startLocation.lat,
            longitude: startLocation.lon,
          },
        },
      },
      destination: {
        location: {
          latLng: {
            latitude: arrivalLocation.lat,
            longitude: arrivalLocation.lon,
          },
        },
      },
      travelMode: "DRIVE",
      routingPreference: "TRAFFIC_AWARE_OPTIMAL",
      trafficModel: "PESSIMISTIC",
      polylineQuality: polylineQuality,
      computeAlternativeRoutes: false,
      routeModifiers: {
        avoidTolls: false,
        avoidHighways: false,
        avoidFerries: false,
      },
      languageCode: "fr-FR",
      units: "METRIC",
    },
    {
      headers: {
        "Content-Type": "application/json",
        "X-Goog-Api-Key": apiKey,
        "X-Goog-FieldMask": "routes.duration,routes.legs.distanceMeters,routes.legs.polyline.encodedPolyline,routes.viewport",
      },
    }
  );

  const route = response.data.routes[0];
  const leg = route.legs[0];
  const distanceInMeters = leg.distanceMeters;
  const durationInSeconds = parseInt(route.duration.slice(0, -1), 10);

  return {
    distanceMeters: distanceInMeters,
    durationSeconds: durationInSeconds,
    polyline: leg.polyline.encodedPolyline,
    bounds: {
      northeast: {
        lat: route.viewport.high.latitude,
        lng: route.viewport.high.longitude,
      },
      southwest: {
        lat: route.viewport.low.latitude,
        lng: route.viewport.low.longitude,
      },
    },
  };
}

export async function createMultipleRoutesData(
  start: LatLng,
  end: LatLng,
  polylineQuality: "HIGH_QUALITY" | "OVERVIEW" = "HIGH_QUALITY"
): Promise<RouteData[]> {
  const response = await axios.post(
    "https://routes.googleapis.com/directions/v2:computeRoutes",
    {
      origin: { location: { latLng: { latitude: start.lat, longitude: start.lon } } },
      destination: { location: { latLng: { latitude: end.lat, longitude: end.lon } } },
      travelMode: "DRIVE",
      routingPreference: "TRAFFIC_AWARE_OPTIMAL",
      trafficModel: "PESSIMISTIC",
      polylineQuality,
      computeAlternativeRoutes: true,
      routeModifiers: { avoidTolls: false, avoidHighways: false, avoidFerries: false },
      languageCode: "fr-FR",
      units: "METRIC",
    },
    {
      headers: {
        "Content-Type": "application/json",
        "X-Goog-Api-Key": apiKey,
        "X-Goog-FieldMask": "routes.duration,routes.legs.distanceMeters,routes.legs.polyline.encodedPolyline,routes.viewport",
      },
    }
  );

  return response.data.routes.map((r: any) => {
    const leg = r.legs[0];
    const durationInSeconds = parseInt(r.duration.slice(0, -1), 10);
    return {
      distanceMeters: leg.distanceMeters,
      durationSeconds: durationInSeconds,
      polyline: leg.polyline.encodedPolyline,
      bounds: {
        northeast: { lat: r.viewport.high.latitude, lng: r.viewport.high.longitude },
        southwest: { lat: r.viewport.low.latitude, lng: r.viewport.low.longitude },
      },
    };
  });
}

// Firebase Functions for route calculation
export const getRouteData = onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 5,
    memory: "512MiB",
    concurrency: 1000,
    maxInstances: 1,
  },
  async (request) => {
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "The function must be called while authenticated.");
    }

    const { startLocation, arrivalLocation, highQuality } = request.data as {
      startLocation: LatLng;
      arrivalLocation: LatLng;
      highQuality?: boolean;
    };
    if (!startLocation || !arrivalLocation) {
      throw new HttpsError("invalid-argument", "Start and arrival locations are required.");
    }

    try {
      const routeData = await createRouteData(startLocation, arrivalLocation, highQuality ? "HIGH_QUALITY" : "OVERVIEW");
      logger.info("Route data calculated", {
        distanceMeters: routeData.distanceMeters,
        durationSeconds: routeData.durationSeconds,
      });

      return {
        polyline: routeData.polyline,
        bounds: routeData.bounds,
        durationSeconds: routeData.durationSeconds,
        distanceMeters: routeData.distanceMeters,
      };
    } catch (error) {
      logger.error("Error calculating route data", { error, startLocation, arrivalLocation });
      throw error;
    }
  }
);

export const getMultipleRoutesData = onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 5,
    memory: "512MiB",
    concurrency: 1000,
    maxInstances: 1,
  },
  async (request) => {
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "Authentication is required.");
    }

    const { startLocation, arrivalLocation, highQuality } = request.data;
    if (!startLocation || !arrivalLocation) {
      throw new HttpsError("invalid-argument", "Start and arrival locations are required.");
    }

    try {
      const data = await createMultipleRoutesData(startLocation, arrivalLocation, highQuality ? "HIGH_QUALITY" : "OVERVIEW");

      logger.info("Multiple routes data calculated", {
        routeCount: data.length,
        startLocation,
        arrivalLocation,
      });

      return data;
    } catch (error) {
      logger.error("Error calculating multiple routes data", {
        error: error instanceof Error ? error.message : String(error),
        startLocation,
        arrivalLocation,
      });

      throw error;
    }
  }
);
