import { admin } from './config';
import { getUserPreferences } from './user_notification_preferences';
import { getTenantCollection } from './tenant_utils';

export type NotificationType =
    | 'driver_moving'
    | 'driver_arrived'
    | 'trip_paid'
    | 'reservation_reminder'
    | 'driver_timeout';

interface AdminNotificationConfig {
    enableRingtoneByDefault: boolean;
    enableDriverMovingNotification: boolean;
    enableDriverArrivedNotification: boolean;
    enableTripPaidNotification: boolean;
    enableReservationReminders: boolean;
    reservationReminderTimes: number[];
}

// Cache for admin configuration to reduce Firestore reads
let adminConfigCache: { config: AdminNotificationConfig | null; timestamp: number } = {
    config: null,
    timestamp: 0,
};

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Get admin notification configuration with caching
 */
async function getAdminNotificationConfig(tenantId: string): Promise<AdminNotificationConfig | null> {
    const now = Date.now();

    // Check if cache is still valid
    if (adminConfigCache.config && (now - adminConfigCache.timestamp) < CACHE_DURATION) {
        return adminConfigCache.config;
    }

    try {
        const configDoc = await getTenantCollection(tenantId, 'configurations')
            .doc('passengerNotifications')
            .get();

        if (!configDoc.exists) {
            // Return default configuration if document doesn't exist
            const defaultConfig: AdminNotificationConfig = {
                enableRingtoneByDefault: true,
                enableDriverMovingNotification: true,
                enableDriverArrivedNotification: true,
                enableTripPaidNotification: true,
                enableReservationReminders: true,
                reservationReminderTimes: [60, 15],
            };
            adminConfigCache = { config: defaultConfig, timestamp: now };
            return defaultConfig;
        }

        const data = configDoc.data();
        const config = data?.value as AdminNotificationConfig;

        // Update cache
        adminConfigCache = { config, timestamp: now };
        return config;
    } catch (error) {
        console.error('Error fetching admin notification config:', error);
        return null;
    }
}

/**
 * Determine if a notification should be sent based on type and admin config
 */
export async function shouldSendNotification(
    userId: string,
    notificationType: NotificationType,
    tenantId: string
): Promise<boolean> {
    const adminConfig = await getAdminNotificationConfig(tenantId);
    if (!adminConfig) return true; // Default to sending if config fetch fails

    // Check if this notification type is enabled by admin
    switch (notificationType) {
        case 'driver_moving':
            if (!adminConfig.enableDriverMovingNotification) return false;
            break;
        case 'driver_arrived':
            if (!adminConfig.enableDriverArrivedNotification) return false;
            break;
        case 'trip_paid':
            if (!adminConfig.enableTripPaidNotification) return false;
            break;
        case 'reservation_reminder':
            if (!adminConfig.enableReservationReminders) return false;
            break;
        case 'driver_timeout':
            // Driver timeout notifications are always sent (critical for user experience)
            break;
    }

    // Get user preferences
    const userPrefs = await getUserPreferences(userId);
    if (!userPrefs) return true; // Default to sending if no user prefs

    // Check user-specific settings
    const notificationSettings = userPrefs.notificationSettings;
    switch (notificationType) {
        case 'driver_moving':
            return notificationSettings.enableDriverMovingNotification;
        case 'driver_arrived':
            return notificationSettings.enableDriverArrivedNotification;
        case 'trip_paid':
            return notificationSettings.enableTripPaidNotification;
        case 'reservation_reminder':
            return notificationSettings.enableReservationReminders;
        case 'driver_timeout':
            // Driver timeout notifications are always sent (critical for user experience)
            return true;
        default:
            return true;
    }
}

/**
 * Determine if a ringtone should be used for the notification
 */
export async function shouldUseRingtone(
    userId: string,
    notificationType: NotificationType,
    tenantId: string
): Promise<boolean> {
    // Trip paid notifications are always silent
    if (notificationType === 'trip_paid') {
        return false;
    }

    // Driver moving notifications should be silent (informational only)
    if (notificationType === 'driver_moving') {
        return false;
    }

    const adminConfig = await getAdminNotificationConfig(tenantId);
    const userPrefs = await getUserPreferences(userId);

    // If user has explicitly set their ringtone preference, use it
    if (userPrefs && userPrefs.notificationSettings.enableRingtone !== null) {
        return userPrefs.notificationSettings.enableRingtone;
    }

    // Otherwise, use admin default
    return adminConfig?.enableRingtoneByDefault ?? true;
}

/**
 * Get reservation reminder times from admin config
 */
export async function getReservationReminderTimes(tenantId: string): Promise<number[]> {
    const adminConfig = await getAdminNotificationConfig(tenantId);
    return adminConfig?.reservationReminderTimes ?? [60, 15];
}

/**
 * Build notification payload with proper sound configuration
 */
export function buildNotificationPayload(
    title: string,
    body: string,
    data: Record<string, string>,
    useRingtone: boolean,
    notificationType: NotificationType
): admin.messaging.Message {
    // Determine if notification should be completely silent
    const isSilent = notificationType === 'trip_paid' || notificationType === 'driver_moving';

    const basePayload: admin.messaging.Message = {
        notification: {
            title,
            body,
        },
        data: {
            ...data,
            notificationType,
            click_action: 'FLUTTER_NOTIFICATION_CLICK',
        },
        android: {
            priority: 'high',
            notification: {
                defaultSound: !isSilent && !useRingtone, // Only use default sound if not silent and not using ringtone
                priority: 'high',
                channelId: useRingtone ? 'passenger_ringtone_channel' : 'silent_notification',
            },
        },
        apns: {
            payload: {
                aps: {
                    // CRITICAL: DO NOT CHANGE - Respect ringtone preferences
                sound: isSilent ? undefined : (useRingtone ? 'passenger_ringtone.caf' : 'default'),
                    'content-available': 1,
                },
            },
        },
        // Token will be set by the caller
        token: '',
    };

    // Add sound configuration for Android if using ringtone
    if (useRingtone && basePayload.android?.notification) {
        // CRITICAL: DO NOT CHANGE - Custom ringtone for passenger notifications
        basePayload.android.notification.sound = 'passenger_ringtone';
    } else if (isSilent && basePayload.android?.notification) {
        // Explicitly set no sound for silent notifications
        basePayload.android.notification.sound = undefined;
        basePayload.android.notification.defaultSound = false;
    }

    return basePayload;
}

/**
 * Clear the admin config cache (useful for testing or when config is updated)
 */
export function clearAdminConfigCache(): void {
    adminConfigCache = { config: null, timestamp: 0 };
}