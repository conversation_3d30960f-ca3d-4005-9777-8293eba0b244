import { logger } from 'firebase-functions/v2';
import { Timestamp } from 'firebase-admin/firestore';
import { getTenantCollection } from './tenant_utils';
import { RouteData } from './navigation';

/**
 * Encodes an array of lat/lng coordinates into a polyline string using Google's polyline algorithm
 * @param coordinates Array of coordinates with lat and lng properties
 * @returns Encoded polyline string
 */
export function encodePolyline(coordinates: Array<{ lat: number; lng: number }>): string {
  if (!coordinates || coordinates.length === 0) {
    return '';
  }

  let encoded = '';
  let lastLat = 0;
  let lastLng = 0;

  for (const coord of coordinates) {
    const lat = Math.round(coord.lat * 1e5);
    const lng = Math.round(coord.lng * 1e5);

    const dLat = lat - lastLat;
    const dLng = lng - lastLng;

    encoded += encodeValue(dLat);
    encoded += encodeValue(dLng);

    lastLat = lat;
    lastLng = lng;
  }

  return encoded;
}

/**
 * Encodes a single value for polyline encoding
 */
function encodeValue(value: number): string {
  // Left shift negative values and invert
  value = value < 0 ? ~(value << 1) : value << 1;

  let encoded = '';
  while (value >= 0x20) {
    encoded += String.fromCharCode(((value & 0x1f) | 0x20) + 63);
    value >>= 5;
  }
  encoded += String.fromCharCode(value + 63);

  return encoded;
}

/**
 * Builds final route data from trip logs
 * This function reads all trip logs and creates a RouteData object with:
 * - Encoded polyline of the actual route taken
 * - Total distance in meters
 * - Total duration in seconds
 * - Bounding box of the route
 */
export async function buildFinalRouteDataFromLogs(
  tripId: string,
  tenantId: string
): Promise<RouteData | null> {
  try {
    logger.info(`Building final route data for trip ${tripId}`);

    // Get trip data to calculate duration
    const tripRef = getTenantCollection(tenantId, 'trips').doc(tripId);
    const tripDoc = await tripRef.get();

    if (!tripDoc.exists) {
      logger.warn(`Trip ${tripId} not found`);
      return null;
    }

    const tripData = tripDoc.data()!;

    // Get all logs ordered by timestamp
    const logsSnapshot = await getTenantCollection(tenantId, 'trips')
      .doc(tripId)
      .collection('logs')
      .orderBy('ts', 'asc')
      .get();

    if (logsSnapshot.empty) {
      logger.warn(`No logs found for trip ${tripId}`);
      return null;
    }

    // Process logs to build polyline coordinates and calculate distance
    const coordinates: Array<{ lat: number; lng: number }> = [];
    let totalDistanceMeters = 0;
    let prevLat: number | undefined;
    let prevLng: number | undefined;
    let minLat = 90;
    let maxLat = -90;
    let minLng = 180;
    let maxLng = -180;

    for (const logDoc of logsSnapshot.docs) {
      const logData = logDoc.data();
      const lat = logData.lat;
      const lng = logData.lon; // Note: field is 'lon' not 'lng'

      // Skip invalid coordinates
      if (!isValidCoordinate(lat, lng)) {
        continue;
      }

      // Add to coordinates array
      coordinates.push({ lat, lng });

      // Update bounds
      minLat = Math.min(minLat, lat);
      maxLat = Math.max(maxLat, lat);
      minLng = Math.min(minLng, lng);
      maxLng = Math.max(maxLng, lng);

      // Calculate distance from previous point
      if (prevLat !== undefined && prevLng !== undefined) {
        const distance = calculateHaversineDistance(prevLat, prevLng, lat, lng);
        totalDistanceMeters += distance;
      }

      prevLat = lat;
      prevLng = lng;
    }

    if (coordinates.length === 0) {
      logger.warn(`No valid coordinates found in logs for trip ${tripId}`);
      return null;
    }

    // Calculate duration
    const driverStartTime = tripData.driverStartTime;
    const completedAt = tripData.completedAt || tripData.cancelledAt || Timestamp.now();
    let durationSeconds = 0;

    if (driverStartTime) {
      const durationMs = completedAt.toMillis() - driverStartTime.toMillis();
      durationSeconds = Math.round(durationMs / 1000);
    }

    // Encode polyline
    const polyline = encodePolyline(coordinates);

    // Build route data
    const routeData: RouteData = {
      distanceMeters: Math.round(totalDistanceMeters),
      durationSeconds,
      polyline,
      bounds: {
        northeast: { lat: maxLat, lng: maxLng },
        southwest: { lat: minLat, lng: minLng }
      }
    };

    logger.info(`Built final route data for trip ${tripId}:`, {
      coordinateCount: coordinates.length,
      distanceKm: (totalDistanceMeters / 1000).toFixed(2),
      durationMinutes: Math.round(durationSeconds / 60),
      polylineLength: polyline.length
    });

    return routeData;
  } catch (error) {
    logger.error(`Error building final route data for trip ${tripId}:`, error);
    return null;
  }
}

/**
 * Validates if coordinates are valid
 */
function isValidCoordinate(lat: number, lon: number): boolean {
  return (
    typeof lat === 'number' &&
    typeof lon === 'number' &&
    !isNaN(lat) &&
    !isNaN(lon) &&
    lat >= -90 &&
    lat <= 90 &&
    lon >= -180 &&
    lon <= 180
  );
}

/**
 * Calculate distance between two coordinates using Haversine formula
 * @returns Distance in meters
 */
function calculateHaversineDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371000; // Earth's radius in meters
  const φ1 = lat1 * Math.PI / 180;
  const φ2 = lat2 * Math.PI / 180;
  const Δφ = (lat2 - lat1) * Math.PI / 180;
  const Δλ = (lon2 - lon1) * Math.PI / 180;

  const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) *
    Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c;
}

/**
 * Updates a trip with final route data
 * This is typically called when a trip is completed or cancelled
 */
export async function updateTripWithFinalRouteData(
  tripId: string,
  tenantId: string
): Promise<boolean> {
  try {
    const finalRouteData = await buildFinalRouteDataFromLogs(tripId, tenantId);

    if (!finalRouteData) {
      logger.warn(`Could not build final route data for trip ${tripId}`);
      return false;
    }

    // Update trip document with final route data
    await getTenantCollection(tenantId, 'trips')
      .doc(tripId)
      .update({
        finalRouteData,
        finalRouteDataUpdatedAt: Timestamp.now()
      });

    logger.info(`Updated trip ${tripId} with final route data`);
    return true;
  } catch (error) {
    logger.error(`Error updating trip ${tripId} with final route data:`, error);
    return false;
  }
} 