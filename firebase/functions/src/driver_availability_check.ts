import {onCall, HttpsError} from "firebase-functions/v2/https";
import {ensureTenantId, getAdminAccess} from "./tenant_utils";
import {getAvailableDrivers, checkDriverAvailabilityDetailed} from "./driver_availability";
import {logger} from "firebase-functions/v2";

const europe = "europe-west3";

/**
 * Cloud function to get available drivers for admin panel
 */
export const getAvailableDriversForAdmin = onCall({
  region: europe,
}, async (request) => {
  const {tenantId, requiredCapacity = 1} = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  if (!request.auth) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  // Validate admin has access to this tenant
  const adminAccess = await getAdminAccess(
    request.auth.uid,
    effectiveTenantId
  );

  if (!adminAccess || adminAccess.role < 0) { // Requires at least MANAGER
    throw new HttpsError("permission-denied", "Insufficient permissions");
  }

  try {
    const availableDrivers = await getAvailableDrivers(effectiveTenantId, requiredCapacity);
    
    logger.info("Fetched available drivers for admin", {
      tenantId: effectiveTenantId,
      requiredCapacity,
      count: availableDrivers.length,
      adminUid: request.auth.uid,
    });

    return {
      success: true,
      drivers: availableDrivers,
    };
  } catch (error) {
    logger.error("Error getting available drivers", {tenantId: effectiveTenantId, error});
    throw new HttpsError("internal", "Failed to get available drivers");
  }
});

/**
 * Check if a specific driver is available
 */
export const checkDriverAvailability = onCall({
  region: europe,
}, async (request) => {
  const {tenantId, driverUID, requiredCapacity = 1} = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  if (!request.auth) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  // Validate admin has access to this tenant
  const adminAccess = await getAdminAccess(
    request.auth.uid,
    effectiveTenantId
  );

  if (!adminAccess || adminAccess.role < 0) { // Requires at least MANAGER
    throw new HttpsError("permission-denied", "Insufficient permissions");
  }

  if (!driverUID) {
    throw new HttpsError("invalid-argument", "Driver UID is required");
  }

  try {
    const availabilityResult = await checkDriverAvailabilityDetailed(driverUID, effectiveTenantId, requiredCapacity);
    
    logger.info("Driver availability check for admin", {
      driverUID,
      tenantId: effectiveTenantId,
      requiredCapacity,
      isAvailable: availabilityResult.isAvailable,
      reason: availabilityResult.reason,
      adminUid: request.auth.uid,
    });
    
    return {
      success: true,
      isAvailable: availabilityResult.isAvailable,
      reason: availabilityResult.reason,
      details: availabilityResult.details,
      driverUID,
    };
  } catch (error) {
    logger.error("Error checking driver availability", {driverUID, tenantId: effectiveTenantId, error});
    throw new HttpsError("internal", "Failed to check driver availability");
  }
});