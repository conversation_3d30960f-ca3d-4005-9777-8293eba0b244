import { onSchedule } from 'firebase-functions/v2/scheduler';
import { logger } from 'firebase-functions';
import { normalizeDataForFirestore } from "./utils";
import { db } from './config';

/**
 * Scheduled function that runs every 24 hours to clean up stale device IDs
 * for users who haven't been active for more than 7 days.
 *
 * Note: mobile_users collection is global (shared across all tenants) as per
 * multi-tenant architecture, so this cleanup applies to all users.
 */
export const cleanupStaleDeviceIds = onSchedule({
  schedule: "every 24 hours",
  timeZone: 'UTC',
  region: "europe-west3",
  timeoutSeconds: 60,
  memory: "512MiB",
  maxInstances: 1,
}, async (event) => {
  const now = new Date();

  // Find users who haven't been active for more than 7 days
  const inactiveThreshold = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // 7 days ago

  try {
    // Query for users with lastSeen older than the threshold and have a deviceId
    // Note: This query requires a composite index on lastSeen (ASC) and deviceId (ASC)
    // The index is defined in firestore.indexes.json
    // mobile_users collection is global (shared across all tenants)
    const staleUsersSnapshot = await db.collection('mobile_users')
      .where('lastSeen', '<', inactiveThreshold)
      .where('deviceId', '!=', null)
      .get();

    if (staleUsersSnapshot.empty) {
      logger.info('No stale device IDs found');
      return;
    }

    // Batch update to clear deviceId for stale users
    const batch = db.batch();
    let count = 0;

    staleUsersSnapshot.forEach(doc => {
      const updateData = normalizeDataForFirestore(
        { deviceId: null },
        `cleanupStaleDeviceIds:batch:update:${doc.id}`
      );
      // Only add to batch if there's something to update (deviceId: null is a valid update)
      // The normalize function as written won't make this object empty unless deviceId was initially undefined
      // which is not the case here. Still, good practice.
      if (Object.keys(updateData).length > 0) {
        batch.update(doc.ref, updateData);
      }
      count++;
    });

    await batch.commit();
    logger.info(`Cleaned up ${count} stale device IDs`);
  } catch (error) {
    logger.error('Error cleaning up stale device IDs:', error);
  }
}); 