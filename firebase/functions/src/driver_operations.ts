import * as functions from "firebase-functions/v2/https";
import { logger } from "firebase-functions/v2";
import { FieldValue } from "firebase-admin/firestore";
import { normalizeDataForFirestore } from "./utils";
import { canTransition } from "./trip_state_machine";
import { getTenantCollection, ensureTenantId } from "./tenant_utils";
import { checkDriverAvailabilityDetailed } from "./driver_availability";
import { sendDriverAssignmentNotification } from "./notifications";
import { db } from "./config";

// Driver availability error messages in different languages
const driverErrorMessages = {
  en: {
    "Driver account not found": "Your driver account could not be found in the system.",
    "Driver is currently on another trip": "You are currently assigned to another trip. Please complete your current trip first.",
    "Account is not configured as a driver": "Your account is not configured for driver operations. Please contact support.",
    "Driver does not have access to this service area": "You do not have access to this service area. Please contact your administrator.",
    "Driver profile is not set up for this service area": "Your driver profile needs to be set up for this service area. Please complete your registration.",
    "Driver profile is deactivated": "Your driver account has been temporarily disabled. Please contact support.",
    "Driver is currently offline": "You are currently offline. Please go online to accept trip requests.",
    "Driver registration is pending approval": "Your driver registration is pending approval. Please wait for confirmation.",
    "No vehicle assigned to driver": "You need to have a vehicle assigned before accepting trips. Please contact your administrator.",
    "Vehicle assignment is invalid": "Your vehicle assignment is invalid. Please contact your administrator.",
    "Assigned vehicle not found": "The vehicle assigned to you was not found in the system. Please contact your administrator.",
    "Assigned vehicle is deactivated": "The vehicle assigned to you has been deactivated. Please contact your administrator.",
    "Vehicle capacity is insufficient": "Your vehicle does not have sufficient capacity for this trip.",
    "Missing required documents and vehicle not activated": "You are missing required documents and your vehicle needs admin activation. Please ensure all documents are uploaded and approved.",
    "System error while checking availability": "A system error occurred while checking your availability. Please try again."
  },
  fr: {
    "Driver account not found": "Votre compte chauffeur est introuvable dans le système.",
    "Driver is currently on another trip": "Vous êtes actuellement assigné à un autre trajet. Veuillez d'abord terminer votre trajet actuel.",
    "Account is not configured as a driver": "Votre compte n'est pas configuré pour les opérations de chauffeur. Veuillez contacter le support.",
    "Driver does not have access to this service area": "Vous n'avez pas accès à cette zone de service. Veuillez contacter votre administrateur.",
    "Driver profile is not set up for this service area": "Votre profil chauffeur doit être configuré pour cette zone de service. Veuillez compléter votre inscription.",
    "Driver profile is deactivated": "Votre compte chauffeur a été temporairement désactivé. Veuillez contacter le support.",
    "Driver is currently offline": "Vous êtes actuellement hors ligne. Veuillez vous connecter pour accepter les demandes de trajet.",
    "Driver registration is pending approval": "Votre inscription de chauffeur est en attente d'approbation. Veuillez attendre la confirmation.",
    "No vehicle assigned to driver": "Vous devez avoir un véhicule assigné avant d'accepter des trajets. Veuillez contacter votre administrateur.",
    "Vehicle assignment is invalid": "Votre assignation de véhicule est invalide. Veuillez contacter votre administrateur.",
    "Assigned vehicle not found": "Le véhicule qui vous est assigné n'a pas été trouvé dans le système. Veuillez contacter votre administrateur.",
    "Assigned vehicle is deactivated": "Le véhicule qui vous est assigné a été désactivé. Veuillez contacter votre administrateur.",
    "Vehicle capacity is insufficient": "Votre véhicule n'a pas une capacité suffisante pour ce trajet.",
    "Missing required documents and vehicle not activated": "Il vous manque des documents requis et votre véhicule nécessite l'activation par l'administrateur. Veuillez vous assurer que tous les documents sont téléchargés et approuvés.",
    "System error while checking availability": "Une erreur système s'est produite lors de la vérification de votre disponibilité. Veuillez réessayer."
  }
};

/**
 * Get user's language preference
 */
async function getUserLanguage(uid: string): Promise<string> {
  try {
    const userDoc = await db.collection("mobile_users").doc(uid).get();
    const userData = userDoc.data();
    
    if (userData?.primaryLanguage?.languageCode) {
      return userData.primaryLanguage.languageCode === "fr" ? "fr" : "en";
    }
  } catch (error) {
    logger.error("Error getting user language", { uid, error });
  }
  
  return "en"; // Default to English
}

/**
 * Get localized error message for driver availability issues
 */
function getLocalizedDriverError(errorReason: string, language: string): string {
  const messages = driverErrorMessages[language as keyof typeof driverErrorMessages] || driverErrorMessages.en;
  return messages[errorReason as keyof typeof messages] || errorReason;
}

// Function to request a driver from admin
export const adminRequestDriver = functions.onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 5,
    memory: "512MiB",
    concurrency: 1000,
  },
  async (request) => {
    if (!request.auth) {
      throw new functions.HttpsError("unauthenticated", "Authentication is required.");
    }

    const { tripId, driverUid, driverLocation, tenantId } = request.data;
    const effectiveTenantId = ensureTenantId(tenantId);
    if (!tripId || !driverUid || !driverLocation) {
      throw new functions.HttpsError("invalid-argument", "Trip ID, driver UID, and driver location are required.");
    }

    try {
      const tripRef = getTenantCollection(effectiveTenantId, "trips").doc(tripId);

      await db.runTransaction(async (transaction) => {
        const tripDoc = await transaction.get(tripRef);
        if (!tripDoc.exists) {
          throw new functions.HttpsError("not-found", "Trip not found");
        }

        // Get driver info
        const driverRef = db.collection("mobile_users").doc(driverUid);
        const driverDoc = await transaction.get(driverRef);
        if (!driverDoc.exists) {
          throw new functions.HttpsError("not-found", "Driver not found");
        }

        const driverData = driverDoc.data();

        const tripUpdateData = normalizeDataForFirestore(
          {
            uidChosenDriver: driverUid,
            status: "requestingDriver",
            driverLocation: driverLocation,
            driver: {
              displayName: driverData?.displayName,
              photoURL: driverData?.photoURL,
              phoneNumber: driverData?.phoneNumber,
            },
            driverNotificationSent: false,
          },
          "adminRequestDriver:tripRef:update"
        );

        if (Object.keys(tripUpdateData).length > 0) {
          transaction.update(tripRef, tripUpdateData);
        }
      });

      logger.info("Driver requested successfully", { tripId, driverUid });
      return { success: true };
    } catch (error) {
      logger.error("Error requesting driver", { tripId, driverUid, error });
      throw new functions.HttpsError("internal", "Error requesting driver", { originalError: error });
    }
  }
);

// Function to assign a driver to a trip
export const adminAssignDriver = functions.onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 5,
    memory: "512MiB",
    concurrency: 1000,
  },
  async (request) => {
    if (!request.auth) {
      throw new functions.HttpsError("unauthenticated", "Authentication is required.");
    }

    const { tripId, driverUid, tenantId } = request.data;
    const effectiveTenantId = ensureTenantId(tenantId);
    if (!tripId || !driverUid) {
      throw new functions.HttpsError("invalid-argument", "Trip ID and driver UID are required.");
    }

    try {
      const tripRef = getTenantCollection(effectiveTenantId, "trips").doc(tripId);
      const driverRef = db.collection("mobile_users").doc(driverUid);

      const result = await db.runTransaction(async (transaction) => {
        // Check if the driver exists and is available
        const driverSnapshot = await transaction.get(driverRef);
        if (!driverSnapshot.exists) {
          return 1; // Driver not found
        }

        const driverData = driverSnapshot.data();
        if (driverData?.occupiedByTripId) {
          return 2; // Driver already occupied
        }

        // Get trip data to check passenger count
        const tripSnapshot = await transaction.get(tripRef);
        if (!tripSnapshot.exists) {
          return 3; // Trip not found
        }

        const tripData = tripSnapshot.data();
        const passengerCount = tripData?.passengerCount || 1;

        // Check enhanced driver availability (for informational purposes)
        const availabilityCheck = await checkDriverAvailabilityDetailed(driverUid, effectiveTenantId, passengerCount);
        if (!availabilityCheck.isAvailable) {
          // Note: This warning is for informational purposes only.
          // The admin assignment will proceed regardless because:
          // 1. Admins have override authority
          // 2. The system now allows drivers with activated vehicles to accept trips
          //    even without complete documentation
          logger.warn("Driver does not meet enhanced availability criteria", {
            tripId,
            driverUid,
            tenantId: effectiveTenantId,
            passengerCount,
            reason: availabilityCheck.reason,
            details: availabilityCheck.details,
            adminUid: request.auth?.uid,
          });
          // Log warning but allow admin override for now
        }

        // Update trip with driver info
        const driverInfo = {
          displayName: driverData?.displayName,
          photoURL: driverData?.photoURL,
          phoneNumber: driverData?.phoneNumber,
        };

        const tripUpdatePayload = normalizeDataForFirestore(
          {
            uidChosenDriver: driverUid,
            status: "driverApproaching",
            driver: driverInfo,
            skippedDriverIds: [],
            distanceTotalMeters: 0, // Initialize with 0 when assigning driver
          },
          "adminAssignDriver:tripRef:update"
        );

        if (Object.keys(tripUpdatePayload).length > 0) {
          transaction.update(tripRef, tripUpdatePayload);
        }

        // Mark driver as occupied
        const driverUpdatePayload = normalizeDataForFirestore(
          {
            occupiedByTripId: tripId,
          },
          "adminAssignDriver:driverRef:update"
        );

        if (Object.keys(driverUpdatePayload).length > 0) {
          transaction.update(driverRef, driverUpdatePayload);
        }

        return 0; // Success
      });

      logger.info("Driver assignment result", { tripId, driverUid, result });

      // Send notification to the driver about the assignment
      try {
        // Get the driver data and trip data to include in notification
        const driverDoc = await db.collection("mobile_users").doc(driverUid).get();
        const driverData = driverDoc.data();
        const tripDoc = await getTenantCollection(effectiveTenantId, "trips").doc(tripId).get();
        const tripData = tripDoc.data();

        if (driverData?.fcmToken && tripData) {
          const pickupTime = tripData.pickupTime?.toDate();
          const destinationLocation = tripData.arrivalLocationName || tripData.arrivalLocation?.name;
          const isReserved = tripData.status === "reserved" || !!pickupTime;

          await sendDriverAssignmentNotification(driverData.fcmToken, tripId, pickupTime, destinationLocation, isReserved, driverUid);

          logger.info("Driver assignment notification sent", {
            tripId,
            driverUid,
            pickupTime: pickupTime?.toISOString(),
            isReserved,
          });
        }
      } catch (notificationError) {
        // Log error but don't fail the assignment
        logger.error("Error sending driver assignment notification", {
          tripId,
          driverUid,
          error: notificationError,
        });
      }

      return { result };
    } catch (error) {
      logger.error("Error assigning driver", { tripId, driverUid, error });
      throw new functions.HttpsError("internal", "Error assigning driver", { originalError: error });
    }
  }
);

// Function to cancel a driver request from the mobile app
export const cancelDriverRequest = functions.onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 5,
    memory: "512MiB",
    concurrency: 1000,
  },
  async (request) => {
    if (!request.auth) {
      throw new functions.HttpsError("unauthenticated", "Authentication is required.");
    }

    const { tripId, tenantId } = request.data;
    const effectiveTenantId = ensureTenantId(tenantId);
    if (!tripId) {
      throw new functions.HttpsError("invalid-argument", "Trip ID is required.");
    }

    try {
      const tripRef = getTenantCollection(effectiveTenantId, "trips").doc(tripId);

      await db.runTransaction(async (transaction) => {
        // Get the latest trip data
        const tripSnapshot = await transaction.get(tripRef);
        if (!tripSnapshot.exists) {
          throw new functions.HttpsError("not-found", "Trip not found");
        }

        const tripData = tripSnapshot.data();

        // Verify the trip is still in requesting phase
        if (tripData?.status !== "requestingDriver") {
          throw new functions.HttpsError("failed-precondition", "Trip is no longer in requesting phase");
        }

        // Update the trip
        const tripUpdateDataForCancel = normalizeDataForFirestore(
          {
            uidChosenDriver: null,
            status: "preparing",
            driverRouteData: null,
            driverLocation: null,
            driver: null,
          },
          "cancelDriverRequest:tripRef:update"
        );

        if (Object.keys(tripUpdateDataForCancel).length > 0) {
          transaction.update(tripRef, tripUpdateDataForCancel);
        }

        // Mark the driver as available if a driver was assigned
        if (tripData?.uidChosenDriver) {
          const driverRef = db.collection("mobile_users").doc(tripData.uidChosenDriver);
          const driverUpdateForCancel = normalizeDataForFirestore(
            {
              occupiedByTripId: null,
            },
            "cancelDriverRequest:driverRef:update"
          );
          if (Object.keys(driverUpdateForCancel).length > 0) {
            transaction.update(driverRef, driverUpdateForCancel);
          }
        }
      });

      logger.info("Driver request cancelled", { tripId });
      return { success: true };
    } catch (error) {
      logger.error("Error cancelling driver request", { tripId, error });
      throw new functions.HttpsError("internal", "Error cancelling driver request", { originalError: error });
    }
  }
);

// Function for a driver to accept a trip request
export const driverAcceptRequest = functions.onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 5,
    memory: "512MiB",
    concurrency: 1000,
  },
  async (request) => {
    if (!request.auth) {
      throw new functions.HttpsError("unauthenticated", "Authentication is required.");
    }

    const { tripId, driverUid, currentLocation, driverInfo, tenantId } = request.data;
    const effectiveTenantId = ensureTenantId(tenantId);

    // Enhanced logging for debugging
    logger.info("🚗 Driver accept request initiated", {
      tripId,
      driverUid,
      tenantId: effectiveTenantId,
      currentLocation,
      driverInfo,
      authUid: request.auth.uid,
    });

    if (!tripId || !driverUid || !currentLocation || !driverInfo) {
      throw new functions.HttpsError("invalid-argument", "Trip ID, driver UID, current location, and driver info are required.");
    }

    try {
      const tripRef = getTenantCollection(effectiveTenantId, "trips").doc(tripId);
      const userRef = db.collection("mobile_users").doc(driverUid);

      await db.runTransaction(async (transaction) => {
        // Check if the driver is not already assigned to another trip
        const userSnapshot = await transaction.get(userRef);
        if (!userSnapshot.exists) {
          logger.error("❌ Driver not found", { driverUid });
          throw new functions.HttpsError("not-found", "Driver not found");
        }
        const userData = userSnapshot.data();

        // Enhanced logging for driver status
        logger.info("👤 Driver status check", {
          driverUid,
          occupiedByTripId: userData?.occupiedByTripId,
          primaryUserType: userData?.primaryUserType,
          tenantIDs: userData?.tenantIDs,
          isDriverConfirmed: userData?.isDriverConfirmed,
        });

        if (userData?.occupiedByTripId) {
          logger.warn("⚠️ Driver already occupied", {
            driverUid,
            occupiedByTripId: userData.occupiedByTripId,
            requestedTripId: tripId,
          });
          throw new functions.HttpsError("failed-precondition", "Driver is already assigned to another trip");
        }

        // Get trip data to check passenger count
        const tripSnapshot = await transaction.get(tripRef);
        if (!tripSnapshot.exists) {
          logger.error("❌ Trip not found", { tripId });
          throw new functions.HttpsError("not-found", "Trip not found");
        }

        const tripData = tripSnapshot.data();
        const passengerCount = tripData?.passengerCount || 1;

        // Enhanced logging for trip status
        logger.info("📍 Trip status check", {
          tripId,
          status: tripData?.status,
          uidChosenDriver: tripData?.uidChosenDriver,
          passengerCount,
          skippedDriverIds: tripData?.skippedDriverIds,
        });

        // Check enhanced driver availability with detailed error info
        const availabilityResult = await checkDriverAvailabilityDetailed(driverUid, effectiveTenantId, passengerCount);

        logger.info("✅ Driver availability check", {
          driverUid,
          tenantId: effectiveTenantId,
          passengerCount,
          isAvailable: availabilityResult.isAvailable,
          reason: availabilityResult.reason,
          details: availabilityResult.details,
        });

        if (!availabilityResult.isAvailable) {
          logger.error("❌ Driver does not meet availability requirements", {
            driverUid,
            tenantId: effectiveTenantId,
            passengerCount,
            reason: availabilityResult.reason,
            details: availabilityResult.details,
          });
          
          // Get driver's language and provide localized error message
          const driverLanguage = await getUserLanguage(driverUid);
          const localizedMessage = getLocalizedDriverError(
            availabilityResult.reason || "System error while checking availability", 
            driverLanguage
          );
          
          throw new functions.HttpsError(
            "failed-precondition",
            localizedMessage,
            { 
              details: availabilityResult.details,
              originalReason: availabilityResult.reason,
              language: driverLanguage
            }
          );
        }

        // Update the trip
        const tripAcceptUpdateData = normalizeDataForFirestore(
          {
            status: "driverApproaching",
            driver: {
              displayName: driverInfo.displayName,
              photoURL: driverInfo.photoURL,
              phoneNumber: driverInfo.phoneNumber,
            },
          },
          "driverAcceptRequest:tripRef:update"
        );

        if (Object.keys(tripAcceptUpdateData).length > 0) {
          transaction.update(tripRef, tripAcceptUpdateData);
        }

        // Mark the driver as occupied
        const userUpdateData = normalizeDataForFirestore(
          {
            occupiedByTripId: tripId,
          },
          "driverAcceptRequest:userRef:update"
        );
        if (Object.keys(userUpdateData).length > 0) {
          transaction.update(userRef, userUpdateData);
        }

        logger.info("✅ Trip accepted successfully", {
          tripId,
          driverUid,
          newStatus: "driverApproaching",
        });
      });

      logger.info("✅ Driver accepted trip request", { tripId, driverUid });
      return { success: true };
    } catch (error) {
      logger.error("❌ Error accepting trip request", {
        tripId,
        driverUid,
        error: error instanceof Error ? error.message : error,
        errorType: error instanceof functions.HttpsError ? error.code : "unknown",
      });

      // Re-throw HttpsError as-is, wrap other errors
      if (error instanceof functions.HttpsError) {
        throw error;
      }

      throw new functions.HttpsError("internal", "Error accepting trip request", { originalError: error });
    }
  }
);

// Function for a driver to reject a trip request
export const driverRejectRequest = functions.onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 5,
    memory: "512MiB",
    concurrency: 1000,
  },
  async (request) => {
    if (!request.auth) {
      throw new functions.HttpsError("unauthenticated", "Authentication is required.");
    }

    const { tripId, driverUid, reasonType, customReason, tenantId } = request.data;
    const effectiveTenantId = ensureTenantId(tenantId);
    if (!tripId || !driverUid || !reasonType) {
      throw new functions.HttpsError("invalid-argument", "Trip ID, driver UID, and reason type are required.");
    }

    try {
      const tripRef = getTenantCollection(effectiveTenantId, "trips").doc(tripId);
      const driverRef = db.collection("mobile_users").doc(driverUid);

      await db.runTransaction(async (transaction) => {
        // Get the latest trip data
        const tripSnapshot = await transaction.get(tripRef);
        if (!tripSnapshot.exists) {
          throw new functions.HttpsError("not-found", "Trip not found");
        }

        const tripData = tripSnapshot.data();

        // Verify the trip is in requesting phase and this driver is the chosen one
        if (tripData?.status !== "requestingDriver") {
          throw new functions.HttpsError("failed-precondition", "Trip is no longer in requesting phase");
        }

        if (tripData?.uidChosenDriver !== driverUid) {
          throw new functions.HttpsError("failed-precondition", "Driver is not assigned to this trip");
        }

        // Validate state machine transition
        if (!canTransition("requestingDriver", "DRIVER_REJECT")) {
          throw new functions.HttpsError("failed-precondition", "Invalid state transition: requestingDriver -> DRIVER_REJECT");
        }

        // Get driver info for event logging
        const driverSnapshot = await transaction.get(driverRef);
        if (!driverSnapshot.exists) {
          throw new functions.HttpsError("not-found", "Driver not found");
        }
        const driverData = driverSnapshot.data();

        // Update the trip - add driver to skipped list and reset to preparing (following state machine)
        const currentSkippedDriverIds = tripData?.skippedDriverIds || [];
        const tripUpdateData = normalizeDataForFirestore(
          {
            uidChosenDriver: null,
            status: "preparing",
            driverRouteData: null,
            driverLocation: null,
            driver: null,
            skippedDriverIds: [...currentSkippedDriverIds, driverUid],
            driverNotificationSent: false,
          },
          "driverRejectRequest:tripRef:update"
        );

        if (Object.keys(tripUpdateData).length > 0) {
          transaction.update(tripRef, tripUpdateData);
        }

        // Mark the driver as available
        const driverUpdateData = normalizeDataForFirestore(
          {
            occupiedByTripId: null,
          },
          "driverRejectRequest:driverRef:update"
        );
        if (Object.keys(driverUpdateData).length > 0) {
          transaction.update(driverRef, driverUpdateData);
        }

        // Create event log for the rejection
        const eventLogRef = getTenantCollection(effectiveTenantId, "event_logs").doc();
        const eventLogData = normalizeDataForFirestore(
          {
            uid: driverUid,
            type: "DRIVER_TRIP_REJECTED",
            timestamp: FieldValue.serverTimestamp(),
            timestampDT: new Date(),
            driver: {
              uid: driverUid,
              displayName: driverData?.displayName,
              photoURL: driverData?.photoURL,
              email: driverData?.email,
            },
            trip: {
              id: tripId,
              arrivalLocationName: tripData?.arrivalLocationName,
              startLocationName: tripData?.startLocationName,
            },
            tripRejectionReasonType: reasonType,
            ...(customReason && { reason: customReason }),
          },
          "driverRejectRequest:eventLogRef:set"
        );

        transaction.set(eventLogRef, eventLogData);
      });

      logger.info("Driver rejected trip request", { tripId, driverUid, reasonType });
      return { success: true };
    } catch (error) {
      logger.error("Error rejecting trip request", { tripId, driverUid, error });
      throw new functions.HttpsError("internal", "Error rejecting trip request", { originalError: error });
    }
  }
);

/**
 * Disconnects a driver from their current trip
 * @param data Object containing the driverUid
 * @returns Result of the operation
 */
export const disconnectDriverFromTrip = functions.onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 5,
    memory: "512MiB",
    concurrency: 1000,
    maxInstances: 1,
  },
  async (request) => {
    if (!request.auth) {
      throw new functions.HttpsError("unauthenticated", "Authentication is required.");
    }

    const { driverUid, tenantId } = request.data;
    const effectiveTenantId = ensureTenantId(tenantId);
    if (!driverUid) {
      throw new functions.HttpsError("invalid-argument", "Driver UID is required.");
    }

    try {
      // Store admin UID for tracking who cancelled the trip
      const adminUid = request.auth.uid;
      const driverRef = db.collection("mobile_users").doc(driverUid);

      // Check driver status first
      const driverDoc = await driverRef.get();
      if (!driverDoc.exists) {
        // Although the driver doesn't exist, we treat this as success from the perspective of disconnecting.
        logger.warn(`Attempted to disconnect non-existent driver: ${driverUid}`);
        return {
          success: true,
          message: `Driver with UID ${driverUid} not found. No action taken.`,
        };
      }

      const driverData = driverDoc.data();
      if (!driverData?.occupiedByTripId) {
        logger.info(`Driver ${driverUid} is not currently assigned to any trip.`);
        return {
          success: true,
          message: `Driver ${driverUid} is not currently assigned to any trip. No action taken.`,
        };
      }

      // Find trips where this driver is assigned
      const tripsRef = getTenantCollection(effectiveTenantId, "trips");
      const q = tripsRef
        .where("uidChosenDriver", "==", driverUid)
        .where("status", "in", ["requestingDriver", "reserved", "driverApproaching", "driverAwaiting", "inProgress"]);

      const tripDocs = await q.get();

      // If no active trips found, it might be stale data. Clear driver status.
      if (tripDocs.empty) {
        logger.info(`No active trips found for driver ${driverUid}, but they were marked as occupied. Clearing status.`);
        const driverClearStatusUpdate = normalizeDataForFirestore(
          { occupiedByTripId: null },
          "disconnectDriverFromTrip:driverRef:update:noActiveTrips"
        );
        if (Object.keys(driverClearStatusUpdate).length > 0) {
          await driverRef.update(driverClearStatusUpdate);
        }
        return {
          success: true,
          message: `No active trips found for driver ${driverUid}. Driver status has been updated.`,
        };
      }

      // Update each trip based on its status
      const batch = db.batch();
      let cancelledTrips = 0;
      let preparingTrips = 0;

      tripDocs.forEach((doc) => {
        const tripData = doc.data();
        const tripStatus = tripData.status;

        if (["inProgress", "driverAwaiting", "driverApproaching"].includes(tripStatus)) {
          const cancelUpdate = normalizeDataForFirestore(
            {
              status: "cancelled",
              cancelledAt: new Date(),
              uidCancelledBy: adminUid,
              driverNotificationSent: false,
              skippedDriverIds: [...(tripData.skippedDriverIds || []), driverUid],
            },
            `disconnectDriverFromTrip:batch:updateCancel:trip:${doc.id}`
          );
          if (Object.keys(cancelUpdate).length > 0) {
            batch.update(doc.ref, cancelUpdate);
          }
          cancelledTrips++;
        } else if (["reserved", "completed", "cancelled", "paid"].includes(tripStatus)) {
          // Do nothing here as the trip is already done
        } else {
          const preparingUpdate = normalizeDataForFirestore(
            {
              status: "preparing",
              uidChosenDriver: null,
              driverLocation: null,
              driverRouteData: null,
              skippedDriverIds: [...(tripData.skippedDriverIds || []), driverUid],
            },
            `disconnectDriverFromTrip:batch:updatePreparing:trip:${doc.id}`
          );
          if (Object.keys(preparingUpdate).length > 0) {
            batch.update(doc.ref, preparingUpdate);
          }
          preparingTrips++;
        }
      });

      // Update driver status
      const driverFinalUpdate = normalizeDataForFirestore(
        {
          occupiedByTripId: null,
        },
        "disconnectDriverFromTrip:batch:updateDriverStatus"
      );
      if (Object.keys(driverFinalUpdate).length > 0) {
        batch.update(driverRef, driverFinalUpdate);
      }

      await batch.commit();
      logger.info(
        `Driver ${driverUid} disconnected from trips: ${cancelledTrips} cancelled, ${preparingTrips} reset to preparing`
      );

      return {
        success: true,
        message: `Driver disconnected from ${tripDocs.size} trip(s). ${cancelledTrips} cancelled, ${preparingTrips} reset to preparing.`,
      };
    } catch (error) {
      logger.error("Error disconnecting driver from trip:", error);
      throw new functions.HttpsError("internal", "Failed to disconnect driver from trip");
    }
  }
);

/**
 * Clears a driver's occupied status if they're stuck with a stale trip assignment
 * This is a safety function to help drivers who can't accept new trips due to stale data
 */
export const clearDriverStaleAssignment = functions.onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 5,
    memory: "512MiB",
    concurrency: 1000,
  },
  async (request) => {
    if (!request.auth) {
      throw new functions.HttpsError("unauthenticated", "Authentication is required.");
    }

    const { driverUid, tenantId } = request.data;
    const effectiveTenantId = ensureTenantId(tenantId);

    if (!driverUid) {
      throw new functions.HttpsError("invalid-argument", "Driver UID is required.");
    }

    // Verify that the authenticated user is the driver
    if (request.auth.uid !== driverUid) {
      throw new functions.HttpsError("permission-denied", "You can only clear your own assignment.");
    }

    try {
      const driverRef = db.collection("mobile_users").doc(driverUid);

      // Check current driver status
      const driverDoc = await driverRef.get();
      if (!driverDoc.exists) {
        throw new functions.HttpsError("not-found", "Driver not found.");
      }

      const driverData = driverDoc.data();
      const occupiedByTripId = driverData?.occupiedByTripId;

      if (!occupiedByTripId) {
        logger.info("Driver is not currently assigned to any trip", { driverUid });
        return {
          success: true,
          message: "You are not currently assigned to any trip.",
          wasCleared: false,
        };
      }

      // Check if the trip still exists and is active
      const tripRef = getTenantCollection(effectiveTenantId, "trips").doc(occupiedByTripId);
      const tripDoc = await tripRef.get();

      if (!tripDoc.exists) {
        // Trip doesn't exist, clear the driver's status
        await driverRef.update({ occupiedByTripId: null });
        logger.info("Cleared stale trip assignment - trip no longer exists", {
          driverUid,
          staleTrip: occupiedByTripId,
        });
        return {
          success: true,
          message: "Your stale trip assignment has been cleared. You can now accept new trips.",
          wasCleared: true,
        };
      }

      const tripData = tripDoc.data();
      const tripStatus = tripData?.status;
      const activeStatuses = ["requestingDriver", "driverApproaching", "driverAwaiting", "inProgress"];

      // Check if trip is still active
      if (!activeStatuses.includes(tripStatus)) {
        // Trip is no longer active, clear the driver's status
        await driverRef.update({ occupiedByTripId: null });
        logger.info("Cleared stale trip assignment - trip no longer active", {
          driverUid,
          staleTrip: occupiedByTripId,
          tripStatus,
        });
        return {
          success: true,
          message: "Your stale trip assignment has been cleared. You can now accept new trips.",
          wasCleared: true,
        };
      }

      // Trip is still active, don't clear
      logger.warn("Cannot clear assignment - trip is still active", {
        driverUid,
        tripId: occupiedByTripId,
        tripStatus,
      });

      return {
        success: false,
        message: `You are still assigned to an active trip (status: ${tripStatus}). Please complete or cancel it properly.`,
        wasCleared: false,
        activeTrip: {
          id: occupiedByTripId,
          status: tripStatus,
        },
      };
    } catch (error) {
      logger.error("Error clearing driver stale assignment:", {
        driverUid,
        error: error instanceof Error ? error.message : error,
      });
      throw new functions.HttpsError("internal", "Failed to clear stale assignment");
    }
  }
);
