import { onSchedule } from 'firebase-functions/v2/scheduler';
import { onRequest } from 'firebase-functions/v2/https';
import { onCall, HttpsError } from 'firebase-functions/v2/https';
import { Timestamp, Transaction, FieldValue } from 'firebase-admin/firestore';
import { db } from './config';
import * as logger from 'firebase-functions/logger';
import { CloudTasksClient } from '@google-cloud/tasks';
import type { google } from '@google-cloud/tasks/build/protos/protos';
import { checkUpcomingReservations, monitorNotificationReliability } from './reservation_notifications';
import { getTenantCollection, getDefaultTenantId, getAllActiveTenants, getRouteDataForTrip, ensureTenantId } from './tenant_utils';
import { checkDriverRequestTimeouts } from './driver_request_timeout';
import { updateTripWithFinalRouteData } from './trip_final_route';

const REGION = 'europe-west3';
const CALCULATION_INTERVAL_MINUTES = 1; // Cloud Scheduler minimum interval is 1 minute
const MAX_DIRECT_PROCESSING = 50;
const CLOUD_TASKS_QUEUE = 'trip-cost-calculations';

// Initialize Cloud Tasks client
const tasksClient = new CloudTasksClient();

/**
 * Scheduled function to calculate trip costs and distances
 * Runs every minute to process active trips (Cloud Scheduler minimum interval)
 */
export const calculateTripCosts = onSchedule({
  schedule: `every ${CALCULATION_INTERVAL_MINUTES} minutes`,
  timeZone: 'UTC',
  region: REGION,
  timeoutSeconds: 540,
  memory: '512MiB'
}, async () => {
  logger.info('Starting trip cost calculation run');

  // Track execution count for periodic checks
  const executionCount = await incrementExecutionCount();
  const shouldCheckReservations = true; // Check every minute now
  const shouldMonitorNotifications = executionCount % 60 === 0; // Every hour (60 * 1min = 60min)

  try {
    // Check for driver request timeouts every execution (every minute)
    logger.info('Checking for driver request timeouts');
    await checkDriverRequestTimeouts().catch(error => {
      logger.error('Error checking driver request timeouts:', error);
    });

    // Check for reservation reminders every minute
    if (shouldCheckReservations) {
      logger.info('Checking for upcoming reservation reminders');
      await checkUpcomingReservations().catch(error => {
        logger.error('Error checking reservations:', error);
      });
    }

    // Monitor notification reliability every hour
    if (shouldMonitorNotifications) {
      logger.info('Monitoring notification reliability');
      await monitorNotificationReliability().catch(error => {
        logger.error('Error monitoring notifications:', error);
      });
    }

    // Query all active trips across all tenants
    const tenants = await getAllActiveTenants();
    let totalActiveTripCount = 0;
    const allActiveTrips: { tripId: string; tenantId: string; calculationCursor?: any }[] = [];

    // Gather all active trips from all tenants
    for (const tenantId of tenants) {
      const activeTripsSnapshot = await getTenantCollection(tenantId, 'trips')
        .where('status', '==', 'inProgress')
        .get();

      activeTripsSnapshot.docs.forEach(doc => {
        allActiveTrips.push({
          tripId: doc.id,
          tenantId,
          calculationCursor: doc.data().driver?.calculationCursor || null
        });
      });

      totalActiveTripCount += activeTripsSnapshot.size;
    }

    logger.info(`Found ${totalActiveTripCount} active trips across ${tenants.length} tenants`);

    if (totalActiveTripCount === 0) {
      logger.info('No active trips to process');
      return;
    }

    if (totalActiveTripCount <= MAX_DIRECT_PROCESSING) {
      // Process directly in this function
      logger.info('Processing trips directly in cron function');

      const processPromises = allActiveTrips.map(trip =>
        calculateTripProgress(trip.tripId, trip.tenantId).catch((error: any) => {
          logger.error(`Error processing trip ${trip.tripId}:`, error);
        })
      );

      await Promise.all(processPromises);
    } else {
      // Use Cloud Tasks for parallel processing
      logger.info('Using Cloud Tasks for parallel processing');

      const project = process.env.GCLOUD_PROJECT;
      const location = REGION;
      const queue = CLOUD_TASKS_QUEUE;
      const parent = tasksClient.queuePath(project!, location, queue);

      const taskPromises = allActiveTrips.map(async (trip) => {
        // Create a Cloud Task for this trip
        const task: google.cloud.tasks.v2.ITask = {
          httpRequest: {
            httpMethod: 'POST',
            url: `https://${location}-${project}.cloudfunctions.net/processTripCostTask`,
            headers: {
              'Content-Type': 'application/json',
            },
            body: Buffer.from(JSON.stringify({
              tripId: trip.tripId,
              tenantId: trip.tenantId,
              calculationCursor: trip.calculationCursor
            })).toString('base64'),
          },
        };

        // Set task to execute immediately
        task.scheduleTime = {
          seconds: Math.floor(Date.now() / 1000),
        };

        try {
          await tasksClient.createTask({ parent, task });
          logger.info(`Created task for trip ${trip.tripId} in tenant ${trip.tenantId}`);
        } catch (error) {
          logger.error(`Error creating task for trip ${trip.tripId}:`, error);
        }
      });

      await Promise.all(taskPromises);
    }

    logger.info('Trip cost calculation run completed');
  } catch (error) {
    logger.error('Error in calculateTripCosts:', error);
  }
});

/**
 * NEW FUNCTION: Calculate initial trip estimate
 * This function is called when a trip is created or its route changes.
 * It calculates all cost components based on the initial route data.
 */
export async function calculateTripEstimate(tripId: string, tenantId?: string): Promise<void> {
  const effectiveTenantId = tenantId || getDefaultTenantId();
  logger.info(`Calculating initial estimate for trip ${tripId} in tenant ${effectiveTenantId}`);

  try {
    const tripRef = getTenantCollection(effectiveTenantId, 'trips').doc(tripId);
    const tripDoc = await tripRef.get();

    if (!tripDoc.exists) {
      logger.warn(`Trip ${tripId} not found for estimate calculation.`);
      return;
    }

    const tripData = tripDoc.data()!;

    // Only calculate if the trip is in a pre-active state
    if (['completed', 'cancelled', 'paid', 'inProgress'].includes(tripData.status)) {
      logger.info(`Trip ${tripId} is in status '${tripData.status}', skipping estimate calculation.`);
      return;
    }

    // Get configuration - use embedded if available, otherwise fetch global
    const config = tripData.tripConfiguration || (await getTenantCollection(effectiveTenantId, 'configurations').doc('tripConfiguration').get()).data();

    if (!config) {
      logger.error(`No trip configuration found for trip ${tripId}`);
      return;
    }

    // Get route data
    const routeData = await getRouteDataForTrip(tripId, effectiveTenantId);

    if (!routeData) {
      logger.warn(`No route data found for trip ${tripId}, cannot calculate estimate.`);
      return;
    }

    const distanceKm = (routeData.distanceMeters || 0) / 1000;
    // The duration from the route is in seconds
    const durationHours = (routeData.durationSeconds || 0) / 3600;

    // Calculate all cost components
    const costDistance = calculateDistanceCost(distanceKm, config.costPerKilometer);
    const costDuration = calculateDurationCost(durationHours, config.costPerHour);
    const highestCost = calculateHighestCost(costDistance, costDuration, config.minimumTripCost || 15000);

    // This is the initial estimate, so it becomes the baseline for traffic jam protection
    const estimatedCost = highestCost;
    // costTotal is also set to the initial estimate
    const costTotal = highestCost;

    const updates = {
      estimatedCost,
      costTotal,
      costDistance,
      costDuration,
      costTotalCurrency: 'MGA', // As per memory
      estimatedCostCurrency: 'MGA',
      costDistanceCurrency: 'MGA',
      costDurationCurrency: 'MGA',
      lastCalculationTime: Timestamp.now(),
    };

    await tripRef.update(updates);
    logger.info(`Successfully calculated and set initial estimate for trip ${tripId}:`, updates);
  } catch (error) {
    logger.error(`Error calculating estimate for trip ${tripId}:`, error);
    throw error;
  }
}

/**
 * HTTP function to process individual trip cost calculation
 * Called by Cloud Tasks when there are many active trips
 */
export const processTripCostTask = onRequest({
  region: REGION,
  timeoutSeconds: 60,
  memory: '256MiB'
}, async (request, response) => {
  try {
    const { tripId, tenantId } = request.body;

    if (!tripId || !tenantId) {
      response.status(400).send('Missing tripId or tenantId');
      return;
    }

    await calculateTripProgress(tripId, tenantId);
    response.status(200).send(`Processed trip ${tripId} for tenant ${tenantId}`);
  } catch (error) {
    logger.error('Error processing trip cost task:', error);
    response.status(500).send('Internal server error');
  }
});

/**
 * Calculate trip progress including distance and cost
 * @param tripId - The ID of the trip to calculate
 */
export async function calculateTripProgress(tripId: string, tenantId?: string): Promise<void> {
  const effectiveTenantId = tenantId || getDefaultTenantId();

  try {
    // First, get trip data and logs outside transaction to minimize lock time
    const tripRef = getTenantCollection(effectiveTenantId, 'trips').doc(tripId);
    const tripDoc = await tripRef.get();

    if (!tripDoc.exists) {
      logger.warn(`Trip ${tripId} not found`);
      return;
    }

    const tripData = tripDoc.data()!;
    const currentCursor = tripData.driver?.calculationCursor || null;

    // Query logs after the cursor
    let logsQuery = getTenantCollection(effectiveTenantId, 'trips').doc(tripId).collection('logs')
      .orderBy('ts', 'asc');

    if (currentCursor) {
      // Get the cursor document to find its timestamp
      const cursorDoc = await getTenantCollection(effectiveTenantId, 'trips').doc(tripId).collection('logs').doc(currentCursor).get();

      if (cursorDoc.exists) {
        const cursorTimestamp = cursorDoc.data()!.ts;
        logsQuery = logsQuery.where('ts', '>', cursorTimestamp);
      }
    }

    const logsSnapshot = await logsQuery.limit(100).get();

    if (logsSnapshot.empty) {
      logger.info(`No new logs for trip ${tripId}`);
      return;
    }

    // Calculate incremental distance
    let lastLat = tripData.lastCalculatedLat;
    let lastLon = tripData.lastCalculatedLon;
    let totalDistanceMeters = tripData.distanceTotalMeters || 0;
    let newCursor = currentCursor;
    let processedCount = 0;

    // Get configuration - use embedded if available, otherwise fetch global
    let config;
    if (tripData.tripConfiguration) {
      config = tripData.tripConfiguration;
    } else {
      // Fetch global configuration
      const configDoc = await getTenantCollection(effectiveTenantId, 'configurations')
        .doc('tripConfiguration')
        .get();

      config = configDoc.exists ? configDoc.data()! : {
        costPerKilometer: 4000,
        costPerHour: 25000,
        minimumTripCost: 15000
      };
    }

    // Process logs to calculate distance
    for (const logDoc of logsSnapshot.docs) {
      const logData = logDoc.data();
      const lat = logData.lat;
      const lon = logData.lon;

      // Skip invalid or duplicate points
      if (!isValidCoordinate(lat, lon)) {
        continue;
      }

      // Calculate distance from last point
      if (lastLat !== undefined && lastLon !== undefined) {
        const deltaDistance = calculateHaversineDistance(
          lastLat, lastLon, lat, lon
        );

        // Add all distance regardless of GPS quality (keep raw data)
        totalDistanceMeters += deltaDistance;
      }

      lastLat = lat;
      lastLon = lon;
      newCursor = logDoc.id;
      processedCount++;
    }

    // Calculate duration
    const driverStartTime = tripData.driverStartTime;
    const currentTime = Timestamp.now();
    let durationHours = 0;

    if (driverStartTime) {
      const durationMs = currentTime.toMillis() - driverStartTime.toMillis();
      durationHours = durationMs / (1000 * 60 * 60);
    }

    // Calculate costs using the trip's embedded configuration
    const distanceKm = totalDistanceMeters / 1000;
    const costDistance = calculateDistanceCost(distanceKm, config.costPerKilometer);
    const costDuration = calculateDurationCost(durationHours, config.costPerHour);
    const realCost = calculateHighestCost(
      costDistance,
      costDuration,
      config.minimumTripCost || 15000
    );

    // Get estimated cost for 10% traffic jam protection cap
    // Priority: 1) estimatedCost (new trips), 2) costTotal (existing trips), 3) realCost (fallback)
    const estimatedCost = tripData.estimatedCost || tripData.costTotal || realCost;

    // Apply 10% traffic jam protection cap
    const maxAllowedCost = estimatedCost * 1.10; // 10% above estimate
    const finalCost = tripData.adminOverrideCost || Math.min(realCost, maxAllowedCost);
    const wasCapped = realCost > maxAllowedCost && !tripData.adminOverrideCost;

    // Update trip document using transaction for atomicity
    await db.runTransaction(async (transaction: Transaction) => {
      // Re-read trip to ensure we have latest data
      const currentTripDoc = await transaction.get(tripRef);

      if (!currentTripDoc.exists) {
        throw new Error(`Trip ${tripId} disappeared during calculation`);
      }

      const updates: any = {
        distanceTotalMeters: totalDistanceMeters,
        costDistance: costDistance,
        costDuration: costDuration,
        realCost: realCost,
        realCostCurrency: 'MGA',
        costTotal: finalCost, // This is what users see and pay
        costCappedAt10Percent: wasCapped,
        lastCalculatedLat: lastLat,
        lastCalculatedLon: lastLon,
        lastCalculationTime: currentTime,
        'driver.calculationCursor': newCursor
      };

      // Only set estimatedCost if it doesn't exist (preserve original estimate)
      if (!tripData.estimatedCost) {
        updates.estimatedCost = estimatedCost;
      }

      transaction.update(tripRef, updates);
    });

    logger.info(`Updated trip ${tripId}: processed ${processedCount} logs, ` +
      `distance: ${distanceKm.toFixed(2)}km, realCost: ${realCost} MGA, finalCost: ${finalCost} MGA, capped: ${wasCapped}`);
  } catch (error) {
    logger.error(`Error calculating progress for trip ${tripId}:`, error);
    throw error;
  }
}

/**
 * Calculate distance between two coordinates using Haversine formula
 * @returns Distance in meters
 */
function calculateHaversineDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371000; // Earth's radius in meters
  const φ1 = lat1 * Math.PI / 180;
  const φ2 = lat2 * Math.PI / 180;
  const Δφ = (lat2 - lat1) * Math.PI / 180;
  const Δλ = (lon2 - lon1) * Math.PI / 180;

  const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) *
    Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c;
}

/**
 * Validate coordinate values
 */
function isValidCoordinate(lat: number, lon: number): boolean {
  return lat !== undefined &&
    lon !== undefined &&
    lat >= -90 && lat <= 90 &&
    lon >= -180 && lon <= 180;
}

/**
 * Calculate distance cost (ported from TripConfigurationModel.dart)
 */
function calculateDistanceCost(distanceKm: number, costPerKm: number): number {
  // Round distance up before calculation
  const roundedDistance = Math.ceil(distanceKm);
  return roundedDistance * costPerKm;
}

/**
 * Calculate duration cost (ported from TripConfigurationModel.dart)
 */
function calculateDurationCost(durationHours: number, costPerHour: number): number {
  // Add 1 minute buffer
  const bufferedHours = durationHours + (1 / 60);
  const roundedHours = Math.ceil(bufferedHours);
  return roundedHours * costPerHour;
}

/**
 * Calculate highest cost (ported from TripConfigurationModel.dart)
 */
function calculateHighestCost(
  distanceCost: number,
  durationCost: number,
  minimumCost: number
): number {
  const maxCost = Math.max(distanceCost, durationCost);
  return Math.max(maxCost, minimumCost);
}

/**
 * Trigger immediate calculation when trip completes
 */
export async function calculateFinalTripCost(tripId: string, tenantId?: string): Promise<void> {
  logger.info(`Calculating final cost for trip ${tripId}`);
  const effectiveTenantId = tenantId || getDefaultTenantId();

  try {
    // Process all remaining logs
    await calculateTripProgress(tripId, tenantId);

    // Verify accuracy by recalculating from all logs
    const verificationResult = await verifyTripCalculationAccuracy(tripId, tenantId);

    if (!verificationResult.isAccurate) {
      logger.error(`Trip ${tripId} calculation discrepancy detected!`, {
        accumulated: verificationResult.accumulatedValues,
        recalculated: verificationResult.recalculatedValues,
        differences: verificationResult.differences
      });

      // Update with the accurate recalculated values
      await getTenantCollection(effectiveTenantId, 'trips')
        .doc(tripId)
        .update({
          distanceTotalMeters: verificationResult.recalculatedValues.distanceMeters,
          costDistance: verificationResult.recalculatedValues.costDistance,
          costDuration: verificationResult.recalculatedValues.costDuration,
          realCost: verificationResult.recalculatedValues.realCost,
          costTotal: verificationResult.recalculatedValues.finalCost,
          recalculationPerformed: true,
          recalculationReason: 'Discrepancy detected during final calculation'
        });
    }

    // Build and update final route data from logs
    await updateTripWithFinalRouteData(tripId, effectiveTenantId).catch(error => {
      logger.error(`Failed to build final route data for trip ${tripId}:`, error);
      // Don't throw - we still want to finalize the cost calculation
    });

    // Mark calculation as finalized
    await getTenantCollection(effectiveTenantId, 'trips')
      .doc(tripId)
      .update({
        costCalculationFinalized: true,
        costCalculationFinalizedAt: Timestamp.now()
      });

    logger.info(`Finalized cost calculation for trip ${tripId}`);
  } catch (error) {
    logger.error(`Error finalizing cost for trip ${tripId}:`, error);
    throw error;
  }
}

/**
 * Verify trip calculation accuracy by recalculating from all logs
 * This ensures 100% accuracy by comparing accumulated values with a full recalculation
 */
export async function verifyTripCalculationAccuracy(
  tripId: string,
  tenantId?: string
): Promise<{
  isAccurate: boolean;
  accumulatedValues: any;
  recalculatedValues: any;
  differences: any;
}> {
  const effectiveTenantId = tenantId || getDefaultTenantId();
  logger.info(`Verifying calculation accuracy for trip ${tripId}`);

  try {
    const tripRef = getTenantCollection(effectiveTenantId, 'trips').doc(tripId);
    const tripDoc = await tripRef.get();

    if (!tripDoc.exists) {
      throw new Error(`Trip ${tripId} not found`);
    }

    const tripData = tripDoc.data()!;

    // Store accumulated values
    const accumulatedValues = {
      distanceMeters: tripData.distanceTotalMeters || 0,
      costDistance: tripData.costDistance || 0,
      costDuration: tripData.costDuration || 0,
      realCost: tripData.realCost || 0,
      finalCost: tripData.costTotal || 0
    };

    // Recalculate from ALL logs
    const allLogsSnapshot = await getTenantCollection(effectiveTenantId, 'trips')
      .doc(tripId)
      .collection('logs')
      .orderBy('ts', 'asc')
      .get();

    logger.info(`Recalculating from ${allLogsSnapshot.size} total logs for trip ${tripId}`);

    let recalculatedDistance = 0;
    let prevLat: number | undefined;
    let prevLon: number | undefined;
    let logsProcessed = 0;

    // Process ALL logs from the beginning
    for (const logDoc of allLogsSnapshot.docs) {
      const logData = logDoc.data();
      const lat = logData.lat;
      const lon = logData.lon;

      if (!isValidCoordinate(lat, lon)) {
        continue;
      }

      if (prevLat !== undefined && prevLon !== undefined) {
        const distance = calculateHaversineDistance(prevLat, prevLon, lat, lon);
        recalculatedDistance += distance;
      }

      prevLat = lat;
      prevLon = lon;
      logsProcessed++;
    }

    // Get configuration for cost calculation
    const config = tripData.tripConfiguration ||
      (await getTenantCollection(effectiveTenantId, 'configurations').doc('tripConfiguration').get()).data() ||
    {
      costPerKilometer: 4000,
      costPerHour: 25000,
      minimumTripCost: 15000
    };

    // Calculate duration
    const driverStartTime = tripData.driverStartTime;
    const completedAt = tripData.completedAt || Timestamp.now();
    let durationHours = 0;

    if (driverStartTime) {
      const durationMs = completedAt.toMillis() - driverStartTime.toMillis();
      durationHours = durationMs / (1000 * 60 * 60);
    }

    // Recalculate costs
    const recalculatedDistanceKm = recalculatedDistance / 1000;
    const recalculatedCostDistance = calculateDistanceCost(recalculatedDistanceKm, config.costPerKilometer);
    const recalculatedCostDuration = calculateDurationCost(durationHours, config.costPerHour);
    const recalculatedRealCost = calculateHighestCost(
      recalculatedCostDistance,
      recalculatedCostDuration,
      config.minimumTripCost || 15000
    );

    // Apply traffic jam protection
    const estimatedCost = tripData.estimatedCost || tripData.costTotal || recalculatedRealCost;
    const maxAllowedCost = estimatedCost * 1.10;
    const recalculatedFinalCost = tripData.adminOverrideCost || Math.min(recalculatedRealCost, maxAllowedCost);

    const recalculatedValues = {
      distanceMeters: recalculatedDistance,
      costDistance: recalculatedCostDistance,
      costDuration: recalculatedCostDuration,
      realCost: recalculatedRealCost,
      finalCost: recalculatedFinalCost
    };

    // Calculate differences
    const differences = {
      distanceMeters: Math.abs(accumulatedValues.distanceMeters - recalculatedValues.distanceMeters),
      costDistance: Math.abs(accumulatedValues.costDistance - recalculatedValues.costDistance),
      costDuration: Math.abs(accumulatedValues.costDuration - recalculatedValues.costDuration),
      realCost: Math.abs(accumulatedValues.realCost - recalculatedValues.realCost),
      finalCost: Math.abs(accumulatedValues.finalCost - recalculatedValues.finalCost)
    };

    // Check if values match (allow small floating point differences)
    const isAccurate =
      differences.distanceMeters < 0.01 && // Less than 1cm difference
      differences.costDistance < 1 && // Less than 1 MGA difference
      differences.costDuration < 1 &&
      differences.realCost < 1 &&
      differences.finalCost < 1;

    logger.info(`Verification complete for trip ${tripId}:`, {
      isAccurate,
      logsProcessed,
      accumulated: accumulatedValues,
      recalculated: recalculatedValues,
      differences
    });

    return {
      isAccurate,
      accumulatedValues,
      recalculatedValues,
      differences
    };
  } catch (error) {
    logger.error(`Error verifying calculation accuracy for trip ${tripId}:`, error);
    throw error;
  }
}

/**
 * Admin function to force recalculation from all logs
 * This can be used to fix any accumulated errors
 */
export const recalculateTripFromAllLogs = onCall({
  region: REGION,
  timeoutSeconds: 60,
  memory: '512MiB'
}, async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'Authentication required');
  }

  const { tripId, tenantId } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  // Verify admin permissions
  const adminDoc = await db.collection('admin_users').doc(request.auth.uid).get();
  if (!adminDoc.exists || !adminDoc.data()?.isActive) {
    throw new HttpsError('permission-denied', 'Admin access required');
  }

  try {
    logger.info(`Admin ${request.auth.uid} requested full recalculation for trip ${tripId}`);

    // Get trip data
    const tripRef = getTenantCollection(effectiveTenantId, 'trips').doc(tripId);
    const tripDoc = await tripRef.get();

    if (!tripDoc.exists) {
      throw new HttpsError('not-found', 'Trip not found');
    }

    // Perform verification and get recalculated values
    const verificationResult = await verifyTripCalculationAccuracy(tripId, effectiveTenantId);

    // Update trip with recalculated values
    await tripRef.update({
      distanceTotalMeters: verificationResult.recalculatedValues.distanceMeters,
      costDistance: verificationResult.recalculatedValues.costDistance,
      costDuration: verificationResult.recalculatedValues.costDuration,
      realCost: verificationResult.recalculatedValues.realCost,
      costTotal: verificationResult.recalculatedValues.finalCost,
      lastCalculatedLat: FieldValue.delete(), // Reset cursor-related fields
      lastCalculatedLon: FieldValue.delete(),
      'driver.calculationCursor': FieldValue.delete(),
      recalculationPerformed: true,
      recalculationByAdmin: request.auth.uid,
      recalculationAt: Timestamp.now(),
      lastCalculationTime: Timestamp.now()
    });

    // Also rebuild final route data from logs
    await updateTripWithFinalRouteData(tripId, effectiveTenantId).catch(error => {
      logger.error(`Failed to rebuild final route data for trip ${tripId}:`, error);
      // Don't throw - we still want to return the recalculation results
    });

    logger.info(`Successfully recalculated trip ${tripId} from all logs`);

    return {
      success: true,
      tripId,
      previousValues: verificationResult.accumulatedValues,
      newValues: verificationResult.recalculatedValues,
      differences: verificationResult.differences
    };
  } catch (error) {
    logger.error(`Error in recalculateTripFromAllLogs:`, error);
    throw error;
  }
});

/**
 * Increment and return the cron execution count
 * Used to determine when to check for reservation reminders
 */
async function incrementExecutionCount(): Promise<number> {
  const statsDoc = db.collection('system').doc('cron_stats');

  try {
    const result = await db.runTransaction(async (transaction) => {
      const doc = await transaction.get(statsDoc);
      const currentCount = doc.exists ? (doc.data()?.tripCostCalculatorCount || 0) : 0;
      const newCount = currentCount + 1;

      transaction.set(statsDoc, {
        tripCostCalculatorCount: newCount,
        lastExecution: Timestamp.now()
      }, { merge: true });

      return newCount;
    });

    return result;
  } catch (error) {
    logger.error('Error incrementing execution count:', error);
    return 1; // Default to checking reservations on error
  }
}

/**
 * HTTPS callable function to manually trigger trip cost calculation
 * This is used by the admin panel for monitoring scheduled functions
 * Only accessible by Zedeck user
 */
export const testCalculateTripCosts = onCall({
  region: REGION,
  timeoutSeconds: 540,
  memory: '512MiB'
}, async (request) => {
  // Verify authentication
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'Authentication required');
  }

  // Verify Zedeck user
  if (request.auth.uid !== 'oEay2mlxWgXAYRyKxvuIjL54pII3') {
    throw new HttpsError('permission-denied', 'Access denied');
  }

  const startTime = Date.now();
  const logs: string[] = [];
  const stats = {
    totalActiveTripCount: 0,
    tenantsProcessed: 0,
    tripsProcessed: 0,
    errors: 0,
    executionCount: 0,
    checkedReservations: false,
    monitoredNotifications: false,
    driverTimeoutsChecked: false
  };

  // Custom logger that captures logs
  const captureLogger = {
    info: (message: string, ...args: any[]) => {
      const logMessage = `[INFO] ${message} ${args.length > 0 ? JSON.stringify(args) : ''}`;
      logs.push(logMessage);
      logger.info(message, ...args);
    },
    error: (message: string, ...args: any[]) => {
      const logMessage = `[ERROR] ${message} ${args.length > 0 ? JSON.stringify(args) : ''}`;
      logs.push(logMessage);
      logger.error(message, ...args);
      stats.errors++;
    },
    warn: (message: string, ...args: any[]) => {
      const logMessage = `[WARN] ${message} ${args.length > 0 ? JSON.stringify(args) : ''}`;
      logs.push(logMessage);
      logger.warn(message, ...args);
    }
  };

  try {
    captureLogger.info('Starting manual trip cost calculation run');

    // Track execution count for reservation checking
    const executionCount = await incrementExecutionCount();
    stats.executionCount = executionCount;
    const shouldCheckReservations = executionCount % 3 === 0;
    const shouldMonitorNotifications = executionCount % 180 === 0;
    stats.checkedReservations = shouldCheckReservations;
    stats.monitoredNotifications = shouldMonitorNotifications;

    // Check for driver request timeouts
    captureLogger.info('Checking for driver request timeouts');
    stats.driverTimeoutsChecked = true;
    await checkDriverRequestTimeouts().catch(error => {
      captureLogger.error('Error checking driver request timeouts:', error);
    });

    // Check for reservation reminders
    if (shouldCheckReservations) {
      captureLogger.info('Checking for upcoming reservation reminders');
      await checkUpcomingReservations().catch(error => {
        captureLogger.error('Error checking reservations:', error);
      });
    }

    // Monitor notification reliability
    if (shouldMonitorNotifications) {
      captureLogger.info('Monitoring notification reliability');
      await monitorNotificationReliability().catch(error => {
        captureLogger.error('Error monitoring notifications:', error);
      });
    }

    // Query all active trips across all tenants
    const tenants = await getAllActiveTenants();
    stats.tenantsProcessed = tenants.length;
    const allActiveTrips: { tripId: string; tenantId: string; calculationCursor?: any }[] = [];

    // Gather all active trips from all tenants
    for (const tenantId of tenants) {
      const activeTripsSnapshot = await getTenantCollection(tenantId, 'trips')
        .where('status', '==', 'inProgress')
        .get();

      activeTripsSnapshot.docs.forEach(doc => {
        allActiveTrips.push({
          tripId: doc.id,
          tenantId,
          calculationCursor: doc.data().driver?.calculationCursor || null
        });
      });

      stats.totalActiveTripCount += activeTripsSnapshot.size;
    }

    captureLogger.info(`Found ${stats.totalActiveTripCount} active trips across ${tenants.length} tenants`);

    if (stats.totalActiveTripCount === 0) {
      captureLogger.info('No active trips to process');
    } else if (stats.totalActiveTripCount <= MAX_DIRECT_PROCESSING) {
      // Process directly
      captureLogger.info('Processing trips directly');

      const processPromises = allActiveTrips.map(trip =>
        calculateTripProgress(trip.tripId, trip.tenantId).then(() => {
          stats.tripsProcessed++;
        }).catch((error: any) => {
          captureLogger.error(`Error processing trip ${trip.tripId}:`, error);
        })
      );

      await Promise.all(processPromises);
    } else {
      // For manual testing, we'll process directly instead of using Cloud Tasks
      captureLogger.info(`Processing ${stats.totalActiveTripCount} trips directly (manual mode)`);

      for (const trip of allActiveTrips) {
        try {
          await calculateTripProgress(trip.tripId, trip.tenantId);
          stats.tripsProcessed++;
        } catch (error) {
          captureLogger.error(`Error processing trip ${trip.tripId}:`, error);
        }
      }
    }

    captureLogger.info('Trip cost calculation run completed');

    const executionTime = Date.now() - startTime;

    return {
      success: true,
      executionTime,
      timestamp: new Date().toISOString(),
      stats,
      logs
    };

  } catch (error) {
    captureLogger.error('Error in testCalculateTripCosts:', error);

    return {
      success: false,
      executionTime: Date.now() - startTime,
      timestamp: new Date().toISOString(),
      stats,
      logs,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});