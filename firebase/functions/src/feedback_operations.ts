import { onDocumentCreated, onDocumentUpdated } from "firebase-functions/v2/firestore";
import { onSchedule } from "firebase-functions/v2/scheduler";
import { db } from "./config";
import { sendAdminNotification } from "./chat_notifications";
import { getTenantCollection, getDefaultTenantId } from "./tenant_utils";
import { updateDriverRating } from "./driver_ratings";
import { FieldValue, Timestamp } from "firebase-admin/firestore";

const region = "europe-west3";

/**
 * Type definitions
 */
interface Feedback {
  uid: string;
  type: "trip" | "application";
  tripId?: string;
  rating?: number;
  message: string;
  images: string[];
  createdAt: Timestamp;
  status: "newFeedback" | "seen" | "addressed" | "archived";
  chatSessionId?: string;
}

interface MobileUser {
  displayName?: string;
  fullName?: string;
  email?: string;
  phone?: string;
}

/**
 * Process new feedback submissions
 */
export const onFeedbackCreated = onDocumentCreated(
  {
    document: "tenants/{tenantId}/feedbacks/{feedbackId}",
    region,
  },
  async (event) => {
    const snapshot = event.data;
    if (!snapshot) return;
    const { feedbackId, tenantId } = event.params;
    const feedback = snapshot.data() as Feedback;

    try {
      // Get user information
      const userDoc = await db.collection("mobile_users").doc(feedback.uid).get();
      const userData = userDoc.data() as MobileUser | undefined;
      const userName = userData?.displayName || userData?.fullName || "User";

      // Create notification for admins
      await notifyAdminsOfNewFeedback(feedbackId, feedback, userName, tenantId);

      // If it's a trip feedback with low rating, auto-create a chat session
      if (feedback.type === "trip" && feedback.rating && feedback.rating <= 2) {
        console.log(`Creating auto chat session for low-rated trip feedback: ${feedbackId}`);
        await createAutoChatSession(feedbackId, feedback, userName, tenantId);
      }

      // Update driver rating if it's a trip feedback
      await updateDriverRating(feedback, tenantId);

      // Log the feedback creation
      await logFeedbackEvent(feedbackId, feedback, "created", tenantId);

    } catch (error) {
      console.error("Error in onFeedbackCreated:", error);
    }
  }
);

/**
 * Handle feedback status updates
 */
export const onFeedbackUpdated = onDocumentUpdated(
  {
    document: "tenants/{tenantId}/feedbacks/{feedbackId}",
    region,
  },
  async (event) => {
    const { feedbackId, tenantId } = event.params;
    const change = event.data;
    if (!change) return;
    const before = change.before.data() as Feedback;
    const after = change.after.data() as Feedback;

    try {
      // Check if status changed
      if (before.status !== after.status) {
        await logFeedbackEvent(feedbackId, after, "status_changed", tenantId, {
          oldStatus: before.status,
          newStatus: after.status,
        });
      }

      // Check if chat session was linked
      if (!before.chatSessionId && after.chatSessionId) {
        await logFeedbackEvent(feedbackId, after, "chat_linked", tenantId, {
          chatSessionId: after.chatSessionId,
        });
      }

    } catch (error) {
      console.error("Error in onFeedbackUpdated:", error);
    }
  }
);

/**
 * Create automatic chat session for low-rated feedback
 */
async function createAutoChatSession(
  feedbackId: string,
  feedback: Feedback,
  userName: string,
  tenantId: string
): Promise<void> {
  try {
    const chatData = {
      participantUids: [feedback.uid, "admin"],
      title: `Feedback Follow-up: ${userName}`,
      category: "feedbackFollowup",
      status: "active",
      createdAt: FieldValue.serverTimestamp(),
      lastMessageAt: FieldValue.serverTimestamp(),
      feedbackId: feedbackId,
      isAdminInitiated: true,
    };

    const chatRef = await getTenantCollection(tenantId, "chat_sessions").add(chatData);

    // Add initial system message
    await chatRef.collection("chat_messages").add({
      senderUid: "system",
      senderType: "admin",
      message: `This chat was automatically created for a ${feedback.rating}-star trip feedback. Please follow up with the passenger.`,
      timestamp: FieldValue.serverTimestamp(),
      readBy: {},
      messageType: "system",
    });

    // Update feedback with chat session ID
    await getTenantCollection(tenantId, "feedbacks").doc(feedbackId).update({
      chatSessionId: chatRef.id,
    });

  } catch (error) {
    console.error("Error creating auto chat session:", error);
  }
}

/**
 * Notify admins of new feedback
 */
async function notifyAdminsOfNewFeedback(
  feedbackId: string,
  feedback: Feedback,
  userName: string,
  tenantId: string
): Promise<void> {
  try {
    // Prepare notification details
    const feedbackTypeText = feedback.type === "trip" ? "Trip" : "Application";
    const ratingText = feedback.rating ? ` (${feedback.rating} stars)` : "";
    const messagePreview = feedback.message.substring(0, 100) + (feedback.message.length > 100 ? "..." : "");

    const title = `New ${feedbackTypeText} Feedback${ratingText}`;
    const body = `${userName}: ${messagePreview}`;

    // Send notification using the enhanced notification system
    await sendAdminNotification(
      "feedback_submitted",
      title,
      body,
      {
        type: "feedback_submitted",
        feedbackId: feedbackId,
        senderName: userName
      },
      tenantId
    );

    console.log(`Sent feedback notification to admins for feedback ${feedbackId}`);

  } catch (error) {
    console.error("Error notifying admins:", error);
  }
}

/**
 * Log feedback events for analytics
 */
async function logFeedbackEvent(
  feedbackId: string,
  feedback: Feedback,
  eventType: string,
  tenantId: string,
  metadata?: Record<string, any>
): Promise<void> {
  try {
    await getTenantCollection(tenantId, "feedback_events").add({
      feedbackId,
      feedbackType: feedback.type,
      eventType,
      uid: feedback.uid,
      timestamp: FieldValue.serverTimestamp(),
      metadata: metadata || {},
    });
  } catch (error) {
    console.error("Error logging feedback event:", error);
  }
}

/**
 * Generate feedback statistics (runs daily)
 */
export const generateFeedbackStats = onSchedule(
  {
    schedule: "every 24 hours",
    timeZone: 'UTC',
    region,
  },
  async () => {
    const defaultTenantId = getDefaultTenantId();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    try {
      // Get all feedback from yesterday
      const feedbackSnapshot = await getTenantCollection(defaultTenantId, "feedbacks")
        .where("createdAt", ">=", Timestamp.fromDate(yesterday))
        .where("createdAt", "<", Timestamp.fromDate(today))
        .get();

      // Calculate statistics
      const stats = {
        date: yesterday,
        totalFeedbacks: feedbackSnapshot.size,
        tripFeedbacks: 0,
        applicationFeedbacks: 0,
        averageRating: 0,
        ratingsCount: 0,
        lowRatings: 0, // 1-2 stars
        mediumRatings: 0, // 3 stars
        highRatings: 0, // 4-5 stars
      };

      let totalRating = 0;

      feedbackSnapshot.forEach((doc) => {
        const feedback = doc.data() as Feedback;

        if (feedback.type === "trip") {
          stats.tripFeedbacks++;
          if (feedback.rating) {
            stats.ratingsCount++;
            totalRating += feedback.rating;

            if (feedback.rating <= 2) stats.lowRatings++;
            else if (feedback.rating === 3) stats.mediumRatings++;
            else stats.highRatings++;
          }
        } else {
          stats.applicationFeedbacks++;
        }
      });

      if (stats.ratingsCount > 0) {
        stats.averageRating = totalRating / stats.ratingsCount;
      }

      // Save statistics
      await getTenantCollection(defaultTenantId, "feedback_statistics").doc(yesterday.toISOString().split("T")[0]).set(stats);

      console.log(`Generated feedback statistics for ${yesterday.toISOString().split("T")[0]}:`, stats);

    } catch (error) {
      console.error("Error generating feedback statistics:", error);
    }
  }
);