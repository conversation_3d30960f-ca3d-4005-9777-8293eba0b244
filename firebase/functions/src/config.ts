import * as admin from "firebase-admin";
import { isDevelopment } from "./environment";
import * as path from "path";

// Initialize Firebase Admin with proper credentials
if (isDevelopment()) {
  // In development mode, use service account for proper push notification support
  const serviceAccountPath = path.join(__dirname, "../../fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json");
  try {
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccountPath),
      projectId: "fiaranow",
    });
  } catch (error) {
    // Fallback to default initialization if service account is not available
    console.warn("Service account not found, falling back to default initialization:", error);
    admin.initializeApp();
  }
} else {
  // In production, use default initialization (environment-based credentials)
  admin.initializeApp();
}

export const db = admin.firestore();
export { admin };
