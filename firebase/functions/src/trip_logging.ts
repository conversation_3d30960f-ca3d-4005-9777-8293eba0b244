import { Transaction, FieldValue } from 'firebase-admin/firestore';
import * as logger from 'firebase-functions/logger';
import { TripEvent, TripStatus } from './trip_state_machine';
import { normalizeDataForFirestore } from './utils';

export interface StateTransitionLog {
  transitionId: string;
  previousState: TripStatus;
  newState: TripStatus;
  event: TripEvent;
  userId: string;
  userRole: 'passenger' | 'driver' | 'admin';
  timestamp: any;
  metadata?: Record<string, any>;
  success: boolean;
  errorMessage?: string;
  errorStack?: string;
}

export async function logStateTransition(
  tripId: string,
  previousState: TripStatus,
  newState: TripStatus,
  event: TripEvent,
  userId: string,
  userRole: 'passenger' | 'driver' | 'admin',
  tenantId: string,
  transaction: Transaction,
  error?: Error
): Promise<void> {
  const transitionLog: StateTransitionLog = {
    transitionId: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    previousState,
    newState,
    event: normalizeDataForFirestore(event, 'logStateTransition:event'),
    userId,
    userRole,
    timestamp: FieldValue.serverTimestamp(),
    success: !error,
    metadata: {
      eventType: event.type,
      userAgent: 'firebase-functions',
      functionVersion: process.env.K_REVISION || 'unknown'
    }
  };

  // Add error details if present
  if (error) {
    transitionLog.errorMessage = error.message;
    transitionLog.errorStack = error.stack;
  }

  // Add event-specific metadata
  switch (event.type) {
    case 'REQUEST_DRIVER':
      transitionLog.metadata!.driverUid = event.driverUid;
      transitionLog.metadata!.driverLocation = event.driverLocation;
      break;
    case 'START_TRIP':
      transitionLog.metadata!.startedBy = event.userType;
      break;
    case 'COMPLETE_TRIP':
      if (event.costData) {
        transitionLog.metadata!.totalCost = event.costData.total;
      }
      break;
    case 'CANCEL':
      transitionLog.metadata!.cancelReason = event.reason;
      break;
    // UPDATE_LOCATION removed - position tracking now handled via logs subcollection
  }

  // Store in Firestore subcollection
  const db = require('firebase-admin').firestore();
  const logRef = db.doc(`tenants/${tenantId}/trips/${tripId}/state_transitions/${transitionLog.transitionId}`);
  transaction.set(logRef, transitionLog);

  // Log to Firebase Functions logger for monitoring
  const logLevel = error ? 'error' : 'info';
  logger[logLevel]('Trip state transition', {
    tripId,
    transitionId: transitionLog.transitionId,
    previousState,
    newState,
    event: event.type,
    userId,
    userRole,
    success: transitionLog.success,
    error: error?.message
  });
}

export async function getTransitionHistory(
  tripId: string,
  tenantId: string,
  limit: number = 50
): Promise<StateTransitionLog[]> {
  const db = require('firebase-admin').firestore();
  const snapshot = await db
    .collection(`tenants/${tenantId}/trips/${tripId}/state_transitions`)
    .orderBy('timestamp', 'desc')
    .limit(limit)
    .get();

  return snapshot.docs.map((doc: any) => doc.data() as StateTransitionLog);
}

export async function getTransitionMetrics(
  startDate: Date,
  endDate: Date
): Promise<{
  totalTransitions: number;
  successfulTransitions: number;
  failedTransitions: number;
  transitionsByType: Record<string, number>;
  transitionsByState: Record<string, number>;
  averageTransitionTime: number;
}> {
  // This would be better implemented with BigQuery for production
  // For now, we'll provide a structure for the metrics
  logger.warn('getTransitionMetrics is not fully implemented. Use BigQuery for production metrics.');

  return {
    totalTransitions: 0,
    successfulTransitions: 0,
    failedTransitions: 0,
    transitionsByType: {},
    transitionsByState: {},
    averageTransitionTime: 0
  };
}

export function createTransitionSummary(
  transitions: StateTransitionLog[]
): {
  totalTransitions: number;
  successRate: number;
  mostCommonTransition: string;
  averageTimeInState: Record<TripStatus, number>;
  errorRate: number;
} {
  const total = transitions.length;
  const successful = transitions.filter(t => t.success).length;

  // Count transitions by type
  const transitionCounts: Record<string, number> = {};
  transitions.forEach(t => {
    const key = `${t.previousState}->${t.newState}`;
    transitionCounts[key] = (transitionCounts[key] || 0) + 1;
  });

  const mostCommon = Object.entries(transitionCounts)
    .sort(([, a], [, b]) => b - a)[0]?.[0] || 'none';

  // Calculate time in each state (simplified)
  // Would need timestamps to calculate properly

  return {
    totalTransitions: total,
    successRate: total > 0 ? (successful / total) * 100 : 0,
    mostCommonTransition: mostCommon,
    averageTimeInState: {} as any, // Would need timestamps to calculate
    errorRate: total > 0 ? ((total - successful) / total) * 100 : 0
  };
}