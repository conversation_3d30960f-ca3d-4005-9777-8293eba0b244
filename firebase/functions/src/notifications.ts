import * as admin from "firebase-admin";
import { db } from "./config";
import { sendPushNotification } from "./notification_utils";
import { NotificationType, shouldSendNotification, shouldUseRingtone, buildNotificationPayload } from "./notification_engine";
import { createMobileUserNotification } from "./notification_operations";

/**
 * Type for notification message structure
 */
type NotificationMessageType = {
  [language: string]: {
    driverApproaching: { title: string; body: string };
    driverAwaiting: { title: string; body: string };
    driverMoving: { title: string; body: string };
    tripPaid: { title: string; body: string };
    reservationReminder: { title: string; body: string };
    driverTimeout: { title: string; body: string };
    driverTripRequest: { title: string; body: string };
    driverTripAssigned: { title: string; body: string };
    driverReservedTripAssigned: { title: string; body: string };
    driverReservationReminder: { title: string; body: string };
  };
};

/**
 * Map from notification type to object key
 */
const notificationTypeToKey = {
  approaching: "driverApproaching",
  awaiting: "driverAwaiting",
  driver_moving: "driverMoving",
  driver_arrived: "driverAwaiting", // Maps to same as awaiting
  trip_paid: "tripPaid",
  reservation_reminder: "reservationReminder",
  driver_timeout: "driverTimeout",
} as const;

/**
 * Notification messages in different languages
 */
const notificationMessages: NotificationMessageType = {
  en: {
    driverApproaching: {
      title: "Driver is approaching",
      body: "Your driver is on the way to your location",
    },
    driverAwaiting: {
      title: "Driver has arrived",
      body: "Your driver is waiting for you at the pickup location",
    },
    driverMoving: {
      title: "Driver is on the way",
      body: "Your driver has started heading to pick you up",
    },
    tripPaid: {
      title: "Payment completed",
      body: "Your trip payment has been processed successfully",
    },
    reservationReminder: {
      title: "Upcoming trip reminder",
      body: "Your scheduled trip is coming up soon",
    },
    driverTimeout: {
      title: "Driver unavailable",
      body: "The driver was not available to answer in time. Please select another driver.",
    },
    driverTripRequest: {
      title: "New trip request",
      body: "A passenger is waiting for you",
    },
    driverTripAssigned: {
      title: "Trip Assigned",
      body: "You have been assigned a new trip",
    },
    driverReservedTripAssigned: {
      title: "Reserved Trip Assigned",
      body: "You have been assigned a reserved trip",
    },
    driverReservationReminder: {
      title: "Upcoming pickup reminder",
      body: "Your scheduled pickup is coming soon",
    },
  },
  fr: {
    driverApproaching: {
      title: "Le chauffeur est en route",
      body: "Votre chauffeur est en route vers le point de départ",
    },
    driverAwaiting: {
      title: "Le chauffeur est arrivé",
      body: "Votre chauffeur vous attend au point de départ",
    },
    driverMoving: {
      title: "Le chauffeur est en route",
      body: "Votre chauffeur a commencé à se diriger vers vous",
    },
    tripPaid: {
      title: "Paiement terminé",
      body: "Le paiement de votre trajet a été traité avec succès",
    },
    reservationReminder: {
      title: "Rappel de trajet à venir",
      body: "Votre trajet programmé approche",
    },
    driverTimeout: {
      title: "Chauffeur indisponible",
      body: "Le chauffeur n'était pas disponible pour répondre à temps. Veuillez sélectionner un autre chauffeur.",
    },
    driverTripRequest: {
      title: "Nouvelle demande de trajet",
      body: "Un passager vous attend",
    },
    driverTripAssigned: {
      title: "Trajet assigné",
      body: "Un nouveau trajet vous a été assigné",
    },
    driverReservedTripAssigned: {
      title: "Trajet réservé assigné",
      body: "Un trajet réservé vous a été assigné",
    },
    driverReservationReminder: {
      title: "Rappel de récupération",
      body: "Votre récupération programmée approche",
    },
  },
};

/**
 * Get user's language and locale preferences
 *
 * @param uid - The user's ID
 * @returns Object with language code and full locale info
 */
async function getUserLocale(uid: string): Promise<{ language: string; locale: string; countryCode: string }> {
  try {
    const userDoc = await db.collection("mobile_users").doc(uid).get();
    const userData = userDoc.data();

    if (userData?.primaryLanguage?.languageCode) {
      const languageCode = userData.primaryLanguage.languageCode;
      const countryCode = userData.primaryLanguage.countryCode || (languageCode === "fr" ? "FR" : "US");

      // Only support 'en' and 'fr' for now
      const language = languageCode === "fr" ? "fr" : "en";
      const locale = `${language}-${countryCode}`;

      return { language, locale, countryCode };
    }
  } catch (error) {
    console.error("Error getting user locale:", error);
  }

  // Default to English US
  return { language: "en", locale: "en-US", countryCode: "US" };
}

/**
 * Format time according to user's locale in GMT+3 (Antananarivo/Madagascar timezone)
 *
 * @param date - The date to format
 * @param locale - The locale string (e.g., 'en-US', 'fr-FR')
 * @returns Formatted time string in GMT+3 timezone
 */
function formatTimeForLocale(date: Date, locale: string): string {
  try {
    return new Intl.DateTimeFormat(locale, {
      hour: "2-digit",
      minute: "2-digit",
      hour12: locale.startsWith("en"),
      timeZone: "Indian/Antananarivo", // GMT+3 timezone for Madagascar
    }).format(date);
  } catch (error) {
    console.error("Error formatting time for locale:", error);
    // Fallback to simple format with GMT+3 offset
    const gmtPlus3Date = new Date(date.getTime() + 3 * 60 * 60 * 1000); // Add 3 hours
    const hours = gmtPlus3Date.getUTCHours();
    const minutes = gmtPlus3Date.getUTCMinutes().toString().padStart(2, "0");

    if (locale.startsWith("en")) {
      const period = hours >= 12 ? "PM" : "AM";
      const hours12 = hours % 12 || 12;
      return `${hours12}:${minutes} ${period}`;
    } else {
      return `${hours.toString().padStart(2, "0")}:${minutes}`;
    }
  }
}

export async function sendDriverNotification(
  fcmToken: string,
  tripData?: {
    tripId?: string;
    pickupTime?: Date;
    startLocation?: string;
    destinationLocation?: string;
    driverUid?: string;
  }
) {
  // Get driver's locale if driverUid is provided
  let language = "en";
  let locale = "en-US";
  
  if (tripData?.driverUid) {
    const localeInfo = await getUserLocale(tripData.driverUid);
    language = localeInfo.language;
    locale = localeInfo.locale;
  }

  // Get localized title and body
  let title = notificationMessages[language].driverTripRequest.title;
  let body = notificationMessages[language].driverTripRequest.body;

  // Include pickup time in notification if available
  if (tripData?.pickupTime) {
    const formattedTime = formatTimeForLocale(tripData.pickupTime, locale);
    if (language === "fr") {
      body = `Récupération à ${formattedTime}`;
      if (tripData.destinationLocation) {
        body += ` vers ${tripData.destinationLocation}`;
      }
    } else {
      body = `Pickup at ${formattedTime}`;
      if (tripData.destinationLocation) {
        body += ` to ${tripData.destinationLocation}`;
      }
    }
  } else if (tripData?.destinationLocation) {
    body = language === "fr" ? `Trajet vers ${tripData.destinationLocation}` : `Trip to ${tripData.destinationLocation}`;
  }

  const payload: admin.messaging.Message = {
    token: fcmToken,
    notification: {
      title,
      body,
    },
    data: {
      isDriverNotification: "true",
      notificationType: "driver_trip_request",
      ...(tripData?.tripId && { tripId: tripData.tripId }),
      ...(tripData?.pickupTime && { pickupTime: tripData.pickupTime.toISOString() }),
    },
    android: {
      notification: {
        channelId: "new_trip_notification",
        priority: "max",
        // CRITICAL: DO NOT CHANGE - Special ringtone for driver trip requests
        sound: "phone_ringtone_ultra",
        visibility: "public",
      },
      ttl: 60,
    },
    apns: {
      payload: {
        aps: {
          // CRITICAL: DO NOT CHANGE - Special ringtone for driver trip requests
          sound: "phone_ringtone_ultra.caf",
          category: "INCOMING_CALL",
          contentAvailable: true,
        },
      },
      headers: {
        "apns-priority": "10",
        "apns-push-type": "voip", // VoIP push for maximum priority
      },
    },
  };
  // Driver UID is required - no fallbacks allowed
  if (!tripData?.driverUid) {
    throw new Error(`Driver UID is required for driver notifications. TripData: ${JSON.stringify(tripData)}`);
  }
  
  await sendPushNotification(payload, `driver_notification`, tripData.driverUid);
}

/**
 * Sends a push notification to passengers with ringtone support and notification preferences
 */
export async function sendPassengerNotification(
  fcmToken: string,
  notificationType: NotificationType,
  userId: string,
  data: Record<string, string> = {},
  customBody?: string, // Allow custom body for reservation reminders
  customTime?: Date, // Allow custom time for proper locale formatting
  tenantId?: string // Optional for backward compatibility
) {
  // Use default tenant if not provided
  const effectiveTenantId = tenantId || "fiaranow";

  // Check if notification should be sent based on user preferences
  const shouldSend = await shouldSendNotification(userId, notificationType, effectiveTenantId);
  if (!shouldSend) {
    console.log(`Notification ${notificationType} not sent to user ${userId} due to preferences`);
    return;
  }

  // Get user's locale preferences
  const { language, locale } = await getUserLocale(userId);

  // Map the notification type to the correct key
  const messageKey = notificationTypeToKey[notificationType as keyof typeof notificationTypeToKey];
  if (!messageKey) {
    console.error(`Unknown notification type: ${notificationType}`);
    return;
  }

  // Get localized notification content
  let title = notificationMessages[language][messageKey].title;
  let body = customBody || notificationMessages[language][messageKey].body;

  // Format time in notification if customTime is provided
  if (customTime) {
    const formattedTime = formatTimeForLocale(customTime, locale);
    // Replace time placeholder in body if it exists
    body = body.replace("{time}", formattedTime);
  }

  // Determine if ringtone should be used
  const useRingtone = await shouldUseRingtone(userId, notificationType, effectiveTenantId);

  // Build the notification payload
  const payload = buildNotificationPayload(title, body, data, useRingtone, notificationType);
  // Override the token in the payload
  const messageWithToken: admin.messaging.Message = {
    ...payload,
    token: fcmToken,
  };

  try {
    const fcmResult = await sendPushNotification(messageWithToken, `passenger_${notificationType}_${userId}`, userId);
    console.log(`Sent ${notificationType} notification to user ${userId} with ringtone: ${useRingtone}`);
    
    // Create mobile user notification document for meaningful notifications
    const meaningfulTypes: NotificationType[] = ['driver_moving', 'driver_arrived', 'trip_paid', 'reservation_reminder', 'driver_timeout'];
    if (meaningfulTypes.includes(notificationType)) {
      const notificationId = await createMobileUserNotification(
        effectiveTenantId,
        userId,
        notificationType,
        title,
        body,
        {
          ...data,
          fcmMessageId: (fcmResult as any)?.messageId,
          notificationId: data?.notificationId,
          type: notificationType,
        }
      );
      console.log(`Created mobile user notification ${notificationId} for ${notificationType}`);
    }
  } catch (error) {
    console.error(`Error sending ${notificationType} notification to user ${userId}:`, error);
    throw error;
  }
}

/**
 * Sends a push notification to drivers for trip assignment with pickup time
 */
export async function sendDriverAssignmentNotification(
  fcmToken: string,
  tripId: string,
  pickupTime?: Date,
  destinationLocation?: string,
  isReserved: boolean = false,
  driverUid?: string,
  isReminder: boolean = false
) {
  // Get the driver's locale
  let language = "en";
  let locale = "en-US";
  
  if (driverUid) {
    const localeInfo = await getUserLocale(driverUid);
    language = localeInfo.language;
    locale = localeInfo.locale;
  }

  // Get localized title and body
  let messageKey: keyof NotificationMessageType[string];
  if (isReminder) {
    messageKey = "driverReservationReminder";
  } else {
    messageKey = isReserved ? "driverReservedTripAssigned" : "driverTripAssigned";
  }
  let title = notificationMessages[language][messageKey].title;
  let body = notificationMessages[language][messageKey].body;

  // Add pickup time information if available
  if (pickupTime) {
    const formattedTime = formatTimeForLocale(pickupTime, locale);
    if (language === "fr") {
      body = `Récupération à ${formattedTime}`;
      if (destinationLocation) {
        body += ` vers ${destinationLocation}`;
      }
    } else {
      body = `Pickup at ${formattedTime}`;
      if (destinationLocation) {
        body += ` to ${destinationLocation}`;
      }
    }
  } else if (destinationLocation) {
    body = language === "fr" ? `Trajet vers ${destinationLocation}` : `Trip to ${destinationLocation}`;
  }

  const payload: admin.messaging.Message = {
    token: fcmToken,
    notification: {
      title,
      body,
    },
    data: {
      isDriverNotification: "true",
      notificationType: "driver_trip_assignment",
      tripId,
      ...(pickupTime && { pickupTime: pickupTime.toISOString() }),
      ...(isReserved && { isReservedTrip: "true" }),
    },
    android: {
      notification: {
        channelId: "trip_assignment_notification",
        priority: "high",
        sound: "default",
        visibility: "public",
      },
      ttl: 3600, // 1 hour
    },
    apns: {
      payload: {
        aps: {
          sound: "default",
          category: "TRIP_ASSIGNMENT",
          contentAvailable: true,
        },
      },
      headers: {
        "apns-priority": "10",
        "apns-push-type": "alert",
      },
    },
  };

  // Driver UID is required - no fallbacks allowed
  if (!driverUid) {
    throw new Error(`Driver UID is required for driver assignment notifications. TripId: ${tripId}`);
  }
  
  await sendPushNotification(payload, `driver_assignment_${tripId}`, driverUid);
}
