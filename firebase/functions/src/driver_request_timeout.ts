import { logger } from "firebase-functions/v2";
import { Timestamp, FieldValue, DocumentReference, DocumentSnapshot } from "firebase-admin/firestore";
import { getTenantCollection, getAllActiveTenants } from "./tenant_utils";
import { sendPassengerNotification } from "./notifications";
import { normalizeDataForFirestore } from "./utils";
import tripStateMachine, { TripEvent, TripStatus, canTransition } from "./trip_state_machine";
import { createActor } from 'xstate';
import { db } from "./config";
const DRIVER_REQUEST_TIMEOUT_SECONDS = 60; // 1 minute timeout

/**
 * Check for driver requests that have timed out and handle them
 * This function is called by the existing 20-second scheduled function
 */
export async function checkDriverRequestTimeouts(): Promise<void> {
  try {
    const now = Timestamp.now();
    const timeoutThreshold = new Timestamp(
      now.seconds - DRIVER_REQUEST_TIMEOUT_SECONDS,
      now.nanoseconds
    );

    // Get all active tenants
    const tenants = await getAllActiveTenants();
    let totalTimeouts = 0;

    for (const tenantId of tenants) {
      // Query trips in requestingDriver status that haven't been updated recently
      const timedOutTripsSnapshot = await getTenantCollection(tenantId, 'trips')
        .where('status', '==', 'requestingDriver')
        .where('lastModified', '<=', timeoutThreshold)
        .get();

      for (const tripDoc of timedOutTripsSnapshot.docs) {
        const trip = tripDoc.data();
        const tripId = tripDoc.id;

        try {
          // Use transaction to ensure atomic updates
          await db.runTransaction(async (transaction) => {
            // === PERFORM ALL READS FIRST ===

            // Re-read the trip within transaction to ensure it's still in requestingDriver state
            const freshTripDoc = await transaction.get(tripDoc.ref);
            if (!freshTripDoc.exists) {
              logger.warn(`Trip ${tripId} no longer exists during timeout processing`);
              return;
            }

            const freshTrip = freshTripDoc.data();
            if (freshTrip?.status !== 'requestingDriver') {
              logger.info(`Trip ${tripId} is no longer in requestingDriver status, skipping timeout`);
              return;
            }

            // Check if enough time has passed since last modification
            const lastModified = freshTrip.lastModified?.toDate() || new Date(0);
            const timeSinceModified = (now.toDate().getTime() - lastModified.getTime()) / 1000;

            if (timeSinceModified < DRIVER_REQUEST_TIMEOUT_SECONDS) {
              logger.debug(`Trip ${tripId} not yet timed out (${timeSinceModified}s < ${DRIVER_REQUEST_TIMEOUT_SECONDS}s)`);
              return;
            }

            // Verify state transition is valid
            if (!canTransition('requestingDriver', 'DRIVER_TIMEOUT')) {
              logger.error(`Invalid state transition for timeout: requestingDriver -> preparing`);
              return;
            }

            const driverUid = freshTrip.uidChosenDriver;

            // Read driver document BEFORE any writes
            let driverData: any = null;
            let driverRef: DocumentReference | null = null;
            let shouldReleaseDriver = false;

            if (driverUid) {
              driverRef = db.collection('mobile_users').doc(driverUid);
              const driverDoc = await transaction.get(driverRef) as DocumentSnapshot;

              if (driverDoc.exists) {
                driverData = driverDoc.data();
                if (driverData?.occupiedByTripId === tripId) {
                  shouldReleaseDriver = true;
                }
              }
            }

            // === NOW PERFORM ALL WRITES ===

            // Create state machine actor with current trip state
            const actor = createActor(tripStateMachine, {
              input: {
                tripId,
                uidPassenger: freshTrip.uidPassenger,
                uidChosenDriver: freshTrip.uidChosenDriver,
                status: freshTrip.status as TripStatus,
                driverLocation: freshTrip.driverLocation,
                driverDismissed: freshTrip.driverDismissed,
                passengerDismissed: freshTrip.passengerDismissed,
              }
            });

            actor.start();

            // Send DRIVER_TIMEOUT event to state machine
            const timeoutEvent: TripEvent = {
              type: 'DRIVER_TIMEOUT'
            };

            actor.send(timeoutEvent);
            const snapshot = actor.getSnapshot();
            const newState = snapshot.value as TripStatus;

            // Build updates based on state machine context
            const tripUpdate = normalizeDataForFirestore({
              status: newState,
              uidChosenDriver: null,
              driverLocation: null,
              driverRouteData: null,
              driver: null,
              driverNotificationSent: false,
              lastModified: FieldValue.serverTimestamp(),
              // NOTE: We do NOT add the driver to skippedDriverIds for timeouts
              // This allows the driver to be selected again
              // Add timeout metadata for tracking
              lastTimeoutAt: FieldValue.serverTimestamp(),
              timeoutReason: 'Driver did not respond within 60 seconds'
            }, "driverRequestTimeout:tripUpdate");

            transaction.update(tripDoc.ref, tripUpdate);

            // Release the driver if they were occupied
            if (shouldReleaseDriver && driverRef) {
              const driverUpdate = normalizeDataForFirestore({
                occupiedByTripId: null
              }, "driverRequestTimeout:driverUpdate");

              transaction.update(driverRef, driverUpdate);
            }

            // Log state transition event for audit trail
            const eventLogData = normalizeDataForFirestore({
              uid: 'system', // System-initiated timeout
              type: 'driverRequestTimeout',
              timestamp: FieldValue.serverTimestamp(),
              timestampDT: new Date(),
              driver: driverUid ? {
                uid: driverUid,
                displayName: freshTrip.driver?.displayName || 'Unknown Driver'
              } : null,
              trip: {
                id: tripId,
                startLocationName: freshTrip.startLocationName,
                arrivalLocationName: freshTrip.arrivalLocationName,
                passenger: freshTrip.passenger
              },
              reason: 'Driver request timed out after 60 seconds',
              metadata: {
                timeoutSeconds: DRIVER_REQUEST_TIMEOUT_SECONDS,
                processedBy: 'system-timeout-handler',
                previousStatus: 'requestingDriver',
                newStatus: newState
              }
            }, "driverRequestTimeout:eventLog");

            // Create event log document
            const eventLogRef = getTenantCollection(tenantId, 'event_logs').doc();
            transaction.create(eventLogRef, eventLogData);

            logger.info(`Driver request timeout processed for trip ${tripId}`, {
              tripId,
              driverUid,
              tenantId,
              timeoutSeconds: DRIVER_REQUEST_TIMEOUT_SECONDS,
              newState
            });
          });

          // Send notification to passenger after successful transaction
          await sendTimeoutNotificationToPassenger(trip.uidPassenger, tripId, tenantId);
          totalTimeouts++;

        } catch (error) {
          logger.error(`Error processing timeout for trip ${tripId}:`, error);
        }
      }
    }

    if (totalTimeouts > 0) {
      logger.info(`Processed ${totalTimeouts} driver request timeouts across all tenants`);
    }
  } catch (error) {
    logger.error('Error in checkDriverRequestTimeouts:', error);
  }
}

/**
 * Send a localized notification to the passenger about the driver timeout
 */
async function sendTimeoutNotificationToPassenger(
  passengerUid: string,
  tripId: string,
  tenantId: string
): Promise<void> {
  try {
    // Get passenger data for FCM token
    const passengerDoc = await db.collection('mobile_users').doc(passengerUid).get();
    if (!passengerDoc.exists) {
      logger.warn(`Passenger ${passengerUid} not found for timeout notification`);
      return;
    }

    const passengerData = passengerDoc.data();
    const fcmToken = passengerData?.fcmToken;

    if (!fcmToken) {
      logger.debug(`No FCM token for passenger ${passengerUid}`);
      return;
    }

    // Send notification using the existing passenger notification system
    // Note: The notification system handles localization internally
    await sendPassengerNotification(
      fcmToken,
      'driver_timeout' as any, // Custom notification type
      passengerUid,
      {
        tripId,
        type: 'driver_timeout',
        action: 'select_new_driver'
      },
      undefined, // Let the notification system use the default message
      undefined,
      tenantId
    );

    logger.info(`Timeout notification sent to passenger ${passengerUid} for trip ${tripId}`);
  } catch (error) {
    logger.error(`Error sending timeout notification to passenger ${passengerUid}:`, error);
  }
} 