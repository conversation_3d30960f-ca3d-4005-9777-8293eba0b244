import { setup, assign } from "xstate";
import * as logger from "firebase-functions/logger";
import { FieldValue } from "firebase-admin/firestore";

export interface TripContext {
  tripId: string;
  uidPassenger: string;
  uidChosenDriver?: string;
  status: TripStatus;
  driverLocation?: any;
  userType?: "passenger" | "driver" | "admin";
  cancelledBy?: string;
  cancelReason?: string;
  finalRouteData?: any;
  costData?: any;
  error?: string;
  timestamp?: any;
  driverDismissed?: boolean;
  passengerDismissed?: boolean;
}

export type TripStatus =
  | "preparing"
  | "requestingDriver"
  | "driverApproaching"
  | "driverAwaiting"
  | "inProgress"
  | "completed"
  | "paid"
  | "reserved"
  | "cancelled";

export type TripEvent =
  | { type: "REQUEST_DRIVER"; driverUid: string; driverLocation: any }
  | { type: "DRIVER_ACCEPT" }
  | { type: "DRIVER_REJECT"; driverUid: string; reasonType: string; customReason?: string }
  | { type: "DRIVER_TIMEOUT" }
  | { type: "DRIVER_ARRIVED" }
  | { type: "START_TRIP"; userType: "passenger" | "driver"; tripConfiguration?: any }
  | { type: "COMPLETE_TRIP"; finalRouteData?: any; costData?: any }
  | { type: "CONFIRM_PAYMENT" }
  | { type: "CANCEL"; cancelledBy: string; reason?: string }
  | { type: "DISMISS"; userType: "passenger" | "driver" }
  | { type: "UNDISMISS"; userType: "passenger" | "driver" }
  | { type: "RESERVE" }
  | { type: "ACTIVATE_RESERVATION" };

const tripStateMachine = setup({
  types: {} as {
    context: TripContext;
    events: TripEvent;
    input: Partial<TripContext>;
  },
  actions: {},
}).createMachine({
  id: "tripStateMachine",
  initial: "preparing",
  context: ({ input }) => ({
    tripId: input?.tripId || "",
    uidPassenger: input?.uidPassenger || "",
    status: input?.status || "preparing",
    driverLocation: input?.driverLocation,
    uidChosenDriver: input?.uidChosenDriver,
    driverDismissed: input?.driverDismissed,
    passengerDismissed: input?.passengerDismissed,
    cancelledBy: input?.cancelledBy,
    cancelReason: input?.cancelReason,
    timestamp: FieldValue.serverTimestamp(),
  }),
  states: {
    preparing: {
      entry: ({ context }) => {
        logger.info(`Trip ${context.tripId} entered PREPARING state`, {
          tripId: context.tripId,
          passenger: context.uidPassenger,
          timestamp: new Date().toISOString(),
        });
      },
      on: {
        REQUEST_DRIVER: {
          target: "requestingDriver",
          actions: assign({
            uidChosenDriver: ({ event }) => event.driverUid,
            driverLocation: ({ event }) => event.driverLocation,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
        RESERVE: {
          target: "reserved",
          actions: assign({
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
        CANCEL: {
          target: "cancelled",
          actions: assign({
            cancelledBy: ({ event }) => event.cancelledBy,
            cancelReason: ({ event }) => event.reason,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
      },
    },

    requestingDriver: {
      entry: ({ context }) => {
        logger.info(`Trip ${context.tripId} entered REQUESTING_DRIVER state`, {
          tripId: context.tripId,
          driver: context.uidChosenDriver,
          timestamp: new Date().toISOString(),
        });
      },
      on: {
        DRIVER_ACCEPT: {
          target: "driverApproaching",
          actions: assign({
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
        DRIVER_REJECT: {
          target: "preparing",
          actions: assign({
            uidChosenDriver: () => undefined,
            driverLocation: () => null,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
        DRIVER_TIMEOUT: {
          target: "preparing",
          actions: assign({
            uidChosenDriver: () => undefined,
            driverLocation: () => null,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
        CANCEL: {
          target: "cancelled",
          actions: assign({
            cancelledBy: ({ event }) => event.cancelledBy,
            cancelReason: ({ event }) => event.reason,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
        REQUEST_DRIVER: {
          target: "requestingDriver",
          actions: assign({
            uidChosenDriver: ({ event }) => event.driverUid,
            driverLocation: ({ event }) => event.driverLocation,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
      },
    },

    driverApproaching: {
      entry: ({ context }) => {
        logger.info(`Trip ${context.tripId} entered DRIVER_APPROACHING state`, {
          tripId: context.tripId,
          driver: context.uidChosenDriver,
          timestamp: new Date().toISOString(),
        });
      },
      on: {
        DRIVER_ARRIVED: {
          target: "driverAwaiting",
          actions: assign({
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
        CANCEL: {
          target: "cancelled",
          actions: assign({
            cancelledBy: ({ event }) => event.cancelledBy,
            cancelReason: ({ event }) => event.reason,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
      },
    },

    driverAwaiting: {
      entry: ({ context }) => {
        logger.info(`Trip ${context.tripId} entered DRIVER_AWAITING state`, {
          tripId: context.tripId,
          driver: context.uidChosenDriver,
          timestamp: new Date().toISOString(),
        });
      },
      on: {
        START_TRIP: {
          target: "inProgress",
          actions: assign({
            userType: ({ event }) => event.userType,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
        CANCEL: {
          target: "cancelled",
          actions: assign({
            cancelledBy: ({ event }) => event.cancelledBy,
            cancelReason: ({ event }) => event.reason,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
      },
    },

    inProgress: {
      entry: ({ context }) => {
        logger.info(`Trip ${context.tripId} entered IN_PROGRESS state`, {
          tripId: context.tripId,
          startedBy: context.userType,
          timestamp: new Date().toISOString(),
        });
      },
      on: {
        START_TRIP: {
          target: "inProgress",
          actions: assign({
            userType: ({ event }) => event.userType,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
        COMPLETE_TRIP: {
          target: "completed",
          actions: assign({
            finalRouteData: ({ event }) => event.finalRouteData,
            costData: ({ event }) => event.costData,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
      },
    },

    completed: {
      entry: ({ context }) => {
        logger.info(`Trip ${context.tripId} entered COMPLETED state`, {
          tripId: context.tripId,
          timestamp: new Date().toISOString(),
        });
      },
      on: {
        CONFIRM_PAYMENT: {
          target: "paid",
          actions: assign({
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
        DISMISS: {
          actions: assign({
            userType: ({ event }) => event.userType,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
      },
    },

    paid: {
      entry: ({ context }) => {
        logger.info(`Trip ${context.tripId} entered PAID state`, {
          tripId: context.tripId,
          timestamp: new Date().toISOString(),
        });
      },
      on: {
        DISMISS: {
          actions: assign({
            userType: ({ event }) => event.userType,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
      },
      type: "final",
    },

    reserved: {
      entry: ({ context }) => {
        logger.info(`Trip ${context.tripId} entered RESERVED state`, {
          tripId: context.tripId,
          timestamp: new Date().toISOString(),
        });
      },
      on: {
        REQUEST_DRIVER: {
          target: "requestingDriver",
          actions: assign({
            uidChosenDriver: ({ event }) => event.driverUid,
            driverLocation: ({ event }) => event.driverLocation,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
        ACTIVATE_RESERVATION: {
          target: "preparing",
          actions: assign({
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
        CANCEL: {
          target: "cancelled",
          actions: assign({
            cancelledBy: ({ event }) => event.cancelledBy,
            cancelReason: ({ event }) => event.reason,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
      },
    },

    cancelled: {
      entry: ({ context }) => {
        logger.info(`Trip ${context.tripId} entered CANCELLED state`, {
          tripId: context.tripId,
          cancelledBy: context.cancelledBy,
          reason: context.cancelReason,
          timestamp: new Date().toISOString(),
        });
      },
      on: {
        DISMISS: {
          target: "cancelled", // Explicitly stay in cancelled state
          actions: assign({
            userType: ({ event }) => event.userType,
            driverDismissed: () => true,
            timestamp: () => FieldValue.serverTimestamp(),
          }),
        },
      },
      type: "final",
    },
  },
});

export default tripStateMachine;

export function canTransition(currentState: TripStatus, event: TripEvent["type"]): boolean {
  const validTransitions: Record<TripStatus, string[]> = {
    preparing: ["REQUEST_DRIVER", "RESERVE", "CANCEL"],
    requestingDriver: ["DRIVER_ACCEPT", "DRIVER_REJECT", "DRIVER_TIMEOUT", "CANCEL", "REQUEST_DRIVER"],
    driverApproaching: ["DRIVER_ARRIVED", "CANCEL"],
    driverAwaiting: ["START_TRIP", "CANCEL"],
    inProgress: ["START_TRIP", "COMPLETE_TRIP"],
    completed: ["CONFIRM_PAYMENT", "DISMISS"],
    paid: ["DISMISS"],
    reserved: ["REQUEST_DRIVER", "ACTIVATE_RESERVATION", "CANCEL"],
    cancelled: ["DISMISS"],
  };

  return validTransitions[currentState]?.includes(event) || false;
}
