import { onDocumentWritten } from "firebase-functions/v2/firestore";
import { FieldValue } from "firebase-admin/firestore";
import { logger } from "firebase-functions/v2";
import { db } from "./config";

/**
 * Sync isServiceActive status from tenant state to mobile user document
 * This maintains a denormalized map for efficient querying
 */
export const onTenantStateChangeUpdateUser = onDocumentWritten(
  {
    document: "mobile_users/{uid}/tenant_states/{tenantId}",
    region: "europe-west3",
    timeoutSeconds: 30,
    memory: "256MiB",
  },
  async (event) => {
    const { uid, tenantId } = event.params;
    const change = event.data;

    // Guard against deletions - remove tenant from map
    if (!change || !change.after.exists) {
      logger.info(`Tenant state for ${uid} in ${tenantId} was deleted. Removing from map.`);
      const userRef = db.collection("mobile_users").doc(uid);
      await userRef.update({
        [`isServiceActiveByTenant.${tenantId}`]: FieldValue.delete(),
      });
      return;
    }

    const beforeData = change.before.data();
    const afterData = change.after.data();

    // Check if afterData exists
    if (!afterData) {
      logger.error("No data in after document");
      return;
    }

    const isServiceActive = afterData.isServiceActive;
    const userRef = db.collection("mobile_users").doc(uid);

    // Check if we need to update the denormalized field
    let shouldUpdate = false;
    let reason = "";

    // Check if the isServiceActive value has changed
    if (beforeData?.isServiceActive !== afterData.isServiceActive) {
      shouldUpdate = true;
      reason = "isServiceActive value changed";
    } else {
      // Even if value hasn't changed, check if the denormalized field exists
      try {
        const userDoc = await userRef.get();
        if (userDoc.exists) {
          const userData = userDoc.data();
          const currentDenormalizedValue = userData?.isServiceActiveByTenant?.[tenantId];

          // If the denormalized field doesn't exist or has the wrong value, update it
          if (currentDenormalizedValue === undefined || currentDenormalizedValue !== isServiceActive) {
            shouldUpdate = true;
            reason = `denormalized field missing or incorrect (expected: ${isServiceActive}, found: ${currentDenormalizedValue})`;
          }
        } else {
          // User document doesn't exist, we should create/update it
          shouldUpdate = true;
          reason = "user document doesn't exist";
        }
      } catch (error) {
        logger.error(`Error checking denormalized field for user ${uid}:`, error);
        // On error, proceed with update to be safe
        shouldUpdate = true;
        reason = "error checking denormalized field, updating to be safe";
      }
    }

    if (!shouldUpdate) {
      logger.info("No update needed - denormalized field is already correct.", {
        uid,
        tenantId,
        isServiceActive,
        beforeData,
        afterData,
      });
      return;
    }

    logger.info(`Updating isServiceActiveByTenant for user ${uid}, tenant ${tenantId} to ${isServiceActive}`, {
      reason,
      uid,
      tenantId,
      isServiceActive,
    });

    try {
      await userRef.update({
        [`isServiceActiveByTenant.${tenantId}`]: isServiceActive,
      });
      logger.info("Successfully updated MobileUser document.", {
        uid,
        tenantId,
        isServiceActive,
        reason,
      });
    } catch (error) {
      logger.error(`Failed to update isServiceActiveByTenant for user ${uid}`, error);
    }
  }
);
