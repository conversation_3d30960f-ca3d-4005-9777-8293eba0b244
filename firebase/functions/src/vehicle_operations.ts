import { FieldValue } from "firebase-admin/firestore";
import { onCall, HttpsError } from "firebase-functions/v2/https";
import {
  getTenantCollection,
  ensureTenantId,
  getAdminAccess,
} from "./tenant_utils";
import { db } from "./config";
import {
  sendVehicleApprovalNotification,
  sendVehicleAssignmentNotification
} from "./notification_operations";

const europe = "europe-west3";

// Type for vehicle approval notification data
interface VehicleApprovalNotificationData {
  ownerUID: string;
  vehicleInfo: {
    brand: string;
    model: string;
    registrationNumber: string;
  };
}

// Assignment reasons
export enum AssignmentReason {
  adminAssignment = "adminAssignment",
  driverSwitch = "driverSwitch",
  vehicleMaintenance = "vehicleMaintenance",
  driverUnavailable = "driverUnavailable",
}

// Create vehicle (mobile user or admin initiated)
export const createVehicle = onCall({
  region: europe,
}, async (request) => {
  const { tenantId, vehicleData } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  if (!request.auth) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  if (!vehicleData || !vehicleData.brand || !vehicleData.model ||
    !vehicleData.color || !vehicleData.year || !vehicleData.registrationNumber) {
    throw new HttpsError("invalid-argument", "Missing required vehicle data");
  }

  // Check if user is creating their own vehicle or is an admin
  const isAdmin = await getAdminAccess(request.auth!.uid, effectiveTenantId)
    .then(access => access !== null && access.role >= 1)
    .catch(() => false);

  return db.runTransaction(async (transaction) => {
    const vehiclesRef = db.collection("vehicles");
    const newVehicleRef = vehiclesRef.doc();

    const vehicle: any = {
      brand: vehicleData.brand,
      model: vehicleData.model,
      color: vehicleData.color,
      year: vehicleData.year,
      registrationNumber: vehicleData.registrationNumber.toUpperCase(),
      maxPassengers: vehicleData.maxPassengers || 4,
      isActive: true,
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
      createdBy: request.auth!.uid,
    };

    // If not admin, set as user-owned vehicle
    if (!isAdmin) {
      vehicle.ownerUID = request.auth!.uid;
    }

    transaction.set(newVehicleRef, vehicle);

    // Create a linking for the tenant
    const linkingRef = getTenantCollection(effectiveTenantId, "vehicles_linking").doc();
    const linking = {
      vehicleId: newVehicleRef.id,
      tenantId: effectiveTenantId,
      linkedAt: FieldValue.serverTimestamp(),
      linkedBy: request.auth!.uid,
      isActive: true,
      isOwnedByTenant: isAdmin, // Admin-created = tenant-owned
      tenantApproved: isAdmin, // Auto-approve tenant-owned vehicles
    };
    transaction.set(linkingRef, linking);

    return { success: true, vehicleId: newVehicleRef.id, linkingId: linkingRef.id };
  });
});

// Link vehicle to tenant
export const linkVehicleToTenant = onCall({
  region: europe,
}, async (request) => {
  const { tenantId, vehicleId } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  if (!request.auth) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  // Validate admin has access to this tenant
  const adminAccess = await getAdminAccess(
    request.auth.uid,
    effectiveTenantId
  );

  if (!adminAccess || adminAccess.role < 1) { // Requires ADMIN
    throw new HttpsError("permission-denied", "Insufficient permissions");
  }

  if (!vehicleId) {
    throw new HttpsError("invalid-argument", "Missing vehicle ID");
  }

  return db.runTransaction(async (transaction) => {
    const vehicleRef = db.collection("vehicles").doc(vehicleId);
    const vehicleDoc = await transaction.get(vehicleRef);

    if (!vehicleDoc.exists) {
      throw new HttpsError("not-found", "Vehicle not found");
    }

    const vehicleData = vehicleDoc.data()!;
    const linkingsRef = getTenantCollection(effectiveTenantId, "vehicles_linking");

    // Check if already linked
    const existingQuery = await transaction.get(
      linkingsRef.where("vehicleId", "==", vehicleId).where("isActive", "==", true).limit(1)
    );

    if (!existingQuery.empty) {
      throw new HttpsError("already-exists", "Vehicle already linked to this tenant");
    }

    const newLinkingRef = linkingsRef.doc();
    const linking = {
      vehicleId,
      tenantId: effectiveTenantId,
      linkedAt: FieldValue.serverTimestamp(),
      linkedBy: request.auth!.uid,
      isActive: true,
      isOwnedByTenant: !vehicleData.ownerUID,
      tenantApproved: !vehicleData.ownerUID, // Auto-approve tenant-owned vehicles
    };

    transaction.set(newLinkingRef, linking);

    return { success: true, linkingId: newLinkingRef.id };
  });
});

// Assign vehicle to driver
export const assignVehicleToDriver = onCall({
  region: europe,
}, async (request) => {
  const { tenantId, vehicleId, linkingId, driverUID, reason, notes } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  if (!request.auth) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  // Validate admin has access to this tenant
  const adminAccess = await getAdminAccess(
    request.auth.uid,
    effectiveTenantId
  );

  if (!adminAccess || adminAccess.role < 1) { // Requires ADMIN
    throw new HttpsError("permission-denied", "Insufficient permissions");
  }

  if (!vehicleId || !linkingId || !driverUID) {
    throw new HttpsError("invalid-argument", "Missing required fields");
  }

  // Prepare notification data outside transaction
  let vehicleInfo: { brand: string, model: string, registrationNumber: string } | null = null;

  const result = await db.runTransaction(async (transaction) => {
    // === PERFORM ALL READS FIRST ===

    // Read 1: Verify linking exists and is approved
    const linkingRef = getTenantCollection(effectiveTenantId, "vehicles_linking").doc(linkingId);
    const linkingDoc = await transaction.get(linkingRef);

    if (!linkingDoc.exists) {
      throw new HttpsError("not-found", "Vehicle linking not found");
    }

    const linkingData = linkingDoc.data()!;
    if (!linkingData.tenantApproved) {
      throw new HttpsError("failed-precondition", "Vehicle not approved for use");
    }

    if (linkingData.currentDriverId) {
      throw new HttpsError("failed-precondition", "Vehicle already assigned to another driver");
    }

    // Read 2: Verify driver exists and is active in tenant
    const driverRef = db.collection("mobile_users").doc(driverUID);
    const tenantStateRef = driverRef.collection("tenant_states").doc(effectiveTenantId);

    const [driverDoc, tenantStateDoc] = await transaction.getAll(driverRef, tenantStateRef);

    if (!driverDoc.exists) {
      throw new HttpsError("not-found", "Driver not found");
    }

    // Check if driver already has a vehicle assigned
    const driverTenantState = tenantStateDoc.data();
    if (driverTenantState?.currentVehicleLinkingId) {
      throw new HttpsError("failed-precondition", "Driver already has a vehicle assigned");
    }

    // Read 3: Get vehicle info for notification
    const vehicleDoc = await transaction.get(db.collection("vehicles").doc(vehicleId));
    if (vehicleDoc.exists) {
      const vehicleData = vehicleDoc.data()!;
      vehicleInfo = {
        brand: vehicleData.brand,
        model: vehicleData.model,
        registrationNumber: vehicleData.registrationNumber,
      };
    }

    // Read 4: Check for overlapping assignments
    const overlappingQuery = await transaction.get(
      db.collection("vehicle_assignments")
        .where("vehicleLinkingId", "==", linkingId)
        .where("isActive", "==", true)
        .orderBy("createdAt", "desc")
        .limit(1)
    );

    if (!overlappingQuery.empty) {
      throw new HttpsError("failed-precondition", "Vehicle has an active assignment that must be ended first");
    }

    // === NOW PERFORM ALL MUTATIONS ===

    // Create assignment record
    const assignmentsRef = db.collection("vehicle_assignments");
    const newAssignmentRef = assignmentsRef.doc();

    const assignment = {
      vehicleId,
      vehicleLinkingId: linkingId,
      driverUID,
      tenantId: effectiveTenantId,
      createdAt: FieldValue.serverTimestamp(),
      assignedAt: FieldValue.serverTimestamp(),
      assignedBy: request.auth!.uid,
      reason: reason || AssignmentReason.adminAssignment,
      notes: notes || "",
      isActive: true,
    };

    transaction.set(newAssignmentRef, assignment);

    // Update linking with current driver
    transaction.update(linkingRef, {
      currentDriverId: driverUID,
      updatedAt: FieldValue.serverTimestamp(),
    });

    // Update or create driver tenant state
    const tenantStateData = {
      uid: driverUID,
      tenantId: effectiveTenantId,
      currentVehicleLinkingId: linkingId,
      lastActiveAt: FieldValue.serverTimestamp(),
    };

    if (tenantStateDoc.exists) {
      transaction.update(tenantStateRef, tenantStateData);
    } else {
      transaction.set(tenantStateRef, {
        ...tenantStateData,
        isActive: true,
        driverTags: [],
        joinedAt: FieldValue.serverTimestamp(),
        isServiceActive: true,
      });
    }

    // Update driver's tenantIDs array if needed
    const driverData = driverDoc.data()!;
    const tenantIDs = driverData.tenantIDs || [];
    if (!tenantIDs.includes(effectiveTenantId)) {
      transaction.update(driverRef, {
        tenantIDs: FieldValue.arrayUnion(effectiveTenantId),
      });
    }

    return { success: true, assignmentId: newAssignmentRef.id };
  });

  // Send notification outside transaction
  if (vehicleInfo) {
    await sendVehicleAssignmentNotification(
      driverUID,
      vehicleInfo,
      "assigned"
    );
  }

  return result;
});

// Unassign vehicle from driver
export const unassignVehicleFromDriver = onCall({
  region: europe,
}, async (request) => {
  const { tenantId, vehicleId, linkingId, driverUID, reason, notes } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  if (!request.auth) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  // Validate admin has access to this tenant
  const adminAccess = await getAdminAccess(
    request.auth.uid,
    effectiveTenantId
  );

  if (!adminAccess || adminAccess.role < 1) { // Requires ADMIN
    throw new HttpsError("permission-denied", "Insufficient permissions");
  }

  if (!vehicleId || !linkingId || !driverUID) {
    throw new HttpsError("invalid-argument", "Missing required fields");
  }

  // Prepare notification data outside transaction
  let vehicleInfo: { brand: string, model: string, registrationNumber: string } | null = null;

  const result = await db.runTransaction(async (transaction) => {
    // === PERFORM ALL READS FIRST ===

    // Read 1: Find active assignment
    const assignmentsQuery = await transaction.get(
      db.collection("vehicle_assignments")
        .where("vehicleLinkingId", "==", linkingId)
        .where("driverUID", "==", driverUID)
        .where("isActive", "==", true)
        .limit(1)
    );

    if (assignmentsQuery.empty) {
      throw new HttpsError("not-found", "Active assignment not found");
    }

    const assignmentDoc = assignmentsQuery.docs[0];

    // Read 2: Get vehicle info for notification
    const vehicleDoc = await transaction.get(db.collection("vehicles").doc(vehicleId));
    if (vehicleDoc.exists) {
      const vehicleData = vehicleDoc.data()!;
      vehicleInfo = {
        brand: vehicleData.brand,
        model: vehicleData.model,
        registrationNumber: vehicleData.registrationNumber,
      };
    }

    // Read 3: Check for active trips assigned to this driver
    const activeTripsQuery = await transaction.get(
      getTenantCollection(effectiveTenantId, "trips")
        .where("uidChosenDriver", "==", driverUID)
        .where("status", "in", [
          "requestingDriver",
          "driverApproaching",
          "driverAwaiting",
          "inProgress",
        ])
        .limit(1)
    );

    if (!activeTripsQuery.empty) {
      const activeTrip = activeTripsQuery.docs[0];
      const tripData = activeTrip.data();
      throw new HttpsError(
        "failed-precondition",
        `Cannot unassign vehicle: Driver is currently handling an active trip (${activeTrip.id}) with status '${tripData.status}'. Please complete or cancel the trip before unassigning the vehicle.`
      );
    }

    // === NOW PERFORM ALL MUTATIONS ===

    // Update assignment as inactive
    transaction.update(assignmentDoc.ref, {
      isActive: false,
      unassignedAt: FieldValue.serverTimestamp(),
      unassignedBy: request.auth!.uid,
      unassignReason: reason || "manual_unassignment",
      unassignNotes: notes || "",
    });

    // Update linking to remove current driver
    const linkingRef = getTenantCollection(effectiveTenantId, "vehicles_linking").doc(linkingId);
    transaction.update(linkingRef, {
      currentDriverId: null,
      updatedAt: FieldValue.serverTimestamp(),
    });

    // Update driver tenant state to remove vehicle
    const tenantStateRef = db.collection("mobile_users")
      .doc(driverUID)
      .collection("tenant_states")
      .doc(effectiveTenantId);

    transaction.update(tenantStateRef, {
      currentVehicleLinkingId: null,
      lastActiveAt: FieldValue.serverTimestamp(),
    });

    return { success: true };
  });

  // Send notification outside transaction
  if (vehicleInfo) {
    await sendVehicleAssignmentNotification(
      driverUID,
      vehicleInfo,
      "unassigned"
    );
  }

  return result;
});

// Approve or reject user vehicle
export const approveUserVehicle = onCall({
  region: europe,
}, async (request) => {
  const { tenantId, linkingId, approved, remark } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  if (!request.auth) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  // Validate admin has access to this tenant
  const adminAccess = await getAdminAccess(
    request.auth.uid,
    effectiveTenantId
  );

  if (!adminAccess || adminAccess.role < 1) { // Requires ADMIN
    throw new HttpsError("permission-denied", "Insufficient permissions");
  }

  if (!linkingId || approved === undefined) {
    throw new HttpsError("invalid-argument", "Missing required fields");
  }

  // Use a wrapper object to avoid TypeScript narrowing issues
  const transactionResult: {
    needsNotification: boolean;
    notificationData?: VehicleApprovalNotificationData;
  } = { needsNotification: false };

  await db.runTransaction(async (transaction) => {
    // === PERFORM ALL READS FIRST ===

    // Read 1: Get linking document
    const linkingRef = getTenantCollection(effectiveTenantId, "vehicles_linking").doc(linkingId);
    const linkingDoc = await transaction.get(linkingRef);

    if (!linkingDoc.exists) {
      throw new HttpsError("not-found", "Vehicle linking not found");
    }

    const linkingData = linkingDoc.data()!;

    // Read 2: Get vehicle info for notification if user-owned
    if (!linkingData.isOwnedByTenant && linkingData.vehicleId) {
      const vehicleDoc = await transaction.get(db.collection("vehicles").doc(linkingData.vehicleId));
      if (vehicleDoc.exists) {
        const vehicleData = vehicleDoc.data()!;
        if (vehicleData.ownerUID) {
          // Store notification data for use after transaction
          transactionResult.needsNotification = true;
          transactionResult.notificationData = {
            ownerUID: vehicleData.ownerUID,
            vehicleInfo: {
              brand: vehicleData.brand,
              model: vehicleData.model,
              registrationNumber: vehicleData.registrationNumber,
            }
          };
        }
      }
    }

    // === NOW PERFORM ALL MUTATIONS ===

    const updateData: any = {
      tenantApproved: approved,
      reviewedAt: FieldValue.serverTimestamp(),
      reviewedBy: request.auth!.uid,
      updatedAt: FieldValue.serverTimestamp(),
    };

    if (remark !== undefined) {
      updateData.tenantRemark = remark;
    }

    transaction.update(linkingRef, updateData);
  });

  // Send notification outside transaction
  if (transactionResult.needsNotification && transactionResult.notificationData) {
    await sendVehicleApprovalNotification(
      transactionResult.notificationData.ownerUID,
      transactionResult.notificationData.vehicleInfo,
      approved,
      remark
    );
  }

  return { success: true, linkingId, approved };
});

// Update vehicle status
export const updateVehicleStatus = onCall({
  region: europe,
}, async (request) => {
  const { tenantId, vehicleId, isActive } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  if (!request.auth) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  if (!vehicleId || isActive === undefined) {
    throw new HttpsError("invalid-argument", "Missing required fields");
  }

  return db.runTransaction(async (transaction) => {
    const vehicleRef = db.collection("vehicles").doc(vehicleId);
    const vehicleDoc = await transaction.get(vehicleRef);

    if (!vehicleDoc.exists) {
      throw new HttpsError("not-found", "Vehicle not found");
    }

    const vehicleData = vehicleDoc.data()!;

    // Check permissions
    if (vehicleData.ownerUID) {
      // User-owned vehicle - check if user is owner
      if (request.auth!.uid !== vehicleData.ownerUID) {
        // Not owner, check admin access
        const adminAccess = await getAdminAccess(
          request.auth!.uid,
          effectiveTenantId
        );
        if (!adminAccess || adminAccess.role < 1) {
          throw new HttpsError("permission-denied", "Insufficient permissions");
        }
      }
    } else {
      // Tenant vehicle - require admin access
      const adminAccess = await getAdminAccess(
        request.auth!.uid,
        effectiveTenantId
      );
      if (!adminAccess || adminAccess.role < 1) {
        throw new HttpsError("permission-denied", "Insufficient permissions");
      }
    }

    transaction.update(vehicleRef, {
      isActive,
      updatedAt: FieldValue.serverTimestamp(),
    });

    return { success: true, vehicleId, isActive };
  });
});

// Unlink vehicle from tenant
export const unlinkVehicleFromTenant = onCall({
  region: europe,
}, async (request) => {
  const { tenantId, vehicleId, linkingId } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  if (!request.auth) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  // Validate admin has access to this tenant
  const adminAccess = await getAdminAccess(
    request.auth.uid,
    effectiveTenantId
  );

  if (!adminAccess || adminAccess.role < 1) { // Requires ADMIN
    throw new HttpsError("permission-denied", "Insufficient permissions");
  }

  if (!vehicleId || !linkingId) {
    throw new HttpsError("invalid-argument", "Missing required fields");
  }

  return db.runTransaction(async (transaction) => {
    const linkingRef = getTenantCollection(effectiveTenantId, "vehicles_linking").doc(linkingId);
    const linkingDoc = await transaction.get(linkingRef);

    if (!linkingDoc.exists) {
      throw new HttpsError("not-found", "Vehicle linking not found");
    }

    const linkingData = linkingDoc.data()!;

    // If vehicle is assigned, unassign first
    if (linkingData.currentDriverId) {
      throw new HttpsError(
        "failed-precondition",
        "Vehicle must be unassigned before unlinking"
      );
    }

    // Mark linking as inactive
    transaction.update(linkingRef, {
      isActive: false,
      unlinkedAt: FieldValue.serverTimestamp(),
      unlinkedBy: request.auth!.uid,
    });

    return { success: true };
  });
});