import { FieldValue } from "firebase-admin/firestore";
import { onCall, HttpsError } from "firebase-functions/v2/https";
import { onSchedule } from "firebase-functions/v2/scheduler";
import {
  getTenantCollection,
  ensureTenantId,
  getAdminAccess,
} from "./tenant_utils";
import { db } from "./config";
import { sendNotificationToUser } from "./notification_operations";

const europe = "europe-west3";

// Document types
export enum DocumentType {
  license = "license",
  insurance = "insurance",
  vehicleRegistration = "vehicleRegistration",
  nationalId = "nationalId",
  other = "other",
}

// Document status
export enum DocumentStatus {
  pendingReview = "pendingReview",
  approved = "approved",
  rejected = "rejected",
  expired = "expired",
  expiringSoon = "expiringSoon",
}

// Update document status
export const updateDocumentStatus = onCall({
  region: europe,
}, async (request) => {
  const { tenantId, documentId, driverUID, status, adminNotes } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  if (!request.auth) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  // Validate admin has access to this tenant
  const adminAccess = await getAdminAccess(
    request.auth.uid,
    effectiveTenantId
  );

  if (!adminAccess || adminAccess.role < 1) { // Requires ADMIN
    throw new HttpsError("permission-denied", "Insufficient permissions");
  }

  if (!documentId || !driverUID || !status) {
    throw new HttpsError("invalid-argument", "Missing required fields");
  }

  // Validate status
  if (!Object.values(DocumentStatus).includes(status)) {
    throw new HttpsError("invalid-argument", "Invalid document status");
  }

  return db.runTransaction(async (transaction) => {
    const docRef = db
      .collection("mobile_users")
      .doc(driverUID)
      .collection("driver_documents")
      .doc(documentId);

    const docSnapshot = await transaction.get(docRef);
    if (!docSnapshot.exists) {
      throw new HttpsError("not-found", "Document not found");
    }

    const docData = docSnapshot.data()!;

    // Check if admin has access to this document via tenantIDs
    const tenantIds = docData.tenantIDs || [];
    if (!tenantIds.includes(effectiveTenantId)) {
      throw new HttpsError("permission-denied", "No access to this document");
    }

    // Update document
    const updateData: any = {
      status,
      reviewedAt: FieldValue.serverTimestamp(),
      reviewedBy: request.auth!.uid,
      updatedAt: FieldValue.serverTimestamp(),
    };

    if (adminNotes !== undefined) {
      updateData.adminNotes = adminNotes;
    }

    transaction.update(docRef, updateData);

    // Send notification to driver
    const notificationData = {
      type: "document_status_update",
      documentName: docData.documentName,
      documentType: docData.documentType,
      status,
      adminNotes,
    };

    let notificationMessage = "";
    if (status === DocumentStatus.approved) {
      notificationMessage = `Your ${docData.documentName} has been approved`;
    } else if (status === DocumentStatus.rejected) {
      notificationMessage = `Your ${docData.documentName} has been rejected${adminNotes ? ": " + adminNotes : ""}`;
    }

    if (notificationMessage) {
      await sendNotificationToUser(
        driverUID,
        notificationMessage,
        "Document Review Update",
        notificationData
      );
    }

    return { success: true, documentId, status };
  });
});

// Scheduled function to notify about expiring documents
export const notifyExpiringDocuments = onSchedule({
  schedule: "0 7 * * 1-5", // 10am GMT+3 (7am UTC), Monday-Friday
  timeZone: "Africa/Nairobi",
  region: europe,
}, async (_event) => {
  const now = new Date();
  const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));
  const sevenDaysFromNow = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000));

  // Query all driver documents
  const documentsSnapshot = await db
    .collectionGroup("driver_documents")
    .where("status", "==", DocumentStatus.approved)
    .where("expiryDate", "<=", thirtyDaysFromNow)
    .get();

  const notifications: Promise<any>[] = [];
  const adminNotifications: Map<string, any[]> = new Map();

  for (const doc of documentsSnapshot.docs) {
    const docData = doc.data();
    const expiryDate = docData.expiryDate.toDate();
    const daysUntilExpiry = Math.floor((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    // Check if we should send notification
    let shouldNotify = false;
    let urgency = "low";

    if (daysUntilExpiry <= 1) {
      shouldNotify = true;
      urgency = "high";
    } else if (daysUntilExpiry <= 7 && expiryDate <= sevenDaysFromNow) {
      shouldNotify = true;
      urgency = "medium";
    } else if (daysUntilExpiry <= 30 && expiryDate <= thirtyDaysFromNow) {
      shouldNotify = true;
      urgency = "low";
    }

    if (shouldNotify) {
      // Extract driver UID from document path
      const pathParts = doc.ref.path.split("/");
      const driverUID = pathParts[1]; // mobile_users/{uid}/driver_documents/{docId}

      // Notification to driver
      const driverMessage = daysUntilExpiry <= 0
        ? `Your ${docData.documentName} has expired`
        : `Your ${docData.documentName} expires in ${daysUntilExpiry} day${daysUntilExpiry !== 1 ? "s" : ""}`;

      const notificationData = {
        type: "document_expiry",
        documentId: doc.id,
        documentName: docData.documentName,
        documentType: docData.documentType,
        expiryDate: expiryDate.toISOString(),
        daysUntilExpiry,
        urgency,
      };

      notifications.push(
        sendNotificationToUser(
          driverUID,
          driverMessage,
          "Document Expiry Notice",
          notificationData
        )
      );

      // Collect for admin notifications per tenant
      const tenantIds = docData.tenantIDs || [];
      for (const tid of tenantIds) {
        if (!adminNotifications.has(tid)) {
          adminNotifications.set(tid, []);
        }
        adminNotifications.get(tid)!.push({
          driverUID,
          documentName: docData.documentName,
          daysUntilExpiry,
          urgency,
        });
      }
    }
  }

  // Send admin notifications per tenant
  for (const [tid, docs] of adminNotifications) {
    // Get tenant admins
    const adminsSnapshot = await db
      .collectionGroup("tenants")
      .where("tenantId", "==", tid)
      .where("isActive", "==", true)
      .where("role", ">=", 1) // ADMIN or higher
      .get();

    const highUrgencyCount = docs.filter(d => d.urgency === "high").length;
    const mediumUrgencyCount = docs.filter(d => d.urgency === "medium").length;
    const lowUrgencyCount = docs.filter(d => d.urgency === "low").length;

    let adminMessage = `${docs.length} driver document${docs.length !== 1 ? "s" : ""} expiring soon`;
    if (highUrgencyCount > 0) {
      adminMessage = `URGENT: ${highUrgencyCount} document${highUrgencyCount !== 1 ? "s" : ""} expiring within 24 hours`;
    } else if (mediumUrgencyCount > 0) {
      adminMessage = `${mediumUrgencyCount} document${mediumUrgencyCount !== 1 ? "s" : ""} expiring within 7 days`;
    }

    const adminNotificationData = {
      type: "admin_document_expiry_summary",
      tenantId: tid,
      totalCount: docs.length,
      highUrgencyCount,
      mediumUrgencyCount,
      lowUrgencyCount,
      documents: docs,
    };

    for (const adminDoc of adminsSnapshot.docs) {
      const adminUID = adminDoc.ref.parent.parent?.id;
      if (adminUID) {
        notifications.push(
          sendNotificationToUser(
            adminUID,
            adminMessage,
            "Document Expiry Alert",
            adminNotificationData
          )
        );
      }
    }
  }

  await Promise.all(notifications);

  console.log(`Sent ${notifications.length} expiry notifications`);
});

// Create driver tag
export const createDriverTag = onCall({
  region: europe,
}, async (request) => {
  const { tenantId, name, color, description } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  if (!request.auth) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  // Validate admin has access to this tenant
  const adminAccess = await getAdminAccess(
    request.auth.uid,
    effectiveTenantId
  );

  if (!adminAccess || adminAccess.role < 1) { // Requires ADMIN
    throw new HttpsError("permission-denied", "Insufficient permissions");
  }

  if (!name || !color) {
    throw new HttpsError("invalid-argument", "Missing required fields");
  }

  return db.runTransaction(async (transaction) => {
    const tagsRef = getTenantCollection(effectiveTenantId, "driver_tags");

    // Check if tag already exists
    const existingQuery = await transaction.get(
      tagsRef.where("name", "==", name).limit(1)
    );

    if (!existingQuery.empty) {
      throw new HttpsError("already-exists", "Tag already exists");
    }

    const newTagRef = tagsRef.doc();
    const tagData = {
      name,
      color,
      description: description || "",
      isActive: true,
      createdAt: FieldValue.serverTimestamp(),
      createdBy: request.auth!.uid,
      usageCount: 0,
    };

    transaction.set(newTagRef, tagData);

    return { success: true, tagId: newTagRef.id, ...tagData };
  });
});

// Update driver tag
export const updateDriverTag = onCall({
  region: europe,
}, async (request) => {
  const { tenantId, tagId, updates } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  if (!request.auth) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  // Validate admin has access to this tenant
  const adminAccess = await getAdminAccess(
    request.auth.uid,
    effectiveTenantId
  );

  if (!adminAccess || adminAccess.role < 1) { // Requires ADMIN
    throw new HttpsError("permission-denied", "Insufficient permissions");
  }

  if (!tagId || !updates) {
    throw new HttpsError("invalid-argument", "Missing required fields");
  }

  return db.runTransaction(async (transaction) => {
    const tagRef = getTenantCollection(effectiveTenantId, "driver_tags").doc(tagId);
    const tagDoc = await transaction.get(tagRef);

    if (!tagDoc.exists) {
      throw new HttpsError("not-found", "Tag not found");
    }

    const allowedUpdates: any = {};
    const allowedFields = ["name", "color", "description", "isActive"];

    for (const field of allowedFields) {
      if (field in updates) {
        allowedUpdates[field] = updates[field];
      }
    }

    allowedUpdates.updatedAt = FieldValue.serverTimestamp();

    transaction.update(tagRef, allowedUpdates);

    return { success: true, tagId, updates: allowedUpdates };
  });
});

// Delete driver tag
export const deleteDriverTag = onCall({
  region: europe,
}, async (request) => {
  const { tenantId, tagId } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  if (!request.auth) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  // Validate admin has access to this tenant
  const adminAccess = await getAdminAccess(
    request.auth.uid,
    effectiveTenantId
  );

  if (!adminAccess || adminAccess.role < 1) { // Requires ADMIN
    throw new HttpsError("permission-denied", "Insufficient permissions");
  }

  if (!tagId) {
    throw new HttpsError("invalid-argument", "Missing required fields");
  }

  return db.runTransaction(async (transaction) => {
    const tagRef = getTenantCollection(effectiveTenantId, "driver_tags").doc(tagId);
    const tagDoc = await transaction.get(tagRef);

    if (!tagDoc.exists) {
      throw new HttpsError("not-found", "Tag not found");
    }

    // Instead of deleting, mark as inactive
    transaction.update(tagRef, {
      isActive: false,
      deletedAt: FieldValue.serverTimestamp(),
      deletedBy: request.auth!.uid,
    });

    return { success: true, tagId };
  });
});