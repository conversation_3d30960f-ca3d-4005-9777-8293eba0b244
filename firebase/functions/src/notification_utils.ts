import { admin } from "./config";
import { logger } from "firebase-functions/v2";
import { isProduction } from "./environment";
import { isDevNotificationAllowedForUser } from "./dev_config";

/**
 * Send push notification with user-specific development mode guard
 * 
 * IMPORTANT: UIDs are shared across admin_users and mobile_users collections.
 * The same person can exist in both collections with the same UID:
 * - mobile_users/{uid} - Mobile app access  
 * - admin_users/{uid} - Admin dashboard access
 * 
 * @param message - The FCM message to send
 * @param context - Context string for logging (e.g., "passenger_trip_paid_123")
 * @param userId - REQUIRED: The user's UID (works for both mobile and admin users)
 */
export async function sendPushNotification(
  message: admin.messaging.Message, 
  context: string, 
  userId: string
): Promise<void> {
  const isProd = isProduction();
  const shouldSendNotification = isProd || isDevNotificationAllowedForUser(userId);

  if (!shouldSendNotification) {
    // In dev mode, log notifications for users not in allowed list
    const token = "token" in message ? message.token : null;
    logger.info(`🔔 [DEV MODE - SKIPPED] Push notification skipped for user not in allowed list:`, {
      context,
      userId,
      environment: "development", 
      title: message.notification?.title,
      body: message.notification?.body,
      fcmToken: token ? `${token.substring(0, 20)}...` : "N/A",
      reason: "User not in dev mode allowed list",
    });
    return;
  }

  // Send the actual notification
  const token = "token" in message ? message.token : null;
  const response = await admin.messaging().send(message);

  const environmentEmoji = isProd ? "📱" : "🔔";
  const environmentLabel = isProd ? "PRODUCTION" : "DEV MODE";

  logger.info(`${environmentEmoji} [${environmentLabel}] Push notification sent successfully`, {
    context,
    userId,
    environment: isProd ? "production" : "development",
    to: "token" in message ? "single_device" : "topic",
    title: message.notification?.title,
    fcmToken: token ? `${token.substring(0, 20)}...` : "N/A",
    messageId: response,
  });
}
