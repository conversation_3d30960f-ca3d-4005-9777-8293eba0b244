import { db, admin } from "./config";
import { logger } from "firebase-functions/v2";
import { getTenantCollection } from "./tenant_utils";
import { FieldValue } from "firebase-admin/firestore";

interface Feedback {
  uid: string;
  type: "trip" | "application";
  tripId?: string;
  rating?: number;
}

interface RatingStats {
  totalTrips: number;
  totalRating: number;
  averageRating: number;
  lastRatingDate: admin.firestore.Timestamp;
  distribution: Record<number, number>;
}

/**
 * Updates driver rating statistics in their tenant state
 */
export async function updateDriverRating(
  feedback: Feedback,
  tenantId: string
): Promise<void> {
  if (feedback.type !== "trip" || !feedback.rating || !feedback.tripId) {
    return;
  }

  try {
    // Get trip to find driver
    const tripDoc = await getTenantCollection(tenantId, "trips")
      .doc(feedback.tripId)
      .get();

    if (!tripDoc.exists) {
      logger.warn("Trip not found for feedback", { tripId: feedback.tripId });
      return;
    }

    const trip = tripDoc.data();
    const driverUid = trip?.uidChosenDriver;

    if (!driverUid) {
      logger.warn("No driver found for trip", { tripId: feedback.tripId });
      return;
    }

    // Update driver's tenant state with new rating
    await db.runTransaction(async (transaction) => {
      const tenantStateRef = db
        .collection("mobile_users")
        .doc(driverUid)
        .collection("tenant_states")
        .doc(tenantId);

      const tenantStateDoc = await transaction.get(tenantStateRef);

      let currentStats: RatingStats;

      if (tenantStateDoc.exists && tenantStateDoc.data()?.ratingStats) {
        currentStats = tenantStateDoc.data()!.ratingStats;
      } else {
        // Initialize rating stats
        currentStats = {
          totalTrips: 0,
          totalRating: 0,
          averageRating: 0,
          lastRatingDate: FieldValue.serverTimestamp() as any,
          distribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
        };
      }

      // Update statistics
      const rating = feedback.rating!; // We've already validated this exists
      const newTotalTrips = currentStats.totalTrips + 1;
      const newTotalRating = currentStats.totalRating + rating;
      const newAverageRating = Math.round((newTotalRating / newTotalTrips) * 10) / 10; // 1 decimal place

      // Update distribution
      const newDistribution = { ...currentStats.distribution };
      newDistribution[rating] = (newDistribution[rating] || 0) + 1;

      const updatedStats: RatingStats = {
        totalTrips: newTotalTrips,
        totalRating: newTotalRating,
        averageRating: newAverageRating,
        lastRatingDate: FieldValue.serverTimestamp() as any,
        distribution: newDistribution
      };

      // Update tenant state
      if (tenantStateDoc.exists) {
        transaction.update(tenantStateRef, {
          ratingStats: updatedStats,
          lastActiveAt: FieldValue.serverTimestamp()
        });
      } else {
        // Create tenant state if it doesn't exist
        transaction.set(tenantStateRef, {
          uid: driverUid,
          tenantId: tenantId,
          isActive: true,
          isServiceActive: true,
          driverTags: [],
          joinedAt: FieldValue.serverTimestamp(),
          lastActiveAt: FieldValue.serverTimestamp(),
          ratingStats: updatedStats
        });
      }

      logger.info("Driver rating updated", {
        driverUid,
        tenantId,
        newRating: feedback.rating,
        newAverage: newAverageRating,
        totalTrips: newTotalTrips
      });
    });

  } catch (error) {
    logger.error("Error updating driver rating", { error, feedback });
    throw error;
  }
}

/**
 * Recalculate all ratings for a driver in a tenant (admin function)
 */
export async function recalculateDriverRatings(
  driverUid: string,
  tenantId: string
): Promise<RatingStats> {
  try {
    // Get all trip feedbacks for this driver
    const feedbacksSnapshot = await getTenantCollection(tenantId, "feedbacks")
      .where("type", "==", "trip")
      .get();

    let totalRating = 0;
    let totalTrips = 0;
    const distribution: Record<number, number> = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };

    // Process each feedback
    for (const feedbackDoc of feedbacksSnapshot.docs) {
      const feedback = feedbackDoc.data();

      if (!feedback.tripId || !feedback.rating) continue;

      // Check if this trip belongs to the driver
      const tripDoc = await getTenantCollection(tenantId, "trips")
        .doc(feedback.tripId)
        .get();

      if (tripDoc.exists && tripDoc.data()?.uidChosenDriver === driverUid) {
        totalTrips++;
        totalRating += feedback.rating;
        distribution[feedback.rating]++;
      }
    }

    const averageRating = totalTrips > 0
      ? Math.round((totalRating / totalTrips) * 10) / 10
      : 0;

    const ratingStats: RatingStats = {
      totalTrips,
      totalRating,
      averageRating,
      lastRatingDate: FieldValue.serverTimestamp() as any,
      distribution
    };

    // Update tenant state
    await db
      .collection("mobile_users")
      .doc(driverUid)
      .collection("tenant_states")
      .doc(tenantId)
      .update({ ratingStats });

    logger.info("Driver ratings recalculated", {
      driverUid,
      tenantId,
      totalTrips,
      averageRating
    });

    return ratingStats;

  } catch (error) {
    logger.error("Error recalculating driver ratings", { error, driverUid, tenantId });
    throw error;
  }
} 