import { getRouteData, getMultipleRoutesData } from "./navigation";
import { cleanupStaleDeviceIds } from "./cron";

// Import trip operations
import { cancelTrip, updateSkippedDrivers, onTripDeleted, onTripMutations } from "./trip_operations";

// Import trip cost calculator
import {
  calculateTripCosts,
  processTripCostTask,
  calculateTripEstimate,
  testCalculateTripCosts,
  recalculateTripFromAllLogs,
} from "./trip_cost_calculator";
import {
  adminRequestDriver,
  adminAssignDriver,
  cancelDriverRequest,
  driverAcceptRequest,
  driverRejectRequest,
  disconnectDriverFromTrip,
} from "./driver_operations";

// Import state machine operations
import {
  transitionTripState,
  requestDriverTransition,
  driverArrivedTransition,
  startTripTransition,
  completeTripTransition,
  cancelTripTransition,
  dismissTripTransition,
  adminOverrideTripCost,
} from "./trip_state_operations";

// Import chat and feedback operations
import { onChatMessageCreated, updateUnreadCounts, cleanupArchivedChats } from "./chat_notifications";

import { onFeedbackCreated, onFeedbackUpdated, generateFeedbackStats } from "./feedback_operations";

// Import reservation notifications
import { checkUpcomingReservations, monitorNotificationReliability } from "./reservation_notifications";

// Import driver request timeout
import { checkDriverRequestTimeouts } from "./driver_request_timeout";

// Import document operations
import {
  updateDocumentStatus,
  notifyExpiringDocuments,
  createDriverTag,
  updateDriverTag,
  deleteDriverTag,
} from "./document_operations";

// Import vehicle operations
import {
  createVehicle,
  linkVehicleToTenant,
  assignVehicleToDriver,
  unassignVehicleFromDriver,
  approveUserVehicle,
  updateVehicleStatus,
  unlinkVehicleFromTenant,
} from "./vehicle_operations";

// Import driver availability functions
import { getAvailableDriversForAdmin, checkDriverAvailability } from "./driver_availability_check";

// Import driver ratings functions
import { recalculateDriverRatings } from "./driver_ratings";

// Import tenant state sync function
import { onTenantStateChangeUpdateUser } from "./tenant_state_sync";

// Import Algolia sync functions
import {
  syncTripsToAlgolia,
  syncMobileUsersToAlgolia,
  syncVehiclesToAlgolia,
  syncPaymentsToAlgolia,
  syncChatSessionsToAlgolia,
} from "./algolia_sync";

// Re-export functions
export {
  cleanupStaleDeviceIds,
  // Trip operations
  cancelTrip,
  updateSkippedDrivers,
  onTripDeleted,
  onTripMutations,
  // Driver operations
  adminRequestDriver,
  adminAssignDriver,
  cancelDriverRequest,
  driverAcceptRequest,
  driverRejectRequest,
  disconnectDriverFromTrip,
  // State machine operations
  transitionTripState,
  requestDriverTransition,
  driverArrivedTransition,
  startTripTransition,
  completeTripTransition,
  cancelTripTransition,
  dismissTripTransition,
  adminOverrideTripCost,
  // Trip cost calculator
  calculateTripCosts,
  processTripCostTask,
  calculateTripEstimate,
  testCalculateTripCosts,
  recalculateTripFromAllLogs,
  // Chat and feedback operations
  onChatMessageCreated,
  updateUnreadCounts,
  cleanupArchivedChats,
  onFeedbackCreated,
  onFeedbackUpdated,
  generateFeedbackStats,
  // Reservation notifications
  checkUpcomingReservations,
  monitorNotificationReliability,
  // Driver request timeout
  checkDriverRequestTimeouts,
  // Document operations
  updateDocumentStatus,
  notifyExpiringDocuments,
  createDriverTag,
  updateDriverTag,
  deleteDriverTag,
  // Vehicle operations
  createVehicle,
  linkVehicleToTenant,
  assignVehicleToDriver,
  unassignVehicleFromDriver,
  approveUserVehicle,
  updateVehicleStatus,
  unlinkVehicleFromTenant,
  // Driver availability
  getAvailableDriversForAdmin,
  checkDriverAvailability,
  // Route functions
  getRouteData,
  getMultipleRoutesData,
  // Driver ratings
  recalculateDriverRatings,
  // Tenant state sync
  onTenantStateChangeUpdateUser,
  // Algolia sync
  syncTripsToAlgolia,
  syncMobileUsersToAlgolia,
  syncVehiclesToAlgolia,
  syncPaymentsToAlgolia,
  syncChatSessionsToAlgolia,
};
