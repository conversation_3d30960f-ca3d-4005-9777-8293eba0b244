import { onDocumentCreated, onDocumentUpdated } from "firebase-functions/v2/firestore";
import { onSchedule } from "firebase-functions/v2/scheduler";
import { Timestamp, FieldValue } from "firebase-admin/firestore";
import { db, admin } from "./config";
import { sendPushNotification } from "./notification_utils";
import { getTenantCollection, getDefaultTenantId } from "./tenant_utils";
import { ChatPresence, PRESENCE_ACTIVE_THRESHOLD_SECONDS } from "./types/chat_presence";

const region = "europe-west3";

/**
 * Type definitions
 */
interface ChatMessage {
  senderUid: string;
  senderType: "passenger" | "admin";
  message: string;
  timestamp: Timestamp;
  messageType: "text" | "image" | "system";
  imageUrl?: string;
}

/**
 * Type for localized chat notification messages
 */
type ChatNotificationMessageType = {
  [language: string]: {
    newMessage: { title: string; body: string };
    supportTeam: string;
    imageMessage: string;
  }
};

/**
 * Localized chat notification messages
 */
const chatNotificationMessages: ChatNotificationMessageType = {
  en: {
    newMessage: {
      title: "New Message",
      body: "{senderName}: {messagePreview}"
    },
    supportTeam: "Support Team",
    imageMessage: "📷 Image"
  },
  fr: {
    newMessage: {
      title: "Nouveau message",
      body: "{senderName}: {messagePreview}"
    },
    supportTeam: "Équipe de support",
    imageMessage: "📷 Image"
  }
};

interface ChatSession {
  participantUids: string[];
  title: string;
  status: "active" | "resolved" | "archived";
  lastMessageAt: Timestamp;
  lastMessage?: string;
}

interface NotificationData {
  sessionId?: string;
  messageId?: string;
  senderName?: string;
  messagePreview?: string;
  click_action?: string;
  // New fields for other notification types
  tripId?: string;
  feedbackId?: string;
  passengerId?: string;
  type: "chat_message" | "trip_booking" | "feedback_submitted" | "trip_cancelled" | "reservation_reminder";
  [key: string]: any; // Allow additional fields
}

interface AdminUser {
  uid: string;
  email: string;
  displayName: string;
  isActive: boolean;
  fcmToken?: string;  // Single token instead of array
  fcmTokens?: string[];  // Kept for backward compatibility during migration
}

/**
 * Get user's language and locale preferences
 *
 * @param uid - The user's ID
 * @returns Object with language code and full locale info
 */
async function getUserLocale(uid: string): Promise<{ language: string; locale: string; countryCode: string }> {
  try {
    const userDoc = await db.collection('mobile_users').doc(uid).get();
    const userData = userDoc.data();

    if (userData?.primaryLanguage?.languageCode) {
      const languageCode = userData.primaryLanguage.languageCode;
      const countryCode = userData.primaryLanguage.countryCode || (languageCode === 'fr' ? 'FR' : 'US');

      // Only support 'en' and 'fr' for now
      const language = languageCode === 'fr' ? 'fr' : 'en';
      const locale = `${language}-${countryCode}`;

      return { language, locale, countryCode };
    }
  } catch (error) {
    console.error("Error getting user locale:", error);
  }

  // Default to English US
  return { language: 'en', locale: 'en-US', countryCode: 'US' };
}

/**
 * Get click action URL based on notification type
 */
function getClickActionForType(type: string, data: NotificationData): string {
  switch (type) {
    case "chat_message":
      return `/support/chats/${data.sessionId}`;
    case "trip_booking":
      return `/rides/trips/${data.tripId}/details`;
    case "trip_cancelled":
      return `/rides/trips/${data.tripId}/details`;
    case "feedback_submitted":
      return `/support/feedbacks/${data.feedbackId}`;
    default:
      return "/";
  }
}

/**
 * Check if a user is actively viewing a specific chat session
 */
async function isUserActiveInChat(userId: string, sessionId: string, tenantId: string): Promise<boolean> {
  try {
    const presenceDoc = await getTenantCollection(tenantId, "chat_presence").doc(userId).get();
    
    if (!presenceDoc.exists) {
      return false;
    }
    
    const presence = presenceDoc.data() as ChatPresence;
    
    // Check if user is in the correct session and is active
    if (presence.sessionId !== sessionId || !presence.isActive) {
      return false;
    }
    
    // Check if heartbeat is recent (within threshold)
    const now = Timestamp.now();
    const heartbeatAge = now.seconds - presence.lastHeartbeat.seconds;
    
    return heartbeatAge <= PRESENCE_ACTIVE_THRESHOLD_SECONDS;
  } catch (error) {
    console.error("Error checking user presence:", error);
    // On error, assume user is not active (fail-safe to send notification)
    return false;
  }
}

/**
 * Check if any admin is actively viewing a specific chat session
 */
async function isAnyAdminActiveInChat(sessionId: string, tenantId: string): Promise<boolean> {
  try {
    // Calculate the threshold timestamp for active presence
    const now = Timestamp.now();
    const thresholdTimestamp = new Timestamp(
      now.seconds - PRESENCE_ACTIVE_THRESHOLD_SECONDS,
      now.nanoseconds
    );
    
    // Count active admin presences for this session with recent heartbeat
    const activeAdminsCount = await getTenantCollection(tenantId, "chat_presence")
      .where("userType", "==", "admin")
      .where("sessionId", "==", sessionId)
      .where("isActive", "==", true)
      .where("lastHeartbeat", ">=", thresholdTimestamp)
      .count()
      .get();
    
    // If count > 0, at least one admin is actively viewing
    return activeAdminsCount.data().count > 0;
  } catch (error) {
    console.error("Error checking admin presence:", error);
    // On error, assume no admin is active (fail-safe to send notification)
    return false;
  }
}

/**
 * Send notifications to admin users (both web push and database)
 */
export async function sendAdminNotification(
  notificationType: "chat_message" | "trip_booking" | "feedback_submitted" | "trip_cancelled" | "reservation_reminder",
  title: string,
  body: string,
  data: NotificationData,
  tenantId?: string,
  clickAction?: string
) {
  const effectiveTenantId = tenantId || getDefaultTenantId();
  try {
    // First, create a global admin notification in the database
    const notificationDoc = {
      type: notificationType,
      title,
      message: body,
      createdAt: Timestamp.now(),
      clickAction: clickAction || getClickActionForType(notificationType, data),
      readByAdmins: [],
      ...(data.tripId && { tripId: data.tripId }),
      ...(data.feedbackId && { feedbackId: data.feedbackId }),
      ...(data.sessionId && { chatSessionId: data.sessionId }),
    };

    await getTenantCollection(effectiveTenantId, "admin_notifications").add(notificationDoc);

    // Get all active admin users
    const adminSnapshot = await db
      .collection("admin_users")
      .where("isActive", "==", true)
      .get();

    // Filter admins with access to this tenant
    const tenantAdmins = [];
    for (const adminDoc of adminSnapshot.docs) {
      const tenantAccessDoc = await db
        .doc(`admin_users/${adminDoc.id}/tenants/${effectiveTenantId}`)
        .get();

      if (tenantAccessDoc.exists && tenantAccessDoc.data()?.isActive) {
        tenantAdmins.push(adminDoc);
      }
    }

    const webPushPromises = tenantAdmins.map(async (doc) => {
      const adminUser = doc.data() as AdminUser;
      // Use single fcmToken if available, fallback to first token in array for backward compatibility
      const token = adminUser.fcmToken || (adminUser.fcmTokens && adminUser.fcmTokens[0]);

      if (!token) return;

      // Send notification to admin's single device
      try {
        const webPushMessage: admin.messaging.Message = {
          token,
          notification: {
            title,
            body,
          },
          data: {
            type: notificationType,
            ...(data.sessionId && { chatSessionId: data.sessionId }),
            ...(data.messageId && { messageId: data.messageId }),
            ...(data.tripId && { tripId: data.tripId }),
            ...(data.feedbackId && { feedbackId: data.feedbackId }),
          },
          webpush: {
            notification: {
              icon: "/icon-192.png",
              badge: "/icon-72.png",
              tag: `${notificationType}-${data.feedbackId || data.tripId || data.sessionId || Date.now()}`,
              requireInteraction: true,
              actions: [
                {
                  action: "view",
                  title: "View Chat",
                },
                {
                  action: "close",
                  title: "Close",
                },
              ],
            },
            fcmOptions: {
              link: clickAction || getClickActionForType(notificationType, data),
            },
          },
        };

        await sendPushNotification(webPushMessage, `admin_${notificationType}_${adminUser.uid}`, adminUser.uid);
      } catch (error) {
        console.error(`Error sending web push to admin ${adminUser.uid}:`, error);
        // Remove invalid token
        if ((error as any).code === "messaging/invalid-registration-token" ||
          (error as any).code === "messaging/registration-token-not-registered") {
          await db.collection("admin_users").doc(adminUser.uid).update({
            fcmToken: FieldValue.delete(),
            // Also clean up legacy array if it exists
            fcmTokens: FieldValue.delete(),
          });
        }
      }
    });

    await Promise.all(webPushPromises);
  } catch (error) {
    console.error("Error sending web push to admins:", error);
  }
}

/**
 * Send chat notification when a new message is created
 */
export const onChatMessageCreated = onDocumentCreated(
  {
    document: "tenants/{tenantId}/chat_sessions/{sessionId}/chat_messages/{messageId}",
    region,
  },
  async (event) => {
    const snapshot = event.data;
    if (!snapshot) return;

    const { sessionId, messageId, tenantId } = event.params;
    const message = snapshot.data() as ChatMessage;

    try {
      // Get the chat session
      const sessionDoc = await getTenantCollection(tenantId, "chat_sessions").doc(sessionId).get();
      if (!sessionDoc.exists) {
        console.error(`Chat session ${sessionId} not found`);
        return;
      }

      const session = sessionDoc.data() as ChatSession;

      // Get sender information
      const senderDoc = await db.collection("mobile_users").doc(message.senderUid).get();
      const senderName = senderDoc.exists
        ? senderDoc.data()?.displayName || senderDoc.data()?.fullName || "User"
        : message.senderType === "admin" ? "Support Team" : "User";

      // Determine recipients (everyone except sender)
      const recipientUids = session.participantUids.filter(uid => uid !== message.senderUid);

      // Base notification data (will be enhanced with localized content per recipient)
      const baseNotificationData: NotificationData = {
        sessionId,
        messageId,
        senderName,
        messagePreview: message.messageType === "image" ? "📷 Image" : message.message.substring(0, 100) + (message.message.length > 100 ? "..." : ""),
        type: "chat_message",
      };

      // Send notifications to each recipient
      const notificationPromises = recipientUids.map(async (uid) => {
        // If the recipient is "admin" or if a passenger sent the message (notify admins)
        if (uid === "admin") {
          // Check if any admin is actively viewing this chat
          const anyAdminActive = await isAnyAdminActiveInChat(sessionId, tenantId);
          
          if (anyAdminActive) {
            console.log(`Skipping admin notifications - at least one admin is actively viewing session ${sessionId}`);
            return;
          }
          
          const title = session.title || "New Chat Message";
          const body = `${senderName}: ${baseNotificationData.messagePreview}`;
          await sendAdminNotification("chat_message", title, body, baseNotificationData, tenantId);
          return;
        }

        // For mobile users, check if they're actively viewing the chat
        const userActive = await isUserActiveInChat(uid, sessionId, tenantId);
        
        if (userActive) {
          console.log(`Skipping notification - user ${uid} is actively viewing session ${sessionId}`);
          return;
        }
        
        // For mobile users, send FCM notification
        const userDoc = await db.collection("mobile_users").doc(uid).get();
        if (!userDoc.exists) return;

        const userData = userDoc.data();
        // Handle both fcmToken (singular) and fcmTokens (array) for backward compatibility
        const fcmTokens = userData?.fcmTokens || (userData?.fcmToken ? [userData.fcmToken] : []);

        if (fcmTokens.length === 0) return;

        // Get user's locale for localized messages
        const { language } = await getUserLocale(uid);
        
        // Get localized sender name for admin messages
        const localizedSenderName = message.senderType === "admin" 
          ? chatNotificationMessages[language].supportTeam 
          : senderName;

        // Get localized message preview for images
        const localizedMessagePreview = message.messageType === "image" 
          ? chatNotificationMessages[language].imageMessage 
          : message.message.substring(0, 100) + (message.message.length > 100 ? "..." : "");

        // Build localized notification content
        const messageTitle = session.title || chatNotificationMessages[language].newMessage.title;
        const messageBody = chatNotificationMessages[language].newMessage.body
          .replace("{senderName}", localizedSenderName)
          .replace("{messagePreview}", localizedMessagePreview);

        // Create localized notification data
        const localizedNotificationData: NotificationData = {
          ...baseNotificationData,
          senderName: localizedSenderName,
          messagePreview: localizedMessagePreview,
        };

        // Send notification to all user's devices
        const fcmPromises = fcmTokens.map(async (token: string) => {
          const fcmMessage: admin.messaging.Message = {
            token,
            notification: {
              title: messageTitle,
              body: messageBody,
            },
            data: {
              type: "chat_message",
              sessionId,
              messageId,
              click_action: "FLUTTER_NOTIFICATION_CLICK",
            },
            android: {
              notification: {
                channelId: "silent_notification",
                priority: "high",
                sound: undefined,
                defaultSound: false,
              },
              data: localizedNotificationData as any,
            },
            apns: {
              payload: {
                aps: {
                  alert: {
                    title: messageTitle,
                    body: messageBody,
                  },
                  sound: undefined, // Silent for iOS as well
                  badge: 1,
                },
              },
            },
          };

          try {
            await sendPushNotification(fcmMessage, `chat_message_${uid}`, uid);
          } catch (error) {
            console.error(`Error sending FCM to token ${token}:`, error);
            // Remove invalid tokens
            if ((error as any).code === "messaging/invalid-registration-token" ||
              (error as any).code === "messaging/registration-token-not-registered") {
              // Handle both fcmToken (singular) and fcmTokens (array) for backward compatibility
              if (userData?.fcmTokens) {
                await db.collection("mobile_users").doc(uid).update({
                  fcmTokens: FieldValue.arrayRemove(token),
                });
              } else if (userData?.fcmToken === token) {
                await db.collection("mobile_users").doc(uid).update({
                  fcmToken: FieldValue.delete(),
                });
              }
            }
          }
        });

        await Promise.all(fcmPromises);
      });

      await Promise.all(notificationPromises);

    } catch (error) {
      console.error("Error in onChatMessageCreated:", error);
    }
  }
);

/**
 * Update unread counts when a message is marked as read
 */
export const updateUnreadCounts = onDocumentUpdated(
  {
    document: "tenants/{tenantId}/chat_sessions/{sessionId}/chat_messages/{messageId}",
    region,
  },
  async (event) => {
    const { sessionId, tenantId } = event.params;
    const before = event.data?.before.data();
    const after = event.data?.after.data();

    if (!before || !after) return;

    // Check if readBy field was updated
    const beforeReadBy = before.readBy || {};
    const afterReadBy = after.readBy || {};

    const newReaders = Object.keys(afterReadBy).filter(uid => !beforeReadBy[uid]);

    if (newReaders.length === 0) return;

    try {
      // Get all unread messages for each new reader
      const unreadCountPromises = newReaders.map(async (uid) => {
        const unreadSnapshot = await getTenantCollection(tenantId, "chat_sessions")
          .doc(sessionId)
          .collection("chat_messages")
          .where("senderUid", "!=", uid)
          .get();

        let unreadCount = 0;
        unreadSnapshot.forEach((doc) => {
          const message = doc.data();
          if (!message.readBy || !message.readBy[uid]) {
            unreadCount++;
          }
        });

        // Update the session with unread count for this user
        // This is a simplified approach - in production, you might want to store per-user unread counts
        return { uid, unreadCount };
      });

      await Promise.all(unreadCountPromises);

    } catch (error) {
      console.error("Error updating unread counts:", error);
    }
  }
);

/**
 * Clean up old archived chat sessions (runs daily)
 */
export const cleanupArchivedChats = onSchedule(
  {
    schedule: "every 24 hours",
    timeZone: 'UTC',
    region,
  },
  async () => {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const defaultTenantId = getDefaultTenantId();

    try {
      const archivedChats = await getTenantCollection(defaultTenantId, "chat_sessions")
        .where("status", "==", "archived")
        .where("lastMessageAt", "<", Timestamp.fromDate(thirtyDaysAgo))
        .get();

      const deletePromises = archivedChats.docs.map(async (doc) => {
        // Delete all messages in the session
        const messagesSnapshot = await doc.ref.collection("chat_messages").get();
        const messageDeletePromises = messagesSnapshot.docs.map(msgDoc => msgDoc.ref.delete());
        await Promise.all(messageDeletePromises);

        // Delete the session
        return doc.ref.delete();
      });

      await Promise.all(deletePromises);
      console.log(`Cleaned up ${archivedChats.size} archived chat sessions`);

    } catch (error) {
      console.error("Error cleaning up archived chats:", error);
    }
  }
);

// Note: Stale presence cleanup is now handled by Firestore TTL policy
// Configure TTL on the 'expiresAt' field in the chat_presence collection
// Documents will be automatically deleted after expiration