{"functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["pushd functions && npx tsc && popd"], "runtime": "nodejs22"}], "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "emulators": {"functions": {"port": 5001, "host": "0.0.0.0"}, "firestore": {"port": 8080, "host": "0.0.0.0"}, "auth": {"port": 9099, "host": "0.0.0.0"}, "storage": {"port": 9199, "host": "0.0.0.0"}, "ui": {"enabled": true, "port": 9000, "host": "0.0.0.0"}, "singleProjectMode": true, "hosting": {"port": 5000}, "pubsub": {"port": 8085}}, "hosting": {"public": "./web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}}