{"indexes": [{"collectionGroup": "driver_documents", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "tenantIDs", "arrayConfig": "CONTAINS"}, {"fieldPath": "uploadedAt", "order": "DESCENDING"}]}, {"collectionGroup": "driver_documents", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "expiryDate", "order": "ASCENDING"}]}, {"collectionGroup": "mobile_users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isServiceActiveByTenant.fiaranow", "order": "ASCENDING"}, {"fieldPath": "primaryUserType", "order": "ASCENDING"}, {"fieldPath": "occupiedByTripId", "order": "ASCENDING"}, {"fieldPath": "position.geohash", "order": "ASCENDING"}]}, {"collectionGroup": "mobile_users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lastSeen", "order": "ASCENDING"}, {"fieldPath": "deviceId", "order": "ASCENDING"}]}, {"collectionGroup": "mobile_users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "deviceId", "order": "ASCENDING"}, {"fieldPath": "lastSeen", "order": "ASCENDING"}]}, {"collectionGroup": "payments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tripId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "tenant_states", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}]}, {"collectionGroup": "tenant_states", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "isServiceActive", "order": "ASCENDING"}, {"fieldPath": "ratingStats.averageRating", "order": "DESCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "driverDismissed", "order": "ASCENDING"}, {"fieldPath": "uidChosenDriver", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "passengerDismissed", "order": "ASCENDING"}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "passengerDismissed", "order": "ASCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "passengerDismissed", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "passengerDismissed", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "pickupTime", "order": "ASCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uidChosenDriver", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "vehicle_assignments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "vehicles_linking", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "linkedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chat_sessions", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "participantUids", "arrayConfig": "CONTAINS"}, {"fieldPath": "lastMessageAt", "order": "DESCENDING"}]}, {"collectionGroup": "mobile_users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isServiceActiveByTenant.fiaranow", "order": "ASCENDING"}, {"fieldPath": "primaryUserType", "order": "ASCENDING"}, {"fieldPath": "position.geohash", "order": "ASCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "lastModified", "order": "ASCENDING"}]}, {"collectionGroup": "vehicles", "queryScope": "COLLECTION", "fields": [{"fieldPath": "ownerUID", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "feedbacks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tripId", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}]}, {"collectionGroup": "chat_presence", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userType", "order": "ASCENDING"}, {"fieldPath": "sessionId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "lastHeartbeat", "order": "ASCENDING"}]}, {"collectionGroup": "chat_sessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "lastMessageAt", "order": "ASCENDING"}]}, {"collectionGroup": "admin_users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "role", "order": "ASCENDING"}]}, {"collectionGroup": "mobile_users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "occupiedByTripId", "order": "ASCENDING"}, {"fieldPath": "primaryUserType", "order": "ASCENDING"}, {"fieldPath": "tenantIDs", "arrayConfig": "CONTAINS"}]}, {"collectionGroup": "vehicle_assignments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "ownerUID", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "vehicles_linking", "queryScope": "COLLECTION", "fields": [{"fieldPath": "vehicleId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "vehicle_assignments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "vehicleLinkingId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "vehicle_assignments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "vehicleLinkingId", "order": "ASCENDING"}, {"fieldPath": "driverUID", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "vehicle_assignments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "vehicle_assignments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "vehicleId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "mobile_user_notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "recipientUID", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}