#!/bin/bash

# Setup Firestore scheduled backups using the official method
# IMPORTANT: This script should be run by the USER, not by AI agents!

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
PROJECT_ID="fiaranow"
DATABASE_ID="(default)"
RETENTION_PERIOD="2w"  # 2 weeks retention

echo -e "${BLUE}🚀 Setting up Firestore scheduled backups${NC}"
echo -e "Project: ${PROJECT_ID}"
echo -e "Database: ${DATABASE_ID}"
echo -e "Retention: ${RETENTION_PERIOD}"
echo -e ""

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${RED}❌ Not authenticated with gcloud. Please run: gcloud auth login${NC}"
    exit 1
fi

# Check if gcloud supports backup schedules
echo -e "${YELLOW}📋 Checking gcloud firestore backup support...${NC}"
if ! gcloud firestore backups schedules --help &>/dev/null; then
    echo -e "${RED}❌ Firestore backup schedules not available in your gcloud version${NC}"
    echo -e "${YELLOW}Please ensure gcloud is updated to the latest version${NC}"
    echo -e "Run: gcloud components update"
    echo -e ""
    echo -e "${BLUE}Alternative: Manual backup export${NC}"
    echo -e "You can manually export backups using:"
    echo -e "  gcloud firestore export gs://YOUR_BUCKET/backups/\$(date +%Y%m%d-%H%M%S)"
    exit 1
fi

# Check current backup schedules
echo -e "${YELLOW}📋 Checking existing backup schedules...${NC}"
EXISTING_SCHEDULES=$(gcloud firestore backups schedules list --database="${DATABASE_ID}" 2>/dev/null || echo "")

if [ -n "$EXISTING_SCHEDULES" ]; then
    echo -e "${YELLOW}Existing schedules found:${NC}"
    echo "$EXISTING_SCHEDULES"
    echo -e ""
    read -p "Do you want to continue and create a new schedule? (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Aborting...${NC}"
        exit 0
    fi
fi

# Create daily backup schedule
echo -e "${BLUE}📅 Creating daily backup schedule...${NC}"
if gcloud firestore backups schedules create \
    --database="${DATABASE_ID}" \
    --recurrence=daily \
    --retention="${RETENTION_PERIOD}"; then
    
    echo -e "${GREEN}✅ Daily backup schedule created successfully!${NC}"
    echo -e ""
    echo -e "${BLUE}ℹ️  Backup Information:${NC}"
    echo -e "  • Backups run daily at a Google-managed time"
    echo -e "  • Retention period: ${RETENTION_PERIOD}"
    echo -e "  • Backups are stored in the same location as your database"
    echo -e "  • No performance impact on your database"
    echo -e ""
    echo -e "${YELLOW}💰 Cost Information:${NC}"
    echo -e "  • You'll be charged for backup storage"
    echo -e "  • Restore operations are charged based on backup size"
    echo -e ""
    echo -e "${BLUE}🔧 Useful Commands:${NC}"
    echo -e "  List schedules:  gcloud firestore backups schedules list --database='${DATABASE_ID}'"
    echo -e "  List backups:    gcloud firestore backups list --database='${DATABASE_ID}'"
    echo -e "  Delete schedule: gcloud firestore backups schedules delete SCHEDULE_ID --database='${DATABASE_ID}'"
else
    echo -e "${RED}❌ Failed to create backup schedule${NC}"
    echo -e ""
    echo -e "${YELLOW}Alternative: Manual backup approach${NC}"
    echo -e "You can create a Cloud Scheduler job to trigger backups:"
    echo -e "1. Create a Cloud Storage bucket for backups"
    echo -e "2. Use Cloud Scheduler to run daily exports:"
    echo -e "   gcloud firestore export gs://YOUR_BUCKET/backups/\$(date +%Y%m%d-%H%M%S)"
    exit 1
fi