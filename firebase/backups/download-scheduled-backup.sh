#!/bin/bash

# Download Firestore scheduled backup for local development
# IMPORTANT: This script should be run by the USER, not by AI agents!

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
PROJECT_ID="fiaranow"
DATABASE_ID="(default)"
LOCATION="europe-west3"
TEMP_BUCKET="fiaranow-temp-backup-download"
LOCAL_BACKUP_DIR="$(dirname "$0")/../emulators_data"

echo -e "${BLUE}🔄 Downloading Firestore scheduled backup for local development${NC}"
echo -e "Project: ${PROJECT_ID}"
echo -e "Database: ${DATABASE_ID}"
echo -e "Location: ${LOCATION}"
echo -e ""

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${RED}❌ Not authenticated with gcloud. Please run: gcloud auth login${NC}"
    exit 1
fi

# Check if emulators_data directory exists and prompt user to delete it
if [ -d "$LOCAL_BACKUP_DIR" ]; then
    echo -e "${YELLOW}⚠️  The emulators_data directory already exists: ${LOCAL_BACKUP_DIR}${NC}"
    echo -e "${YELLOW}This directory needs to be deleted before downloading a new backup.${NC}"
    echo -e ""
    read -p "Do you want to delete the existing emulators_data directory? [y/N]: " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}🗑️  Deleting existing emulators_data directory...${NC}"
        rm -rf "$LOCAL_BACKUP_DIR"
        echo -e "${GREEN}✅ Directory deleted${NC}"
    else
        echo -e "${RED}❌ Cannot proceed without deleting the existing directory${NC}"
        exit 1
    fi
fi

# List available backups
echo -e "${YELLOW}📋 Fetching available scheduled backups...${NC}"
BACKUPS_JSON=$(gcloud firestore backups list --location="${LOCATION}" --format=json 2>/dev/null || echo "[]")

if [ "$BACKUPS_JSON" = "[]" ]; then
    echo -e "${RED}❌ No scheduled backups found in location ${LOCATION}${NC}"
    echo -e "${YELLOW}Possible reasons:${NC}"
    echo -e "  • No backup schedule has been created yet"
    echo -e "  • The first backup hasn't run yet (wait up to 24 hours)"
    echo -e "  • Backups are in a different location"
    echo -e ""
    echo -e "${BLUE}To check backup schedules, run:${NC}"
    echo -e "  gcloud firestore backups schedules list --database='${DATABASE_ID}'"
    echo -e ""
    echo -e "${BLUE}To create a backup schedule, run:${NC}"
    echo -e "  gcloud firestore backups schedules create --database='${DATABASE_ID}' --recurrence=daily --retention=P14D"
    exit 1
fi

# Display available backups
echo -e "${GREEN}Available scheduled backups:${NC}"
echo "$BACKUPS_JSON" | jq -r '.[] | "\(.name | split("/") | last) - Created: \(.snapshotTime) - State: \(.state)"'
echo -e ""

# Get the latest backup name
LATEST_BACKUP=$(echo "$BACKUPS_JSON" | jq -r 'sort_by(.snapshotTime) | reverse | .[0].name' 2>/dev/null)

if [ -z "$LATEST_BACKUP" ] || [ "$LATEST_BACKUP" = "null" ]; then
    echo -e "${RED}❌ Could not determine latest backup${NC}"
    exit 1
fi

BACKUP_ID=$(echo "$LATEST_BACKUP" | awk -F'/' '{print $NF}')
echo -e "${GREEN}✓ Using latest backup: ${BACKUP_ID}${NC}"

# Generate unique temporary database name
TEMP_DB_NAME="temp-restore-$(date +%s)"
echo -e "${BLUE}📥 Creating temporary database: ${TEMP_DB_NAME}${NC}"

# Restore the backup to temporary database
echo -e "${BLUE}🔄 Restoring backup to temporary database...${NC}"
echo -e "${YELLOW}This may take several minutes...${NC}"

if ! gcloud firestore databases restore \
    --source-backup="${LATEST_BACKUP}" \
    --destination-database="${TEMP_DB_NAME}" \
    --quiet; then
    echo -e "${RED}❌ Failed to restore backup${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Backup restored to temporary database${NC}"

# Cleanup function
cleanup() {
    echo -e "${YELLOW}🧹 Cleaning up temporary resources...${NC}"
    
    # Delete temporary database
    if gcloud firestore databases describe "${TEMP_DB_NAME}" &>/dev/null; then
        echo -e "Deleting temporary database: ${TEMP_DB_NAME}"
        gcloud firestore databases delete "${TEMP_DB_NAME}" --quiet || true
    fi
    
    # Delete temporary bucket
    if gsutil ls -b "gs://${TEMP_BUCKET}" &>/dev/null; then
        echo -e "Deleting temporary bucket: gs://${TEMP_BUCKET}"
        gsutil -m rm -r "gs://${TEMP_BUCKET}" || true
    fi
}

# Set up cleanup trap
trap cleanup EXIT

# Create temporary bucket for export
echo -e "${BLUE}🗂️  Creating temporary bucket for export...${NC}"
gsutil mb -l "${LOCATION}" "gs://${TEMP_BUCKET}" || true

# Export from temporary database to bucket
EXPORT_PATH="gs://${TEMP_BUCKET}/export-$(date +%s)"
echo -e "${BLUE}📤 Exporting temporary database to Cloud Storage...${NC}"
echo -e "${YELLOW}This may take several minutes...${NC}"

if ! gcloud firestore export "${EXPORT_PATH}" --database="${TEMP_DB_NAME}" --quiet; then
    echo -e "${RED}❌ Failed to export database${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Database exported to Cloud Storage${NC}"

# Create temporary directory for download
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR; cleanup" EXIT

# Download the export from Cloud Storage
echo -e "${BLUE}📥 Downloading export from Cloud Storage...${NC}"
if ! gsutil -m cp -r "${EXPORT_PATH}/*" "$TEMP_DIR/"; then
    echo -e "${RED}❌ Failed to download export${NC}"
    exit 1
fi

# Find the export data
EXPORT_DATA_DIR=$(find "$TEMP_DIR" -name "all_namespaces" -type d | head -n 1)
if [ -z "$EXPORT_DATA_DIR" ]; then
    echo -e "${RED}❌ Could not find export data (all_namespaces directory)${NC}"
    echo -e "${YELLOW}Downloaded files:${NC}"
    find "$TEMP_DIR" -type f | head -20
    exit 1
fi

EXPORT_BASE_DIR=$(dirname "$EXPORT_DATA_DIR")
echo -e "${GREEN}✓ Found export data at: ${EXPORT_BASE_DIR}${NC}"

# Prepare emulator data directory
echo -e "${BLUE}🔧 Preparing emulator data directory...${NC}"
mkdir -p "$LOCAL_BACKUP_DIR/firestore_export"

# Copy the export data
cp -r "$EXPORT_DATA_DIR" "$LOCAL_BACKUP_DIR/firestore_export/"

# Find and copy the metadata file
METADATA_FILE=$(find "$EXPORT_BASE_DIR" -name "*.overall_export_metadata" -type f | head -n 1)
if [ -n "$METADATA_FILE" ]; then
    cp "$METADATA_FILE" "$LOCAL_BACKUP_DIR/firestore_export/firestore_export.overall_export_metadata"
    echo -e "${GREEN}✓ Copied metadata file${NC}"
else
    echo -e "${YELLOW}⚠️  No metadata file found (this might be okay)${NC}"
fi

# Create metadata file for emulator
cat > "$LOCAL_BACKUP_DIR/firebase-export-metadata.json" << EOF
{
  "version": "13.0.0",
  "firestore": {
    "version": "1.19.0",
    "path": "firestore_export",
    "metadata_file": "firestore_export/firestore_export.overall_export_metadata"
  }
}
EOF

# Create a marker file to indicate this is production data
echo "${BACKUP_ID}" > "$LOCAL_BACKUP_DIR/.production-backup"

echo -e "${GREEN}✅ Scheduled backup downloaded and prepared for emulator!${NC}"
echo -e "Location: ${LOCAL_BACKUP_DIR}"
echo -e "Backup ID: ${BACKUP_ID}"
echo -e ""
echo -e "${YELLOW}⚠️  This is production data - will be loaded in READ-ONLY mode${NC}"
echo -e ""
echo -e "${BLUE}🚀 To use this backup, run:${NC}"
echo -e "  ./dev-start.sh"
echo -e ""
echo -e "The script will detect the production backup and ask if you want to use it."
echo -e ""
echo -e "${BLUE}📁 Backup location: ${LOCAL_BACKUP_DIR}${NC}"