#!/bin/bash

# Download manual Firestore backup for local development
# IMPORTANT: This script should be run by the USER, not by AI agents!

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
PROJECT_ID="fiaranow"
DATABASE_ID="(default)"
BUCKET_NAME="fiaranow-backups"
LOCAL_BACKUP_DIR="$(dirname "$0")/../emulators_data"

echo -e "${BLUE}🔄 Downloading manual Firestore backup for local development${NC}"
echo -e "Project: ${PROJECT_ID}"
echo -e "Bucket: ${BUCKET_NAME}"
echo -e ""

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${RED}❌ Not authenticated with gcloud. Please run: gcloud auth login${NC}"
    exit 1
fi

# Check if emulators_data directory exists and prompt user to delete it
if [ -d "$LOCAL_BACKUP_DIR" ]; then
    echo -e "${YELLOW}⚠️  The emulators_data directory already exists: ${LOCAL_BACKUP_DIR}${NC}"
    echo -e "${YELLOW}This directory needs to be deleted before downloading a new backup.${NC}"
    echo -e ""
    read -p "Do you want to delete the existing emulators_data directory? [y/N]: " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}🗑️  Deleting existing emulators_data directory...${NC}"
        rm -rf "$LOCAL_BACKUP_DIR"
        echo -e "${GREEN}✅ Directory deleted${NC}"
    else
        echo -e "${RED}❌ Cannot proceed without deleting the existing directory${NC}"
        exit 1
    fi
fi

# List available manual backups
echo -e "${YELLOW}📋 Fetching available manual backups...${NC}"
BACKUPS=$(gsutil ls "gs://${BUCKET_NAME}/manual-backups/" 2>/dev/null || echo "")

if [ -z "$BACKUPS" ]; then
    echo -e "${RED}❌ No manual backups found in gs://${BUCKET_NAME}/manual-backups/${NC}"
    echo -e "${BLUE}To create a manual backup, run:${NC}"
    echo -e "  ./manual-backup.sh"
    exit 1
fi

# Display available backups
echo -e "${GREEN}Available manual backups:${NC}"
echo "$BACKUPS" | while read -r backup; do
    if [ -n "$backup" ]; then
        backup_name=$(basename "$backup")
        echo "  - $backup_name"
    fi
done
echo -e ""

# Get the latest backup
LATEST_BACKUP=$(echo "$BACKUPS" | sort -r | head -n 1)
BACKUP_NAME=$(basename "$LATEST_BACKUP")

echo -e "${GREEN}✓ Using latest backup: ${BACKUP_NAME}${NC}"

# Create temporary directory for download
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

# Download the backup
echo -e "${BLUE}📥 Downloading backup from GCS...${NC}"
gsutil -m cp -r "${LATEST_BACKUP}*" "$TEMP_DIR/"

# Prepare emulator data directory
echo -e "${BLUE}🔧 Preparing emulator data directory...${NC}"
mkdir -p "$LOCAL_BACKUP_DIR/firestore_export"

# Copy the backup data
cp -r "$TEMP_DIR/$BACKUP_NAME"/* "$LOCAL_BACKUP_DIR/firestore_export/" 2>/dev/null || \
cp -r "$TEMP_DIR"/* "$LOCAL_BACKUP_DIR/firestore_export/"

# Find and rename the metadata file if needed
METADATA_FILE=$(find "$LOCAL_BACKUP_DIR/firestore_export" -name "*.overall_export_metadata" -type f | head -n 1)
if [ -n "$METADATA_FILE" ] && [ "$METADATA_FILE" != "$LOCAL_BACKUP_DIR/firestore_export/firestore_export.overall_export_metadata" ]; then
    mv "$METADATA_FILE" "$LOCAL_BACKUP_DIR/firestore_export/firestore_export.overall_export_metadata"
fi

# Create metadata file for emulator
cat > "$LOCAL_BACKUP_DIR/firebase-export-metadata.json" << EOF
{
  "version": "13.0.0",
  "firestore": {
    "version": "1.19.0",
    "path": "firestore_export",
    "metadata_file": "firestore_export/firestore_export.overall_export_metadata"
  }
}
EOF

# Create a marker file to indicate this is production data
echo "${BACKUP_NAME}" > "$LOCAL_BACKUP_DIR/.production-backup"

echo -e "${GREEN}✅ Manual backup downloaded and prepared for emulator!${NC}"
echo -e "Location: ${LOCAL_BACKUP_DIR}"
echo -e "Backup: ${BACKUP_NAME}"
echo -e ""
echo -e "${YELLOW}⚠️  This is production data - will be loaded in READ-ONLY mode${NC}"
echo -e ""
echo -e "${BLUE}🚀 To use this backup, run:${NC}"
echo -e "  cd ../.. && ./dev-start.sh"
echo -e ""
echo -e "The script will detect the production backup and ask if you want to use it."