# Firestore Backup System

This directory contains scripts for managing Firestore scheduled backups using the official Firebase backup feature.

## Setup

1. **Enable scheduled backups** (run once):
   ```bash
   gcloud firestore backups schedules create \
     --database='(default)' \
     --recurrence=daily \
     --retention=P14D \
     --location=europe-west3
   ```
   This creates a daily backup schedule with 14-day retention.

2. **Download latest backup** for local development:
   ```bash
   ./download-scheduled-backup.sh
   ```
   This downloads the latest scheduled backup and prepares it for the Firebase emulator.

## How it works

- **Scheduled Backups**: Uses the official `gcloud firestore backups` feature
- **Daily Schedule**: Backups run automatically once per day at a Google-managed time
- **14-day Retention**: Old backups are automatically deleted after 14 days
- **No Performance Impact**: Backups don't affect your production database performance

## Local Development

When you run `./download-scheduled-backup.sh`:
1. It fetches the latest backup from your production Firestore
2. Restores it to a temporary database
3. Exports the temporary database to Cloud Storage
4. Downloads and converts it to emulator-compatible format
5. Places it in `firebase/emulators_data/` with a `.production-backup` marker file
6. Automatically cleans up temporary resources
7. The development script will detect this and offer to use it in read-only mode

## Costs

- **Storage**: You're charged for backup storage (same rate as Firestore storage)
- **Restore**: Charged based on the size of the backup when restoring
- **Export**: Small cost per GB exported during download process

## Useful Commands

```bash
# List backup schedules
gcloud firestore backups schedules list --database='(default)'

# List all backups
gcloud firestore backups list --location=europe-west3

# Delete a backup schedule
gcloud firestore backups schedules delete SCHEDULE_ID --database='(default)'

# Manually trigger a backup (if schedule exists)
gcloud firestore backups create --database='(default)' --location=europe-west3
```

## Security Note

Production backups contain real user data. When using them locally:
- They're loaded in read-only mode by default
- Never commit the `firebase/emulators_data/` directory
- Delete local copies when done: `rm -rf ../emulators_data`

## Troubleshooting

### "No backups found" Error:
- Make sure you have scheduled backups set up (see Setup section above)
- Wait up to 24 hours for the first backup to be created
- Check that backups are in the correct location (europe-west3)

### Permission Errors:
- Ensure you're authenticated: `gcloud auth login`
- Check that you have proper permissions to access Firestore backups