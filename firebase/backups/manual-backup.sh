#!/bin/bash

# Manual Firestore backup script
# IMPORTANT: This script should be run by the USER, not by AI agents!

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
PROJECT_ID="fiaranow"
DATABASE_ID="(default)"
BUCKET_NAME="fiaranow-backups"
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
BACKUP_PATH="gs://${BUCKET_NAME}/manual-backups/${TIMESTAMP}"

echo -e "${BLUE}🚀 Manual Firestore Backup${NC}"
echo -e "Project: ${PROJECT_ID}"
echo -e "Database: ${DATABASE_ID}"
echo -e "Backup path: ${BACKUP_PATH}"
echo -e ""

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${RED}❌ Not authenticated with gcloud. Please run: gcloud auth login${NC}"
    exit 1
fi

# Check if bucket exists
echo -e "${YELLOW}📋 Checking backup bucket...${NC}"
if ! gsutil ls -b "gs://${BUCKET_NAME}" &>/dev/null; then
    echo -e "${YELLOW}Bucket gs://${BUCKET_NAME} doesn't exist. Creating...${NC}"
    if gsutil mb -l europe-west3 "gs://${BUCKET_NAME}"; then
        echo -e "${GREEN}✅ Bucket created successfully${NC}"
    else
        echo -e "${RED}❌ Failed to create bucket${NC}"
        exit 1
    fi
fi

# Export Firestore data
echo -e "${BLUE}📤 Exporting Firestore data...${NC}"
if gcloud firestore export "${BACKUP_PATH}" --project="${PROJECT_ID}"; then
    echo -e "${GREEN}✅ Backup completed successfully!${NC}"
    echo -e "Backup location: ${BACKUP_PATH}"
    
    # Clean up old backups (older than 14 days)
    echo -e ""
    echo -e "${YELLOW}🧹 Cleaning up old backups...${NC}"
    CUTOFF_DATE=$(date -d "14 days ago" +%Y%m%d 2>/dev/null || date -v-14d +%Y%m%d 2>/dev/null || echo "")
    
    if [ -n "$CUTOFF_DATE" ]; then
        # List and delete old backups
        gsutil ls "gs://${BUCKET_NAME}/manual-backups/" | while read -r backup_dir; do
            # Extract timestamp from backup directory name
            backup_name=$(basename "$backup_dir")
            backup_date=$(echo "$backup_name" | cut -d'-' -f1)
            
            # Check if it's a valid date and if it's older than cutoff
            if [[ "$backup_date" =~ ^[0-9]{8}$ ]] && [ "$backup_date" -lt "$CUTOFF_DATE" ]; then
                echo -e "Deleting old backup: $backup_dir"
                gsutil -m rm -r "$backup_dir" || true
            fi
        done
        echo -e "${GREEN}✅ Cleanup completed${NC}"
    else
        echo -e "${YELLOW}⚠️  Could not determine cutoff date for cleanup${NC}"
    fi
else
    echo -e "${RED}❌ Backup failed${NC}"
    exit 1
fi

