#!/usr/bin/env node

/**
 * Firestore Emulator Update/Delete Tool
 * 
 * A comprehensive CLI tool for updating and deleting Firestore emulator data.
 * Supports various commands for updating, patching, modifying, and deleting documents.
 * Includes support for recursive deletion of subcollections.
 */

const path = require('path');

// Find firebase-admin from the functions directory
const functionsDir = path.join(__dirname, '..', 'functions');
const admin = require(path.join(functionsDir, 'node_modules', 'firebase-admin'));
const fs = require('fs').promises;

// Configuration
const FIRESTORE_HOST = process.env.FIRESTORE_EMULATOR_HOST || 'localhost:8080';
const PROJECT_ID = process.env.FIREBASE_PROJECT_ID || 'fiaranow';

// Initialize Admin SDK
process.env.FIRESTORE_EMULATOR_HOST = FIRESTORE_HOST;
admin.initializeApp({ projectId: PROJECT_ID });
const db = admin.firestore();

// Valid operators for where clauses
const VALID_OPERATORS = ['==', '!=', '<', '<=', '>', '>=', 'in', 'not-in', 'array-contains', 'array-contains-any'];

// Command line argument parser
class CLIParser {
  constructor() {
    this.args = process.argv.slice(2);
    this.command = this.args[0];
    this.collection = this.args[1];
    this.docId = this.args[2];
    this.options = this.parseOptions();
  }

  parseOptions() {
    const options = {
      data: {},
      merge: false,
      where: [],
      limit: null,
      dryRun: false,
      fields: {},
      increment: {},
      arrayUnion: {},
      arrayRemove: {},
      deleteFields: [],
      fromFile: null,
      batch: false,
      recursive: true
    };

    for (let i = 3; i < this.args.length; i++) {
      const arg = this.args[i];
      const nextArg = this.args[i + 1];

      switch (arg) {
        case '--data':
          try {
            options.data = JSON.parse(nextArg);
          } catch (e) {
            console.error('❌ Invalid JSON for --data:', nextArg);
            console.error('Expected valid JSON format, e.g.: \'{"field": "value"}\'');
            process.exit(1);
          }
          i++;
          break;
        case '--merge':
          options.merge = true;
          break;
        case '--set':
          // --set field value
          if (i + 2 < this.args.length) {
            const field = nextArg;
            const value = this.parseValue(this.args[i + 2]);
            this.setNestedField(options.fields, field, value);
            i += 2;
          }
          break;
        case '--increment':
          // --increment field value
          if (i + 2 < this.args.length) {
            const field = nextArg;
            const value = parseFloat(this.args[i + 2]);
            if (isNaN(value)) {
              console.error(`❌ Invalid number for --increment: ${this.args[i + 2]}`);
              console.error('Expected a numeric value');
              process.exit(1);
            }
            options.increment[field] = value;
            i += 2;
          }
          break;
        case '--array-union':
          // --array-union field value
          if (i + 2 < this.args.length) {
            const field = nextArg;
            const value = this.parseValue(this.args[i + 2]);
            options.arrayUnion[field] = value;
            i += 2;
          }
          break;
        case '--array-remove':
          // --array-remove field value
          if (i + 2 < this.args.length) {
            const field = nextArg;
            const value = this.parseValue(this.args[i + 2]);
            options.arrayRemove[field] = value;
            i += 2;
          }
          break;
        case '--delete':
          // --delete field
          options.deleteFields.push(nextArg);
          i++;
          break;
        case '--where':
          // Enhanced where clause parsing - support multiple formats
          const whereResult = this.parseWhereClause(i);
          if (whereResult.error) {
            throw new Error(whereResult.error);
          }
          options.where.push(whereResult.clause);
          i += whereResult.consumed - 1;
          break;
        case '--limit':
          options.limit = parseInt(nextArg) || null;
          i++;
          break;
        case '--dry-run':
          options.dryRun = true;
          break;
        case '--from-file':
          options.fromFile = nextArg;
          i++;
          break;
        case '--batch':
          options.batch = true;
          break;
        case '--no-recursive':
          options.recursive = false;
          break;
      }
    }

    return options;
  }

  parseWhereClause(startIndex) {
    const args = this.args;

    // Make sure we have at least one argument after --where
    if (startIndex + 1 >= args.length) {
      return {
        error: 'Missing where clause after --where\n' +
          'Expected: --where "field operator value" or --where field=value'
      };
    }

    // Try to parse as a single quoted string first
    const quotedMatch = args[startIndex + 1]?.match(/^["'](.+)["']$/);
    if (quotedMatch) {
      const whereStr = quotedMatch[1];
      const parsed = this.parseWhereString(whereStr);
      if (parsed.error) {
        return { error: parsed.error };
      }
      return { clause: parsed, consumed: 2 };
    }

    // Try to parse as unquoted string with operator
    const whereStr = args[startIndex + 1];

    // Check if it contains an operator in the string itself
    for (const op of VALID_OPERATORS) {
      if (whereStr.includes(op)) {
        const parsed = this.parseWhereString(whereStr);
        if (!parsed.error) {
          return { clause: parsed, consumed: 2 };
        }
      }
    }

    // Check for shorthand equality (field=value)
    if (whereStr.includes('=') && !whereStr.includes('==')) {
      const parsed = this.parseWhereString(whereStr);
      if (!parsed.error) {
        return { clause: parsed, consumed: 2 };
      }
    }

    // Parse as three separate arguments: field operator value
    // Make sure we have enough arguments
    if (startIndex + 3 < args.length) {
      const field = args[startIndex + 1];
      const operator = args[startIndex + 2];
      const value = args[startIndex + 3];

      // Validate operator
      if (VALID_OPERATORS.includes(operator)) {
        return {
          clause: {
            field: field,
            operator: operator,
            value: this.parseValue(value)
          },
          consumed: 4
        };
      }
    }

    // If we get here, we couldn't parse the where clause
    return {
      error: 'Invalid where clause format.\n' +
        'Expected formats:\n' +
        '  --where "field operator value"\n' +
        '  --where field operator value\n' +
        '  --where field=value\n' +
        'Examples:\n' +
        '  --where "status == completed"\n' +
        '  --where status == completed\n' +
        '  --where primaryUserType=1\n' +
        'Got: ' + args.slice(startIndex + 1, Math.min(startIndex + 4, args.length)).join(' ')
    };
  }

  parseWhereString(whereStr) {
    // Try different patterns

    // Pattern 1: field=value (shorthand for ==)
    const equalMatch = whereStr.match(/^(\S+)=([^=].*)$/);
    if (equalMatch) {
      return {
        field: equalMatch[1],
        operator: '==',
        value: this.parseValue(equalMatch[2])
      };
    }

    // Pattern 2: field operator value (with spaces)
    for (const op of VALID_OPERATORS) {
      const regex = new RegExp(`^(\\S+)\\s*${op.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*(.+)$`);
      const match = whereStr.match(regex);
      if (match) {
        return {
          field: match[1],
          operator: op,
          value: this.parseValue(match[2])
        };
      }
    }

    return {
      error: `Could not parse where clause: "${whereStr}"\n` +
        `Expected format: "field operator value"\n` +
        `Valid operators: ${VALID_OPERATORS.join(', ')}`
    };
  }

  setNestedField(obj, path, value) {
    const parts = path.split('.');
    let current = obj;

    for (let i = 0; i < parts.length - 1; i++) {
      if (!current[parts[i]]) {
        current[parts[i]] = {};
      }
      current = current[parts[i]];
    }

    current[parts[parts.length - 1]] = value;
  }

  parseValue(value) {
    // Remove surrounding quotes if present
    if ((value.startsWith('"') && value.endsWith('"')) ||
      (value.startsWith("'") && value.endsWith("'"))) {
      value = value.slice(1, -1);
    }

    // Try to parse as JSON first
    try {
      return JSON.parse(value);
    } catch (e) {
      // Check for special values
      if (value === 'true') return true;
      if (value === 'false') return false;
      if (value === 'null') return null;

      // Check if it's a number
      const num = Number(value);
      if (!isNaN(num) && value.trim() !== '') return num;

      // Check if it's a date
      const date = new Date(value);
      if (!isNaN(date.getTime()) && value.includes('-')) {
        return admin.firestore.Timestamp.fromDate(date);
      }

      // Return as string
      return value;
    }
  }
}

// Commands
const commands = {
  async update(collection, docId, options) {
    if (!docId) {
      throw new Error('Document ID is required for update command');
    }

    const docRef = db.collection(collection).doc(docId);

    // Check if document exists
    const doc = await docRef.get();
    if (!doc.exists) {
      throw new Error(`Document ${docId} not found in ${collection}`);
    }

    // Build update object
    const updateData = this.buildUpdateData(options);

    if (options.dryRun) {
      return {
        operation: 'update',
        collection,
        documentId: docId,
        wouldUpdate: updateData,
        currentData: doc.data()
      };
    }

    // Perform update
    if (options.merge) {
      await docRef.set(updateData, { merge: true });
    } else {
      await docRef.update(updateData);
    }

    // Return updated document
    const updatedDoc = await docRef.get();
    return {
      operation: 'update',
      collection,
      documentId: docId,
      updatedData: updatedDoc.data(),
      timestamp: new Date().toISOString()
    };
  },

  async updateMany(collection, options) {
    let query = db.collection(collection);

    // Apply filters
    for (const where of options.where) {
      try {
        query = query.where(where.field, where.operator, where.value);
      } catch (error) {
        if (error.message.includes('Invalid query')) {
          throw new Error(
            `Invalid query: ${error.message}\n` +
            `Check that field "${where.field}" exists and supports the "${where.operator}" operator.\n` +
            `Value type: ${typeof where.value}`
          );
        }
        throw error;
      }
    }

    // Apply limit if specified
    if (options.limit) {
      query = query.limit(options.limit);
    }

    const snapshot = await query.get();

    if (snapshot.empty) {
      return {
        operation: 'updateMany',
        collection,
        matchedCount: 0,
        updatedCount: 0,
        message: 'No documents matched the query'
      };
    }

    const updateData = this.buildUpdateData(options);

    if (options.dryRun) {
      const documents = [];
      snapshot.forEach(doc => {
        documents.push({
          id: doc.id,
          currentData: doc.data()
        });
      });

      return {
        operation: 'updateMany',
        collection,
        wouldUpdate: updateData,
        matchedCount: snapshot.size,
        documents: documents.slice(0, 10), // Show first 10 for dry run
        message: `Would update ${snapshot.size} documents`
      };
    }

    // Perform updates
    let updatedCount = 0;
    const errors = [];

    if (options.batch) {
      // Use batch for atomic updates
      const batch = db.batch();

      snapshot.forEach(doc => {
        batch.update(doc.ref, updateData);
      });

      try {
        await batch.commit();
        updatedCount = snapshot.size;
      } catch (error) {
        throw new Error(`Batch update failed: ${error.message}`);
      }
    } else {
      // Update one by one
      for (const doc of snapshot.docs) {
        try {
          await doc.ref.update(updateData);
          updatedCount++;
        } catch (error) {
          errors.push({
            documentId: doc.id,
            error: error.message
          });
        }
      }
    }

    return {
      operation: 'updateMany',
      collection,
      matchedCount: snapshot.size,
      updatedCount,
      errors: errors.length > 0 ? errors : undefined,
      timestamp: new Date().toISOString()
    };
  },

  async set(collection, docId, options) {
    if (!docId) {
      throw new Error('Document ID is required for set command');
    }

    const docRef = db.collection(collection).doc(docId);

    // Load data from file if specified
    let setData = options.data;
    if (options.fromFile) {
      try {
        const fileContent = await fs.readFile(options.fromFile, 'utf8');
        setData = JSON.parse(fileContent);
      } catch (error) {
        if (error.code === 'ENOENT') {
          throw new Error(`File not found: ${options.fromFile}`);
        } else if (error instanceof SyntaxError) {
          throw new Error(`Invalid JSON in file ${options.fromFile}: ${error.message}`);
        }
        throw error;
      }
    }

    if (!setData || Object.keys(setData).length === 0) {
      throw new Error('No data provided for set operation. Use --data or --from-file');
    }

    // Add timestamp
    setData.updatedAt = admin.firestore.FieldValue.serverTimestamp();

    if (options.dryRun) {
      const currentDoc = await docRef.get();
      return {
        operation: 'set',
        collection,
        documentId: docId,
        wouldSet: setData,
        currentExists: currentDoc.exists,
        currentData: currentDoc.exists ? currentDoc.data() : null
      };
    }

    // Perform set
    await docRef.set(setData, { merge: options.merge });

    // Return updated document
    const updatedDoc = await docRef.get();
    return {
      operation: 'set',
      collection,
      documentId: docId,
      setData: updatedDoc.data(),
      merge: options.merge,
      timestamp: new Date().toISOString()
    };
  },

  async increment(collection, docId, options) {
    if (!docId) {
      throw new Error('Document ID is required for increment command');
    }

    if (Object.keys(options.increment).length === 0) {
      throw new Error('No fields specified for increment. Use --increment field value');
    }

    const docRef = db.collection(collection).doc(docId);

    // Check if document exists
    const doc = await docRef.get();
    if (!doc.exists) {
      throw new Error(`Document ${docId} not found in ${collection}`);
    }

    // Build increment update
    const updateData = {};
    for (const [field, value] of Object.entries(options.increment)) {
      updateData[field] = admin.firestore.FieldValue.increment(value);
    }

    if (options.dryRun) {
      const currentData = doc.data();
      const preview = {};
      for (const [field, value] of Object.entries(options.increment)) {
        const currentValue = this.getNestedField(currentData, field) || 0;
        preview[field] = {
          current: currentValue,
          increment: value,
          result: currentValue + value
        };
      }

      return {
        operation: 'increment',
        collection,
        documentId: docId,
        preview
      };
    }

    // Perform update
    await docRef.update(updateData);

    // Return updated values
    const updatedDoc = await docRef.get();
    const updatedData = updatedDoc.data();
    const results = {};

    for (const field of Object.keys(options.increment)) {
      results[field] = this.getNestedField(updatedData, field);
    }

    return {
      operation: 'increment',
      collection,
      documentId: docId,
      updatedFields: results,
      timestamp: new Date().toISOString()
    };
  },

  async delete(collection, docId, options) {
    if (!docId) {
      throw new Error('Document ID is required for delete command');
    }

    const docRef = db.collection(collection).doc(docId);

    // Check if document exists
    const doc = await docRef.get();
    if (!doc.exists) {
      throw new Error(`Document ${docId} not found in ${collection}`);
    }

    // Get subcollections if recursive delete
    let subcollections = [];
    let subcollectionCount = 0;

    if (options.recursive !== false) { // Default is true
      subcollections = await docRef.listCollections();

      // Count documents in subcollections
      for (const subcol of subcollections) {
        const snapshot = await subcol.get();
        subcollectionCount += snapshot.size;
      }
    }

    if (options.dryRun) {
      const subcollectionInfo = subcollections.map(col => ({
        name: col.id,
        path: `${collection}/${docId}/${col.id}`
      }));

      return {
        operation: 'delete',
        collection,
        documentId: docId,
        wouldDelete: {
          document: doc.data(),
          subcollections: subcollectionInfo,
          subcollectionDocumentCount: subcollectionCount
        },
        recursive: options.recursive !== false
      };
    }

    // Delete subcollections if recursive
    if (options.recursive !== false) {
      for (const subcol of subcollections) {
        await this.deleteCollection(subcol, options.batch ? 500 : 10);
      }
    }

    // Delete the document
    await docRef.delete();

    return {
      operation: 'delete',
      collection,
      documentId: docId,
      deleted: true,
      subcollectionsDeleted: subcollections.length,
      subcollectionDocumentsDeleted: subcollectionCount,
      timestamp: new Date().toISOString()
    };
  },

  async deleteMany(collection, options) {
    let query = db.collection(collection);

    // Apply filters
    for (const where of options.where) {
      query = query.where(where.field, where.operator, where.value);
    }

    // Apply limit if specified
    if (options.limit) {
      query = query.limit(options.limit);
    }

    const snapshot = await query.get();

    if (snapshot.empty) {
      return {
        operation: 'deleteMany',
        collection,
        matchedCount: 0,
        deletedCount: 0,
        message: 'No documents matched the query'
      };
    }

    // Count subcollections if recursive
    let totalSubcollectionCount = 0;
    let totalSubcollectionDocs = 0;

    if (options.recursive !== false && options.dryRun) {
      for (const doc of snapshot.docs) {
        const subcollections = await doc.ref.listCollections();
        totalSubcollectionCount += subcollections.length;

        for (const subcol of subcollections) {
          const subSnapshot = await subcol.get();
          totalSubcollectionDocs += subSnapshot.size;
        }
      }
    }

    if (options.dryRun) {
      const documents = [];
      snapshot.forEach(doc => {
        documents.push({
          id: doc.id,
          data: doc.data()
        });
      });

      return {
        operation: 'deleteMany',
        collection,
        wouldDelete: {
          documentCount: snapshot.size,
          documents: documents.slice(0, 10), // Show first 10 for dry run
          subcollectionCount: totalSubcollectionCount,
          subcollectionDocumentCount: totalSubcollectionDocs
        },
        recursive: options.recursive !== false,
        message: `Would delete ${snapshot.size} documents${options.recursive !== false ? ' and their subcollections' : ''}`
      };
    }

    // Perform deletes
    let deletedCount = 0;
    let subcollectionsDeleted = 0;
    let subcollectionDocsDeleted = 0;
    const errors = [];

    if (options.batch) {
      // For batch deletes, we need to handle subcollections separately
      if (options.recursive !== false) {
        for (const doc of snapshot.docs) {
          try {
            const subcollections = await doc.ref.listCollections();
            for (const subcol of subcollections) {
              const count = await this.deleteCollection(subcol, 500);
              subcollectionDocsDeleted += count;
              subcollectionsDeleted++;
            }
          } catch (error) {
            errors.push({
              documentId: doc.id,
              phase: 'subcollections',
              error: error.message
            });
          }
        }
      }

      // Use batch for document deletes
      const batch = db.batch();

      snapshot.forEach(doc => {
        batch.delete(doc.ref);
      });

      try {
        await batch.commit();
        deletedCount = snapshot.size;
      } catch (error) {
        throw new Error(`Batch delete failed: ${error.message}`);
      }
    } else {
      // Delete one by one
      for (const doc of snapshot.docs) {
        try {
          // Delete subcollections first if recursive
          if (options.recursive !== false) {
            const subcollections = await doc.ref.listCollections();
            for (const subcol of subcollections) {
              const count = await this.deleteCollection(subcol, 10);
              subcollectionDocsDeleted += count;
              subcollectionsDeleted++;
            }
          }

          // Delete the document
          await doc.ref.delete();
          deletedCount++;
        } catch (error) {
          errors.push({
            documentId: doc.id,
            error: error.message
          });
        }
      }
    }

    return {
      operation: 'deleteMany',
      collection,
      matchedCount: snapshot.size,
      deletedCount,
      subcollectionsDeleted,
      subcollectionDocumentsDeleted: subcollectionDocsDeleted,
      errors: errors.length > 0 ? errors : undefined,
      timestamp: new Date().toISOString()
    };
  },

  // Helper function to delete a collection
  async deleteCollection(collectionRef, batchSize) {
    const query = collectionRef.limit(batchSize);
    let totalDeleted = 0;

    return new Promise((resolve, reject) => {
      this.deleteQueryBatch(query, resolve, reject, totalDeleted);
    });
  },

  async deleteQueryBatch(query, resolve, reject, totalDeleted) {
    const snapshot = await query.get();

    if (snapshot.size === 0) {
      resolve(totalDeleted);
      return;
    }

    const batch = db.batch();
    snapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    const deleted = snapshot.size;
    totalDeleted += deleted;

    await batch.commit();

    // Recurse on the next process tick
    process.nextTick(() => {
      this.deleteQueryBatch(query, resolve, reject, totalDeleted);
    });
  },

  buildUpdateData(options) {
    const updateData = { ...options.data };

    // Add individual field updates
    for (const [field, value] of Object.entries(options.fields)) {
      this.setNestedFieldInObject(updateData, field, value);
    }

    // Add increment operations
    for (const [field, value] of Object.entries(options.increment)) {
      updateData[field] = admin.firestore.FieldValue.increment(value);
    }

    // Add array union operations
    for (const [field, value] of Object.entries(options.arrayUnion)) {
      updateData[field] = admin.firestore.FieldValue.arrayUnion(value);
    }

    // Add array remove operations
    for (const [field, value] of Object.entries(options.arrayRemove)) {
      updateData[field] = admin.firestore.FieldValue.arrayRemove(value);
    }

    // Add delete field operations
    for (const field of options.deleteFields) {
      updateData[field] = admin.firestore.FieldValue.delete();
    }

    // Add timestamp
    updateData.updatedAt = admin.firestore.FieldValue.serverTimestamp();

    return updateData;
  },

  setNestedFieldInObject(obj, path, value) {
    const parts = path.split('.');
    let current = obj;

    for (let i = 0; i < parts.length - 1; i++) {
      if (!current[parts[i]]) {
        current[parts[i]] = {};
      }
      current = current[parts[i]];
    }

    current[parts[parts.length - 1]] = value;
  },

  getNestedField(obj, path) {
    const parts = path.split('.');
    let current = obj;

    for (const part of parts) {
      if (!current || typeof current !== 'object') {
        return undefined;
      }
      current = current[part];
    }

    return current;
  }
};

// Output formatters
const formatters = {
  pretty(data) {
    console.log(JSON.stringify(data, null, 2));
  },

  json(data) {
    console.log(JSON.stringify(data));
  }
};

// Help text
function showHelp() {
  console.log(`
Firestore Emulator Update/Delete Tool

Usage: node firestore-update.js <command> <collection> [docId] [options]

Commands:
  update <collection> <docId>          Update a specific document
  updateMany <collection>              Update multiple documents matching query
  set <collection> <docId>             Set/create a document
  increment <collection> <docId>       Increment numeric fields
  delete <collection> <docId>          Delete a document (with subcollections by default)
  deleteMany <collection>              Delete multiple documents matching query

Options:
  --data <json>                       JSON data to update/set
  --set <field> <value>               Set a specific field
  --increment <field> <value>         Increment a numeric field
  --array-union <field> <value>       Add value to array field
  --array-remove <field> <value>      Remove value from array field
  --delete <field>                    Delete a field
  --merge                             Merge data instead of overwrite (for set)
  --where <clause>                    Filter for updateMany/deleteMany (can use multiple)
  --limit <n>                         Limit documents for updateMany/deleteMany
  --dry-run                           Preview changes without applying
  --from-file <file>                  Load data from JSON file
  --batch                             Use batch for updateMany/deleteMany (atomic)
  --no-recursive                      Don't delete subcollections (for delete/deleteMany)

Where Clause Formats:
  --where "field == value"            Quoted format with operator
  --where field == value              Unquoted format (three arguments)
  --where field=value                 Shorthand for equality
  --where "status != pending"         Other operators work the same way

Operators for --where:
  ==, !=, <, <=, >, >=, in, not-in, array-contains, array-contains-any

Examples:
  # Update a single document
  node firestore-update.js update tenants/fiaranow/trips TRIP_ID --set status completed

  # Update with different where formats
  node firestore-update.js updateMany tenants/fiaranow/trips --where status=pending --set status cancelled
  node firestore-update.js updateMany tenants/fiaranow/trips --where "status == pending" --set status cancelled
  node firestore-update.js updateMany mobile_users --where primaryUserType=1 --set isActive true

  # Set document data
  node firestore-update.js set mobile_users USER_ID --data '{"name":"John","age":30}'

  # Increment a field
  node firestore-update.js increment tenants/fiaranow/trips TRIP_ID --increment distanceKm 5.2

  # Delete a single document (with subcollections)
  node firestore-update.js delete mobile_users USER_ID

  # Delete without subcollections
  node firestore-update.js delete mobile_users USER_ID --no-recursive

  # Delete multiple documents matching query
  node firestore-update.js deleteMany tenants/fiaranow/trips --where status=cancelled

  # Complex update with multiple operations
  node firestore-update.js update mobile_users USER_ID \\
    --set "profile.lastActive" "2024-01-15" \\
    --increment loginCount 1 \\
    --array-union tags premium \\
    --delete oldField

  # Dry run to preview what would be deleted
  node firestore-update.js deleteMany tenants/fiaranow/trips \\
    --where "createdAt < 2024-01-01" \\
    --dry-run

  # Update from file
  node firestore-update.js set tenants/fiaranow/configurations trip_default \\
    --from-file config.json \\
    --merge

Environment Variables:
  FIRESTORE_EMULATOR_HOST   Firestore emulator host (default: localhost:8080)
  FIREBASE_PROJECT_ID       Firebase project ID (default: fiaranow)
`);
}

// Main execution
async function main() {
  try {
    const cli = new CLIParser();

    if (!cli.command || cli.command === 'help' || cli.command === '--help') {
      showHelp();
      process.exit(0);
    }

    if (!commands[cli.command]) {
      console.error(`❌ Unknown command: "${cli.command}"\n`);
      console.error('Available commands: ' + Object.keys(commands).join(', '));
      console.error('\nRun with --help to see usage examples');
      process.exit(1);
    }

    if (!cli.collection && cli.command !== 'help') {
      console.error('❌ Collection path is required\n');
      console.error('Usage: firestore-update.js <command> <collection> [options]');
      console.error('Example: firestore-update.js update mobile_users USER_ID --set status active');
      process.exit(1);
    }

    // Execute command
    let result;
    if (cli.command === 'updateMany' || cli.command === 'deleteMany') {
      result = await commands[cli.command](cli.collection, cli.options);
    } else {
      if (!cli.docId) {
        console.error(`❌ Document ID is required for ${cli.command} command\n`);
        console.error(`Usage: firestore-update.js ${cli.command} <collection> <docId> [options]`);
        console.error(`Example: firestore-update.js ${cli.command} mobile_users USER_ID`);
        process.exit(1);
      }
      result = await commands[cli.command](cli.collection, cli.docId, cli.options);
    }

    // Format output
    const formatter = formatters[cli.options.format || 'pretty'];
    formatter(result);

  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.code) {
      console.error('Firebase Error Code:', error.code);
    }

    // Provide helpful context for common errors
    if (error.message.includes('permission')) {
      console.error('\n💡 This looks like a permissions error. Make sure:');
      console.error('   - The emulators are running');
      console.error('   - You have the correct FIRESTORE_EMULATOR_HOST set');
    } else if (error.message.includes('connect')) {
      console.error('\n💡 Cannot connect to Firestore emulator. Make sure:');
      console.error('   - The emulators are running (use dev-start.sh)');
      console.error('   - FIRESTORE_EMULATOR_HOST is set correctly (current: ' + FIRESTORE_HOST + ')');
    } else if (error.message.includes('not found')) {
      console.error('\n💡 Document or collection not found. Check:');
      console.error('   - The collection path is correct');
      console.error('   - The document ID exists');
      console.error('   - You have the right tenant path (e.g., tenants/fiaranow/...)');
    }

    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}
