/**
 * Cleanup Archived Chats Runner for Emulator
 * 
 * This module simulates the cleanupArchivedChats function logic
 * against the Firebase emulator for development testing.
 */

const path = require('path');
const admin = require('firebase-admin');

module.exports = async function cleanupArchivedChats() {
  try {
    const db = admin.firestore();
    const { Timestamp } = admin.firestore;
    const startTime = Date.now();

    console.log('Starting archived chat cleanup...');

    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    // Use fiaranow as tenant ID for emulator
    const defaultTenantId = 'fiaranow';

    // Find archived chat sessions older than 30 days
    const archivedChats = await db
      .collection(`tenants/${defaultTenantId}/chat_sessions`)
      .where("status", "==", "archived")
      .where("lastMessageAt", "<", Timestamp.fromDate(thirtyDaysAgo))
      .get();

    console.log(`Found ${archivedChats.size} archived chat sessions older than 30 days`);

    if (archivedChats.empty) {
      console.log('No old archived chats to cleanup');
      return;
    }

    let deletedSessions = 0;
    let deletedMessages = 0;

    // In emulator mode, we'll process the deletions
    const deletePromises = archivedChats.docs.map(async (doc) => {
      const sessionData = doc.data();
      console.log(`Processing chat session ${doc.id} (last message: ${sessionData.lastMessageAt?.toDate()?.toISOString()})`);

      try {
        // Delete all messages in the session
        const messagesSnapshot = await doc.ref.collection("chat_messages").get();
        console.log(`  Found ${messagesSnapshot.size} messages to delete`);
        
        const messageDeletePromises = messagesSnapshot.docs.map(msgDoc => {
          console.log(`    Deleting message ${msgDoc.id}`);
          return msgDoc.ref.delete();
        });
        
        await Promise.all(messageDeletePromises);
        deletedMessages += messagesSnapshot.size;

        // Delete the session
        console.log(`  Deleting chat session ${doc.id}`);
        await doc.ref.delete();
        deletedSessions++;

      } catch (error) {
        console.error(`Error deleting chat session ${doc.id}:`, error);
        throw error;
      }
    });

    await Promise.all(deletePromises);

    console.log(`Cleanup completed:`);
    console.log(`  Deleted sessions: ${deletedSessions}`);
    console.log(`  Deleted messages: ${deletedMessages}`);

    const duration = Date.now() - startTime;
    console.log(`Archived chat cleanup completed in ${duration}ms`);

  } catch (error) {
    console.error('Error in cleanupArchivedChats:', error);
    throw error;
  }
};