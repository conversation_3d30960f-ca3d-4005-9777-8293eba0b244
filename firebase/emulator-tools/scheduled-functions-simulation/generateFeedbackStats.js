/**
 * Generate Feedback Stats Runner for Emulator
 * 
 * This module simulates the generateFeedbackStats function logic
 * against the Firebase emulator for development testing.
 */

const path = require('path');
const admin = require('firebase-admin');

module.exports = async function generateFeedbackStats() {
  try {
    const db = admin.firestore();
    const { Timestamp } = admin.firestore;
    const startTime = Date.now();

    console.log('Starting feedback statistics generation...');

    // Use fiaranow as tenant ID for emulator
    const defaultTenantId = 'fiaranow';
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get all feedback from yesterday
    const feedbackSnapshot = await db
      .collection(`tenants/${defaultTenantId}/feedbacks`)
      .where("createdAt", ">=", Timestamp.fromDate(yesterday))
      .where("createdAt", "<", Timestamp.fromDate(today))
      .get();

    console.log(`Found ${feedbackSnapshot.size} feedbacks from yesterday`);

    if (feedbackSnapshot.empty) {
      console.log('No feedback data to process for yesterday');
      return;
    }

    // Calculate statistics
    const stats = {
      date: yesterday.toISOString(),
      totalFeedbacks: feedbackSnapshot.size,
      averageRating: 0,
      ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
      feedbackByType: {
        driver: 0,
        passenger: 0,
        general: 0
      }
    };

    let totalRating = 0;
    let ratingCount = 0;

    feedbackSnapshot.docs.forEach(doc => {
      const feedback = doc.data();
      
      // Rating statistics
      if (feedback.rating && feedback.rating >= 1 && feedback.rating <= 5) {
        totalRating += feedback.rating;
        ratingCount++;
        stats.ratingDistribution[feedback.rating]++;
      }

      // Type statistics
      if (feedback.type) {
        if (stats.feedbackByType.hasOwnProperty(feedback.type)) {
          stats.feedbackByType[feedback.type]++;
        }
      }
    });

    stats.averageRating = ratingCount > 0 ? (totalRating / ratingCount).toFixed(2) : 0;

    console.log('Feedback Statistics Generated:');
    console.log(`  Date: ${stats.date}`);
    console.log(`  Total feedbacks: ${stats.totalFeedbacks}`);
    console.log(`  Average rating: ${stats.averageRating}`);
    console.log(`  Rating distribution:`, stats.ratingDistribution);
    console.log(`  Feedback by type:`, stats.feedbackByType);

    // In emulator mode, we'll just log the stats instead of saving them
    console.log('(Would save feedback statistics to database in production mode)');

    const duration = Date.now() - startTime;
    console.log(`Feedback statistics generation completed in ${duration}ms`);

  } catch (error) {
    console.error('Error in generateFeedbackStats:', error);
    throw error;
  }
};