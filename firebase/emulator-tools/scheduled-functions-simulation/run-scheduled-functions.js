#!/usr/bin/env node

/**
 * Scheduled Functions Runner for Firebase Emulator
 * 
 * This tool runs scheduled functions against the Firebase emulator
 * with faster-than-production schedules for development testing.
 * 
 * Environment Variables:
 *   CALCULATE_TRIP_COSTS_INTERVAL - Interval for calculateTripCosts (default: 60000ms = 1 minute)
 *   NOTIFY_EXPIRING_DOCS_INTERVAL - Interval for notifyExpiringDocuments (default: 300000ms = 5 minutes)
 *   GENERATE_FEEDBACK_STATS_INTERVAL - Interval for generateFeedbackStats (default: 300000ms = 5 minutes)
 *   CLEANUP_ARCHIVED_CHATS_INTERVAL - Interval for cleanupArchivedChats (default: 300000ms = 5 minutes)
 */

const path = require('path');
const admin = require('firebase-admin');

// Configuration
const EMULATOR_HOST = 'firebase-emulators';
const FIRESTORE_PORT = 8080;
const AUTH_PORT = 9099;

// Intervals (in milliseconds)
const INTERVALS = {
  calculateTripCosts: parseInt(process.env.CALCULATE_TRIP_COSTS_INTERVAL) || 60000,      // 1 minute
  notifyExpiringDocuments: parseInt(process.env.NOTIFY_EXPIRING_DOCS_INTERVAL) || 300000, // 5 minutes
  generateFeedbackStats: parseInt(process.env.GENERATE_FEEDBACK_STATS_INTERVAL) || 300000, // 5 minutes
  cleanupArchivedChats: parseInt(process.env.CLEANUP_ARCHIVED_CHATS_INTERVAL) || 300000    // 5 minutes
};

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Logging utilities
function log(message, color = 'reset') {
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// Active timers for cleanup
const activeTimers = new Map();

// Initialize Firebase Admin SDK for emulator
async function initializeFirebase() {
  try {
    // Configure for emulator
    process.env.FIRESTORE_EMULATOR_HOST = `${EMULATOR_HOST}:${FIRESTORE_PORT}`;
    process.env.FIREBASE_AUTH_EMULATOR_HOST = `${EMULATOR_HOST}:${AUTH_PORT}`;

    // Initialize without credentials for emulator
    admin.initializeApp({
      projectId: 'fiaranow'
    });

    logSuccess('Connected to Firebase Emulator Suite');
    return admin;
  } catch (error) {
    logError(`Failed to initialize Firebase: ${error.message}`);
    process.exit(1);
  }
}

// Function runners
const functionRunners = {
  calculateTripCosts: require('./calculateTripCosts'),
  notifyExpiringDocuments: require('./notifyExpiringDocuments'),
  generateFeedbackStats: require('./generateFeedbackStats'),
  cleanupArchivedChats: require('./cleanupArchivedChats')
};

// Execute a function with error handling
async function executeFunction(functionName, runner) {
  const startTime = Date.now();
  
  try {
    logInfo(`Executing ${functionName}...`);
    await runner();
    const duration = Date.now() - startTime;
    logSuccess(`${functionName} completed in ${duration}ms`);
  } catch (error) {
    const duration = Date.now() - startTime;
    logError(`${functionName} failed after ${duration}ms: ${error.message}`);
    console.error(error);
  }
}

// Schedule a function to run at intervals
function scheduleFunction(functionName, interval) {
  const runner = functionRunners[functionName];
  
  if (!runner) {
    logError(`Function runner not found: ${functionName}`);
    return;
  }

  logInfo(`Scheduling ${functionName} to run every ${interval}ms (${interval / 1000}s)`);

  // Execute immediately
  executeFunction(functionName, runner);

  // Schedule periodic execution
  const timerId = setInterval(() => {
    executeFunction(functionName, runner);
  }, interval);

  activeTimers.set(functionName, timerId);
}

// Graceful shutdown
function setupGracefulShutdown() {
  const shutdown = () => {
    logInfo('Shutting down scheduled functions runner...');
    
    // Clear all timers
    activeTimers.forEach((timerId, functionName) => {
      clearInterval(timerId);
      logInfo(`Stopped ${functionName} scheduler`);
    });
    
    activeTimers.clear();
    logSuccess('Scheduled functions runner stopped');
    process.exit(0);
  };

  process.on('SIGINT', shutdown);
  process.on('SIGTERM', shutdown);
}

// Main execution
async function main() {
  try {
    logInfo('Starting Scheduled Functions Runner (Emulator Mode)');
    
    // Initialize Firebase
    await initializeFirebase();
    
    // Setup graceful shutdown
    setupGracefulShutdown();
    
    // Display configuration
    logInfo('Configuration:');
    Object.entries(INTERVALS).forEach(([functionName, interval]) => {
      log(`  ${functionName}: ${interval}ms (${interval / 1000}s)`, 'blue');
    });
    
    // Schedule all functions
    Object.entries(INTERVALS).forEach(([functionName, interval]) => {
      scheduleFunction(functionName, interval);
    });
    
    logSuccess('All scheduled functions are running');
    
    // Keep process alive
    process.stdin.resume();
    
  } catch (error) {
    logError(`Failed to start scheduled functions runner: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main, executeFunction };