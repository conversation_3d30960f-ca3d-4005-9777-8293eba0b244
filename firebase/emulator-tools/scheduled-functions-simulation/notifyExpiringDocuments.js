/**
 * Notify Expiring Documents Runner for Emulator
 * 
 * This module simulates the notifyExpiringDocuments function logic
 * against the Firebase emulator for development testing.
 */

const path = require('path');
const admin = require('firebase-admin');

module.exports = async function notifyExpiringDocuments() {
  try {
    const db = admin.firestore();
    const startTime = Date.now();

    console.log('Starting document expiry notification check...');

    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));

    // Query all driver documents that are expiring within 30 days
    const documentsSnapshot = await db
      .collectionGroup("driver_documents")
      .where("status", "==", "approved")
      .where("expiryDate", "<=", thirtyDaysFromNow)
      .get();

    console.log(`Found ${documentsSnapshot.size} documents expiring within 30 days`);

    if (documentsSnapshot.empty) {
      console.log('No expiring documents found');
      return;
    }

    let notificationsCount = 0;
    const adminNotifications = new Map();

    for (const doc of documentsSnapshot.docs) {
      const docData = doc.data();
      const expiryDate = docData.expiryDate.toDate();
      const daysUntilExpiry = Math.floor((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      // Only notify for 30 days and 7 days
      if (daysUntilExpiry === 30 || daysUntilExpiry === 7) {
        console.log(`Document ${doc.id} expires in ${daysUntilExpiry} days - would send notification`);
        
        // Extract tenant ID and driver ID from document path
        const pathParts = doc.ref.path.split('/');
        const tenantId = pathParts[1];
        const driverId = pathParts[3];

        // In emulator mode, we'll just log what would be sent
        console.log(`  Would notify driver ${driverId} in tenant ${tenantId} about document ${docData.type}`);

        // Collect for admin notification
        if (!adminNotifications.has(tenantId)) {
          adminNotifications.set(tenantId, []);
        }
        adminNotifications.get(tenantId).push({
          driverId,
          documentType: docData.type,
          expiryDate: expiryDate.toISOString(),
          daysUntilExpiry
        });

        notificationsCount++;
      }
    }

    // Log admin notifications summary
    for (const [tenantId, docs] of adminNotifications) {
      console.log(`Admin notification for tenant ${tenantId}: ${docs.length} documents expiring`);
    }

    const duration = Date.now() - startTime;
    console.log(`Document expiry notification check completed in ${duration}ms`);
    console.log(`Total notifications that would be sent: ${notificationsCount}`);

  } catch (error) {
    console.error('Error in notifyExpiringDocuments:', error);
    throw error;
  }
};