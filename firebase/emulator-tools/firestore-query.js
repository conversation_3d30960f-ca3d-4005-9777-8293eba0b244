#!/usr/bin/env node

/**
 * Firestore Query Tool
 * 
 * A comprehensive CLI tool for querying Firestore data.
 * Supports both Firebase emulator (default) and production modes.
 */

const path = require('path');

// Find firebase-admin from the functions directory
const functionsDir = path.join(__dirname, '..', 'functions');
const admin = require(path.join(functionsDir, 'node_modules', 'firebase-admin'));
const fs = require('fs').promises;

// Configuration
const FIRESTORE_HOST = process.env.FIRESTORE_EMULATOR_HOST || 'localhost:8080';
const PROJECT_ID = process.env.FIREBASE_PROJECT_ID || 'fiaranow';

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Logging utilities
function log(message, color = 'reset') {
  console.error(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) { log(`✅ ${message}`, 'green'); }
function logError(message) { log(`❌ ${message}`, 'red'); }
function logWarning(message) { log(`⚠️ ${message}`, 'yellow'); }
function logInfo(message) { log(`ℹ️ ${message}`, 'blue'); }

// Parse arguments to check for production mode
const args = process.argv.slice(2);
const useProduction = args.includes('--prod') || args.includes('--production');

// Initialize Firebase based on mode
async function initializeFirebase() {
  if (useProduction) {
    // Production mode - requires service account
    const serviceAccountPath = path.join(__dirname, '..', 'fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json');
    
    try {
      await fs.access(serviceAccountPath);
    } catch (error) {
      throw new Error(
        'Service account file not found at firebase/fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json\n' +
        'Please ensure you have the service account key file in the correct location.\n' +
        'If you need to download it:\n' +
        '1. Go to Firebase Console > Project Settings > Service Accounts\n' +
        '2. Generate new private key\n' +
        '3. Save as firebase/fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json'
      );
    }

    const serviceAccount = require(serviceAccountPath);
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });

    logSuccess(`Connected to Production Firebase Project: ${serviceAccount.project_id}`);
    logWarning('You are accessing PRODUCTION data!');
  } else {
    // Emulator mode (default)
    process.env.FIRESTORE_EMULATOR_HOST = FIRESTORE_HOST;
    admin.initializeApp({ projectId: PROJECT_ID });
    logSuccess('Connected to Firebase Emulator');
    logInfo(`Emulator host: ${FIRESTORE_HOST}`);
  }
}

// Initialize Firebase
let db;

// Valid operators for where clauses
const VALID_OPERATORS = ['==', '!=', '<', '<=', '>', '>=', 'in', 'not-in', 'array-contains', 'array-contains-any'];

// Command line argument parser
class CLIParser {
  constructor() {
    this.args = process.argv.slice(2);
    // Filter out production flags before parsing
    this.filteredArgs = this.args.filter(arg => !['--prod', '--production'].includes(arg));
    this.command = this.filteredArgs[0];
    this.collection = this.filteredArgs[1];
    this.options = this.parseOptions();
  }

  parseOptions() {
    const options = {
      limit: 20,
      orderBy: null,
      where: [],
      output: null,
      format: 'pretty', // pretty, json, csv
      fields: null,
      includeId: true,
      subcollection: null, // For deep-search
      maxDepth: 3 // For deep-search
    };

    for (let i = 2; i < this.filteredArgs.length; i++) {
      const arg = this.filteredArgs[i];
      const nextArg = this.filteredArgs[i + 1];

      switch (arg) {
        case '--limit':
          options.limit = parseInt(nextArg) || 20;
          i++;
          break;
        case '--orderBy':
          options.orderBy = { field: nextArg, direction: this.filteredArgs[i + 2] || 'asc' };
          i += 2;
          break;
        case '--where':
          // Enhanced where clause parsing - support multiple formats
          const whereResult = this.parseWhereClause(i);
          if (whereResult.error) {
            throw new Error(whereResult.error);
          }
          options.where.push(whereResult.clause);
          i += whereResult.consumed - 1;
          break;
        case '--output':
          options.output = nextArg;
          i++;
          break;
        case '--format':
          options.format = nextArg;
          i++;
          break;
        case '--fields':
          options.fields = nextArg.split(',');
          i++;
          break;
        case '--no-id':
          options.includeId = false;
          break;
        case '--subcollection':
          options.subcollection = nextArg;
          i++;
          break;
        case '--max-depth':
          options.maxDepth = parseInt(nextArg) || 3;
          i++;
          break;
      }
    }

    return options;
  }

  parseWhereClause(startIndex) {
    const args = this.filteredArgs;

    // Make sure we have at least one argument after --where
    if (startIndex + 1 >= args.length) {
      return {
        error: 'Missing where clause after --where\n' +
          'Expected: --where "field operator value" or --where field=value'
      };
    }

    // Try to parse as a single quoted string first
    const quotedMatch = args[startIndex + 1]?.match(/^["'](.+)["']$/);
    if (quotedMatch) {
      const whereStr = quotedMatch[1];
      const parsed = this.parseWhereString(whereStr);
      if (parsed.error) {
        return { error: parsed.error };
      }
      return { clause: parsed, consumed: 2 };
    }

    // Try to parse as unquoted string with operator
    const whereStr = args[startIndex + 1];

    // Check if it contains an operator in the string itself
    for (const op of VALID_OPERATORS) {
      if (whereStr.includes(op)) {
        const parsed = this.parseWhereString(whereStr);
        if (!parsed.error) {
          return { clause: parsed, consumed: 2 };
        }
      }
    }

    // Check for shorthand equality (field=value)
    if (whereStr.includes('=') && !whereStr.includes('==')) {
      const parsed = this.parseWhereString(whereStr);
      if (!parsed.error) {
        return { clause: parsed, consumed: 2 };
      }
    }

    // Parse as three separate arguments: field operator value
    // Make sure we have enough arguments
    if (startIndex + 3 < args.length) {
      const field = args[startIndex + 1];
      const operator = args[startIndex + 2];
      const value = args[startIndex + 3];

      // Validate operator
      if (VALID_OPERATORS.includes(operator)) {
        return {
          clause: {
            field: field,
            operator: operator,
            value: this.parseValue(value)
          },
          consumed: 4
        };
      }
    }

    // If we get here, we couldn't parse the where clause
    return {
      error: 'Invalid where clause format.\n' +
        'Expected formats:\n' +
        '  --where "field operator value"\n' +
        '  --where field operator value\n' +
        '  --where field=value\n' +
        'Examples:\n' +
        '  --where "status == completed"\n' +
        '  --where status == completed\n' +
        '  --where primaryUserType=1\n' +
        'Got: ' + args.slice(startIndex + 1, Math.min(startIndex + 4, args.length)).join(' ')
    };
  }

  parseWhereString(whereStr) {
    // Try different patterns

    // Pattern 1: field=value (shorthand for ==)
    const equalMatch = whereStr.match(/^(\S+)=([^=].*)$/);
    if (equalMatch) {
      return {
        field: equalMatch[1],
        operator: '==',
        value: this.parseValue(equalMatch[2])
      };
    }

    // Pattern 2: field operator value (with spaces)
    for (const op of VALID_OPERATORS) {
      const regex = new RegExp(`^(\\S+)\\s*${op.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*(.+)$`);
      const match = whereStr.match(regex);
      if (match) {
        return {
          field: match[1],
          operator: op,
          value: this.parseValue(match[2])
        };
      }
    }

    return {
      error: `Could not parse where clause: "${whereStr}"\n` +
        `Expected format: "field operator value"\n` +
        `Valid operators: ${VALID_OPERATORS.join(', ')}`
    };
  }

  parseValue(value) {
    // Remove surrounding quotes if present
    if ((value.startsWith('"') && value.endsWith('"')) ||
      (value.startsWith("'") && value.endsWith("'"))) {
      value = value.slice(1, -1);
    }

    // Try to parse as JSON first
    try {
      return JSON.parse(value);
    } catch (e) {
      // Check for special values
      if (value === 'true') return true;
      if (value === 'false') return false;
      if (value === 'null') return null;

      // Check if it's a number
      const num = Number(value);
      if (!isNaN(num) && value.trim() !== '') return num;

      // Check if it's a date
      const date = new Date(value);
      if (!isNaN(date.getTime()) && value.includes('-')) {
        return admin.firestore.Timestamp.fromDate(date);
      }

      // Return as string
      return value;
    }
  }
}

// Commands
const commands = {
  async list(collection, options) {
    let query = db.collection(collection);

    // Apply filters
    for (const where of options.where) {
      try {
        query = query.where(where.field, where.operator, where.value);
      } catch (error) {
        if (error.message.includes('Invalid query')) {
          throw new Error(
            `Invalid query: ${error.message}\n` +
            `Check that field "${where.field}" exists and supports the "${where.operator}" operator.\n` +
            `Value type: ${typeof where.value}`
          );
        }
        throw error;
      }
    }

    // Apply ordering
    if (options.orderBy) {
      query = query.orderBy(options.orderBy.field, options.orderBy.direction);
    }

    // Apply limit
    query = query.limit(options.limit);

    const snapshot = await query.get();
    const results = [];

    snapshot.forEach(doc => {
      const data = doc.data();
      if (options.includeId) {
        data._id = doc.id;
      }
      results.push(data);
    });

    return results;
  },

  async get(collection, docId) {
    const doc = await db.collection(collection).doc(docId).get();

    if (!doc.exists) {
      throw new Error(`Document ${docId} not found in ${collection}`);
    }

    const data = doc.data();
    data._id = doc.id;
    return data;
  },

  async count(collection, options) {
    let query = db.collection(collection);

    // Apply filters
    for (const where of options.where) {
      query = query.where(where.field, where.operator, where.value);
    }

    const snapshot = await query.get();
    return { count: snapshot.size };
  },

  async query(collection, options) {
    // Same as list but with more emphasis on filtering
    return commands.list(collection, options);
  },

  async subcollections(collection, docId) {
    const docRef = db.collection(collection).doc(docId);
    const subcollections = await docRef.listCollections();

    const results = [];
    for (const subcol of subcollections) {
      const snapshot = await subcol.get();
      results.push({
        name: subcol.id,
        path: `${collection}/${docId}/${subcol.id}`,
        count: snapshot.size
      });
    }

    return results;
  },

  async export(collection, options) {
    const data = await commands.list(collection, { ...options, limit: 10000 });
    const outputPath = options.output || `${collection.replace(/\//g, '_')}_export.json`;

    await fs.writeFile(
      outputPath,
      JSON.stringify(data, null, 2),
      'utf8'
    );

    return {
      message: `Exported ${data.length} documents to ${outputPath}`,
      count: data.length,
      file: outputPath
    };
  },

  async stats(collection) {
    const snapshot = await db.collection(collection).get();
    const fieldStats = {};
    let totalSize = 0;

    snapshot.forEach(doc => {
      const data = doc.data();
      const size = JSON.stringify(data).length;
      totalSize += size;

      // Collect field statistics
      Object.keys(data).forEach(field => {
        if (!fieldStats[field]) {
          fieldStats[field] = { count: 0, types: new Set() };
        }
        fieldStats[field].count++;
        fieldStats[field].types.add(typeof data[field]);
      });
    });

    // Convert sets to arrays for output
    Object.keys(fieldStats).forEach(field => {
      fieldStats[field].types = Array.from(fieldStats[field].types);
    });

    return {
      collection,
      documentCount: snapshot.size,
      totalSizeBytes: totalSize,
      averageSizeBytes: snapshot.size > 0 ? Math.round(totalSize / snapshot.size) : 0,
      fields: fieldStats
    };
  },

  // New deep-search command
  async 'deep-search'(startCollection, options) {
    const results = [];
    const searchedPaths = [];

    const targetSubcollection = options.subcollection;

    if (!targetSubcollection && (!options.where || options.where.length === 0)) {
      throw new Error('Deep search requires either --subcollection or --where filters');
    }

    console.error(`🔍 Starting deep search${targetSubcollection ? ` for subcollection: ${targetSubcollection}` : ''}`);

    // Search in a specific collection if provided
    const collections = startCollection ? [db.collection(startCollection)] : [];

    async function searchCollection(parentPath, parentId, depth = 0) {
      if (depth > options.maxDepth) return;

      try {
        // Get the parent document reference
        let docRef;
        if (parentPath === '') {
          // Root level - search all collections
          const cols = await db.listCollections();
          for (const col of cols) {
            await searchSubcollection(col.id, null, col, depth);
          }
          return;
        } else {
          docRef = db.doc(`${parentPath}/${parentId}`);
        }

        // List all subcollections
        const subcollections = await docRef.listCollections();

        for (const subcol of subcollections) {
          await searchSubcollection(`${parentPath}/${parentId}`, subcol.id, subcol, depth);
        }
      } catch (error) {
        // Silently skip errors (document might not exist)
      }
    }

    async function searchSubcollection(parentPath, subcollectionName, collectionRef, depth) {
      const fullPath = parentPath ? `${parentPath}/${subcollectionName}` : subcollectionName;

      // If we're looking for a specific subcollection name
      if (targetSubcollection && subcollectionName !== targetSubcollection) {
        // Still need to search deeper
        const snapshot = await collectionRef.get();
        for (const doc of snapshot.docs) {
          await searchCollection(fullPath, doc.id, depth + 1);
        }
        return;
      }

      searchedPaths.push(fullPath);

      // Apply query filters
      let query = collectionRef;

      for (const where of options.where) {
        try {
          query = query.where(where.field, where.operator, where.value);
        } catch (error) {
          // Skip if field doesn't exist in this collection
          return;
        }
      }

      if (options.orderBy) {
        query = query.orderBy(options.orderBy.field, options.orderBy.direction);
      }

      if (options.limit && results.length >= options.limit) {
        return;
      }

      const snapshot = await query.get();

      snapshot.forEach(doc => {
        if (results.length < options.limit) {
          const data = doc.data();
          if (options.includeId) {
            data._id = doc.id;
            data._path = `${fullPath}/${doc.id}`;
          }
          results.push(data);
        }
      });

      // Search deeper
      if (depth < options.maxDepth) {
        for (const doc of snapshot.docs) {
          await searchCollection(fullPath, doc.id, depth + 1);
        }
      }
    }

    // Start the search
    await searchCollection('', '', 0);

    console.error(`✅ Search complete. Searched ${searchedPaths.length} collections.`);

    return results;
  }
};

// Output formatters
const formatters = {
  pretty(data) {
    console.log(JSON.stringify(data, null, 2));
  },

  json(data) {
    console.log(JSON.stringify(data));
  },

  csv(data) {
    if (!Array.isArray(data)) {
      data = [data];
    }

    if (data.length === 0) {
      console.log('No data');
      return;
    }

    // Get all unique keys
    const keys = new Set();
    data.forEach(item => Object.keys(item).forEach(key => keys.add(key)));
    const headers = Array.from(keys);

    // Print headers
    console.log(headers.join(','));

    // Print rows
    data.forEach(item => {
      const row = headers.map(header => {
        const value = item[header];
        if (value === undefined || value === null) return '';
        if (typeof value === 'object') return JSON.stringify(value);
        return String(value).includes(',') ? `"${value}"` : value;
      });
      console.log(row.join(','));
    });
  }
};

// Help text
function showHelp() {
  console.log(`
Firestore Query Tool

Usage: node firestore-query.js <command> <collection> [options]

Commands:
  list <collection>                    List documents in a collection
  get <collection> <docId>             Get a specific document
  count <collection>                   Count documents in a collection
  query <collection>                   Query documents with filters
  subcollections <collection> <docId>  List subcollections of a document
  deep-search [collection]             Search across all subcollections
  export <collection>                  Export collection to JSON file
  stats <collection>                   Show statistics about a collection

Options:
  --limit <n>                         Limit results (default: 20)
  --orderBy <field> [asc|desc]        Order results
  --where <clause>                    Filter results (can use multiple times)
  --output <file>                     Output file for export command
  --format <pretty|json|csv>          Output format (default: pretty)
  --fields <field1,field2>            Select specific fields
  --no-id                             Don't include document IDs
  --subcollection <name>              For deep-search: target subcollection name
  --max-depth <n>                     For deep-search: max depth (default: 3)
  --prod, --production                Use production Firebase project (requires service account)

Where Clause Formats:
  --where "field == value"            Quoted format with operator
  --where field == value              Unquoted format (three arguments)
  --where field=value                 Shorthand for equality
  --where "status != pending"         Other operators work the same way

Operators for --where:
  ==, !=, <, <=, >, >=, in, not-in, array-contains, array-contains-any

Examples (Emulator Mode - Default):
  # List all tenants
  node firestore-query.js list tenants

  # Get a specific trip
  node firestore-query.js get tenants/fiaranow/trips TRIP_ID

  # Query with different where formats
  node firestore-query.js query mobile_users --where primaryUserType=1
  node firestore-query.js query mobile_users --where "primaryUserType == 1"
  node firestore-query.js query mobile_users --where primaryUserType == 1

  # Deep search for a document across all subcollections
  node firestore-query.js deep-search --subcollection driver_documents --where "id == VqDoDb1FaumLpajsLFin"
  
  # Find all pending driver documents
  node firestore-query.js deep-search --subcollection driver_documents --where status=pendingReview

  # List recent trips
  node firestore-query.js list tenants/fiaranow/trips --orderBy createdAt desc --limit 10

  # Export all mobile users
  node firestore-query.js export mobile_users --output users.json

  # Count trips by status
  node firestore-query.js count tenants/fiaranow/trips --where status=completed

Examples (Production Mode):
  # Access production data (requires service account key)
  node firestore-query.js list tenants --prod
  node firestore-query.js get tenants/fiaranow/trips TRIP_ID --production
  node firestore-query.js query mobile_users --where primaryUserType=1 --prod
  node firestore-query.js list tenants/fiaranow/trips --where status=reserved --prod

Environment Variables:
  FIRESTORE_EMULATOR_HOST   Firestore emulator host (default: localhost:8080) - emulator mode only
  FIREBASE_PROJECT_ID       Firebase project ID (default: fiaranow) - emulator mode only

Production Mode Requirements:
  Service account key file at: firebase/fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json
  
  To download the service account key:
  1. Go to Firebase Console > Project Settings > Service Accounts
  2. Generate new private key
  3. Save as firebase/fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json
`);
}

// Main execution - Updated to work with async Firebase initialization
async function main() {
  try {
    // Initialize Firebase first
    await initializeFirebase();
    db = admin.firestore();

    const cli = new CLIParser();

    if (!cli.command || cli.command === 'help' || cli.command === '--help') {
      showHelp();
      process.exit(0);
    }

    if (!commands[cli.command]) {
      logError(`Unknown command: "${cli.command}"`);
      console.error('Available commands: ' + Object.keys(commands).join(', '));
      console.error('\nRun with --help to see usage examples');
      process.exit(1);
    }

    if (!cli.collection && cli.command !== 'help' && cli.command !== 'deep-search') {
      logError('Collection path is required');
      console.error('Usage: firestore-query.js <command> <collection> [options]');
      console.error('Example: firestore-query.js list mobile_users');
      process.exit(1);
    }

    // Execute command
    let result;
    if (cli.command === 'get' || cli.command === 'subcollections') {
      const docId = cli.filteredArgs[2];
      if (!docId) {
        logError(`Document ID is required for ${cli.command} command`);
        console.error(`Usage: firestore-query.js ${cli.command} <collection> <docId>`);
        console.error(`Example: firestore-query.js ${cli.command} mobile_users USER_ID`);
        process.exit(1);
      }
      result = await commands[cli.command](cli.collection, docId, cli.options);
    } else {
      result = await commands[cli.command](cli.collection, cli.options);
    }

    // Format output
    const formatter = formatters[cli.options.format] || formatters.pretty;
    formatter(result);

  } catch (error) {
    logError(`Error: ${error.message}`);
    if (error.code) {
      console.error('Firebase Error Code:', error.code);
    }

    // Provide helpful context for common errors
    if (error.message.includes('permission')) {
      console.error('\n💡 This looks like a permissions error. Make sure:');
      if (useProduction) {
        console.error('   - Your service account has the correct permissions');
        console.error('   - The service account key file is valid and not expired');
      } else {
        console.error('   - The emulators are running');
        console.error('   - You have the correct FIRESTORE_EMULATOR_HOST set');
      }
    } else if (error.message.includes('connect')) {
      console.error('\n💡 Cannot connect to Firestore. Make sure:');
      if (useProduction) {
        console.error('   - You have internet connection');
        console.error('   - The service account key is valid');
        console.error('   - Your Firebase project exists');
      } else {
        console.error('   - The emulators are running (use dev-start.sh)');
        console.error('   - FIRESTORE_EMULATOR_HOST is set correctly (current: ' + FIRESTORE_HOST + ')');
      }
    }

    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
} 