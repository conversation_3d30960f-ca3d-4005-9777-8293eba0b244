# Firebase Emulator Debug Tools

This directory contains tools for querying, updating, and debugging data in Firebase emulators during development.

The Firestore query tool supports production data access! Use `--prod` or `--production` flags to query live Firebase data.

## Prerequisites

Make sure the emulators are running (use `dev-start.sh` or run manually):
```bash
firebase emulators:start --only functions,firestore,auth,pubsub --inspect-functions 9229
```

## Installation

First, ensure you have the required dependencies installed:
```bash
cd firebase/functions
npm install
```

## Usage

### Using the Wrapper Script (Recommended)

```bash
# All commands should be run from the project root directory
firebase/emulator-tools/emulator-query fs <command> [options]
firebase/emulator-tools/emulator-query update <command> [options]
firebase/emulator-tools/emulator-query auth <command> [options]
```

### Direct Usage

```bash
# From project root
node firebase/emulator-tools/firestore-query.js <command> [options]
node firebase/emulator-tools/firestore-update.js <command> [options]
node firebase/emulator-tools/auth-query.js <command> [options]

# Or from within firebase directory
cd firebase
node emulator-tools/firestore-query.js <command> [options]
node emulator-tools/firestore-update.js <command> [options]
node emulator-tools/auth-query.js <command> [options]
```

### Basic Examples

```bash
# List all tenants
firebase/emulator-tools/emulator-query fs list tenants

# Get specific tenant
firebase/emulator-tools/emulator-query fs get tenants fiaranow

# List trips with limit
firebase/emulator-tools/emulator-query fs list tenants/fiaranow/trips --limit 10

# Query drivers using different where formats
firebase/emulator-tools/emulator-query fs query mobile_users --where primaryUserType=1
firebase/emulator-tools/emulator-query fs query mobile_users --where "primaryUserType == 1"
firebase/emulator-tools/emulator-query fs query mobile_users --where primaryUserType == 1

# List all auth users
firebase/emulator-tools/emulator-query auth list

# Get specific auth user
firebase/emulator-tools/emulator-query auth get USER_UID
```

### Deep Search (NEW!)

Search across all subcollections to find documents anywhere in the database:

```bash
# Find a specific document by ID in any subcollection
firebase/emulator-tools/emulator-query fs deep-search --subcollection driver_documents --where "id == VqDoDb1FaumLpajsLFin"

# Find all pending driver documents across all users
firebase/emulator-tools/emulator-query fs deep-search --subcollection driver_documents --where status=pendingReview

# Search with multiple filters
firebase/emulator-tools/emulator-query fs deep-search --subcollection driver_documents \
  --where documentType=license \
  --where status=approved

# Limit search depth (default is 3 levels)
firebase/emulator-tools/emulator-query fs deep-search --subcollection tenant_states --max-depth 2
```

### Update Operations

```bash
# Update a single document
firebase/emulator-tools/emulator-query update update tenants/fiaranow/trips TRIP_ID --set status completed

# Update multiple documents with flexible where syntax
firebase/emulator-tools/emulator-query update updateMany tenants/fiaranow/trips --where status=pending --set status cancelled
firebase/emulator-tools/emulator-query update updateMany tenants/fiaranow/trips --where "status == pending" --set status cancelled
firebase/emulator-tools/emulator-query update updateMany mobile_users --where "primaryUserType != 0" --set verified true

# Set/create a document
firebase/emulator-tools/emulator-query update set mobile_users USER_ID --data '{"name":"John","age":30}'

# Increment numeric fields
firebase/emulator-tools/emulator-query update increment tenants/fiaranow/trips TRIP_ID --increment distanceKm 5.2

# Complex update with multiple operations
firebase/emulator-tools/emulator-query update update mobile_users USER_ID \
  --set "profile.lastActive" "2024-01-15" \
  --increment loginCount 1 \
  --array-union tags premium \
  --delete oldField

# Dry run to preview changes
firebase/emulator-tools/emulator-query update updateMany tenants/fiaranow/trips \
  --where "createdAt < 2024-01-01" \
  --set archived true \
  --dry-run

# Update from JSON file
firebase/emulator-tools/emulator-query update set tenants/fiaranow/configurations trip_default \
  --from-file config.json \
  --merge

# Batch update for atomic operations
firebase/emulator-tools/emulator-query update updateMany mobile_users \
  --where primaryUserType=1 \
  --set "driverStatus" "inactive" \
  --batch
```

### Delete Operations

```bash
# Delete a single document (with subcollections by default)
firebase/emulator-tools/emulator-query update delete mobile_users USER_ID

# Delete without subcollections
firebase/emulator-tools/emulator-query update delete mobile_users USER_ID --no-recursive

# Delete multiple documents matching query
firebase/emulator-tools/emulator-query update deleteMany tenants/fiaranow/trips --where status=cancelled

# Delete with limit
firebase/emulator-tools/emulator-query update deleteMany tenants/fiaranow/trips \
  --where "createdAt < 2023-01-01" \
  --limit 100

# Dry run to preview what would be deleted
firebase/emulator-tools/emulator-query update deleteMany tenants/fiaranow/trips \
  --where status=cancelled \
  --dry-run

# Batch delete for better performance
firebase/emulator-tools/emulator-query update deleteMany tenants/fiaranow/event_logs \
  --where "createdAt < 2024-01-01" \
  --batch

# Delete a document with all its subcollections
firebase/emulator-tools/emulator-query update delete vehicles VEHICLE_ID

# Delete user and all tenant states
firebase/emulator-tools/emulator-query update delete mobile_users USER_ID
```

### Advanced Queries

```bash
# Count documents in a collection
firebase/emulator-tools/emulator-query fs count tenants/fiaranow/trips

# Count with filters
firebase/emulator-tools/emulator-query fs count tenants/fiaranow/trips --where status=completed

# Export collection to JSON file
firebase/emulator-tools/emulator-query fs export mobile_users --output users.json

# Export with CSV format
firebase/emulator-tools/emulator-query fs export mobile_users --format csv --output users.csv

# Query with multiple filters
firebase/emulator-tools/emulator-query fs query tenants/fiaranow/trips \
  --where status=inProgress \
  --where "createdAt > 2024-01-01"

# Get subcollections of a document
firebase/emulator-tools/emulator-query fs subcollections mobile_users USER_UID

# List specific subcollection
firebase/emulator-tools/emulator-query fs list mobile_users/USER_UID/driver_documents

# Active drivers from tenant states
firebase/emulator-tools/emulator-query fs query mobile_users/{uid}/tenant_states --where isServiceActive=true

# Get collection statistics
firebase/emulator-tools/emulator-query fs stats mobile_users

# List vehicle linkings for tenant
firebase/emulator-tools/emulator-query fs list tenants/fiaranow/vehicles_linking
```

## Available Collections

### Global Collections
- `tenants` - Tenant organizations
- `mobile_users` - All users (drivers and passengers)
- `admin_users` - Admin accounts
- `vehicles` - Vehicle registry
- `vehicle_assignments` - Assignment history
- `global_configurations` - App-wide settings

### Tenant-Specific Collections (under `/tenants/{tenantId}/`)
- `trips` - Trip records
- `payments` - Payment transactions
- `configurations` - Tenant settings
- `event_logs` - Audit logs
- `feedbacks` - User feedback
- `chat_threads` - Chat conversations
- `admin_notifications` - Admin notifications
- `vehicles_linking` - Vehicle-tenant relationships

### Subcollections
- `mobile_users/{uid}/tenant_states` - User state per tenant
- `mobile_users/{uid}/driver_documents` - Driver documents
- `vehicles/{vehicleId}/vehicle_documents` - Vehicle documents
- `trips/{tripId}/logs` - Trip location logs
- `chat_threads/{threadId}/messages` - Chat messages
- `admin_users/{uid}/tenants` - Admin tenant access

## Quick Examples for AI Agents

```bash
# List all tenants
firebase/emulator-tools/emulator-query fs list tenants

# Get mobile users who are drivers (flexible syntax)
firebase/emulator-tools/emulator-query fs query mobile_users --where primaryUserType=1

# Count trips in progress
firebase/emulator-tools/emulator-query fs count tenants/fiaranow/trips --where status=inProgress

# Get recent trips
firebase/emulator-tools/emulator-query fs list tenants/fiaranow/trips --orderBy "createdAt desc" --limit 5

# Export all mobile users
firebase/emulator-tools/emulator-query fs export mobile_users --format json

# Get user subcollections
firebase/emulator-tools/emulator-query fs subcollections mobile_users USER_UID

# Deep search for a document
firebase/emulator-tools/emulator-query fs deep-search --subcollection driver_documents --where "id == VqDoDb1FaumLpajsLFin"

# List auth users
firebase/emulator-tools/emulator-query auth list

# Get trip configuration
firebase/emulator-tools/emulator-query fs get tenants/fiaranow/configurations trip_default

# Update a trip status
firebase/emulator-tools/emulator-query update update tenants/fiaranow/trips TRIP_ID --set status completed

# Update user profile
firebase/emulator-tools/emulator-query update update mobile_users USER_ID --set "profile.name" "John Doe" --set "profile.phone" "+************"

# Mark old trips as archived
firebase/emulator-tools/emulator-query update updateMany tenants/fiaranow/trips --where "createdAt < 2024-01-01" --set archived true --dry-run

# Delete a document with subcollections
firebase/emulator-tools/emulator-query update delete mobile_users USER_ID

# Delete old event logs
firebase/emulator-tools/emulator-query update deleteMany tenants/fiaranow/event_logs --where "createdAt < 2024-01-01" --batch
```

## Where Clause Formats (NEW!)

The tools now support multiple where clause formats for flexibility:

### Format 1: Shorthand (field=value)
```bash
--where status=completed
--where primaryUserType=1
--where isActive=true
```

### Format 2: Quoted with Operator
```bash
--where "status == completed"
--where "age > 18"
--where "createdAt < 2024-01-01"
--where "tags array-contains premium"
```

### Format 3: Three Arguments
```bash
--where status == completed
--where age > 18
--where tags array-contains premium
```

## Query Operators

When using `--where` clauses, the following operators are supported:
- `==` (equals) - can also use `=` shorthand
- `!=` (not equals)
- `<`, `<=`, `>`, `>=` (comparisons)
- `in` (value in array)
- `not-in` (value not in array)
- `array-contains`
- `array-contains-any`

## Command Options

### Query Options
- `--limit <n>` - Limit number of results
- `--orderBy "<field> [asc|desc]"` - Sort results (e.g., `--orderBy "createdAt desc"`)
- `--where <clause>` - Filter results (see Where Clause Formats above)
- `--fields <field1,field2>` - Select specific fields
- `--no-id` - Don't include document IDs
- `--output <filename>` - Output file for export command
- `--format <pretty|json|csv>` - Output format (see below)
- `--subcollection <name>` - For deep-search: target subcollection name
- `--max-depth <n>` - For deep-search: max depth (default: 3)

### Update Options
- `--data <json>` - JSON data to update/set
- `--set <field> <value>` - Set a specific field value
- `--increment <field> <value>` - Increment a numeric field
- `--array-union <field> <value>` - Add value to array field
- `--array-remove <field> <value>` - Remove value from array field
- `--delete <field>` - Delete a field
- `--merge` - Merge data instead of overwrite (for set command)
- `--dry-run` - Preview changes without applying them
- `--from-file <file>` - Load data from JSON file
- `--batch` - Use batch for atomic updates (updateMany/deleteMany)
- `--no-recursive` - Don't delete subcollections (for delete/deleteMany commands)

## Output Formats

The tools support multiple output formats:
- `--format pretty` (default) - Human-readable JSON with indentation
- `--format json` - Compact JSON for piping to other tools
- `--format csv` - CSV format for spreadsheet import

## Environment Variables

- `FIRESTORE_EMULATOR_HOST` - Firestore emulator host (default: localhost:8080)
- `FIREBASE_AUTH_EMULATOR_HOST` - Auth emulator host (default: localhost:9099)
- `FIREBASE_PROJECT_ID` - Project ID (default: fiaranow)

## Error Messages (IMPROVED!)

The tools now provide helpful error messages and suggestions:

```bash
# Invalid operator
❌ Error: Invalid operator "=" in where clause.
Valid operators are: ==, !=, <, <=, >, >=, in, not-in, array-contains, array-contains-any
Example: --where status == completed
         --where "age > 18"
         --where primaryUserType=1

# Connection issues
❌ Error: connect ECONNREFUSED
💡 Cannot connect to Firestore emulator. Make sure:
   - The emulators are running (use dev-start.sh)
   - FIRESTORE_EMULATOR_HOST is set correctly (current: localhost:8080)

# Document not found
❌ Error: Document USER_ID not found in mobile_users
💡 Document or collection not found. Check:
   - The collection path is correct
   - The document ID exists
   - You have the right tenant path (e.g., tenants/fiaranow/...)
```

## Tips

1. All commands bypass security rules since they use the Admin SDK
2. The tools work only with emulators, not production
3. Use `--limit` to avoid overwhelming output
4. Export data for backup or analysis
5. Use `stats` command to understand collection structure
6. Use `deep-search` when you need to find documents in subcollections
7. Try the shorthand where syntax (`field=value`) for simpler queries
8. Error messages now include helpful suggestions for common issues 

## Production Mode Support (NEW!)

The `firestore-query.js` tool now supports both emulator and production modes:

### Production Mode Usage

```bash
# Query production data (requires service account key)
firebase/emulator-tools/emulator-query fs list tenants --prod
firebase/emulator-tools/emulator-query fs get tenants/fiaranow/trips TRIP_ID --production
firebase/emulator-tools/emulator-query fs query mobile_users --where primaryUserType=1 --prod

# Search notification reminders in production
firebase/emulator-tools/emulator-query fs list tenants/fiaranow/trips/TRIP_ID/notification_reminders --prod
```

### Requirements for Production Mode

1. **Service Account Key**: Place the Firebase service account key at:
   ```
   firebase/fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json
   ```

2. **Download Instructions**:
   - Go to Firebase Console > Project Settings > Service Accounts
   - Generate new private key
   - Save as `firebase/fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json`

3. **Safety**: Production mode will display clear warnings when accessing live data.

### When to Use Production Mode

- Debugging production issues
- Analyzing live notification reminders
- Investigating data inconsistencies
- Monitoring production trip statuses

⚠️ **Warning**: Always use caution when accessing production data. The tool is read-only but shows live user information. 