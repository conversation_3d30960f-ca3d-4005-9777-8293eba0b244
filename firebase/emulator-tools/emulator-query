#!/bin/bash

# Firebase Query Tool Wrapper
# Simplifies access to Firebase query tools for both emulator and production

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Check if we're in a subdirectory and find project root
PROJECT_ROOT="$SCRIPT_DIR/../.."

# Change to project root for consistent execution
cd "$PROJECT_ROOT"

# Parse the first argument as the tool type
TOOL_TYPE=$1
shift  # Remove first argument

case "$TOOL_TYPE" in
  firestore|fs)
    node firebase/emulator-tools/firestore-query.js "$@"
    ;;
  update|up)
    node firebase/emulator-tools/firestore-update.js "$@"
    ;;
  auth)
    node firebase/emulator-tools/auth-query.js "$@"
    ;;
  help|--help|-h|"")
    echo "Firebase Query Tool Wrapper"
    echo ""
    echo "Usage: emulator-query <tool> <command> [options]"
    echo ""
    echo "Tools:"
    echo "  firestore, fs    Query Firestore (emulator or production)"
    echo "  update, up       Update Firestore emulator data"
    echo "  auth             Query Auth emulator"
    echo ""
    echo "Examples:"
    echo "  # Emulator mode (default)"
    echo "  emulator-query fs list tenants"
    echo "  emulator-query fs get tenants fiaranow"
    echo "  emulator-query fs count mobile_users"
    echo ""
    echo "  # Production mode (requires service account)"
    echo "  emulator-query fs list tenants --prod"
    echo "  emulator-query fs get tenants/fiaranow/trips TRIP_ID --production"
    echo "  emulator-query fs query mobile_users --where primaryUserType=1 --prod"
    echo ""
    echo "  # Other tools (emulator only)"
    echo "  emulator-query update update mobile_users USER_ID --set name John"
    echo "  emulator-query auth list"
    echo ""
    echo "For tool-specific help:"
    echo "  emulator-query fs help"
    echo "  emulator-query update help"
    echo "  emulator-query auth help"
    ;;
  *)
    echo "Unknown tool: $TOOL_TYPE"
    echo "Use 'emulator-query help' for usage information"
    exit 1
    ;;
esac 