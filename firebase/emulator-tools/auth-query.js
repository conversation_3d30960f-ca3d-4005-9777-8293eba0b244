#!/usr/bin/env node

/**
 * Firebase Auth Emulator Query Tool
 * 
 * CLI tool for querying Firebase Auth emulator data.
 */

const path = require('path');

// Find firebase-admin from the functions directory
const functionsDir = path.join(__dirname, '..', 'functions');
const admin = require(path.join(functionsDir, 'node_modules', 'firebase-admin'));

// Configuration
const AUTH_HOST = process.env.FIREBASE_AUTH_EMULATOR_HOST || 'localhost:9099';
const PROJECT_ID = process.env.FIREBASE_PROJECT_ID || 'fiaranow';

// Initialize Admin SDK
process.env.FIREBASE_AUTH_EMULATOR_HOST = AUTH_HOST;
admin.initializeApp({ projectId: PROJECT_ID });
const auth = admin.auth();

// Commands
const commands = {
  async list(options = {}) {
    const listResult = await auth.listUsers(options.limit || 1000);
    const users = listResult.users.map(user => ({
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      phoneNumber: user.phoneNumber,
      disabled: user.disabled,
      emailVerified: user.emailVerified,
      creationTime: user.metadata.creationTime,
      lastSignInTime: user.metadata.lastSignInTime,
      providers: user.providerData.map(p => p.providerId)
    }));

    return users;
  },

  async get(uid) {
    try {
      const user = await auth.getUser(uid);
      return {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        phoneNumber: user.phoneNumber,
        disabled: user.disabled,
        emailVerified: user.emailVerified,
        metadata: {
          creationTime: user.metadata.creationTime,
          lastSignInTime: user.metadata.lastSignInTime,
          lastRefreshTime: user.metadata.lastRefreshTime
        },
        customClaims: user.customClaims || {},
        providers: user.providerData,
        tokensValidAfterTime: user.tokensValidAfterTime
      };
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        throw new Error(`User ${uid} not found`);
      }
      throw error;
    }
  },

  async create(data) {
    const userRecord = await auth.createUser({
      email: data.email,
      password: data.password,
      displayName: data.displayName,
      phoneNumber: data.phoneNumber,
      emailVerified: data.emailVerified || false,
      disabled: data.disabled || false
    });

    return {
      message: 'User created successfully',
      uid: userRecord.uid,
      email: userRecord.email
    };
  },

  async update(uid, data) {
    const updates = {};
    if (data.email) updates.email = data.email;
    if (data.password) updates.password = data.password;
    if (data.displayName !== undefined) updates.displayName = data.displayName;
    if (data.phoneNumber !== undefined) updates.phoneNumber = data.phoneNumber;
    if (data.emailVerified !== undefined) updates.emailVerified = data.emailVerified;
    if (data.disabled !== undefined) updates.disabled = data.disabled;

    const userRecord = await auth.updateUser(uid, updates);

    return {
      message: 'User updated successfully',
      uid: userRecord.uid
    };
  },

  async delete(uid) {
    await auth.deleteUser(uid);
    return {
      message: `User ${uid} deleted successfully`
    };
  },

  async setClaims(uid, claims) {
    await auth.setCustomUserClaims(uid, claims);
    return {
      message: `Custom claims set for user ${uid}`,
      claims
    };
  },

  async count() {
    let count = 0;
    let pageToken;

    do {
      const listResult = await auth.listUsers(1000, pageToken);
      count += listResult.users.length;
      pageToken = listResult.pageToken;
    } while (pageToken);

    return { count };
  }
};

// CLI argument parser
function parseArgs() {
  const args = process.argv.slice(2);
  const command = args[0];
  const params = args.slice(1);

  return { command, params };
}

// Help text
function showHelp() {
  console.log(`
Firebase Auth Emulator Query Tool

Usage: node auth-query.js <command> [parameters]

Commands:
  list                      List all users
  get <uid>                 Get a specific user
  create <json>             Create a new user (JSON format)
  update <uid> <json>       Update user data (JSON format)
  delete <uid>              Delete a user
  setClaims <uid> <json>    Set custom claims for a user
  count                     Count total users

Examples:
  # List all users
  node auth-query.js list

  # Get a specific user
  node auth-query.js get USER_UID

  # Create a new user
  node auth-query.js create '{"email":"<EMAIL>","password":"testpass123","displayName":"Test User"}'

  # Update user
  node auth-query.js update USER_UID '{"displayName":"Updated Name","emailVerified":true}'

  # Set custom claims
  node auth-query.js setClaims USER_UID '{"role":"admin","tenant":"fiaranow"}'

  # Delete a user
  node auth-query.js delete USER_UID

Environment Variables:
  FIREBASE_AUTH_EMULATOR_HOST   Auth emulator host (default: localhost:9099)
  FIREBASE_PROJECT_ID           Firebase project ID (default: fiaranow)
`);
}

// Main execution
async function main() {
  try {
    const { command, params } = parseArgs();

    if (!command || command === 'help' || command === '--help') {
      showHelp();
      process.exit(0);
    }

    let result;

    switch (command) {
      case 'list':
        result = await commands.list();
        break;

      case 'get':
        if (!params[0]) {
          console.error('User UID is required');
          process.exit(1);
        }
        result = await commands.get(params[0]);
        break;

      case 'create':
        if (!params[0]) {
          console.error('User data (JSON) is required');
          process.exit(1);
        }
        result = await commands.create(JSON.parse(params[0]));
        break;

      case 'update':
        if (!params[0] || !params[1]) {
          console.error('User UID and update data (JSON) are required');
          process.exit(1);
        }
        result = await commands.update(params[0], JSON.parse(params[1]));
        break;

      case 'delete':
        if (!params[0]) {
          console.error('User UID is required');
          process.exit(1);
        }
        result = await commands.delete(params[0]);
        break;

      case 'setClaims':
        if (!params[0] || !params[1]) {
          console.error('User UID and claims (JSON) are required');
          process.exit(1);
        }
        result = await commands.setClaims(params[0], JSON.parse(params[1]));
        break;

      case 'count':
        result = await commands.count();
        break;

      default:
        console.error(`Unknown command: ${command}`);
        showHelp();
        process.exit(1);
    }

    console.log(JSON.stringify(result, null, 2));

  } catch (error) {
    console.error('Error:', error.message);
    if (error.code) {
      console.error('Code:', error.code);
    }
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
} 