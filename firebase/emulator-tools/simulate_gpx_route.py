#!/usr/bin/env python3
"""
GPX Route Simulator for iOS Simulator
Converts GPX file to simctl location waypoints
"""

import xml.etree.ElementTree as ET
import subprocess
import sys
import os
import json
import re

def get_available_simulators():
    """Get list of available simulators"""
    try:
        result = subprocess.run(['xcrun', 'simctl', 'list', 'devices', '--json'], 
                              capture_output=True, text=True, check=True)
        data = json.loads(result.stdout)
        
        simulators = []
        for runtime, devices in data['devices'].items():
            if 'Unavailable' in runtime:
                continue
            for device in devices:
                simulators.append({
                    'name': device['name'],
                    'uuid': device['udid'],
                    'state': device['state'],
                    'runtime': runtime
                })
        
        return simulators
    except Exception as e:
        print(f"❌ Error getting simulators: {e}")
        return []

def list_simulators(show_all=False):
    """List available simulators"""
    simulators = get_available_simulators()
    
    if not simulators:
        print("❌ No simulators found")
        return
    
    booted_sims = [s for s in simulators if s['state'] == 'Booted']
    
    if not show_all and not booted_sims:
        print("❌ No booted simulators found")
        print("💡 Use --list-all to see all available simulators")
        return
    
    if not show_all:
        # Show only booted simulators
        print("📱 Booted Simulators:")
        print("-" * 80)
        for sim in booted_sims:
            runtime = sim['runtime'].replace('com.apple.CoreSimulator.SimRuntime.', '')
            print(f"   {sim['name']} ({runtime})")
            print(f"   UUID: {sim['uuid']}")
            print()
    else:
        # Show all simulators
        print("📱 Available Simulators:")
        print("-" * 80)
        
        shutdown_sims = [s for s in simulators if s['state'] == 'Shutdown']
        
        if booted_sims:
            print("🟢 BOOTED:")
            for sim in booted_sims:
                runtime = sim['runtime'].replace('com.apple.CoreSimulator.SimRuntime.', '')
                print(f"   {sim['name']} ({runtime})")
                print(f"   UUID: {sim['uuid']}")
                print()
        
        if shutdown_sims:
            print("⚪ SHUTDOWN:")
            for sim in shutdown_sims:
                runtime = sim['runtime'].replace('com.apple.CoreSimulator.SimRuntime.', '')
                print(f"   {sim['name']} ({runtime})")
                print(f"   UUID: {sim['uuid']}")
                print()

def find_simulator(identifier):
    """Find simulator by name or UUID"""
    simulators = get_available_simulators()
    
    # If identifier is 'booted', return it as-is
    if identifier.lower() == 'booted':
        return 'booted'
    
    # Try exact UUID match
    for sim in simulators:
        if sim['uuid'] == identifier:
            return sim['uuid']
    
    # Try name match (case insensitive) - prefer booted simulators
    booted_matches = []
    shutdown_matches = []
    
    for sim in simulators:
        if sim['name'].lower() == identifier.lower():
            if sim['state'] == 'Booted':
                booted_matches.append(sim)
            else:
                shutdown_matches.append(sim)
    
    # Return booted simulator if available, otherwise first shutdown match
    if booted_matches:
        return booted_matches[0]['uuid']
    elif shutdown_matches:
        print(f"⚠️  Found '{identifier}' but it's shutdown. Consider using UUID for specific device.")
        return shutdown_matches[0]['uuid']
    
    # Try partial name match - prefer booted simulators
    booted_partial = []
    shutdown_partial = []
    
    for sim in simulators:
        if identifier.lower() in sim['name'].lower():
            if sim['state'] == 'Booted':
                booted_partial.append(sim)
            else:
                shutdown_partial.append(sim)
    
    # Return booted simulator if available, otherwise first shutdown match
    if booted_partial:
        return booted_partial[0]['uuid']
    elif shutdown_partial:
        print(f"⚠️  Found partial match '{shutdown_partial[0]['name']}' but it's shutdown.")
        return shutdown_partial[0]['uuid']
    
    return None

def extract_coordinates_from_gpx(gpx_file):
    """Extract coordinates from GPX file"""
    if not os.path.exists(gpx_file):
        print(f"❌ GPX file not found: {gpx_file}")
        return []
    
    try:
        tree = ET.parse(gpx_file)
        root = tree.getroot()
        
        # Find all track points
        namespace = {'gpx': 'http://www.topografix.com/GPX/1/1'}
        track_points = root.findall('.//gpx:trkpt', namespace)
        
        coords = []
        for point in track_points:
            lat = point.get('lat')
            lon = point.get('lon')
            coords.append(f'{lat},{lon}')
        
        print(f"📍 Extracted {len(coords)} coordinates from GPX file")
        return coords
        
    except Exception as e:
        print(f"❌ Error parsing GPX file: {e}")
        return []

def simulate_route(coords, device='booted', speed=10, interval=1, simplify_factor=5):
    """Simulate route using simctl location start"""
    
    if len(coords) < 2:
        print("❌ Need at least 2 coordinates for route simulation")
        return False
    
    # Simplify route to avoid command line length limits
    simplified_coords = coords[::simplify_factor]  # Take every Nth coordinate
    
    # Ensure we include the last coordinate
    if coords[-1] not in simplified_coords:
        simplified_coords.append(coords[-1])
    
    print(f"🎯 Simulating route with {len(simplified_coords)} waypoints")
    print(f"📱 Target device: {device}")
    print(f"⚡ Speed: {speed} m/s, Update interval: {interval}s")
    print(f"📍 Start: {simplified_coords[0]}")
    print(f"🏁 End: {simplified_coords[-1]}")
    
    # Build simctl command
    cmd = [
        'xcrun', 'simctl', 'location', device, 'start',
        f'--speed={speed}',
        f'--interval={interval}',
        '--'  # Prevent negative coordinate parsing issues
    ] + simplified_coords
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✅ Route simulation started successfully!")
        print("📱 Check your iOS simulator - location should be updating")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting simulation: {e}")
        print(f"Command output: {e.stdout}")
        print(f"Command error: {e.stderr}")
        return False

def stop_simulation(device='booted'):
    """Stop current location simulation"""
    try:
        subprocess.run(['xcrun', 'simctl', 'location', device, 'clear'], check=True)
        print(f"🛑 Location simulation stopped on {device}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error stopping simulation: {e}")
        return False

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python3 simulate_gpx_route.py <gpx_file> [device] [speed] [interval] [simplify_factor]")
        print()
        print("Arguments:")
        print("  gpx_file        Path to GPX file")
        print("  device          Simulator UUID, name, or 'booted' (default: booted)")
        print("  speed           Meters per second (default: 10)")
        print("  interval        Update interval in seconds (default: 1)")
        print("  simplify_factor Take every Nth coordinate (default: 5)")
        print()
        print("Examples:")
        print("  python3 simulate_gpx_route.py route.gpx")
        print("  python3 simulate_gpx_route.py route.gpx 'iPhone 16 Pro'")
        print("  python3 simulate_gpx_route.py route.gpx C39D6C83-C6E6-4F25-90D5-94F7268EA698")
        print("  python3 simulate_gpx_route.py route.gpx booted 15 2 10")
        print()
        print("Special commands:")
        print("  --list          List booted simulators")
        print("  --list-all      List all available simulators")
        print("  --stop [device] Stop simulation (default: booted)")
        sys.exit(1)
    
    # Handle special commands
    if sys.argv[1] == '--list':
        list_simulators(show_all=False)
        return
    
    if sys.argv[1] == '--list-all':
        list_simulators(show_all=True)
        return
    
    if sys.argv[1] == '--stop':
        device = sys.argv[2] if len(sys.argv) > 2 else 'booted'
        if device != 'booted':
            device_uuid = find_simulator(device)
            if not device_uuid:
                print(f"❌ Simulator not found: {device}")
                sys.exit(1)
            device = device_uuid
        stop_simulation(device)
        return
    
    # Parse arguments
    gpx_file = sys.argv[1]
    device = sys.argv[2] if len(sys.argv) > 2 else 'booted'
    speed = int(sys.argv[3]) if len(sys.argv) > 3 else 10
    interval = int(sys.argv[4]) if len(sys.argv) > 4 else 1
    simplify_factor = int(sys.argv[5]) if len(sys.argv) > 5 else 5
    
    # Resolve device identifier
    if device != 'booted':
        device_uuid = find_simulator(device)
        if not device_uuid:
            print(f"❌ Simulator not found: {device}")
            print("💡 Use --list to see booted simulators")
            sys.exit(1)
        device = device_uuid
    
    print(f"🗺️  Loading GPX file: {gpx_file}")
    
    # Extract coordinates
    coords = extract_coordinates_from_gpx(gpx_file)
    if not coords:
        sys.exit(1)
    
    # Start simulation
    success = simulate_route(coords, device, speed, interval, simplify_factor)
    if success:
        print("\n⏹️  To stop simulation, run:")
        print(f"   xcrun simctl location {device} clear")
        print("\n📱 Or use this script:")
        print(f"   python3 {sys.argv[0]} --stop {device if device != 'booted' else ''}")
    
if __name__ == "__main__":
    main() 