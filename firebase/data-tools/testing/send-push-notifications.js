#!/usr/bin/env node

/**
 * Push Notification Testing Tool
 * 
 * This tool sends test push notifications to mobile users using their UID.
 * It looks up the user's FCM token from Firestore and sends notifications.
 * 
 * Usage:
 *   node send-push-notifications.js <userUID> <type> [--emulator]
 * 
 * Types:
 *   driver       - Send driver trip request notification (VoIP push)
 *   passenger    - Send passenger notification (high priority)
 *   simple       - Send simple notification (normal priority)
 *   emergency    - Send emergency notification (high priority)
 * 
 * Examples:
 *   node send-push-notifications.js abc123 driver --emulator
 *   node send-push-notifications.js xyz789 passenger
 *   node send-push-notifications.js def456 simple --emulator
 */

const path = require('path');
const fs = require('fs');

// Use firebase-admin from functions directory
const functionsDir = path.join(__dirname, '..', '..', 'functions');
const admin = require(path.join(functionsDir, 'node_modules', 'firebase-admin'));

// Parse command line arguments
const args = process.argv.slice(2);
const userUID = args[0];
const notificationType = args[1];
const useEmulator = args.includes('--emulator');

// Configuration
const EMULATOR_HOST = 'localhost';
const FIRESTORE_PORT = 8080;
const AUTH_PORT = 9099;

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Logging utilities
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log();
  log(`${'='.repeat(60)}`, 'blue');
  log(title, 'bright');
  log(`${'='.repeat(60)}`, 'blue');
  console.log();
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'cyan');
}

// Show usage information
function showUsage() {
  logSection('Push Notification Testing Tool');
  log('Usage: node send-push-notifications.js <userUID> <type> [--emulator]');
  console.log();
  log('Types:', 'bright');
  log('  driver       - Send driver trip request notification (VoIP push)');
  log('  passenger    - Send passenger notification (high priority)');
  log('  simple       - Send simple notification (normal priority)');
  log('  emergency    - Send emergency notification (high priority)');
  console.log();
  log('Examples:', 'bright');
  log('  node send-push-notifications.js abc123 driver --emulator');
  log('  node send-push-notifications.js xyz789 passenger');
  log('  node send-push-notifications.js def456 simple --emulator');
  console.log();
}

// Initialize Firebase Admin SDK
async function initializeFirebase() {
  try {
    // Always need service account for FCM since there's no FCM emulator
    // FCM always hits Google's servers, even in test mode
    const serviceAccountPath = path.join(__dirname, '..', '..', 'fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json');

    if (!fs.existsSync(serviceAccountPath)) {
      throw new Error(
        'Service account file not found at firebase/fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json\n' +
        'Please ensure you have the service account key file in the correct location.\n' +
        'Note: FCM requires credentials even for testing since there is no FCM emulator.\n' +
        'If you need to download it:\n' +
        '1. Go to Firebase Console > Project Settings > Service Accounts\n' +
        '2. Generate new private key\n' +
        '3. Save as firebase/fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json'
      );
    }

    const serviceAccount = require(serviceAccountPath);

    if (useEmulator) {
      // Configure for emulator (Firestore and Auth only, not FCM)
      process.env.FIRESTORE_EMULATOR_HOST = `${EMULATOR_HOST}:${FIRESTORE_PORT}`;
      process.env.FIREBASE_AUTH_EMULATOR_HOST = `${EMULATOR_HOST}:${AUTH_PORT}`;

      // Initialize with credentials (required for FCM)
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.project_id
      });

      logSuccess('Connected to Firebase Emulator Suite (Firestore/Auth)');
      logInfo('Note: FCM always uses production servers (no emulator available)');
    } else {
      // Production mode - just use the service account
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount)
      });

      logSuccess(`Connected to Firebase Project: ${serviceAccount.project_id}`);
      logWarning('Sending to PRODUCTION devices!');
    }

    return {
      db: admin.firestore(),
      messaging: admin.messaging()
    };
  } catch (error) {
    logError(`Failed to initialize Firebase: ${error.message}`);
    process.exit(1);
  }
}

// Get user's FCM token from Firestore
async function getUserFCMToken(db, userUID) {
  try {
    const userDoc = await db.collection('mobile_users').doc(userUID).get();

    if (!userDoc.exists) {
      throw new Error(`User ${userUID} not found in mobile_users collection`);
    }

    const userData = userDoc.data();

    if (!userData.fcmToken) {
      throw new Error(`User ${userUID} does not have an FCM token`);
    }

    logInfo(`Found user: ${userData.displayName || userData.email || 'Unknown'}`);
    logInfo(`FCM Token: ${userData.fcmToken.substring(0, 20)}...`);

    return userData.fcmToken;
  } catch (error) {
    logError(`Failed to get FCM token: ${error.message}`);
    throw error;
  }
}

// High-priority notification types configuration (matching backend)
const HIGH_PRIORITY_TYPES = [
  'trip_request',
  'trip_cancelled', 
  'driver_moving',
  'driver_arrived',
  'chat_message',
  'emergency',
  'payment_required',
];

// Create high-priority message helper (matching backend implementation)
function createHighPriorityMessage(fcmToken, config) {
  const baseMessage = {
    token: fcmToken,
    notification: {
      title: config.title,
      body: config.body,
    },
    data: {
      ...config.data,
      notificationId: Date.now().toString(),
      timestamp: new Date().toISOString(),
      testNotification: "true"
    },
    android: {
      priority: config.priority.android,
      notification: {
        channelId: config.channelId || 'default_channel',
        priority: config.priority.android === 'high' ? 'max' : 'default',
        ...(config.timeoutMs && { timeoutAfter: config.timeoutMs }),
        // Handle sound configuration
        ...(config.sound !== undefined && { sound: config.sound }),
        ...(config.defaultSound !== undefined && { defaultSound: config.defaultSound }),
        ...(config.visibility && { visibility: config.visibility }),
      },
      ...(config.ttl && { ttl: config.ttl }),
    },
    apns: {
      headers: {
        'apns-priority': config.priority.ios,
        'apns-push-type': config.apnsPushType || 'alert',
      },
      payload: {
        aps: {
          ...(config.sound !== undefined && { sound: config.sound }),
          badge: 1,
          'content-available': 1,
          ...(config.category && { category: config.category }),
        },
      },
    },
    webpush: {
      headers: {
        Urgency: config.priority.android === 'high' ? 'high' : 'normal',
      },
    },
  };

  return baseMessage;
}

// Create driver notification payload
function createDriverNotification(fcmToken) {
  // IMPORTANT: VoIP vs Regular Push Decision
  // - Set USE_VOIP = true ONLY if:
  //   1. The iOS app has VoIP push entitlements
  //   2. You're using a VoIP device token (not regular FCM token)
  //   3. The app implements PushKit for VoIP
  // - Set USE_VOIP = false for testing with regular FCM tokens
  const USE_VOIP = false; // Change to true for production-like VoIP testing
  
  if (USE_VOIP) {
    // Production configuration - VoIP push for iOS
    logWarning('Using VoIP push configuration - requires VoIP token, not regular FCM token!');
    return {
      token: fcmToken,
      notification: {
        title: "🚕 Test Driver Notification",
        body: "This is a test trip request notification",
      },
      data: {
        isDriverNotification: "true",
        notificationType: "driver_trip_request",
        testNotification: "true"
      },
      android: {
        notification: {
          channelId: "new_trip_notification",
          priority: "max",
          // CRITICAL: DO NOT CHANGE - Special ringtone for driver trip requests
          sound: "phone_ringtone_ultra",
          visibility: "public",
        },
        ttl: 60,
      },
      apns: {
        payload: {
          aps: {
            // CRITICAL: DO NOT CHANGE - Special ringtone for driver trip requests
            sound: "phone_ringtone_ultra.caf",
            category: "INCOMING_CALL",
            contentAvailable: true,
          },
        },
        headers: {
          "apns-priority": "10",
          "apns-push-type": "voip", // VoIP push for maximum priority
        },
      },
    };
  } else {
    // Testing configuration - Regular push that works with standard FCM tokens
    return {
      token: fcmToken,
      notification: {
        title: "🚕 Test Driver Notification",
        body: "This is a test trip request notification",
      },
      data: {
        isDriverNotification: "true",
        notificationType: "driver_trip_request",
        testNotification: "true"
      },
      android: {
        priority: "high",
        notification: {
          channelId: "new_trip_notification",
          priority: "max",
          // CRITICAL: DO NOT CHANGE - Special ringtone for driver trip requests
          sound: "phone_ringtone_ultra",
          visibility: "public",
        },
      },
      apns: {
        payload: {
          aps: {
            // CRITICAL: DO NOT CHANGE - Special ringtone for driver trip requests
            sound: "phone_ringtone_ultra.caf",
            badge: 1,
            alert: {
              title: "🚕 Test Driver Notification",
              body: "This is a test trip request notification"
            }
          },
        },
        headers: {
          "apns-priority": "10",
        },
      },
    };
  }
}

// Create passenger notification payload
function createPassengerNotification(fcmToken) {
  const config = {
    title: "🚗 Test Passenger Notification",
    body: "Your driver is on the way to pick you up",
    data: {
      notificationType: "driver_moving",
    },
    priority: { android: 'high', ios: '10' },
    channelId: 'passenger_ringtone_channel',
    // CRITICAL: DO NOT CHANGE - Custom ringtone for passenger notifications
    sound: 'passenger_ringtone.caf', // iOS sound
    defaultSound: false,
  };
  
  // Use high priority helper but override Android sound
  const message = createHighPriorityMessage(fcmToken, config);
  
  // CRITICAL: DO NOT CHANGE - Override Android sound to use correct format
  if (message.android?.notification) {
    message.android.notification.sound = 'passenger_ringtone'; // Android sound (without extension)
  }
  
  return message;
}

// Create simple notification payload
function createSimpleNotification(fcmToken) {
  const config = {
    title: "📱 Test Notification",
    body: "This is a simple test notification from Fiaranow",
    data: {
      notificationType: "test",
    },
    priority: { android: 'high', ios: '5' }, // Normal priority
    channelId: 'silent_notification', // Use dedicated silent channel
    // No sound property = silent notification
  };
  
  // Use helper for consistent structure
  const message = createHighPriorityMessage(fcmToken, config);
  
  // Ensure it's truly silent by removing sound properties
  if (message.android?.notification) {
    delete message.android.notification.sound;
    delete message.android.notification.defaultSound;
  }
  if (message.apns?.payload?.aps) {
    delete message.apns.payload.aps.sound;
  }
  
  return message;
}

// Create emergency notification payload
function createEmergencyNotification(fcmToken) {
  const notificationType = 'emergency';
  
  // Verify this is a high priority type
  if (!HIGH_PRIORITY_TYPES.includes(notificationType)) {
    throw new Error(`${notificationType} should be in HIGH_PRIORITY_TYPES`);
  }
  
  const config = {
    title: "🚨 Emergency Test Notification",
    body: "This is a high-priority emergency notification test",
    data: {
      notificationType: notificationType,
      urgency: 'high',
    },
    priority: { android: 'high', ios: '10' }, // Maximum priority
    channelId: 'emergency_channel',
    sound: 'emergency_sound.mp3', // Emergency sound
    timeoutMs: 0, // No timeout for emergency
  };
  
  logInfo(`Creating ${notificationType} notification (HIGH_PRIORITY_TYPES verified)`);
  
  return createHighPriorityMessage(fcmToken, config);
}

// Send the notification
async function sendNotification(messaging, payload, type) {
  try {
    logInfo(`Sending ${type} notification...`);
    

    const response = await messaging.send(payload);

    logSuccess(`Notification sent successfully!`);
    logInfo(`Message ID: ${response}`);

  } catch (error) {
    logError(`Failed to send notification: ${error.message}`);

    // Provide helpful error messages
    if (error.code === 'messaging/registration-token-not-registered') {
      logWarning('The FCM token is no longer valid. The user may have uninstalled the app.');
      logInfo('Ask the user to open the app to refresh their FCM token.');
    } else if (error.code === 'messaging/invalid-registration-token') {
      logWarning('The FCM token format is invalid.');
    } else if (error.code === 'messaging/mismatched-credential') {
      logWarning('The credentials used don\'t have permission to send messages.');
    } else if (error.message && error.message.includes('Requested entity was not found')) {
      logWarning('The FCM token is expired or invalid.');
      logInfo('This often happens when:');
      logInfo('- The app was reinstalled');
      logInfo('- The token is older than 60 days');
      logInfo('- The user switched devices');
      logInfo('Solution: Ask the user to open the app to refresh their FCM token.');
    }

    throw error;
  }
}

// Main execution
async function main() {
  try {
    // Validate arguments
    if (!userUID || !notificationType) {
      logError('Please provide both userUID and notification type');
      showUsage();
      process.exit(1);
    }

    if (!['driver', 'passenger', 'simple', 'emergency'].includes(notificationType)) {
      logError(`Invalid notification type: ${notificationType}`);
      showUsage();
      process.exit(1);
    }

    // Initialize Firebase
    logSection('Initializing Firebase');
    const { db, messaging } = await initializeFirebase();

    // Get user's FCM token
    logSection('Getting User FCM Token');
    const fcmToken = await getUserFCMToken(db, userUID);

    // Create notification payload
    logSection('Creating Notification Payload');
    let payload;

    switch (notificationType) {
      case 'driver':
        payload = createDriverNotification(fcmToken);
        logInfo('Created driver trip request notification');
        break;
      case 'passenger':
        payload = createPassengerNotification(fcmToken);
        logInfo('Created passenger notification');
        break;
      case 'simple':
        payload = createSimpleNotification(fcmToken);
        logInfo('Created simple notification');
        break;
      case 'emergency':
        payload = createEmergencyNotification(fcmToken);
        logInfo('Created emergency notification');
        break;
    }

    // Send notification
    logSection('Sending Notification');
    await sendNotification(messaging, payload, notificationType);

    logSection('Test Complete');
    logSuccess(`${notificationType} notification sent to user ${userUID}`);

    process.exit(0);
  } catch (error) {
    logError(`Test failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main }; 