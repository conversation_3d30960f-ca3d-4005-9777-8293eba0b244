#!/usr/bin/env node

/**
 * Algolia Index Configuration Tool
 * 
 * Configures Algolia indexes with proper settings including searchableAttributes
 * 
 * Usage:
 *   node algolia-configure-indexes.js [--emulator] [--prod] [--tenant=id]
 */

const path = require('path');
const fs = require('fs');

// Use firebase-admin from functions directory
const functionsDir = path.join(__dirname, '..', '..', 'functions');
const admin = require(path.join(functionsDir, 'node_modules', 'firebase-admin'));

// Load algoliasearch from the CommonJS distribution file
const { algoliasearch } = require(path.join(functionsDir, 'node_modules', 'algoliasearch', 'dist', 'node.cjs'));

// Parse command line arguments
const args = process.argv.slice(2);
const useProduction = args.includes('--prod');
const useEmulator = args.includes('--emulator') || !useProduction;

// Extract tenant ID if provided
let tenantFilter = null;
const tenantArg = args.find(arg => arg.startsWith('--tenant='));
if (tenantArg) {
  tenantFilter = tenantArg.split('=')[1];
}

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Logging utilities
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log();
  log(`${'='.repeat(60)}`, 'blue');
  log(title, 'bright');
  log(`${'='.repeat(60)}`, 'blue');
  console.log();
}

function logProgress(message) {
  log(`➡️  ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// Algolia configuration
let ALGOLIA_APP_ID = '';
let ALGOLIA_ADMIN_KEY = '';
let algoliaClient = null;

// Initialize Algolia configuration
function initializeAlgolia() {
  try {
    const envPath = path.join(functionsDir, '.env.json');
    
    if (!fs.existsSync(envPath)) {
      throw new Error('.env.json not found. Please create it from .env.json.example');
    }
    
    const envConfig = JSON.parse(fs.readFileSync(envPath, 'utf8'));
    
    if (useProduction) {
      ALGOLIA_APP_ID = envConfig.algolia?.prod?.app_id || '';
      ALGOLIA_ADMIN_KEY = envConfig.algolia?.prod?.admin_key || '';
    } else {
      // Use dev config for emulator
      ALGOLIA_APP_ID = envConfig.algolia?.dev?.app_id || envConfig.algolia?.app_id || '';
      ALGOLIA_ADMIN_KEY = envConfig.algolia?.dev?.admin_key || envConfig.algolia?.admin_key || '';
    }
    
    if (!ALGOLIA_APP_ID || !ALGOLIA_ADMIN_KEY) {
      throw new Error(`Algolia ${useProduction ? 'production' : 'development'} credentials not found in .env.json`);
    }
    
    algoliaClient = algoliasearch(ALGOLIA_APP_ID, ALGOLIA_ADMIN_KEY);
    logSuccess(`Initialized Algolia client (${useProduction ? 'PRODUCTION' : 'DEVELOPMENT'})`);
    
  } catch (error) {
    logError(`Failed to initialize Algolia: ${error.message}`);
    process.exit(1);
  }
}

// Initialize Firebase to get tenant list if needed
async function initializeFirebase() {
  try {
    if (useEmulator) {
      // Configure for emulator
      process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080';
      process.env.FIREBASE_AUTH_EMULATOR_HOST = 'localhost:9099';

      // Initialize without credentials for emulator
      admin.initializeApp({
        projectId: 'fiaranow'
      });

      logSuccess('Connected to Firebase Emulator Suite');
    } else {
      // Look for service account file
      const serviceAccountPath = path.join(__dirname, '..', '..', 'fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json');

      if (!fs.existsSync(serviceAccountPath)) {
        throw new Error(
          'Service account file not found at firebase/fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json\n' +
          'Please ensure you have the service account key file in the correct location.'
        );
      }

      // Initialize with service account
      const serviceAccount = require(serviceAccountPath);
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount)
      });

      logSuccess(`Connected to Firebase Project: ${serviceAccount.project_id}`);
    }

    return {
      db: admin.firestore(),
      auth: admin.auth(),
      FieldValue: admin.firestore.FieldValue
    };
  } catch (error) {
    logError(`Failed to initialize Firebase: ${error.message}`);
    process.exit(1);
  }
}

// Helper function to get index name with optional tenant suffix
function getIndexName(collection, tenantId) {
  return tenantId ? `${collection}_${tenantId}` : collection;
}

// Index configurations
const indexConfigs = {
  trips: {
    // IMPORTANT: Keep in sync with admin_sveltekit/src/routes/rides/trips/+page.svelte
    searchableAttributes: [
      'startLocationName',
      'arrivalLocationName',
      'passengerName',
      'passengerPhone',
      'driverName',
      'driverPhone',
      'vehiclePlate',
      'vehicleModel'
    ],
    attributesForFaceting: [
      'searchable(status)',
      'searchable(tripType)',
      'searchable(tenantId)',
      'searchable(passengerId)',
      'searchable(driverId)',
      'searchable(vehicleId)'
    ],
    customRanking: [
      'desc(createdAt)'
    ]
  },
  mobile_users: {
    searchableAttributes: [
      'name',
      'phone',
      'email',
      'fcmTokenId',
      'driverTags'
    ],
    attributesForFaceting: [
      'searchable(tenantId)',
      'searchable(isDriverConfirmed)',
      'searchable(isServiceActive)',
      'searchable(driverTags)'
    ],
    customRanking: [
      'desc(createdAt)'
    ]
  },
  vehicles: {
    searchableAttributes: [
      'plateNumber',
      'model',
      'make',
      'color',
      'currentDriverName',
      'currentDriverPhone',
      'tenantRemark'
    ],
    attributesForFaceting: [
      'searchable(tenantId)',
      'searchable(currentDriverId)',
      'searchable(tenantApproved)',
      'searchable(isOwnedByTenant)'
    ],
    customRanking: [
      'desc(createdAt)'
    ]
  },
  payments: {
    searchableAttributes: [
      'paymentMethod',
      'status',
      'tripId',
      'userId'
    ],
    attributesForFaceting: [
      'searchable(status)',
      'searchable(paymentMethod)',
      'searchable(tenantId)',
      'searchable(tripId)',
      'searchable(userId)'
    ],
    customRanking: [
      'desc(createdAt)'
    ]
  },
  chat_sessions: {
    searchableAttributes: [
      'sessionName',
      'participantNames',
      'lastMessage'
    ],
    attributesForFaceting: [
      'searchable(tenantId)',
      'searchable(participants)',
      'searchable(isActive)'
    ],
    customRanking: [
      'desc(lastMessageAt)'
    ]
  }
};

// Configure a single index
async function configureIndex(indexName, config) {
  try {
    logProgress(`Configuring index: ${indexName}`);
    
    await algoliaClient.setSettings({
      indexName,
      indexSettings: {
        searchableAttributes: config.searchableAttributes,
        attributesForFaceting: config.attributesForFaceting,
        customRanking: config.customRanking,
        // Additional recommended settings
        attributesToRetrieve: ['*'],
        attributesToHighlight: config.searchableAttributes,
        attributesToSnippet: [`startLocationName:20`, `arrivalLocationName:20`],
        highlightPreTag: '<em>',
        highlightPostTag: '</em>',
        hitsPerPage: 20,
        paginationLimitedTo: 1000,
        exactOnSingleWordQuery: 'attribute',
        alternativesAsExact: ['ignorePlurals', 'singleWordSynonym']
      }
    });
    
    logSuccess(`Configured index: ${indexName}`);
  } catch (error) {
    logError(`Failed to configure index ${indexName}: ${error.message}`);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    // Initialize services
    logSection('Initializing Services');
    const firebase = await initializeFirebase();
    initializeAlgolia();
    
    // Display configuration
    logSection('Index Configuration');
    log(`Target: ${useProduction ? 'PRODUCTION' : 'DEVELOPMENT'} Algolia`);
    if (tenantFilter) {
      log(`Tenant Filter: ${tenantFilter}`);
    }
    
    // Get tenant list
    let tenantIds = [];
    if (tenantFilter) {
      tenantIds = [tenantFilter];
    } else {
      const tenantsSnapshot = await firebase.db.collection('tenants').get();
      tenantIds = tenantsSnapshot.docs.map(doc => doc.id);
      log(`Found ${tenantIds.length} tenants`);
    }
    
    // Configure indexes
    logSection('Configuring Indexes');
    
    for (const [collection, config] of Object.entries(indexConfigs)) {
      // Configure base index (for queries without tenant filter)
      await configureIndex(collection, config);
      
      // Configure tenant-specific indexes
      for (const tenantId of tenantIds) {
        const tenantIndexName = getIndexName(collection, tenantId);
        await configureIndex(tenantIndexName, config);
      }
    }
    
    logSection('Configuration Complete');
    logSuccess('All indexes have been configured with searchableAttributes');
    
    process.exit(0);
  } catch (error) {
    logError(`Configuration failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { initializeAlgolia, getIndexName };