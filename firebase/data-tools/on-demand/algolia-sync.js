#!/usr/bin/env node

/**
 * Algolia Sync Tool
 * 
 * Syncs Firestore data to Algolia indexes
 * Can sync all collections or specific ones
 * Supports filtering by tenant and dry-run mode
 * 
 * Usage:
 *   node algolia-sync.js sync-all [--emulator] [--prod] [--dry-run] [--tenant=id]
 *   node algolia-sync.js sync-trips [options]
 *   node algolia-sync.js sync-users [options]
 *   etc.
 */

const path = require('path');
const fs = require('fs');

// Use firebase-admin from functions directory
const functionsDir = path.join(__dirname, '..', '..', 'functions');
const admin = require(path.join(functionsDir, 'node_modules', 'firebase-admin'));

// Load algoliasearch from the CommonJS distribution file
const { algoliasearch } = require(path.join(functionsDir, 'node_modules', 'algoliasearch', 'dist', 'node.cjs'));

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0];
const useProduction = args.includes('--prod');
const useEmulator = args.includes('--emulator') || !useProduction; // Default to emulator
const isDryRun = args.includes('--dry-run');

// Extract tenant ID if provided
let tenantFilter = null;
const tenantArg = args.find(arg => arg.startsWith('--tenant='));
if (tenantArg) {
  tenantFilter = tenantArg.split('=')[1];
}

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Logging utilities
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log();
  log(`${'='.repeat(60)}`, 'blue');
  log(title, 'bright');
  log(`${'='.repeat(60)}`, 'blue');
  console.log();
}

function logProgress(message) {
  log(`➡️  ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// Algolia configuration
let ALGOLIA_APP_ID = '';
let ALGOLIA_ADMIN_KEY = '';
let algoliaClient = null;

// Initialize Algolia configuration
function initializeAlgolia() {
  try {
    const envPath = path.join(functionsDir, '.env.json');
    
    if (!fs.existsSync(envPath)) {
      throw new Error('.env.json not found. Please create it from .env.json.example');
    }
    
    const envConfig = JSON.parse(fs.readFileSync(envPath, 'utf8'));
    
    if (useProduction) {
      ALGOLIA_APP_ID = envConfig.algolia?.prod?.app_id || '';
      ALGOLIA_ADMIN_KEY = envConfig.algolia?.prod?.admin_key || '';
    } else {
      // Use dev config for emulator
      ALGOLIA_APP_ID = envConfig.algolia?.dev?.app_id || envConfig.algolia?.app_id || '';
      ALGOLIA_ADMIN_KEY = envConfig.algolia?.dev?.admin_key || envConfig.algolia?.admin_key || '';
    }
    
    if (!ALGOLIA_APP_ID || !ALGOLIA_ADMIN_KEY) {
      throw new Error(`Algolia ${useProduction ? 'production' : 'development'} credentials not found in .env.json`);
    }
    
    algoliaClient = algoliasearch(ALGOLIA_APP_ID, ALGOLIA_ADMIN_KEY);
    logSuccess(`Initialized Algolia client (${useProduction ? 'PRODUCTION' : 'DEVELOPMENT'})`);
    
  } catch (error) {
    logError(`Failed to initialize Algolia: ${error.message}`);
    process.exit(1);
  }
}

// Initialize Firebase
async function initializeFirebase() {
  try {
    if (useEmulator) {
      // Configure for emulator
      process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080';
      process.env.FIREBASE_AUTH_EMULATOR_HOST = 'localhost:9099';

      // Initialize without credentials for emulator
      admin.initializeApp({
        projectId: 'fiaranow'
      });

      logSuccess('Connected to Firebase Emulator Suite');
    } else {
      // Look for service account file
      const serviceAccountPath = path.join(__dirname, '..', '..', 'fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json');

      if (!fs.existsSync(serviceAccountPath)) {
        throw new Error(
          'Service account file not found at firebase/fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json\n' +
          'Please ensure you have the service account key file in the correct location.'
        );
      }

      // Initialize with service account
      const serviceAccount = require(serviceAccountPath);
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount)
      });

      logSuccess(`Connected to Firebase Project: ${serviceAccount.project_id}`);
    }

    return {
      db: admin.firestore(),
      auth: admin.auth(),
      FieldValue: admin.firestore.FieldValue
    };
  } catch (error) {
    logError(`Failed to initialize Firebase: ${error.message}`);
    process.exit(1);
  }
}

// Helper function to get index name with optional tenant suffix
function getIndexName(collection, tenantId) {
  return tenantId ? `${collection}_${tenantId}` : collection;
}

// Helper function to sanitize data for Algolia
function sanitizeForAlgolia(data, collection) {
  const sanitized = { ...data };
  
  // Remove Firebase specific fields
  delete sanitized._firestore;
  delete sanitized._ref;
  
  // Remove large/unnecessary fields for trips
  if (collection === 'trips') {
    // Remove route data (too large and not searchable)
    delete sanitized.routeData;
    delete sanitized.driverRouteData;
    delete sanitized.finalRouteData;
    delete sanitized.mapsPolylines;
    
    // Remove location objects (we'll keep string addresses)
    delete sanitized.startLocation;
    delete sanitized.arrivalLocation;
    delete sanitized.driverLocation;
    
    // Remove other non-searchable large data
    delete sanitized.bounds;
    delete sanitized.estimations;
    delete sanitized.fareBreakdown;
    delete sanitized.routeLegs;
    delete sanitized.waypoints;
    
    // Remove sensitive or unnecessary fields
    delete sanitized.stripePaymentIntentId;
    delete sanitized.stripeChargeId;
    delete sanitized.fcmTokens;
    delete sanitized.chatSessionId;
    delete sanitized.previousDrivers;
    
    // Remove additional large/unnecessary fields
    delete sanitized.routeOverviews;
    delete sanitized.tripConfiguration;
    delete sanitized.routeDataIds;
  }
  
  // Convert timestamps to ISO strings
  Object.keys(sanitized).forEach(key => {
    if (sanitized[key] && sanitized[key].toDate) {
      sanitized[key] = sanitized[key].toDate().toISOString();
    }
  });
  
  return sanitized;
}

// Helper function to get tenant IDs for a document
async function getTenantIds(db, collection, docId) {
  try {
    // First check if the document has a direct tenantId field
    const doc = await db.collection(collection).doc(docId).get();
    if (doc.exists && doc.data().tenantId) {
      return [doc.data().tenantId];
    }
    
    // If not, check the tenant_states subcollection
    const tenantStatesSnapshot = await db.collection(collection).doc(docId).collection('tenant_states').get();
    
    if (!tenantStatesSnapshot.empty) {
      // Return all tenant IDs (document IDs in the tenant_states subcollection)
      return tenantStatesSnapshot.docs.map(doc => doc.id);
    }
    
    return [];
  } catch (error) {
    logError(`Failed to get tenant IDs for ${collection}/${docId}: ${error.message}`);
    return [];
  }
}

// Progress tracking
class SyncProgress {
  constructor() {
    this.stats = {
      processed: 0,
      synced: 0,
      skipped: 0,
      failed: 0,
      deleted: 0
    };
    this.errors = [];
  }

  recordSync() {
    this.stats.processed++;
    this.stats.synced++;
  }

  recordSkip() {
    this.stats.processed++;
    this.stats.skipped++;
  }

  recordDelete() {
    this.stats.processed++;
    this.stats.deleted++;
  }

  recordError(error, context = {}) {
    this.stats.processed++;
    this.stats.failed++;
    this.errors.push({ error: error.message, context });
  }

  printSummary() {
    console.log();
    log('Sync Summary:', 'bright');
    log(`Total Processed: ${this.stats.processed}`);
    logSuccess(`Synced: ${this.stats.synced}`);
    if (this.stats.deleted > 0) {
      logWarning(`Deleted: ${this.stats.deleted}`);
    }
    if (this.stats.skipped > 0) {
      log(`Skipped: ${this.stats.skipped}`, 'yellow');
    }
    if (this.stats.failed > 0) {
      logError(`Failed: ${this.stats.failed}`);
    }

    if (this.errors.length > 0) {
      console.log();
      log('Errors:', 'red');
      this.errors.forEach((err, idx) => {
        console.log(`  ${idx + 1}. ${err.error}`);
        if (err.context && Object.keys(err.context).length > 0) {
          console.log(`     Context: ${JSON.stringify(err.context)}`);
        }
      });
    }
  }
}

// Sync functions for each collection
async function syncTrips(db, progress) {
  logProgress('Syncing trips collection...');
  
  // Trips are stored under /tenants/{tenantId}/trips
  if (tenantFilter) {
    // Query specific tenant's trips
    const query = db.collection('tenants').doc(tenantFilter).collection('trips');
    const snapshot = await query.get();
    log(`Found ${snapshot.size} trips to sync for tenant ${tenantFilter}`);
    
    for (const doc of snapshot.docs) {
      try {
        const data = doc.data();
        const tripId = doc.id;
        const tenantId = tenantFilter; // Use the filter as the tenant ID
        
        const indexName = getIndexName('trips', tenantId);
        
        // Fetch related data
        const [passenger, driver] = await Promise.all([
          data.uidPassenger ? db.collection('mobile_users').doc(data.uidPassenger).get() : null,
          data.uidChosenDriver ? db.collection('mobile_users').doc(data.uidChosenDriver).get() : null,
        ]);
        
        // Get vehicle through driver's tenant state
        let vehicle = null;
        if (data.uidChosenDriver) {
          const driverTenantState = await db.collection('mobile_users')
            .doc(data.uidChosenDriver)
            .collection('tenant_states')
            .doc(tenantId)
            .get();
            
          if (driverTenantState.exists && driverTenantState.data().currentVehicleLinkingId) {
            const vehicleLinking = await db.collection('tenants')
              .doc(tenantId)
              .collection('vehicles_linking')
              .doc(driverTenantState.data().currentVehicleLinkingId)
              .get();
              
            if (vehicleLinking.exists && vehicleLinking.data().vehicleId) {
              vehicle = await db.collection('vehicles').doc(vehicleLinking.data().vehicleId).get();
            }
          }
        }
        
        // Fallback to vehicleId if it exists (for backward compatibility)
        if (!vehicle && data.vehicleId) {
          vehicle = await db.collection('vehicles').doc(data.vehicleId).get();
        }
        
        // IMPORTANT: Keep this data structure in sync with firebase/functions/src/algolia_sync.ts
        // Any changes here must be reflected in the Firebase function
        const record = {
          objectID: tripId,
          ...sanitizeForAlgolia(data, 'trips'),
          passengerName: passenger?.data()?.displayName || '',
          passengerPhone: passenger?.data()?.phoneNumber || '',
          driverName: driver?.data()?.displayName || '',
          driverPhone: driver?.data()?.phoneNumber || '',
          vehiclePlate: vehicle?.data()?.registrationNumber || '',
          vehicleModel: vehicle?.data()?.model || '',
          tenantId: tenantId,
        };
        
        if (!isDryRun) {
          await algoliaClient.saveObject({
            indexName,
            body: record,
          });
        }
        
        progress.recordSync();
        logProgress(`Synced trip ${tripId} to tenant ${tenantId}`);
        
        if (progress.stats.processed % 100 === 0) {
          logProgress(`Processed ${progress.stats.processed} trips...`);
        }
        
      } catch (error) {
        logError(`Failed to sync trip ${doc.id}: ${error.message}`);
        progress.recordError(error, { tripId: doc.id });
      }
    }
  } else {
    // Sync all tenants' trips
    const tenantsSnapshot = await db.collection('tenants').get();
    
    for (const tenantDoc of tenantsSnapshot.docs) {
      const tenantId = tenantDoc.id;
      const tripsSnapshot = await db.collection('tenants').doc(tenantId).collection('trips').get();
      
      log(`Found ${tripsSnapshot.size} trips for tenant ${tenantId}`);
      
      for (const doc of tripsSnapshot.docs) {
        try {
          const data = doc.data();
          const tripId = doc.id;
          
          const indexName = getIndexName('trips', tenantId);
          
          // Fetch related data
          const [passenger, driver] = await Promise.all([
            data.uidPassenger ? db.collection('mobile_users').doc(data.uidPassenger).get() : null,
            data.uidChosenDriver ? db.collection('mobile_users').doc(data.uidChosenDriver).get() : null,
          ]);
          
          // Get vehicle through driver's tenant state
          let vehicle = null;
          if (data.uidChosenDriver) {
            const driverTenantState = await db.collection('mobile_users')
              .doc(data.uidChosenDriver)
              .collection('tenant_states')
              .doc(tenantId)
              .get();
              
            if (driverTenantState.exists && driverTenantState.data().currentVehicleLinkingId) {
              const vehicleLinking = await db.collection('tenants')
                .doc(tenantId)
                .collection('vehicles_linking')
                .doc(driverTenantState.data().currentVehicleLinkingId)
                .get();
                
              if (vehicleLinking.exists && vehicleLinking.data().vehicleId) {
                vehicle = await db.collection('vehicles').doc(vehicleLinking.data().vehicleId).get();
              }
            }
          }
          
          // Fallback to vehicleId if it exists (for backward compatibility)
          if (!vehicle && data.vehicleId) {
            vehicle = await db.collection('vehicles').doc(data.vehicleId).get();
          }
          
          // IMPORTANT: Keep this data structure in sync with firebase/functions/src/algolia_sync.ts
          // Any changes here must be reflected in the Firebase function
          const record = {
            objectID: tripId,
            ...sanitizeForAlgolia(data, 'trips'),
            passengerName: passenger?.data()?.displayName || '',
            passengerPhone: passenger?.data()?.phoneNumber || '',
            driverName: driver?.data()?.displayName || '',
            driverPhone: driver?.data()?.phoneNumber || '',
            vehiclePlate: vehicle?.data()?.registrationNumber || '',
            vehicleModel: vehicle?.data()?.model || '',
            tenantId: tenantId,
          };
          
          if (!isDryRun) {
            await algoliaClient.saveObject({
              indexName,
              body: record,
            });
          }
          
          progress.recordSync();
          logProgress(`Synced trip ${tripId} to tenant ${tenantId}`);
          
        } catch (error) {
          logError(`Failed to sync trip ${doc.id}: ${error.message}`);
          progress.recordError(error, { tripId: doc.id });
        }
      }
    }
  }
}

async function syncMobileUsers(db, progress) {
  logProgress('Syncing mobile_users collection...');
  
  let query = db.collection('mobile_users');
  if (tenantFilter) {
    // Filter by tenantIDs array for multi-tenant support
    query = query.where('tenantIDs', 'array-contains', tenantFilter);
  }
  
  const snapshot = await query.get();
  log(`Found ${snapshot.size} users to sync`);
  
  for (const doc of snapshot.docs) {
    try {
      const data = doc.data();
      const userId = doc.id;
      
      // Get tenant states from subcollection
      const tenantStatesSnapshot = await db.collection('mobile_users')
        .doc(userId)
        .collection('tenant_states')
        .get();
      
      if (tenantStatesSnapshot.empty) {
        logWarning(`User ${userId} has no tenant states, skipping`);
        progress.recordSkip();
        continue;
      }
      
      // Sync user to each tenant they belong to
      for (const tenantStateDoc of tenantStatesSnapshot.docs) {
        const tenantId = tenantStateDoc.id;
        const tenantState = tenantStateDoc.data();
        
        // Skip if filtering by tenant and this isn't the one
        if (tenantFilter && tenantId !== tenantFilter) {
          continue;
        }
        
        // Skip inactive tenant states
        if (!tenantState.isActive) {
          logWarning(`User ${userId} is inactive in tenant ${tenantId}, skipping`);
          continue;
        }
        
        const indexName = getIndexName('mobile_users', tenantId);
        
        // IMPORTANT: Keep this data structure in sync with firebase/functions/src/algolia_sync.ts
        // Any changes here must be reflected in the Firebase function
        const record = {
          objectID: userId,
          ...sanitizeForAlgolia(data),
          // Include tenant-specific state
          tenantId: tenantId,
          isDriverConfirmed: tenantState.isDriverConfirmed || false,
          isServiceActive: tenantState.isServiceActive || false,
          driverTags: tenantState.driverTags || [],
          currentVehicleLinkingId: tenantState.currentVehicleLinkingId || null,
        };
        
        if (!isDryRun) {
          await algoliaClient.saveObject({
            indexName,
            body: record,
          });
        }
        
        progress.recordSync();
        logProgress(`Synced user ${userId} to tenant ${tenantId}`);
      }
      
      if (progress.stats.processed % 100 === 0) {
        logProgress(`Processed ${progress.stats.processed} users...`);
      }
      
    } catch (error) {
      logError(`Failed to sync user ${doc.id}: ${error.message}`);
      progress.recordError(error, { userId: doc.id });
    }
  }
}

async function syncVehicles(db, progress) {
  logProgress('Syncing vehicles collection...');
  
  // Vehicles don't have direct tenant association, need to check vehicle_linking
  if (tenantFilter) {
    // Get all vehicle IDs linked to the specific tenant
    const vehicleLinkingSnapshot = await db.collectionGroup('vehicles_linking')
      .where('tenantId', '==', tenantFilter)
      .where('isActive', '==', true)
      .get();
    
    const vehicleIds = new Set(vehicleLinkingSnapshot.docs.map(doc => doc.data().vehicleId));
    log(`Found ${vehicleIds.size} vehicles linked to tenant ${tenantFilter}`);
    
    // Sync each linked vehicle
    for (const vehicleId of vehicleIds) {
      try {
        const vehicleDoc = await db.collection('vehicles').doc(vehicleId).get();
        
        if (!vehicleDoc.exists) {
          logWarning(`Vehicle ${vehicleId} not found, skipping`);
          progress.recordSkip();
          continue;
        }
        
        const data = vehicleDoc.data();
        
        // Find the linking document for this tenant
        const linkingDoc = vehicleLinkingSnapshot.docs.find(doc => doc.data().vehicleId === vehicleId);
        const linkingData = linkingDoc.data();
        
        const indexName = getIndexName('vehicles', tenantFilter);
        
        // Fetch driver data if assigned
        const driver = linkingData.currentDriverId 
          ? await db.collection('mobile_users').doc(linkingData.currentDriverId).get()
          : null;
        
        // IMPORTANT: Keep this data structure in sync with firebase/functions/src/algolia_sync.ts
        // Any changes here must be reflected in the Firebase function
        // NOTE: Firebase function needs to be updated to include vehicle linking data
        const record = {
          objectID: vehicleId,
          ...sanitizeForAlgolia(data),
          // Add linking-specific data
          currentDriverId: linkingData.currentDriverId || '',
          currentDriverName: driver?.data()?.displayName || '',
          currentDriverPhone: driver?.data()?.phoneNumber || '',
          tenantApproved: linkingData.tenantApproved || false,
          isOwnedByTenant: linkingData.isOwnedByTenant || false,
          tenantRemark: linkingData.tenantRemark || '',
          tenantId: tenantFilter,
        };
        
        if (!isDryRun) {
          await algoliaClient.saveObject({
            indexName,
            body: record,
          });
        }
        
        progress.recordSync();
        logProgress(`Synced vehicle ${vehicleId} to tenant ${tenantFilter}`);
        
      } catch (error) {
        logError(`Failed to sync vehicle ${vehicleId}: ${error.message}`);
        progress.recordError(error, { vehicleId });
      }
    }
  } else {
    // Sync all vehicles to all tenants they're linked to
    const snapshot = await db.collection('vehicles').get();
    log(`Found ${snapshot.size} vehicles to sync`);
    
    for (const doc of snapshot.docs) {
      try {
        const data = doc.data();
        const vehicleId = doc.id;
        
        // Get all tenant linkings for this vehicle
        const vehicleLinkingSnapshot = await db.collectionGroup('vehicles_linking')
          .where('vehicleId', '==', vehicleId)
          .where('isActive', '==', true)
          .get();
        
        if (vehicleLinkingSnapshot.empty) {
          logWarning(`Vehicle ${vehicleId} has no tenant linkings, skipping`);
          progress.recordSkip();
          continue;
        }
        
        // Sync to each tenant
        for (const linkingDoc of vehicleLinkingSnapshot.docs) {
          const linkingData = linkingDoc.data();
          const tenantId = linkingData.tenantId;
          
          const indexName = getIndexName('vehicles', tenantId);
          
          // Fetch driver data if assigned
          const driver = linkingData.currentDriverId 
            ? await db.collection('mobile_users').doc(linkingData.currentDriverId).get()
            : null;
          
          // IMPORTANT: Keep this data structure in sync with firebase/functions/src/algolia_sync.ts
          // Any changes here must be reflected in the Firebase function
          // NOTE: Firebase function needs to be updated to include vehicle linking data
          const record = {
            objectID: vehicleId,
            ...sanitizeForAlgolia(data),
            // Add linking-specific data
            currentDriverId: linkingData.currentDriverId || '',
            currentDriverName: driver?.data()?.displayName || '',
            currentDriverPhone: driver?.data()?.phoneNumber || '',
            tenantApproved: linkingData.tenantApproved || false,
            isOwnedByTenant: linkingData.isOwnedByTenant || false,
            tenantRemark: linkingData.tenantRemark || '',
            tenantId: tenantId,
          };
          
          if (!isDryRun) {
            await algoliaClient.saveObject({
              indexName,
              body: record,
            });
          }
          
          progress.recordSync();
          logProgress(`Synced vehicle ${vehicleId} to tenant ${tenantId}`);
        }
        
        if (progress.stats.processed % 100 === 0) {
          logProgress(`Processed ${progress.stats.processed} vehicles...`);
        }
        
      } catch (error) {
        logError(`Failed to sync vehicle ${doc.id}: ${error.message}`);
        progress.recordError(error, { vehicleId: doc.id });
      }
    }
  }
}

async function syncPayments(db, progress) {
  logProgress('Syncing payments collection...');
  
  // Payments are stored under /tenants/{tenantId}/payments
  if (tenantFilter) {
    // Query specific tenant's payments
    const query = db.collection('tenants').doc(tenantFilter).collection('payments');
    const snapshot = await query.get();
    log(`Found ${snapshot.size} payments to sync for tenant ${tenantFilter}`);
    
    for (const doc of snapshot.docs) {
      try {
        const data = doc.data();
        const paymentId = doc.id;
        const tenantId = tenantFilter; // Use the filter as the tenant ID
        
        const indexName = getIndexName('payments', tenantId);
        
        // IMPORTANT: Keep this data structure in sync with firebase/functions/src/algolia_sync.ts
        // Any changes here must be reflected in the Firebase function
        const record = {
          objectID: paymentId,
          ...sanitizeForAlgolia(data),
          tenantId: tenantId,
        };
        
        if (!isDryRun) {
          await algoliaClient.saveObject({
            indexName,
            body: record,
          });
        }
        
        progress.recordSync();
        logProgress(`Synced payment ${paymentId} to tenant ${tenantId}`);
        
        if (progress.stats.processed % 100 === 0) {
          logProgress(`Processed ${progress.stats.processed} payments...`);
        }
        
      } catch (error) {
        logError(`Failed to sync payment ${doc.id}: ${error.message}`);
        progress.recordError(error, { paymentId: doc.id });
      }
    }
  } else {
    // Sync all tenants' payments
    const tenantsSnapshot = await db.collection('tenants').get();
    
    for (const tenantDoc of tenantsSnapshot.docs) {
      const tenantId = tenantDoc.id;
      const paymentsSnapshot = await db.collection('tenants').doc(tenantId).collection('payments').get();
      
      log(`Found ${paymentsSnapshot.size} payments for tenant ${tenantId}`);
      
      for (const doc of paymentsSnapshot.docs) {
        try {
          const data = doc.data();
          const paymentId = doc.id;
          
          const indexName = getIndexName('payments', tenantId);
          
          // IMPORTANT: Keep this data structure in sync with firebase/functions/src/algolia_sync.ts
          // Any changes here must be reflected in the Firebase function
          const record = {
            objectID: paymentId,
            ...sanitizeForAlgolia(data),
            tenantId: tenantId,
          };
          
          if (!isDryRun) {
            await algoliaClient.saveObject({
              indexName,
              body: record,
            });
          }
          
          progress.recordSync();
          logProgress(`Synced payment ${paymentId} to tenant ${tenantId}`);
          
        } catch (error) {
          logError(`Failed to sync payment ${doc.id}: ${error.message}`);
          progress.recordError(error, { paymentId: doc.id });
        }
      }
    }
  }
}

async function syncChatSessions(db, progress) {
  logProgress('Syncing chat_sessions collection...');
  
  // Chat sessions are stored under /tenants/{tenantId}/chat_sessions
  if (tenantFilter) {
    // Query specific tenant's chat sessions
    const query = db.collection('tenants').doc(tenantFilter).collection('chat_sessions');
    const snapshot = await query.get();
    log(`Found ${snapshot.size} chat sessions to sync for tenant ${tenantFilter}`);
    
    for (const doc of snapshot.docs) {
      try {
        const data = doc.data();
        const sessionId = doc.id;
        const tenantId = tenantFilter; // Use the filter as the tenant ID
        
        const indexName = getIndexName('chat_sessions', tenantId);
        
        // Fetch participants data
        const participantPromises = (data.participants || []).map(participantId =>
          db.collection('mobile_users').doc(participantId).get()
        );
        const participants = await Promise.all(participantPromises);
        
        // IMPORTANT: Keep this data structure in sync with firebase/functions/src/algolia_sync.ts
        // Any changes here must be reflected in the Firebase function
        const record = {
          objectID: sessionId,
          ...sanitizeForAlgolia(data),
          participantNames: participants
            .map(p => p.data()?.name || '')
            .filter(name => name),
          tenantId: tenantId,
        };
        
        if (!isDryRun) {
          await algoliaClient.saveObject({
            indexName,
            body: record,
          });
        }
        
        progress.recordSync();
        logProgress(`Synced chat session ${sessionId} to tenant ${tenantId}`);
        
        if (progress.stats.processed % 100 === 0) {
          logProgress(`Processed ${progress.stats.processed} chat sessions...`);
        }
        
      } catch (error) {
        logError(`Failed to sync chat session ${doc.id}: ${error.message}`);
        progress.recordError(error, { sessionId: doc.id });
      }
    }
  } else {
    // Sync all tenants' chat sessions
    const tenantsSnapshot = await db.collection('tenants').get();
    
    for (const tenantDoc of tenantsSnapshot.docs) {
      const tenantId = tenantDoc.id;
      const chatSessionsSnapshot = await db.collection('tenants').doc(tenantId).collection('chat_sessions').get();
      
      log(`Found ${chatSessionsSnapshot.size} chat sessions for tenant ${tenantId}`);
      
      for (const doc of chatSessionsSnapshot.docs) {
        try {
          const data = doc.data();
          const sessionId = doc.id;
          
          const indexName = getIndexName('chat_sessions', tenantId);
          
          // Fetch participants data
          const participantPromises = (data.participants || []).map(participantId =>
            db.collection('mobile_users').doc(participantId).get()
          );
          const participants = await Promise.all(participantPromises);
          
          // IMPORTANT: Keep this data structure in sync with firebase/functions/src/algolia_sync.ts
          // Any changes here must be reflected in the Firebase function
          const record = {
            objectID: sessionId,
            ...sanitizeForAlgolia(data),
            participantNames: participants
              .map(p => p.data()?.name || '')
              .filter(name => name),
            tenantId: tenantId,
          };
          
          if (!isDryRun) {
            await algoliaClient.saveObject({
              indexName,
              body: record,
            });
          }
          
          progress.recordSync();
          logProgress(`Synced chat session ${sessionId} to tenant ${tenantId}`);
          
        } catch (error) {
          logError(`Failed to sync chat session ${doc.id}: ${error.message}`);
          progress.recordError(error, { sessionId: doc.id });
        }
      }
    }
  }
}

// Main execution
async function main() {
  try {
    // Validate command
    const validCommands = ['sync-all', 'sync-trips', 'sync-users', 'sync-vehicles', 'sync-payments', 'sync-chats'];
    if (!validCommands.includes(command)) {
      logError(`Invalid command: ${command}`);
      console.log('Valid commands:', validCommands.join(', '));
      process.exit(1);
    }
    
    // Initialize services
    logSection('Initializing Services');
    const firebase = await initializeFirebase();
    initializeAlgolia();
    
    // Display sync configuration
    logSection('Sync Configuration');
    log(`Command: ${command}`);
    log(`Target: ${useEmulator ? 'EMULATOR' : 'PRODUCTION'} Firestore`);
    log(`Algolia: ${useProduction ? 'PRODUCTION' : 'DEVELOPMENT'} indexes`);
    log(`Mode: ${isDryRun ? 'DRY RUN' : 'LIVE'}`);
    if (tenantFilter) {
      log(`Tenant Filter: ${tenantFilter}`);
    }
    
    // Execute sync
    logSection('Starting Sync');
    const startTime = Date.now();
    const progress = new SyncProgress();
    
    // Map commands to sync functions
    const syncFunctions = {
      'sync-trips': () => syncTrips(firebase.db, progress),
      'sync-users': () => syncMobileUsers(firebase.db, progress),
      'sync-vehicles': () => syncVehicles(firebase.db, progress),
      'sync-payments': () => syncPayments(firebase.db, progress),
      'sync-chats': () => syncChatSessions(firebase.db, progress),
      'sync-all': async () => {
        await syncTrips(firebase.db, progress);
        await syncMobileUsers(firebase.db, progress);
        await syncVehicles(firebase.db, progress);
        await syncPayments(firebase.db, progress);
        await syncChatSessions(firebase.db, progress);
      }
    };
    
    // Execute the appropriate sync function
    await syncFunctions[command]();
    
    // Print summary
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    logSection('Sync Complete');
    log(`Duration: ${duration} seconds`);
    progress.printSummary();
    
    if (isDryRun) {
      console.log();
      logWarning('This was a DRY RUN - no data was actually synced to Algolia');
    }
    
    process.exit(0);
  } catch (error) {
    logError(`Sync failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { initializeFirebase, initializeAlgolia, SyncProgress };