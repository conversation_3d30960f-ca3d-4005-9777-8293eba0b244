#!/bin/bash

# Notification Testing Convenience Script
# Provides easy commands for testing push notifications

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to show usage
show_usage() {
    echo -e "${BLUE}Firebase Push Notification Testing Tool${NC}"
    echo -e "${BLUE}=======================================${NC}"
    echo ""
    echo "Usage: $0 <command> <userUID> <type>"
    echo ""
    echo "Commands:"
    echo "  test-local <userUID> <type>   Test notification on emulator"
    echo "  send-prod <userUID> <type>    Send notification to production device (⚠️  CAUTION!)"
    echo ""
    echo "Types:"
    echo "  driver       Driver trip request notification (VoIP push)"
    echo "  passenger    Passenger notification (high priority)"
    echo "  simple       Simple notification (normal priority, silent)"
    echo "  emergency    Emergency notification (high priority)"
    echo ""
    echo "Examples:"
    echo "  $0 test-local abc123 driver"
    echo "  $0 send-prod xyz789 passenger"
    echo "  $0 test-local def456 simple"
    echo "  $0 test-local ghi789 emergency"
    echo ""
    echo "Note: The userUID should be a valid user ID from the mobile_users collection"
    echo ""
    echo "Important: FCM requires service account credentials even for testing"
    echo "          (there is no FCM emulator - it always uses Google's servers)"
    echo ""
}

# Function to run notification command
run_notification() {
    local args="$@"
    cd "$SCRIPT_DIR" && node testing/send-push-notifications.js $args
}

# Parse command
COMMAND=$1
USER_UID=$2
NOTIFICATION_TYPE=$3

case $COMMAND in
    test-local)
        if [ -z "$USER_UID" ] || [ -z "$NOTIFICATION_TYPE" ]; then
            echo -e "${RED}Error: Please specify userUID and notification type${NC}"
            show_usage
            exit 1
        fi
        echo -e "${YELLOW}Testing $NOTIFICATION_TYPE notification on emulator for user $USER_UID...${NC}"
        run_notification "$USER_UID" "$NOTIFICATION_TYPE" --emulator
        ;;
    
    send-prod)
        if [ -z "$USER_UID" ] || [ -z "$NOTIFICATION_TYPE" ]; then
            echo -e "${RED}Error: Please specify userUID and notification type${NC}"
            show_usage
            exit 1
        fi
        echo -e "${RED}⚠️  WARNING: You are about to send a notification to a PRODUCTION device!${NC}"
        echo -e "${RED}This will send a real push notification to user: $USER_UID${NC}"
        echo ""
        read -p "Are you sure you want to continue? (type 'yes' to confirm): " CONFIRM
        if [ "$CONFIRM" = "yes" ]; then
            echo -e "${YELLOW}Sending $NOTIFICATION_TYPE notification to production user $USER_UID...${NC}"
            run_notification "$USER_UID" "$NOTIFICATION_TYPE"
        else
            echo -e "${GREEN}Notification cancelled.${NC}"
        fi
        ;;
    
    *)
        show_usage
        exit 1
        ;;
esac 