#!/usr/bin/env node

/**
 * Firebase Migration Runner
 * 
 * This tool uses Firebase Admin SDK with service account for migrations.
 * Can be configured to run against local emulators or production.
 * 
 * Usage:
 *   node run-migrations.js [migration-name] [--emulator] [--dry-run]
 * 
 * Examples:
 *   node run-migrations.js add-users-to-tenant --emulator
 *   node run-migrations.js add-users-to-tenant --dry-run
 *   node run-migrations.js add-users-to-tenant
 */

const path = require('path');
const fs = require('fs');

// Use firebase-admin from functions directory
const functionsDir = path.join(__dirname, '..', '..', 'functions');
const admin = require(path.join(functionsDir, 'node_modules', 'firebase-admin'));

// Parse command line arguments
const args = process.argv.slice(2);
const migrationName = args[0];
const useEmulator = args.includes('--emulator');
const isDryRun = args.includes('--dry-run');

// Configuration
const EMULATOR_HOST = 'localhost';
const FIRESTORE_PORT = 8080;
const AUTH_PORT = 9099;

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Logging utilities
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log();
  log(`${'='.repeat(60)}`, 'blue');
  log(title, 'bright');
  log(`${'='.repeat(60)}`, 'blue');
  console.log();
}

function logProgress(message) {
  log(`➡️  ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// Initialize Firebase Admin SDK
async function initializeFirebase() {
  try {
    if (useEmulator) {
      // Configure for emulator
      process.env.FIRESTORE_EMULATOR_HOST = `${EMULATOR_HOST}:${FIRESTORE_PORT}`;
      process.env.FIREBASE_AUTH_EMULATOR_HOST = `${EMULATOR_HOST}:${AUTH_PORT}`;

      // Initialize without credentials for emulator
      admin.initializeApp({
        projectId: 'fiaranow' // Use actual project ID for emulator
      });

      logSuccess('Connected to Firebase Emulator Suite');
    } else {
      // Look for service account file
      const serviceAccountPath = path.join(__dirname, '..', '..', 'fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json');

      if (!fs.existsSync(serviceAccountPath)) {
        throw new Error(
          'Service account file not found at firebase/fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json\n' +
          'Please ensure you have the service account key file in the correct location.\n' +
          'If you need to download it:\n' +
          '1. Go to Firebase Console > Project Settings > Service Accounts\n' +
          '2. Generate new private key\n' +
          '3. Save as firebase/fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json'
        );
      }

      // Initialize with service account
      const serviceAccount = require(serviceAccountPath);
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount)
      });

      logSuccess(`Connected to Firebase Project: ${serviceAccount.project_id}`);

      if (!isDryRun) {
        logWarning('Running against PRODUCTION database!');
        logWarning('Use --dry-run flag to test first');
      }
    }

    return {
      db: admin.firestore(),
      auth: admin.auth(),
      FieldValue: admin.firestore.FieldValue
    };
  } catch (error) {
    logError(`Failed to initialize Firebase: ${error.message}`);
    process.exit(1);
  }
}

// Migration registry
const migrations = {
  '001__add-users-to-tenant': require('./001__add-users-to-tenant'),
  '002__denormalize-service-active-by-tenant': require('./002__denormalize-service-active-by-tenant'),
  // Add more migrations here as needed
};

// Progress tracking
class MigrationProgress {
  constructor(migrationName) {
    this.migrationName = migrationName;
    this.startTime = Date.now();
    this.stats = {
      processed: 0,
      succeeded: 0,
      failed: 0,
      skipped: 0
    };
    this.errors = [];
  }

  recordSuccess() {
    this.stats.processed++;
    this.stats.succeeded++;
  }

  recordFailure(error, context = {}) {
    this.stats.processed++;
    this.stats.failed++;
    this.errors.push({ error: error.message, context, timestamp: new Date() });
  }

  recordSkipped() {
    this.stats.processed++;
    this.stats.skipped++;
  }

  printSummary() {
    const duration = ((Date.now() - this.startTime) / 1000).toFixed(2);

    logSection('Migration Summary');
    log(`Migration: ${this.migrationName}`);
    log(`Duration: ${duration} seconds`);
    log(`Total Processed: ${this.stats.processed}`);
    logSuccess(`Succeeded: ${this.stats.succeeded}`);
    if (this.stats.failed > 0) {
      logError(`Failed: ${this.stats.failed}`);
    }
    if (this.stats.skipped > 0) {
      logWarning(`Skipped: ${this.stats.skipped}`);
    }

    if (this.errors.length > 0) {
      console.log();
      log('Errors:', 'red');
      this.errors.forEach((err, idx) => {
        console.log(`  ${idx + 1}. ${err.error}`);
        if (err.context && Object.keys(err.context).length > 0) {
          console.log(`     Context: ${JSON.stringify(err.context)}`);
        }
      });
    }
  }
}

// Main execution
async function main() {
  try {
    // Validate arguments
    if (!migrationName) {
      logError('Please specify a migration to run');
      console.log('Available migrations:');
      Object.keys(migrations).forEach(name => {
        console.log(`  - ${name}`);
      });
      process.exit(1);
    }

    if (!migrations[migrationName]) {
      logError(`Unknown migration: ${migrationName}`);
      console.log('Available migrations:');
      Object.keys(migrations).forEach(name => {
        console.log(`  - ${name}`);
      });
      process.exit(1);
    }

    // Initialize Firebase
    logSection('Initializing Firebase');
    const firebase = await initializeFirebase();

    // Get migration
    const migration = migrations[migrationName];

    // Display migration info
    logSection('Migration Information');
    log(`Name: ${migration.name || migrationName}`);
    log(`Description: ${migration.description || 'No description provided'}`);
    log(`Mode: ${isDryRun ? 'DRY RUN' : 'LIVE'}`);
    log(`Target: ${useEmulator ? 'EMULATOR' : 'PRODUCTION'}`);

    // Run migration
    logSection('Running Migration');
    const progress = new MigrationProgress(migrationName);

    await migration.run({
      db: firebase.db,
      auth: firebase.auth,
      FieldValue: firebase.FieldValue,
      isDryRun,
      useEmulator,
      progress,
      log: logProgress,
      logSuccess,
      logError,
      logWarning
    });

    // Print summary
    progress.printSummary();

    logSection('Migration Complete');

    process.exit(0);
  } catch (error) {
    logError(`Migration failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { initializeFirebase, MigrationProgress };