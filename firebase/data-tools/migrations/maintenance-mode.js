/**
 * Maintenance Mode Utility for Firebase Migrations
 * 
 * This utility provides reusable functions for managing maintenance mode
 * during critical database migrations that require downtime.
 * 
 * Usage:
 *   const { activateMaintenanceMode, deactivateMaintenanceMode } = require('./maintenance-mode');
 *   
 *   // In your migration:
 *   await activateMaintenanceMode({ db, FieldValue, isDryRun, log, logSuccess, logError, reason: 'My migration' });
 *   // ... perform critical operations ...
 *   await deactivateMaintenanceMode({ db, FieldValue, isDryRun, log, logSuccess, logError, logWarning });
 */

/**
 * Activates maintenance mode by setting global configuration
 * 
 * @param {Object} options - Configuration options
 * @param {Object} options.db - Firestore database instance
 * @param {Object} options.FieldValue - Firestore FieldValue utility
 * @param {boolean} options.isDryRun - Whether this is a dry run
 * @param {Function} options.log - Logging function
 * @param {Function} options.logSuccess - Success logging function
 * @param {Function} options.logError - Error logging function
 * @param {string} options.reason - Reason for maintenance mode (optional)
 * @param {string} options.message - Custom message to display (optional)
 * @param {number} options.propagationDelayMs - Time to wait for propagation (default: 10000ms)
 * @returns {Promise<boolean>} - Returns true if successfully activated
 */
async function activateMaintenanceMode({
  db,
  FieldValue,
  isDryRun,
  log,
  logSuccess,
  logError,
  reason = 'Database migration in progress',
  message = 'System maintenance in progress. Service will be restored shortly.',
  propagationDelayMs = 10000
}) {
  if (isDryRun) {
    log('📝 DRY RUN: Would activate maintenance mode');
    log(`   Reason: ${reason}`);
    log(`   Message: ${message}`);
    return true;
  }

  try {
    log('🔒 Activating maintenance mode...');
    log(`   Reason: ${reason}`);

    // Update global configuration to enable maintenance mode
    await db.collection('configurations').doc('global').set({
      maintenanceMode: {
        enabled: true,
        message: message,
        startedAt: FieldValue.serverTimestamp(),
        reason: reason
      }
    }, { merge: true });

    logSuccess('✅ Maintenance mode activated');

    // Wait for the change to propagate
    if (propagationDelayMs > 0) {
      log(`⏳ Waiting ${propagationDelayMs / 1000} seconds for maintenance mode to propagate...`);
      await new Promise(resolve => setTimeout(resolve, propagationDelayMs));
    }

    return true;

  } catch (error) {
    logError(`Failed to activate maintenance mode: ${error.message}`);
    throw new Error('Cannot proceed without maintenance mode');
  }
}

/**
 * Deactivates maintenance mode by updating global configuration
 * 
 * @param {Object} options - Configuration options
 * @param {Object} options.db - Firestore database instance
 * @param {Object} options.FieldValue - Firestore FieldValue utility
 * @param {boolean} options.isDryRun - Whether this is a dry run
 * @param {Function} options.log - Logging function
 * @param {Function} options.logSuccess - Success logging function
 * @param {Function} options.logError - Error logging function
 * @param {Function} options.logWarning - Warning logging function
 * @returns {Promise<boolean>} - Returns true if successfully deactivated
 */
async function deactivateMaintenanceMode({
  db,
  FieldValue,
  isDryRun,
  log,
  logSuccess,
  logError,
  logWarning
}) {
  if (isDryRun) {
    log('📝 DRY RUN: Would deactivate maintenance mode');
    return true;
  }

  try {
    log('🔓 Deactivating maintenance mode...');

    await db.collection('configurations').doc('global').set({
      maintenanceMode: {
        enabled: false,
        endedAt: FieldValue.serverTimestamp()
      }
    }, { merge: true });

    logSuccess('✅ Maintenance mode deactivated');
    return true;

  } catch (error) {
    logError(`Failed to deactivate maintenance mode: ${error.message}`);
    logWarning('⚠️  MANUAL ACTION REQUIRED: Please manually deactivate maintenance mode');
    return false;
  }
}

/**
 * Wrapper function that ensures maintenance mode is properly activated and deactivated
 * around a critical migration operation
 * 
 * @param {Object} options - Configuration options
 * @param {Object} options.db - Firestore database instance
 * @param {Object} options.FieldValue - Firestore FieldValue utility
 * @param {boolean} options.isDryRun - Whether this is a dry run
 * @param {Function} options.log - Logging function
 * @param {Function} options.logSuccess - Success logging function
 * @param {Function} options.logError - Error logging function
 * @param {Function} options.logWarning - Warning logging function
 * @param {string} options.reason - Reason for maintenance mode
 * @param {string} options.message - Custom message to display (optional)
 * @param {Function} options.operation - Async function to execute during maintenance mode
 * @returns {Promise<any>} - Returns the result of the operation
 */
async function withMaintenanceMode({
  db,
  FieldValue,
  isDryRun,
  log,
  logSuccess,
  logError,
  logWarning,
  reason,
  message,
  propagationDelayMs = 10000,
  operation
}) {
  let maintenanceModeActivated = false;

  try {
    // Step 1: Activate maintenance mode
    log('\n📍 STEP: Activating maintenance mode...\n');
    await activateMaintenanceMode({
      db,
      FieldValue,
      isDryRun,
      log,
      logSuccess,
      logError,
      reason,
      message,
      propagationDelayMs
    });
    maintenanceModeActivated = true;

    // Step 2: Execute the critical operation
    log('\n📍 STEP: Executing critical operation...\n');
    const result = await operation();

    // Step 3: Deactivate maintenance mode
    log('\n📍 STEP: Deactivating maintenance mode...\n');
    const deactivated = await deactivateMaintenanceMode({
      db,
      FieldValue,
      isDryRun,
      log,
      logSuccess,
      logError,
      logWarning
    });

    return {
      result,
      maintenanceModeActivated: true,
      maintenanceModeDeactivated: deactivated
    };

  } catch (error) {
    logError(`Operation failed: ${error.message}`);

    // Ensure maintenance mode is deactivated even if operation fails
    if (maintenanceModeActivated) {
      log('\n🚨 EMERGENCY: Attempting to deactivate maintenance mode due to failure...');
      await deactivateMaintenanceMode({
        db,
        FieldValue,
        isDryRun,
        log,
        logSuccess,
        logError,
        logWarning
      });
    }

    throw error;
  }
}

/**
 * Checks if maintenance mode is currently active
 * 
 * @param {Object} options - Configuration options
 * @param {Object} options.db - Firestore database instance
 * @param {Function} options.log - Logging function (optional)
 * @returns {Promise<boolean>} - Returns true if maintenance mode is active
 */
async function isMaintenanceModeActive({ db, log }) {
  try {
    const configDoc = await db.collection('configurations').doc('global').get();
    
    if (!configDoc.exists) {
      return false;
    }

    const config = configDoc.data();
    const isActive = config.maintenanceMode?.enabled === true;

    if (log && isActive) {
      log(`⚠️  Maintenance mode is currently active: ${config.maintenanceMode.reason || 'Unknown reason'}`);
    }

    return isActive;
  } catch (error) {
    if (log) {
      log(`Failed to check maintenance mode status: ${error.message}`);
    }
    return false;
  }
}

module.exports = {
  activateMaintenanceMode,
  deactivateMaintenanceMode,
  withMaintenanceMode,
  isMaintenanceModeActive
};