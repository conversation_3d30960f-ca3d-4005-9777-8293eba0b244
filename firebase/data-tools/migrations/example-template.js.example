/**
 * Example Migration Template
 * 
 * Copy this file to create new migrations.
 * Rename it to match your migration name (e.g., update-trip-fields.js)
 * 
 * Remember to:
 * 1. Update the module.exports name and description
 * 2. Register your migration in ../run-migrations.js
 * 3. Test with --emulator and --dry-run first
 * 4. If your migration requires maintenance mode, use the maintenance-mode utility
 */

// For migrations that require maintenance mode, import the utility
// const { withMaintenanceMode, activateMaintenanceMode, deactivateMaintenanceMode } = require('./maintenance-mode');

module.exports = {
  id: '999__example-migration',
  name: 'Example Migration',
  description: 'This is an example migration template',
  version: '1.0.0',
  requiresMaintenanceMode: false, // Set to true if this migration requires maintenance mode
  
  async run({ db, auth, FieldValue, isDryRun, progress, log, logSuccess, logError, logWarning }) {
    // Batch operations for efficiency
    let batch = db.batch();
    let batchCount = 0;
    const MAX_BATCH_SIZE = 500;
    
    // Helper to commit batch when needed
    async function commitBatchIfNeeded(force = false) {
      if (batchCount > 0 && (force || batchCount >= MAX_BATCH_SIZE)) {
        if (!isDryRun) {
          await batch.commit();
          logSuccess(`Committed batch of ${batchCount} operations`);
        } else {
          log(`[DRY RUN] Would commit batch of ${batchCount} operations`);
        }
        // Reset batch and counter
        batch = db.batch();
        batchCount = 0;
      }
    }
    
    // Example: Process all documents in a collection
    log('Starting example migration...');
    
    try {
      // Example: Get all documents from a collection
      const snapshot = await db.collection('your_collection').get();
      log(`Found ${snapshot.size} documents to process`);
      
      // Process each document
      for (const doc of snapshot.docs) {
        const data = doc.data();
        
        try {
          // Example: Check if document needs updating
          if (data.someField === undefined) {
            log(`Processing document ${doc.id}`);
            
            // Example: Update document
            const updates = {
              someField: 'new value',
              updatedAt: FieldValue.serverTimestamp()
            };
            
            batch.update(doc.ref, updates);
            batchCount++;
            
            // Commit batch if needed
            await commitBatchIfNeeded();
            
            // Record success
            progress.recordSuccess();
          } else {
            // Document already has the field, skip it
            progress.recordSkipped();
          }
          
        } catch (error) {
          logError(`Failed to process document ${doc.id}: ${error.message}`);
          progress.recordFailure(error, { documentId: doc.id });
        }
      }
      
      // Commit any remaining operations
      await commitBatchIfNeeded(true);
      
    } catch (error) {
      logError(`Migration failed: ${error.message}`);
      throw error;
    }
    
    // Example: Working with subcollections
    log('\nProcessing subcollections...');
    
    try {
      const parentDocs = await db.collection('parent_collection').get();
      
      for (const parentDoc of parentDocs.docs) {
        const subcollection = await parentDoc.ref
          .collection('subcollection')
          .where('someField', '==', 'someValue')
          .get();
        
        log(`Found ${subcollection.size} subcollection documents for parent ${parentDoc.id}`);
        
        // Process subcollection documents...
      }
      
    } catch (error) {
      logWarning(`Subcollection processing failed: ${error.message}`);
      // Decide whether to throw or continue
    }
    
    // Example: Working with Auth users
    log('\nProcessing auth users...');
    
    try {
      let pageToken;
      do {
        const listResult = await auth.listUsers(1000, pageToken);
        
        for (const user of listResult.users) {
          // Process auth user
          log(`Processing user ${user.uid} (${user.email})`);
          
          // Example: Update custom claims
          if (!isDryRun && user.customClaims?.needsUpdate) {
            await auth.setCustomUserClaims(user.uid, {
              ...user.customClaims,
              migrated: true,
              needsUpdate: undefined
            });
          }
        }
        
        pageToken = listResult.pageToken;
      } while (pageToken);
      
    } catch (error) {
      logError(`Auth processing failed: ${error.message}`);
    }
    
    logSuccess('Example migration completed!');
    
    // ===== MAINTENANCE MODE EXAMPLES =====
    
    // Example 1: Using withMaintenanceMode wrapper (recommended)
    // If your migration requires maintenance mode, use this pattern:
    /*
    const { withMaintenanceMode } = require('./maintenance-mode');
    
    async function performCriticalOperation() {
      // Your critical migration logic here
      log('Performing critical database changes...');
      
      // Example: Update critical fields
      const users = await db.collection('mobile_users').get();
      let batch = db.batch();
      let batchCount = 0;
      
      for (const userDoc of users.docs) {
        batch.update(userDoc.ref, {
          criticalField: 'new_value',
          updatedAt: FieldValue.serverTimestamp()
        });
        batchCount++;
        
        if (batchCount >= 500) {
          await batch.commit();
          batch = db.batch();
          batchCount = 0;
        }
      }
      
      if (batchCount > 0) {
        await batch.commit();
      }
      
      return { usersUpdated: users.size };
    }
    
    // Execute with maintenance mode
    const result = await withMaintenanceMode({
      db,
      FieldValue,
      isDryRun,
      log,
      logSuccess,
      logError,
      logWarning,
      reason: 'Critical database schema update',
      message: 'System maintenance in progress. We\'ll be back shortly!',
      operation: performCriticalOperation
    });
    
    logSuccess(`Migration completed! Updated ${result.result.usersUpdated} users`);
    */
    
    // Example 2: Manual maintenance mode control (advanced)
    // For more complex scenarios where you need fine-grained control:
    /*
    const { activateMaintenanceMode, deactivateMaintenanceMode } = require('./maintenance-mode');
    
    try {
      // Activate maintenance mode
      await activateMaintenanceMode({
        db,
        FieldValue,
        isDryRun,
        log,
        logSuccess,
        logError,
        reason: 'Complex multi-step migration',
        message: 'System maintenance in progress. Multiple steps required.',
        propagationDelayMs: 15000 // Wait 15 seconds for propagation
      });
      
      // Step 1: Perform first critical operation
      log('Step 1: Updating user profiles...');
      // ... your code here ...
      
      // Step 2: Perform second critical operation
      log('Step 2: Updating relationships...');
      // ... your code here ...
      
      // Step 3: Perform final critical operation
      log('Step 3: Cleaning up old data...');
      // ... your code here ...
      
      // Deactivate maintenance mode
      await deactivateMaintenanceMode({
        db,
        FieldValue,
        isDryRun,
        log,
        logSuccess,
        logError,
        logWarning
      });
      
    } catch (error) {
      logError(`Migration failed: ${error.message}`);
      
      // Emergency deactivation of maintenance mode
      await deactivateMaintenanceMode({
        db,
        FieldValue,
        isDryRun,
        log,
        logSuccess,
        logError,
        logWarning
      });
      
      throw error;
    }
    */
  }
}; 