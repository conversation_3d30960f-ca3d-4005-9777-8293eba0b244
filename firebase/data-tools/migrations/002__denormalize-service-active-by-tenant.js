/**
 * Migration: Denormalize isServiceActive to isServiceActiveByTenant map
 * 
 * This migration:
 * 1. Activates maintenance mode
 * 2. Builds the isServiceActiveByTenant map from tenant state data
 * 3. Removes the old isServiceActive field
 * 4. Deactivates maintenance mode
 * 
 * Pre-requisites:
 * - Deploy onTenantStateChangeUpdateUser function first
 * - Update mobile app login flow to create tenant states
 * 
 * This migration is NOT reversible due to field removal
 * REQUIRES MAINTENANCE MODE due to critical field changes
 */

const MIGRATION_ID = '002__denormalize-service-active-by-tenant';
const { withMaintenanceMode } = require('./maintenance-mode');

module.exports = {
  id: MIGRATION_ID,
  description: 'Denormalize isServiceActive to isServiceActiveByTenant map and remove old field',
  reversible: false, // Cannot be reversed due to field deletion
  requiresMaintenanceMode: true, // Critical field changes require maintenance mode

  async run({ db, auth, FieldValue, isDryRun, useEmulator, progress, log, logSuccess, logError, logWarning }) {
    log('\n=== DENORMALIZE SERVICE ACTIVE BY TENANT MIGRATION ===\n');
    log('⚠️  WARNING: This migration requires maintenance mode due to critical field changes\n');

    const stats = {
      totalUsers: 0,
      usersWithTenantStates: 0,
      tenantStatesProcessed: 0,
      usersUpdated: 0,
      fieldRemoved: 0,
      errors: 0,
      maintenanceModeActivated: false,
      maintenanceModeDeactivated: false
    };

    // Track processed users for field removal
    const processedUsers = new Set();

    // Define the critical operation to run during maintenance mode
    async function performMigration() {

      log('Fetching all mobile users...');
      const usersSnapshot = await db.collection('mobile_users').get();
      stats.totalUsers = usersSnapshot.size;
      log(`Found ${stats.totalUsers} mobile users\n`);

      // Process in batches
      const batchSize = 500;
      let batch = db.batch();
      let batchCount = 0;

      async function commitBatchIfNeeded(force = false) {
        if (batchCount > 0 && (force || batchCount >= batchSize)) {
          if (!isDryRun) {
            await batch.commit();
            log(`  ✅ Committed batch of ${batchCount} operations`);
          } else {
            log(`  📝 DRY RUN: Would commit batch of ${batchCount} operations`);
          }
          batch = db.batch();
          batchCount = 0;
        }
      }

      // Step 1: Build isServiceActiveByTenant maps
      log('\n📍 STEP 1: Building isServiceActiveByTenant maps from tenant states...\n');

      for (const userDoc of usersSnapshot.docs) {
        const userId = userDoc.id;
        const userData = userDoc.data();

        try {
          // Get all tenant states for this user
          const tenantStatesSnapshot = await db
            .collection('mobile_users')
            .doc(userId)
            .collection('tenant_states')
            .get();

          if (tenantStatesSnapshot.empty) {
            log(`  ⚠️  User ${userId} (${userData.displayName || userData.email || 'Unknown'}) has no tenant states`);
            continue;
          }

          stats.usersWithTenantStates++;

          // Build the isServiceActiveByTenant map
          const isServiceActiveByTenant = {};

          tenantStatesSnapshot.docs.forEach(doc => {
            const tenantId = doc.id;
            const tenantState = doc.data();
            isServiceActiveByTenant[tenantId] = tenantState.isServiceActive || false;
            stats.tenantStatesProcessed++;
          });

          // Update the user document
          const userRef = db.collection('mobile_users').doc(userId);

          if (isDryRun) {
            log(`  📝 DRY RUN: Would update user ${userId}:`);
            log(`     - Add isServiceActiveByTenant: ${JSON.stringify(isServiceActiveByTenant)}`);
          } else {
            batch.update(userRef, {
              isServiceActiveByTenant: isServiceActiveByTenant
            });
          }

          processedUsers.add(userId);
          stats.usersUpdated++;
          batchCount++;

          await commitBatchIfNeeded();
          progress.recordSuccess();

        } catch (error) {
          logError(`Failed to process user ${userId}: ${error.message}`);
          progress.recordFailure(error, { userId, displayName: userData.displayName });
          stats.errors++;
        }
      }

      // Commit remaining operations
      await commitBatchIfNeeded(true);

      // Step 2: Remove old isServiceActive field
      log('\n📍 STEP 2: Removing old isServiceActive field...\n');
      log('⚠️  CRITICAL: Removing field that current mobile app depends on\n');

      batch = db.batch();
      batchCount = 0;

      for (const userDoc of usersSnapshot.docs) {
        const userId = userDoc.id;
        const userData = userDoc.data();

        try {
          // Only process if user has the old field
          if (!userData.hasOwnProperty('isServiceActive')) {
            continue;
          }

          const userRef = db.collection('mobile_users').doc(userId);

          if (isDryRun) {
            log(`  📝 DRY RUN: Would remove isServiceActive from user ${userId}`);
          } else {
            batch.update(userRef, {
              isServiceActive: FieldValue.delete()
            });
          }

          stats.fieldRemoved++;
          batchCount++;

          await commitBatchIfNeeded();

        } catch (error) {
          logError(`Failed to remove field from user ${userId}: ${error.message}`);
          stats.errors++;
        }
      }

      // Commit remaining removals
      await commitBatchIfNeeded(true);

      return stats;
    }

    // Execute the migration with maintenance mode
    try {
      const result = await withMaintenanceMode({
        db,
        FieldValue,
        isDryRun,
        log,
        logSuccess,
        logError,
        logWarning,
        reason: 'Database migration: Denormalizing service status fields',
        message: 'System maintenance in progress. Service will be restored shortly.',
        propagationDelayMs: useEmulator ? 0 : 10000, // Skip wait for local runs
        operation: performMigration
      });

      // Update stats with maintenance mode status
      stats.maintenanceModeActivated = result.maintenanceModeActivated;
      stats.maintenanceModeDeactivated = result.maintenanceModeDeactivated;

    } catch (error) {
      logError(`Migration failed: ${error.message}`);
      throw error;
    }

    // Summary
    log('\n=== MIGRATION SUMMARY ===\n');
    log(`Total users: ${stats.totalUsers}`);
    log(`Users with tenant states: ${stats.usersWithTenantStates}`);
    log(`Tenant states processed: ${stats.tenantStatesProcessed}`);
    log(`Users updated with map: ${stats.usersUpdated}`);
    log(`Fields removed: ${stats.fieldRemoved}`);
    log(`Errors: ${stats.errors}`);
    log(`Maintenance mode activated: ${stats.maintenanceModeActivated ? '✅' : '❌'}`);
    log(`Maintenance mode deactivated: ${stats.maintenanceModeDeactivated ? '✅' : '❌'}`);

    if (stats.errors > 0) {
      logWarning('\n⚠️  Migration completed with errors. Review the error log above.');
    } else {
      logSuccess('\n✅ Migration completed successfully!');
    }

    // Post-migration notes
    log('\n📋 POST-MIGRATION CHECKLIST:');
    log('1. ✓ Deploy onTenantStateChangeUpdateUser function');
    log('2. ✓ Update mobile app login flow');
    log('3. ✓ Run this migration (with maintenance mode)');
    log('4. □ Deploy updated mobile app immediately');
    log('5. □ Deploy updated admin panel');
    log('6. □ Monitor sync function logs for errors');
    log('7. □ Verify maintenance mode was properly deactivated');

    if (!stats.maintenanceModeDeactivated && !isDryRun) {
      logWarning('\n🚨 WARNING: Maintenance mode may still be active! Please verify manually.');
    }

    return {
      stats,
      success: stats.errors === 0 && stats.maintenanceModeDeactivated
    };
  }
};
