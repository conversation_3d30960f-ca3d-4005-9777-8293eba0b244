/**
 * Add Users to Tenant Migration
 * 
 * This migration ensures all mobile users and admin users are properly
 * associated with the 'fiaranow' tenant.
 * 
 * For mobile users:
 * - Adds 'fiaranow' to their tenantIDs array if not present
 * - Creates a tenant_state subcollection document for 'fiaranow'
 * 
 * For admin users:
 * - Creates a tenant access subcollection document for 'fiaranow'
 * - Assigns ADMIN role (1) by default to existing admins
 */

module.exports = {
  id: '001__add-users-to-tenant',
  name: 'Add Users to Tenant',
  description: 'Ensures all users are properly associated with the fiaranow tenant',
  version: '1.0.0',

  async run({ db, auth, FieldValue, isDryRun, useEmulator, progress, log, logSuccess, logError, logWarning }) {
    const TENANT_ID = 'fiaranow';
    let batch = db.batch();
    let batchCount = 0;
    const MAX_BATCH_SIZE = 500;

    // Stats for summary
    const stats = {
      mobileUsers: {
        needTenantID: 0,
        needTenantState: 0,
        alreadyMigrated: 0
      },
      adminUsers: {
        needTenantAccess: 0,
        alreadyMigrated: 0
      }
    };

    // Helper to commit batch when needed
    async function commitBatchIfNeeded(force = false) {
      if (batchCount > 0 && (force || batchCount >= MAX_BATCH_SIZE)) {
        if (!isDryRun) {
          await batch.commit();
          logSuccess(`Committed batch of ${batchCount} operations`);
        } else {
          log(`[DRY RUN] Would commit batch of ${batchCount} operations`);
        }
        // Reset batch and counter
        batch = db.batch();
        batchCount = 0;
      }
    }

    // Process Mobile Users
    log('Processing mobile users...');

    try {
      const mobileUsersSnapshot = await db.collection('mobile_users').get();
      log(`Found ${mobileUsersSnapshot.size} mobile users\n`);

      for (const doc of mobileUsersSnapshot.docs) {
        const userData = doc.data();
        const uid = doc.id;

        try {
          // Check if user already has fiaranow in tenantIDs
          const currentTenantIDs = userData.tenantIDs || [];
          const needsTenantID = !currentTenantIDs.includes(TENANT_ID);

          // Check if tenant state exists
          const tenantStateRef = db.collection('mobile_users').doc(uid)
            .collection('tenant_states').doc(TENANT_ID);
          const tenantStateDoc = await tenantStateRef.get();
          const needsTenantState = !tenantStateDoc.exists;

          if (!needsTenantID && !needsTenantState) {
            stats.mobileUsers.alreadyMigrated++;
            progress.recordSkipped();
            continue;
          }

          // Show what needs to be done
          const userDisplay = `${userData.displayName || userData.email || uid}`;
          const userType = userData.primaryUserType === 1 ? ' [DRIVER]' : ' [RIDER]';
          log(`👤 Mobile User: ${userDisplay}${userType}`);
          log(`   UID: ${uid}`);

          // Update tenantIDs array if needed
          if (needsTenantID) {
            stats.mobileUsers.needTenantID++;
            if (isDryRun) {
              log(`   📝 UPDATE /mobile_users/${uid}`);
              log(`      + tenantIDs: [${currentTenantIDs.length > 0 ? currentTenantIDs.join(', ') + ', ' : ''}"${TENANT_ID}"]`);
            }
            batch.update(doc.ref, {
              tenantIDs: FieldValue.arrayUnion(TENANT_ID)
            });
            batchCount++;
          }

          // Create tenant state if needed
          if (needsTenantState) {
            stats.mobileUsers.needTenantState++;
            const tenantStateData = {
              uid: uid,
              tenantId: TENANT_ID,
              isActive: true,
              driverTags: [],
              isServiceActive: false,
              joinedAt: FieldValue.serverTimestamp(),
              lastActiveAt: userData.lastSeen || FieldValue.serverTimestamp()
            };

            // If user is a driver (primaryUserType == 1), set driver-specific fields
            if (userData.primaryUserType === 1) {
              tenantStateData.driverType = 'third_party'; // Default for existing drivers
            }

            if (isDryRun) {
              log(`   📁 CREATE /mobile_users/${uid}/tenant_states/${TENANT_ID}`);
              log(`      {`);
              log(`        uid: "${uid}",`);
              log(`        tenantId: "${TENANT_ID}",`);
              log(`        isActive: true,`);
              log(`        driverTags: [],`);
              log(`        isServiceActive: false,`);
              if (userData.primaryUserType === 1) {
                log(`        driverType: "third_party",`);
              }
              log(`        joinedAt: <serverTimestamp>,`);
              log(`        lastActiveAt: ${userData.lastSeen ? '<existing lastSeen>' : '<serverTimestamp>'}`);
              log(`      }`);
            }

            batch.set(tenantStateRef, tenantStateData);
            batchCount++;
          }

          log(''); // Add blank line between users

          await commitBatchIfNeeded();
          progress.recordSuccess();

        } catch (error) {
          logError(`Failed to process mobile user ${uid}: ${error.message}`);
          progress.recordFailure(error, { uid, displayName: userData.displayName });
        }
      }

      // Commit remaining mobile user operations
      await commitBatchIfNeeded(true);

    } catch (error) {
      logError(`Failed to fetch mobile users: ${error.message}`);
      throw error;
    }

    // Process Admin Users
    log('\nProcessing admin users...');

    try {
      const adminUsersSnapshot = await db.collection('admin_users').get();
      log(`Found ${adminUsersSnapshot.size} admin users\n`);

      for (const doc of adminUsersSnapshot.docs) {
        const adminData = doc.data();
        const uid = doc.id;

        try {
          // Check if tenant access exists
          const tenantAccessRef = db.collection('admin_users').doc(uid)
            .collection('tenants').doc(TENANT_ID);
          const tenantAccessDoc = await tenantAccessRef.get();

          if (tenantAccessDoc.exists) {
            stats.adminUsers.alreadyMigrated++;
            progress.recordSkipped();
            continue;
          }

          stats.adminUsers.needTenantAccess++;

          // Show what needs to be done
          log(`👨‍💼 Admin User: ${adminData.displayName || adminData.email}`);
          log(`   UID: ${uid}`);
          log(`   Status: ${adminData.isActive ? 'Active' : 'Inactive'}`);

          // Create tenant access
          const tenantAccessData = {
            tenantId: TENANT_ID,
            isActive: adminData.isActive || false,
            role: 1, // Default to ADMIN role for existing admins
            assignedAt: FieldValue.serverTimestamp(),
            assignedBy: 'migration' // System migration
          };

          if (isDryRun) {
            log(`   📁 CREATE /admin_users/${uid}/tenants/${TENANT_ID}`);
            log(`      {`);
            log(`        tenantId: "${TENANT_ID}",`);
            log(`        isActive: ${adminData.isActive || false},`);
            log(`        role: 1 (ADMIN),`);
            log(`        assignedAt: <serverTimestamp>,`);
            log(`        assignedBy: "migration"`);
            log(`      }`);
          }

          log(''); // Add blank line between users

          batch.set(tenantAccessRef, tenantAccessData);
          batchCount++;

          await commitBatchIfNeeded();
          progress.recordSuccess();

        } catch (error) {
          logError(`Failed to process admin user ${uid}: ${error.message}`);
          progress.recordFailure(error, { uid, email: adminData.email });
        }
      }

      // Commit remaining admin user operations
      await commitBatchIfNeeded(true);

    } catch (error) {
      logError(`Failed to fetch admin users: ${error.message}`);
      throw error;
    }

    // Check if tenant document exists
    log('\nChecking tenant document...');

    try {
      const tenantRef = db.collection('tenants').doc(TENANT_ID);
      const tenantDoc = await tenantRef.get();

      if (!tenantDoc.exists) {
        log(`🏢 Tenant document needs to be created\n`);

        const tenantData = {
          id: TENANT_ID,
          name: 'Fiaranow',
          isActive: true,
          createdAt: FieldValue.serverTimestamp(),
          settings: {
            branding: {
              primaryColor: '#4B5563',
              logo: '/favicon.png'
            },
            features: {
              enableChat: true,
              enableFeedback: true
            }
          }
        };

        if (isDryRun) {
          log(`   �� CREATE /tenants/${TENANT_ID}`);
          log(`      {`);
          log(`        id: "${TENANT_ID}",`);
          log(`        name: "Fiaranow",`);
          log(`        isActive: true,`);
          log(`        createdAt: <serverTimestamp>,`);
          log(`        settings: {`);
          log(`          branding: {`);
          log(`            primaryColor: "#4B5563",`);
          log(`            logo: "/favicon.png"`);
          log(`          },`);
          log(`          features: {`);
          log(`            enableChat: true,`);
          log(`            enableFeedback: true`);
          log(`          }`);
          log(`        }`);
          log(`      }`);
        } else {
          await tenantRef.set(tenantData);
          logSuccess(`Created tenant document for ${TENANT_ID}`);
        }
      } else {
        log(`✅ Tenant document for ${TENANT_ID} already exists`);
      }

    } catch (error) {
      logError(`Failed to check/create tenant document: ${error.message}`);
      throw error;
    }

    // Show summary
    if (isDryRun) {
      log('\n' + '='.repeat(60));
      log('DRY RUN SUMMARY');
      log('='.repeat(60));
      log('\n📊 Mobile Users:');
      log(`   • Need tenantIDs update: ${stats.mobileUsers.needTenantID}`);
      log(`   • Need tenant_states subcollection: ${stats.mobileUsers.needTenantState}`);
      log(`   • Already migrated: ${stats.mobileUsers.alreadyMigrated}`);
      log(`   • Total: ${stats.mobileUsers.needTenantID + stats.mobileUsers.alreadyMigrated}`);

      log('\n📊 Admin Users:');
      log(`   • Need tenant access subcollection: ${stats.adminUsers.needTenantAccess}`);
      log(`   • Already migrated: ${stats.adminUsers.alreadyMigrated}`);
      log(`   • Total: ${stats.adminUsers.needTenantAccess + stats.adminUsers.alreadyMigrated}`);

      const totalOperations = stats.mobileUsers.needTenantID + stats.mobileUsers.needTenantState + stats.adminUsers.needTenantAccess;
      log(`\n🔄 Total operations that would be performed: ${totalOperations}`);
    }

    logSuccess('\nMigration completed successfully!');
  }
}; 