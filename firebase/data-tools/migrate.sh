#!/bin/bash

# Migration Runner Convenience Script
# Provides easy commands for common migration operations

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to show usage
show_usage() {
    echo -e "${BLUE}Firebase Migration Tool${NC}"
    echo -e "${BLUE}======================${NC}"
    echo ""
    echo "Usage: $0 <command> [migration-name]"
    echo ""
    echo "Commands:"
    echo "  list                      List all available migrations"
    echo "  test <migration>          Test migration on emulator (dry-run)"
    echo "  run-local <migration>     Run migration on emulator"
    echo "  test-prod <migration>     Test migration on production (dry-run)"
    echo "  run-prod <migration>      Run migration on production (⚠️  CAUTION!)"
    echo ""
    echo "Examples:"
    echo "  $0 list"
    echo "  $0 test add-users-to-tenant"
    echo "  $0 run-local add-users-to-tenant"
    echo "  $0 test-prod add-users-to-tenant"
    echo ""
}

# Function to run migration command
run_migration() {
    local args="$@"
    cd "$SCRIPT_DIR" && node migrations/run-migrations.js $args
}

# Parse command
COMMAND=$1
MIGRATION=$2

case $COMMAND in
    list)
        echo -e "${BLUE}Available Migrations:${NC}"
        echo ""
        # Hardcoded list with deployment status
        echo -e "  - ${GREEN}✅${NC} 001__add-users-to-tenant ${GREEN}(deployed to production)${NC}"
        echo -e "  - ${GREEN}✅${NC} 002__denormalize-service-active-by-tenant ${GREEN}(deployed to production)${NC}"
        echo ""
        ;;
    
    test)
        if [ -z "$MIGRATION" ]; then
            echo -e "${RED}Error: Please specify a migration name${NC}"
            show_usage
            exit 1
        fi
        echo -e "${YELLOW}Testing migration on emulator (dry-run)...${NC}"
        run_migration "$MIGRATION" --emulator --dry-run
        ;;
    
    run-local)
        if [ -z "$MIGRATION" ]; then
            echo -e "${RED}Error: Please specify a migration name${NC}"
            show_usage
            exit 1
        fi
        echo -e "${GREEN}Running migration on emulator...${NC}"
        run_migration "$MIGRATION" --emulator
        ;;
    
    test-prod)
        if [ -z "$MIGRATION" ]; then
            echo -e "${RED}Error: Please specify a migration name${NC}"
            show_usage
            exit 1
        fi
        echo -e "${YELLOW}Testing migration on production (dry-run)...${NC}"
        run_migration "$MIGRATION" --dry-run
        ;;
    
    run-prod)
        if [ -z "$MIGRATION" ]; then
            echo -e "${RED}Error: Please specify a migration name${NC}"
            show_usage
            exit 1
        fi
        echo -e "${RED}⚠️  WARNING: You are about to run a migration on PRODUCTION!${NC}"
        echo -e "${RED}This will make real changes to your production database.${NC}"
        echo ""
        read -p "Are you sure you want to continue? (type 'yes' to confirm): " CONFIRM
        if [ "$CONFIRM" = "yes" ]; then
            echo -e "${YELLOW}Running migration on production...${NC}"
            run_migration "$MIGRATION"
        else
            echo -e "${GREEN}Migration cancelled.${NC}"
        fi
        ;;
    
    *)
        show_usage
        exit 1
        ;;
esac 