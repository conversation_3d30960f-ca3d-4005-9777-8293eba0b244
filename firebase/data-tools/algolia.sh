#!/bin/bash

# Algolia Sync Tool
# Syncs Firestore data to Algolia indexes

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to show usage
show_usage() {
    echo -e "${BLUE}Algolia Sync Tool${NC}"
    echo -e "${BLUE}=================${NC}"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  sync-all              Sync all collections to Algolia"
    echo "  sync-trips            Sync trips collection"
    echo "  sync-users            Sync mobile_users collection"
    echo "  sync-vehicles         Sync vehicles collection"
    echo "  sync-payments         Sync payments collection"
    echo "  sync-chats            Sync chat_sessions collection"
    echo "  configure-indexes     Configure Algolia indexes with proper settings"
    echo ""
    echo "Options:"
    echo "  --emulator            Use emulator data and dev Algolia (default)"
    echo "  --prod                Use production data and prod Algolia"
    echo "  --dry-run             Preview changes without syncing"
    echo "  --tenant=<id>         Filter by specific tenant ID"
    echo ""
    echo "Examples:"
    echo "  $0 sync-all                          # Sync all collections from emulator"
    echo "  $0 sync-trips --tenant=tenant123     # Sync trips for specific tenant"
    echo "  $0 sync-users --prod --dry-run       # Preview production sync"
    echo "  $0 configure-indexes                 # Configure all indexes from emulator"
    echo "  $0 configure-indexes --prod          # Configure production indexes"
    echo ""
}

# Parse command
COMMAND=$1
shift

# Default options
USE_PROD=false
DRY_RUN=false
TENANT_ID=""

# Parse options
while [[ $# -gt 0 ]]; do
    case $1 in
        --prod)
            USE_PROD=true
            shift
            ;;
        --emulator)
            USE_PROD=false
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --tenant=*)
            TENANT_ID="${1#*=}"
            shift
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            show_usage
            exit 1
            ;;
    esac
done

# Build node command arguments
NODE_ARGS=""

# Add command
case $COMMAND in
    sync-all|sync-trips|sync-users|sync-vehicles|sync-payments|sync-chats)
        NODE_ARGS="$COMMAND"
        SCRIPT_NAME="algolia-sync.js"
        ;;
    configure-indexes)
        NODE_ARGS=""
        SCRIPT_NAME="algolia-configure-indexes.js"
        ;;
    *)
        show_usage
        exit 1
        ;;
esac

# Add environment flag
if [ "$USE_PROD" = true ]; then
    NODE_ARGS="$NODE_ARGS --prod"
    echo -e "${RED}⚠️  WARNING: Running against PRODUCTION data and PRODUCTION Algolia!${NC}"
    echo ""
    read -p "Are you sure you want to continue? (type 'yes' to confirm): " CONFIRM
    if [ "$CONFIRM" != "yes" ]; then
        echo -e "${GREEN}Sync cancelled.${NC}"
        exit 0
    fi
else
    NODE_ARGS="$NODE_ARGS --emulator"
fi

# Add dry-run flag (only for sync commands)
if [ "$DRY_RUN" = true ] && [ "$COMMAND" != "configure-indexes" ]; then
    NODE_ARGS="$NODE_ARGS --dry-run"
fi

# Add tenant filter
if [ -n "$TENANT_ID" ]; then
    NODE_ARGS="$NODE_ARGS --tenant=$TENANT_ID"
fi

# Display configuration
echo -e "${BLUE}Configuration:${NC}"
echo -e "  Command: ${CYAN}$COMMAND${NC}"
echo -e "  Environment: ${CYAN}$([ "$USE_PROD" = true ] && echo "PRODUCTION" || echo "EMULATOR/DEV")${NC}"
if [ "$COMMAND" != "configure-indexes" ] && [ "$DRY_RUN" = true ]; then
    echo -e "  Mode: ${CYAN}DRY RUN${NC}"
elif [ "$COMMAND" != "configure-indexes" ]; then
    echo -e "  Mode: ${CYAN}LIVE${NC}"
fi
if [ -n "$TENANT_ID" ]; then
    echo -e "  Tenant Filter: ${CYAN}$TENANT_ID${NC}"
fi
echo ""

# Run the script
if [ "$COMMAND" = "configure-indexes" ]; then
    echo -e "${GREEN}Starting Algolia index configuration...${NC}"
else
    echo -e "${GREEN}Starting Algolia sync...${NC}"
fi
cd "$SCRIPT_DIR" && node on-demand/$SCRIPT_NAME $NODE_ARGS