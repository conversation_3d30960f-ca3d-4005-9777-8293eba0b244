# Firebase Data Tools

This directory contains tools for data manipulations on Firebase Firestore and Auth. This includes both one-time migrations and general data operations.

## Setup

### 1. Service Account (for Production)

To run data operations against production, you need a service account key:

1. Go to [Firebase Console](https://console.firebase.google.com) → Project Settings → Service Accounts
2. Click "Generate new private key"
3. Save the file as `firebase/fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json`
4. ⚠️ **IMPORTANT**: This file is already in `.gitignore`. Never commit it!

### 2. Firebase Emulator (for Local Development)

Make sure the Firebase emulator is running:

```bash
cd firebase
firebase emulators:start
```

## Quick Start with Convenience Script

Use the `migrate.sh` script for common data operations:

```bash
cd firebase/data-tools

# List available operations
./migrate.sh list

# Test on emulator (recommended first step)
./migrate.sh test add-users-to-tenant

# Run on emulator
./migrate.sh run-local add-users-to-tenant

# Test on production (dry-run)
./migrate.sh test-prod add-users-to-tenant

# Run on production (requires confirmation)
./migrate.sh run-prod add-users-to-tenant
```

You can also call it from anywhere in the project:

```bash
# From project root
firebase/data-tools/migrate.sh list
firebase/data-tools/migrate.sh test add-users-to-tenant
```

**Tip**: For even easier access, you can create an alias in your shell:
```bash
# Add to your ~/.bashrc or ~/.zshrc
alias fdt="firebase/data-tools/migrate.sh"

# Then use it like:
fdt list
fdt test add-users-to-tenant
```

## Manual Usage

You can also run data operations directly (though the `migrate.sh` script is recommended):

```bash
# Navigate to data tools directory
cd firebase/data-tools

# Run against emulator (safe for testing)
node migrations/run-migrations.js add-users-to-tenant --emulator

# Dry run against production (no changes made)
node migrations/run-migrations.js add-users-to-tenant --dry-run

# Run against production (⚠️ makes real changes!)
node migrations/run-migrations.js add-users-to-tenant
```

**Note**: The `run-migrations.js` technical script is located in the `migrations/` folder to keep the main directory clean and emphasize that `migrate.sh` is the primary interface.

### Available Options

- `--emulator` - Run against local Firebase emulator (default: production)
- `--dry-run` - Preview changes without making them
- `--help` - Show available operations

## Available Operations

### add-users-to-tenant

Ensures all users are properly associated with the 'fiaranow' tenant:

- Adds 'fiaranow' to mobile users' `tenantIDs` array
- Creates tenant state documents for mobile users
- Creates tenant access documents for admin users
- Creates the tenant document if it doesn't exist

### 002__denormalize-service-active-by-tenant

⚠️ **REQUIRES MAINTENANCE MODE** - Denormalizes service status fields:

- Builds `isServiceActiveByTenant` maps from tenant state data
- Removes the old `isServiceActive` field from user documents
- This migration is NOT reversible due to field removal
- Automatically manages maintenance mode during critical operations

## Push Notification Testing

### notify-user.sh

A testing tool for sending push notifications to users by their UID. Useful for testing the new iOS ringtone functionality and general notification delivery.

```bash
cd firebase/data-tools

# Test driver notification on emulator
./notify-user.sh test-local abc123 driver

# Test passenger notification on production (with confirmation)
./notify-user.sh send-prod xyz789 passenger

# Test simple notification on emulator
./notify-user.sh test-local def456 simple
```

**Notification Types:**
- `driver` - Driver trip request notification with custom ringtone (`phone_ringtone_ultra`)
- `passenger` - Passenger notification (driver moving) with ringtone (`passenger_ringtone`)
- `simple` - Silent notification using dedicated `silent_notification` channel (no sound, vibration, or lights)

**Requirements:**
- User must exist in `mobile_users` collection
- User must have a valid `fcmToken`
- For production: Requires service account key
- For emulator: Requires Firebase emulator to be running

You can also call the underlying JavaScript directly:
```bash
# Direct usage
node testing/send-push-notifications.js <userUID> <type> [--emulator]
```

**Silent Notification Channel**: The `simple` type uses a dedicated `silent_notification` channel that's also available for general application use when you need to send notifications without sound, vibration, or lights (e.g., for background data sync notifications, status updates, etc.).

## Maintenance Mode for Critical Migrations

Some migrations require **maintenance mode** to ensure data consistency during critical operations. The `maintenance-mode.js` utility provides reusable functions for managing maintenance mode.

### When to Use Maintenance Mode

Use maintenance mode for migrations that:
- Modify critical fields that the app depends on
- Require atomic operations across multiple documents
- Could cause data inconsistency if interrupted
- Remove or restructure essential data fields

### Usage Examples

#### Option 1: Using `withMaintenanceMode` (Recommended)

```javascript
const { withMaintenanceMode } = require('./maintenance-mode');

async function performCriticalOperation() {
  // Your critical migration logic here
  log('Performing critical database changes...');
  
  // Process documents, update fields, etc.
  return { usersUpdated: 100 };
}

const result = await withMaintenanceMode({
  db,
  FieldValue,
  isDryRun,
  log,
  logSuccess,
  logError,
  logWarning,
  reason: 'Critical database schema update',
  message: 'System maintenance in progress. We\'ll be back shortly!',
  operation: performCriticalOperation
});
```

#### Option 2: Manual Control (Advanced)

```javascript
const { activateMaintenanceMode, deactivateMaintenanceMode } = require('./maintenance-mode');

try {
  await activateMaintenanceMode({
    db, FieldValue, isDryRun, log, logSuccess, logError,
    reason: 'Complex multi-step migration',
    message: 'System maintenance in progress. Multiple steps required.',
    propagationDelayMs: 15000 // Wait 15 seconds for propagation
  });
  
  // Perform critical operations
  
  await deactivateMaintenanceMode({
    db, FieldValue, isDryRun, log, logSuccess, logError, logWarning
  });
} catch (error) {
  // Emergency deactivation
  await deactivateMaintenanceMode({
    db, FieldValue, isDryRun, log, logSuccess, logError, logWarning
  });
  throw error;
}
```

### Maintenance Mode Features

- **Automatic activation/deactivation**: Handles the global configuration updates
- **Flexible messaging**: Customize the maintenance message for users
- **Propagation delay**: Configurable wait time for changes to propagate
- **Error handling**: Ensures maintenance mode is deactivated even if migration fails
- **Dry-run support**: Respects `isDryRun` flag for testing

## Creating New Data Operations

1. Create a new file in `migrations/` directory:

```javascript
// migrations/your-operation-name.js

// For maintenance mode migrations, import the utility
const { withMaintenanceMode } = require('./maintenance-mode');

module.exports = {
  name: 'Your Operation Name',
  description: 'What this operation does',
  requiresMaintenanceMode: false, // Set to true if needed
  
  async run({ db, auth, FieldValue, isDryRun, progress, log, logSuccess, logError, logWarning }) {
    // Your data manipulation logic here
    
    // Use progress tracking
    progress.recordSuccess(); // For successful operations
    progress.recordFailure(error, { context }); // For failures
    progress.recordSkipped(); // For skipped items
    
    // Use logging functions
    log('Processing items...');
    logSuccess('Item processed successfully');
    logError('Failed to process item');
    logWarning('Something to watch out for');
    
    // Always check isDryRun before making changes
    if (!isDryRun) {
      // Make actual changes
    } else {
      log('[DRY RUN] Would make changes');
    }
  }
};
```

2. Register it in `migrations/run-migrations.js`:

```javascript
const migrations = {
  'add-users-to-tenant': require('./add-users-to-tenant'),
  'your-operation-name': require('./your-operation-name'),
};
```

## Best Practices

1. **Always test with emulator first**: Run operations against the emulator before production
2. **Use dry-run**: Always do a dry-run before running against production
3. **Batch operations**: Use Firestore batches for multiple writes (max 500 per batch)
4. **Progress tracking**: Use the progress object to track success/failure/skipped items
5. **Error handling**: Catch and log errors for individual items, don't let one failure stop the entire operation
6. **Idempotency**: Operations should be safe to run multiple times

## Security Notes

- The `fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json` file has full admin access to your Firebase project
- Never commit this file to version control
- Keep it secure and rotate keys periodically
- Delete the file after use if not needed regularly

## Troubleshooting

### "Service account file not found"

Make sure you have the service account key saved as `firebase/fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json`

### "Cannot connect to emulator"

Make sure the Firebase emulator is running:
```bash
cd firebase
firebase emulators:start
```

### "Permission denied" errors

Ensure your service account has the necessary permissions. It should have the "Firebase Admin" or "Editor" role.

### Operation seems stuck

Check the console for any errors. Operations process items one by one and may take time for large datasets. 