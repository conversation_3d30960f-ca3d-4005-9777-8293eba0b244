# CHANGELOG

## Sprint #7 — Monday, May 26th until Friday, June 6th

Because I got sick, I was not able to work on this sprint according to the original timeline. I was also unable to get anything done the whole month of June. So, I resumed work from the June 24th until July 1st.

- [x] Notifications
  - Notification for admins when a trip has been booked
    - Integration with push notification
      - Direct link to view the trip when clicking on the notification popup
    - Notification of multiple admins in case of booking, regardless of which account
  - Notification to admin when feedback has been submitted
  - Notification to admin when client cancels the booking

- [x] Passenger notification
  - When?
    - Driver has started moving towards the passenger's pick-up location
    - Driver arrives to the pick-up location
    - Trip has been paid (no ringone, always)
    - A `reserved` trip is about to start (1h, 15mn before)
  - Admin
    - Sound Configurable from Admin side: Default configuration = Ringtone active (not just a notification)
      - Check if existing page exists that will be able to take this configuration
      - If no page is appropriate, create a new entry and a new page for this Passenger notification setting
  - Passenger
    - A new sound is going to be required to separate the sound from how the Driver's phone sound for notification
    - Choice: disable or enable ringtone sound, if user preference is set, this will override configuration set by Admin
    - If default configuration is disabled on admin side: prompt passenger if they want to enable ringtone notification when creating a Trip
    - This preference is therefore stored in the database
      - A new Settings menu entry in the MenuDrawer + a new Settings page containing this notification sound
      - New collection for user preference, under the `mobile_users` collection
      - Migration of other preference fields from `mobile_users` collection to the new collection
    - Notifications will thus be sent after evaluation of the
  - There is already a 30 second cron job
    - Take advantage of it to send Passenger's a notification that soon her Trip will start
    - Admin
      - Also send a push notification to Admins (same 1h and 15mn before)

- [x] Trip & Bookings
  - Client can specify the number of people for their trip
    - Mobile will need to update to allow this
      - UI will not be an input field, but a slider, with different stop points for the number of passengers, for example 1, 2, 3 ... until maximum passenger count of 8 (defaults max from Admin configuration)
      - Since this isn't many changes, re-used existing widget and add this part in (choose the most appropriate)
  - Driver/admin can specify the number of seats for a car
    - Admin
      - In the Driver Details, the Driver Profile section will need a new button to allow the Admin to Edit Driver profile
      - A new configuration page will be created for this, at least for the maximum number of passengers that can be specified from the mobile side (defaults to 8 passengers)
      - Despite the fact that a driver's car doesn't have enough space for the Trip, the Admin can still force assign a Driver to a trip. The no enough seats message will still be clearly shown
    - Mobile
      - A new field also in the Driver profile
    - Model will need upating for the new field
  - The list of available cars for a trip depends on 2 parameters: number of seats, number of people.
    - Example: if the car can only hold 4 passengers, while the trip is for 6 people, this car will be displayed, but with text saying "Not enough seats" ... therefore, the car cannot be selected
  - Keep the UI modern, especially for the Mobile
  - For older data (already on the database)
    - Assume the passenger count was just 1
    - Existing Driver without a set capacity will assume 4 passengers

- [x] Trip full finite state machine integration/conversion
  - Move all calls to mutate theh Trip to Firebase Functions
  - Mobile & Admin will call these methods to cause state changes to the Trip
    - No more local/direct mutation of fields
    - UI interactions must be behind a loading indicator
  - onExit and onEntry full support for clarity of the state changes
    - Fully logged for debugging purposes

- [x] Feedbacks
  - Trajet
    - Possibilité d'envoyer un feedback sur le trajet (sur 5 étoiles)
  - Application
    - Possibilité d'envoyer un feedback sur l'application, bugs, suggestions, etc.
    - Envoyer un screenshot de l'écran en cours
  - Admin
    - Consultation des feedbacks et les images
    - Intégration avec le chat pour poursuivre la conversation

- [x] Chat
  - Possibilité de contacter le client via le chat
  - Possibilité de contacter l'admin via le chat
  - Intégration du feedback dans le chat
  - Floating button pour ouvrir ou fermer le chat

- [x] Multi-tenant support
  - Create a new `tenants` collection
    - The following collections will be moved inside:
      - configurations
      - payments
      - trips
      - event_logs
      - route_data
    - The very first tenant would be "Fiaranow", which also is the name of the project
      - This tenant, unlike other tenants, will have an ID `fiaranow`
      - All current data will need to be moved to that default tenant
        - Plan an efficient migration procedure that will reliably and securely move the data to new location
          - During maintenance time
          - I will manually change Global settings to allow for this maintenance time
        - There are still very few data on the database (app still being rolled-out into production)
    - Different Administrator roles are thus required
      - SUPER ADMIN: Can manage any tenants
      - ADMIN: Can only manage one or more tenants associated with her account
      - MANAGER: Can only manage Trips, cannot control accounts
    - Admin
      - If an ADMIN is able to manage multiple tenants, the top bar will show a tenant selection dropdown
  - Mobile app will default to use `fiaranow` tenant
    - If there are other tenants, they will actually need to commission a different app; so we can hardcode this tenant to avoid too much updates on the mobile
      - Careful updates and analysis will be needed though to ensure everything is correct
      - These different apps will be completely separate Flutter apps (not a concern for this plan)
  - Firestore rules will need appropriate rules to enforce these changes

- [x] Internal and external driver management
  - Clear distinction between internal drivers and third-party drivers on the admin side
    - Implement a driver tag system that will allow the administrator to add different types of tags. For example, external or truck or anything. The tag will need to be stored in a new collection so that it can be reused and it can also be queried. And we know the list of tags. We actually don't need a new page to list the tags, but it is just the fact that the user, the admin user assigning a new tag to a new driver will check that collection and then will update if that tag doesn't exist yet. 
    - Driver tags are stored per tenant and is not available globally.
    - Propose a nice and practical way to present this
  - Management of mandatory driver documents (car insurance, driving license, etc.)
    - From the mobile side, we will need, an UI, a new page that will allow us to upload a document, and a page where the user is able to see his list of documents, click on one and then see about the document. And in that page, be able to click on an icon and then edit the document like the title or some, remarks about the documents and everything around the documents.
    - We are already using Firebase storage in the project, so we should leverage that. 
  - Admin: Centralized space to view documents expiring soon
    - There should be a new section in the admin section where it is called Documents, and it will list all documents from all, uh, drivers. And, uh, that will also allow us to point back to The driver in question, but at the same time, we'll show a quick overview about the driver in question. 
    - The goal here is to allow the administrator to see documents that are soon expiring and be able to take proactive actions like notifying the driver that his document is about to expire or some other actions.
  - On the mobile side, drivers can upload their documents, view them, and add short notes on each document

- Vérification de numéro de téléphone
  - Réutilisation de l'intégration WhatsApp pour vérifier les numéros de téléphone des clients
  - Enregistrement et vérification des numéros de téléphone WhatsApp des admins

- [x] Price calculation change
  - Traffic jam protection: final cost cannot exceed 10% of initial estimate shown to user
  - When there is a traffic jam, the final cost of the trip must not exceed 10% of the initial estimate shown to the user initially
    - Firebase functions will have new `realCost` and `realCostCurrency` fields that store the calculated cost (based on actual trip logs)
    - Firebase functions will have new `estimatedCost` field that stores the initial estimate shown to user
    - Price will be capped to 10% more from the original estimate if it ever needs to go higher
    - `costTotal` field will show the capped price (what user pays), while `realCost` shows actual calculated cost
  - Data migration strategy: use new fields first, fallback to old fields for backward compatibility (old display sections marked as deprecated)
  - Do not show the final Trip cost at the end (on Passenger's mobile)
  - Admin and Driver interfaces will show both real cost and capped cost for transparency
  - Admin manual price override: Allow admins to manually set/override a trip's final price after completion
    - New Firebase function `adminOverrideTripCost` for secure price updates
    - Admin interface will have "Override Price" button in trip details
    - Override actions will be logged for audit purposes

## Sprint #6

## Mobile 1.1.2 build 24 (2025-05-15)

### Fixed on Mobile (iOS only)

- Disabled iPad (and other non-phone devices) from running the app. This actually blocked Review on the App Store.

## Mobile 1.1.1 build 24 (2025-05-15)

### Fixed on Mobile

- Flaky notification permission request and status display (especially on iOS)
- Incorrect error message about login where shown right after installing the app
