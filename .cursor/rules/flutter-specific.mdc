---
description: 
globs: fiaranow_flutter/**/*.dart
alwaysApply: false
---
# Flutter codes

- Mobile codes are in subfolder `/fiaranow_flutter`.
- After editing a `.dart` file, use the `fvm flutter analyze` command to check for any errors. Keep using the command until there are no errors, do not stop the command when there are no errors. Do not rely only on just the VSCode linter, use also the `fvm flutter analyze` command. If an error persists, it may be time to search the Web to ensure you're not doing something stupid.
- My project uses `fvm` to manage Flutter versions. Use `fvm flutter` command instead of just `flutter`.
- You must search the Web for latest versions of packages that you add.
- **Import Order Pattern**: When making changes that require new imports, ALWAYS add the code that uses the import FIRST, then add the import statement LAST. This is because editors (like VS Code with Dart extension) automatically remove unused imports. If you add the import first before the code that uses it, the editor will remove it thinking it's unused. During the time between adding the code and adding the import, linting errors may appear, but these will disappear once the import is added.

# GetX State Management Patterns

This project makes heavy usage of GetX for state management, navigation, and dependency injection. Follow these established patterns:

## GetX Controller Patterns

### Controller Declaration and Lifecycle
```dart
class MyController extends GetxController {
  final Logger _logger = Logger('MyController');

  // Reactive variables
  var isLoading = false.obs;
  var data = Rxn<MyModel>();
  var items = <Item>[].obs;

  // Stream subscriptions and timers
  StreamSubscription<DocumentSnapshot>? _dataSubscription;
  Timer? _debounceTimer;

  @override
  void onInit() {
    super.onInit();
    _logger.info('Initializing MyController');
    _setupListeners();
  }

  @override
  void onClose() {
    _logger.info('Closing MyController');
    _dataSubscription?.cancel();
    _debounceTimer?.cancel();
    super.onClose();
  }
}
```

### Dependency Injection Patterns
```dart
// In main.dart - Early initialization
Get.put(AppState());
Get.put(AuthState());
Get.put(PermissionsState());

// Lazy initialization for services
Get.lazyPut(() => TripStateService());
Get.lazyPut(() => UserPreferencesService());

// In widgets/controllers - Finding dependencies
final authState = Get.find<AuthState>();
final navigationState = Get.find<NavigationState>();
```

## Reactive Widget Patterns

### StatelessWidget with GetX State
```dart
class DriverModeWidget extends StatelessWidget {
  DriverModeWidget({super.key}); // not const

  final navigationState = Get.find<NavigationState>();

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        // use navigationState here
        return Widget();
      },
    );
  }
}
```

**Important**: Don't make the widget const, otherwise it won't rebuild as we want upon change of the variable. The fact that the states are set as class variable makes the widget unable to be const, so that's why we put it there.

### Conditional Reactive Rendering
```dart
return Obx(() {
  if (Get.find<AuthState>().currentMobileUser.value == null) {
    return const Center(child: CircularProgressIndicator());
  }

  return PageView(
    // ... rest of widget
  );
});
```

## GetX Navigation Patterns

### Basic Navigation
```dart
// Navigate to a new screen
Get.to(() => TripDetailsPage(trip: trip));

// Navigate with named routes
Get.toNamed('/edit_driver_profile');
Get.toNamed('/full_day_reservation');

// Replace current screen
Get.offAll(() => const UpdateRequiredScreen());

// Go back
Get.back();
Get.back(result: 'some_result');
```

### Dialog Patterns
```dart
// Simple dialog with result
final choice = await Get.dialog<String>(
  AlertDialog(
    title: Text('Choose Payment Method'),
    content: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ListTile(
          leading: const Icon(Icons.money),
          title: Text('Cash Payment'),
          onTap: () => Get.back(result: PaymentMethod.cash.name),
        ),
        ListTile(
          leading: const Icon(Icons.phone_android),
          title: Text('Mobile Money Payment'),
          onTap: () => Get.back(result: PaymentMethod.mobile.name),
        ),
      ],
    ),
  ),
  barrierDismissible: true,
);

// Loading dialog
Get.dialog(
  const Center(child: CircularProgressIndicator()),
  barrierDismissible: false,
);

// Close dialog when done
Get.back();

// Check if dialog is open before closing
if (Get.isDialogOpen ?? false) Get.back();
```

### Confirmation Dialog Pattern
```dart
final logoutOtherDevices = await Get.dialog<bool>(
  AlertDialog(
    title: Text('Account in Use'),
    content: Text('Your account is already logged in on another device.'),
    actions: [
      TextButton(
        onPressed: () => Get.back(result: false),
        child: Text('Cancel'),
      ),
      TextButton(
        onPressed: () => Get.back(result: true),
        child: Text('Logout Other Devices'),
      ),
    ],
  ),
  barrierDismissible: false,
);
```

## GetX Snackbar Patterns

### Error Snackbars
```dart
Get.snackbar(
  'Error',
  'Failed to get place predictions. Please check your Internet connection.',
  backgroundColor: Colors.red,
  colorText: Colors.white,
  snackPosition: SnackPosition.BOTTOM,
  duration: const Duration(seconds: 3),
);
```

### Success/Info Snackbars
```dart
Get.snackbar(
  'Connection Status',
  'You are connected to the Internet.',
  snackPosition: SnackPosition.TOP,
  backgroundColor: Colors.green,
  colorText: Colors.white,
);
```

### Localized Snackbars
```dart
Get.snackbar(
  AppLocalizations.of(context)!.mapScreen_error,
  AppLocalizations.of(context)!.mapScreen_failedToGetRoutes,
  backgroundColor: Colors.red,
  colorText: Colors.white,
);
```

## GetX Reactive Programming Patterns

### Workers for Side Effects
```dart
// React to changes once
once(authState.currentMobileUser, (MobileUser? mobileUser) {
  if (mobileUser != null && mobileUser.primaryUserType == null) {
    Get.toNamed('/choose_user_type');
  }
});

// React to every change
ever(appState.requiredMinimumVersion, (String minVersion) {
  if (_isUpdateRequired(appState.currentVersion.value, minVersion)) {
    Get.offAll(() => const UpdateRequiredScreen());
  }
});

// Complex reactive logic
final w1 = ever(driverTripRequests, (List<Trip> trips) {
  if (drivingMode.value == UserType.driver) {
    final trip = driverTripRequests.firstWhereOrNull(
      (trip) => trip.status != TripStatus.preparing
    );

    if (trips.isNotEmpty && selectedRequestTripId.value == null) {
      selectedRequestTripId.value = trips.first.id;
    } else if (trips.isEmpty) {
      selectedRequestTripId.value = null;
    }
  }
});
```

### Debouncing with Timers
```dart
void _debounceUpdateCurrentPositionInFirebase(LatLng position) {
  if (_positionUpdateDebounceTimer?.isActive ?? false) {
    _positionUpdateDebounceTimer!.cancel();
  }
  _positionUpdateDebounceTimer = Timer(const Duration(milliseconds: 500), () {
    _updateCurrentPositionInFirebase(position);
  });
}
```

### Service Pattern with GetX
```dart
class UserPreferencesService extends GetxService {
  final Rx<UserPreferences?> userPreferences = Rx<UserPreferences?>(null);
  StreamSubscription<DocumentSnapshot>? _preferencesSubscription;

  @override
  void onInit() {
    super.onInit();
    // Listen to user state changes
    ever(_authState.currentMobileUser, (_) {
      _setupPreferencesListener();
    });
    _setupPreferencesListener();
  }

  @override
  void onClose() {
    _preferencesSubscription?.cancel();
    super.onClose();
  }
}
```

# Flutter State Safety

## Safe Collection Access in Flutter

- Always check if collections are empty before accessing elements
- Use safe access patterns:
  ```dart
  // Avoid direct access without checks
  // BAD: var item = myList.first;
  
  // GOOD: Check before access
  if (myList.isNotEmpty) {
    var item = myList.first;
  }
  
  // GOOD: Use orElse with firstWhere
  var item = myList.firstWhere(
    (element) => element.id == searchId,
    orElse: () => defaultItem,
  );
  
  // GOOD: Provide fallback with null-aware operators
  var item = myList.isNotEmpty ? myList.first : null;
  ```

- For shared collections across widgets or asynchronous code:
  - Always synchronize access to prevent race conditions
  - Implement defensive checks before accessing elements
  - Consider using nullable types with null safety for collections that might be empty

- When working with stream data or API responses:
  - Always handle empty collection cases explicitly
  - Provide sensible defaults or empty state UI

## Mounted Check for setState
- Always protect calls to `setState` with an `if (mounted)` check to prevent calling setState after a widget is disposed
- This applies to any delayed or asynchronous operations that might complete after the widget is removed from the tree
- Example:
  ```dart
  if (mounted) {
    setState(() {
      _selectedIndex = newIndex;
    });
  }
  ```
- This pattern is especially important in:
  - Callback functions
  - Timer callbacks
  - Future completions
  - Event listeners

## State Management Best Practices
- For asynchronous operations that update state:
  - Add a mounted check before setState
  - Consider cancelling pending operations in the dispose method
  - Use a pattern like:
    ```dart
    @override
    void dispose() {
      _myTimer?.cancel();
      _myStreamSubscription?.cancel();
      super.dispose();
    }
    ```
- When using stateful widgets with GetX, combine safe state practices from both systems:
  - Protect Flutter's setState with mounted checks
  - Clean up GetX controllers and streams in dispose method when appropriate