---
description: 
globs: 
alwaysApply: true
---
# Project Structure & Organization

All paths in this rule are relative to the project root.

## Core Components

This project is composed of 3 sub-projects:

### 1. Mobile App (Flutter) - `/fiaranow_flutter`
- **Technology**: Flutter with Android and iOS builds
- **States**: `/fiaranow_flutter/lib/states` (e.g., *AppState*, *NavigationState*)
- **Models**: `/fiaranow_flutter/lib/models`
  - Follow patterns like [Payment.dart](mdc:fiaranow_flutter/lib/models/Payment.dart)
  - **Important**: Dart files must be capitalized (e.g., `NameOfModel.dart`, not `name_of_model.dart`)

### 2. Admin App (SvelteKit) - `/admin_sveltekit`
- **Technology**: Svelte 5 with runes syntax
- **Stores**: `/admin_sveltekit/src/lib/stores` (using `*.svelte.ts` extension)
  - Stores contain both state and models (see [mobile_users.svelte.ts](mdc:admin_sveltekit/src/lib/stores/mobile_users.svelte.ts))
  - Must use Svelte 5 runes, not old store syntax
- **Firebase Integration**: Use exports from [firebase.client.ts](mdc:admin_sveltekit/src/lib/firebase.client.ts)

### 3. Firebase Backend - `/firebase`
- **Functions**: `/firebase/functions/src` - Cloud Functions in TypeScript
- **Hosting**: Admin app build is symlinked to `firebase/web`
- **Emulator Tools**: `/firebase/emulator-tools` - Debug utilities for development
  - `firestore-query.js` - Query Firestore emulator data
  - `auth-query.js` - Query Auth emulator users
  - `emulator-query` - Wrapper script for easy access
  - See [README.md](mdc:firebase/emulator-tools/README.md) for detailed usage

## Development Tools

### Emulator Debugging
For querying Firebase emulator data during development:
```bash
# Quick access via wrapper
firebase/emulator-tools/emulator-query fs list tenants
firebase/emulator-tools/emulator-query auth list

# Direct access
node firebase/emulator-tools/firestore-query.js <command>
node firebase/emulator-tools/auth-query.js <command>
```

For comprehensive debugging documentation, refer to [firebase/emulator-tools/README.md](mdc:firebase/emulator-tools/README.md).

## Process Management
- **tmux**: For long-running processes (>15s) - dev servers, builds, databases
  - Always specify full target: `tmux capture-pane -t "Fiaranow-firebase:window-name" -p`
- **bash**: For quick commands (<15s) - file operations, simple scripts

## Multi-Tenancy
This project uses a multi-tenant system. Refer to [MULTI-TENANT.md](mdc:MULTI-TENANT.md) for details.
Everything must consider multi-tenancy: Admin, Mobile, and Firebase Functions.
