---
description: 
globs: admin_sveltekit/src/**/*.svelte,admin_sveltekit/src/**/*.ts
alwaysApply: false
---
# Comments

- Do not remove existing comments, unless it is no longer relevant to the surrounding codes

# Display

- For all places where pricing is shown, Malagasy Ariary will be used. Do not use *MGA*, use *XXXXX Ar* instead, no decimal place (it will be rounded up if it has decimal).

## Date and Time Formatting in Admin section (Svelte 5 app)

- For all date and time displays in the application, use the utility functions from [datetime.ts](mdc:admin_sveltekit/src/lib/utils/datetime.ts):
  - `formatLongDateTime(date: Date, locale: string = 'en')` for full date and time display
    - English format: "2025-march-15 5:49:40pm"
    - French format: "15-mars-2025 17:49:40"
  - `formatShortDateTime(date: Date, locale: string = 'en')` for abbreviated date and time display
    - English format: "mar-15 at 5:49pm"
    - French format: "15 mars à 17:49"
- The locale parameter should be passed based on the user's selected language
- Do not use native JavaScript date formatting methods like `toLocaleDateString()` or `Intl.DateTimeFormat`
- For relative time displays (e.g. "2 hours ago"), continue using the existing `formatDistanceToNow` function from date-fns

# Code organization

- If need be, organize features into multiple files instead of a gigantic file.
  - For instance, instead of having all of the functions inside [index.ts](mdc:firebase/functions/src/index.ts), place them into existing nearby files, or create a new one that best suits the feature.

# Changes

- Never remove existing features or codes unless you first ask.

# Generate new rules

- Whenever you find a new process that could be used and useful as rules for future tasks, and it's new, and it's not yet part of existing rules, write the rules at the end.
- Do not attempt to update the rules in any *.mdc files, just spit-it out as plain Markdown texts.
- Do not create a new rule file, just generate the Markdown text that I can copy.

# Firebase and API Calls

## Firebase Cloud Functions from Svelte
- Always import callFunction from [firebase.client.ts](mdc:admin_sveltekit/src/lib/firebase.client.ts) for calling Firebase Cloud Functions
- Use proper error handling with try/catch blocks when calling Cloud Functions
- Set appropriate loading states with $state before and after function calls
- Example:
  ```typescript
  let isLoading = $state(false);
  
  async function callMyFunction() {
    try {
      isLoading = true;
      const result = await callFunction("myFunction", { param1: "value1" });
      toast.success("Operation successful");
    } catch (error) {
      console.error("Error:", error);
      toast.error("Operation failed");
    } finally {
      isLoading = false;
    }
  }
  ```