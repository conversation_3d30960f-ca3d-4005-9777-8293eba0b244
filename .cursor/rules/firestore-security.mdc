---
description: 
globs: **/firestore.rules
alwaysApply: false
---
# Firestore Security Rules Guidelines

### Collection Group Query Rules
- **ALWAYS** use `rules_version = '2'` at the top of the file for collection group query support
- **ALWAYS** add collection group rules using recursive wildcard syntax `{path=**}` when the codebase uses `collectionGroup()` queries
- Collection group rules should be placed early in the rules file, typically after helper functions
- **REQUIRED** permissions for collection group queries:
  - `allow list: if <condition>` - Essential for collectionGroup queries to work
  - `allow read: if <condition>` - For individual document access within the group

### Pattern for Collection Group Rules:
```javascript
// Collection group rules for documents that use collectionGroup queries
match /{path=**}/collection_name/{docId} {
  // CRITICAL: allow list is required for collectionGroup queries
  allow list: if isActiveAdmin(request.auth.uid);
  
  // Standard read permissions
  allow read: if <appropriate_condition>;
}
```

### Multi-tenant Document Security
- For documents with `tenantIds` arrays, ensure rules check tenant access properly
- Use helper functions like `hasAnyTenantAccess()` for tenant-scoped permissions
- Always consider both document ownership and admin tenant access

### Common Patterns to Follow:
1. **Driver/Vehicle Documents**: Use collection group rules + individual path rules
2. **Tenant-scoped Collections**: Ensure proper tenant access validation
3. **Admin Functions**: Place helper functions at the top, before match statements
4. **Error Prevention**: Always test collection group queries require `allow list` permissions

### Security Rule Debugging:
- "No matching allow statements" errors often indicate missing `allow list` rules for collection group queries
- Use recursive wildcards `{path=**}` for collection group access patterns
- Ensure rules version 2 is enabled when using collection group queries

### Rule Structure Best Practices:
- Place helper functions at the top after rules_version declaration
- Add collection group rules early, before specific path rules
- Use descriptive comments for complex permission logic
- Test rules with Firebase emulator before deployment
- Always consider the principle of least privilege

### Common Error Patterns to Avoid:
- Missing `allow list` for collection group queries
- Forgetting to add recursive wildcard rules when using `collectionGroup()`
- Not checking tenant access for multi-tenant documents
- Using rules version 1 when collection group queries are needed
- Placing collection group rules after specific path rules (can cause conflicts)
