---
description: 
globs: 
alwaysApply: false
---
# Admin Layout Baseline for Svelte Components

## Essential Layout Structure

For any admin section layout in `*.svelte` files, always use this proven baseline structure that prevents sidebar shrinking and content overlap:

### Layout Container Pattern
```html
<div class="p-4">
  <div class="flex h-full gap-4">
    <!-- Left section - List/Sidebar -->
    <div class="w-1/3">
      <Card.Root>
        <!-- Content here -->
      </Card.Root>
    </div>
    
    <!-- Right section - Detail/Content -->
    <div class="flex-1">
      {@render children()}
    </div>
  </div>
</div>
```

### Detail Page Pattern
```html
<Card.Root class="h-full">
  <Card.Header>
    <!-- Header content -->
  </Card.Header>
  
  <Card.Content class="h-[calc(100vh-262px)] overflow-y-auto">
    <!-- Main content with proper scrolling -->
  </Card.Content>
  
  <Card.Footer class="p-4 border-t">
    <!-- Footer content -->
  </Card.Footer>
</Card.Root>
```

## Content Length Management

### Critical Discovery
Real database content with long text can break even well-structured layouts. The layout itself isn't the problem - it's **uncontrolled content length** that expands beyond intended boundaries.

## Critical Rules

### ✅ DO:
- Use exact structure from working sections like [feedbacks layout](mdc:admin_sveltekit/src/routes/support/feedbacks/+layout.svelte)
- Start with mock/placeholder data for testing layout
- Use `w-1/3` for left section and `flex-1` for right section
- Use `h-[calc(100vh-262px)]` for scrollable content areas
- Keep Card.Root simple: `class="h-full"` only
- **Control content length** before it reaches the DOM
- Test with both short and long content scenarios

### ❌ DON'T:
- Add custom width constraints like `w-full max-w-full`
- Use complex flex layouts like `flex flex-col` on Card.Root
- Add `overflow-hidden` or positioning classes unnecessarily
- Use `flex-shrink-0` or other flex modifiers without purpose
- Start with complex Firestore listeners - use mock data first
- **Assume CSS alone will handle long content** - control at data level

## Working Examples

- **Chat Layout Baseline**: [+layout.svelte](mdc:admin_sveltekit/src/routes/support/chats/+layout.svelte)
- **Chat Detail Baseline**: [+page.svelte](mdc:admin_sveltekit/src/routes/support/chats/[sessionId]/+page.svelte)
- **Reference Working Layout**: [feedbacks +layout.svelte](mdc:admin_sveltekit/src/routes/support/feedbacks/+layout.svelte)

## Troubleshooting

If sidebar shrinks or content overlaps:
1. Remove all custom width/overflow classes
2. Revert to this exact baseline structure
3. Test with mock data before adding real functionality
4. **Check for long content** - titles, messages, arrays
5. **Apply content length limits** at the data level
6. Compare with working reference sections

This baseline has been proven to work correctly with the main layout in [+layout.svelte](mdc:admin_sveltekit/src/routes/+layout.svelte).
