---
description: 
globs: fiaranow_flutter/**/*.dart
alwaysApply: false
---

# GetX Patterns and Best Practices

This document outlines the established GetX patterns used throughout the Fiaranow Flutter application. Follow these patterns for consistency and maintainability.

## Controller Architecture

### Base Controller Structure
```dart
class MyController extends GetxController {
  final Logger _logger = Logger('MyController');
  
  // Reactive variables - use appropriate Rx types
  var isLoading = false.obs;
  var data = Rxn<MyModel>();           // Nullable reactive
  var items = <Item>[].obs;            // List reactive
  var status = MyStatus.idle.obs;      // Enum reactive
  
  // Non-reactive private variables
  StreamSubscription<DocumentSnapshot>? _dataSubscription;
  Timer? _debounceTimer;
  
  @override
  void onInit() {
    super.onInit();
    _logger.info('Initializing MyController');
    _setupListeners();
  }
  
  @override
  void onClose() {
    _logger.info('Closing MyController');
    _cleanup();
    super.onClose();
  }
  
  void _cleanup() {
    _dataSubscription?.cancel();
    _debounceTimer?.cancel();
  }
}
```

### Mixin Pattern for Shared Functionality
```dart
mixin PreparationMixin on BaseNavigationState {
  final Logger _logger = Logger('Preparation');
  
  // Shared reactive variables
  var isStartAddressFocused = false.obs;
  var startAddressPredictions = <AutocompletePrediction>[].obs;
  Timer? _predictionsDebounceTimer;
  
  void clearStartAddress() {
    startAddressController.clear();
    startPosition.value = null;
    startAddressPredictions.clear();
  }
}
```

## Dependency Injection Patterns

### Early vs Lazy Initialization
```dart
// In main.dart - Early initialization for core services
void initializeServices() {
  // Core services that need immediate initialization
  Get.put(AgentLoggingService());
  Get.put(AppState());
  Get.put(AuthState());
  Get.put(PermissionsState());
  Get.put(ThemeState());
  
  // Services that can be lazily loaded
  Get.lazyPut(() => TripStateService());
  Get.lazyPut(() => UserPreferencesService());
}
```

### Finding Dependencies
```dart
// In controllers - find dependencies in onInit or as class variables
class MyController extends GetxController {
  final AuthState authState = Get.find<AuthState>();
  
  @override
  void onInit() {
    super.onInit();
    final navigationState = Get.find<NavigationState>();
  }
}

// In widgets - find as class variables for reactive widgets
class MyWidget extends StatelessWidget {
  MyWidget({super.key}); // NOT const for reactive widgets
  
  final navigationState = Get.find<NavigationState>();
  final authState = Get.find<AuthState>();
}
```

## Reactive Widget Patterns

### StatelessWidget with Reactive State
```dart
class DriverModeWidget extends StatelessWidget {
  DriverModeWidget({super.key}); // NOT const - important!

  final navigationState = Get.find<NavigationState>();
  final authState = Get.find<AuthState>();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Access reactive variables here
      if (navigationState.isLoading.value) {
        return const CircularProgressIndicator();
      }
      
      return Column(
        children: [
          if (authState.currentUser.value != null)
            Text('Welcome ${authState.currentUser.value!.displayName}'),
          // ... rest of widget
        ],
      );
    });
  }
}
```

### Conditional Reactive Rendering
```dart
// Pattern for conditional rendering based on reactive state
return Obx(() {
  final user = Get.find<AuthState>().currentMobileUser.value;
  
  if (user == null) {
    return const Center(child: CircularProgressIndicator());
  }
  
  return PageView(
    controller: _pageController,
    children: _buildPages(user),
  );
});
```

### Reactive Button Pattern
```dart
// Pattern for buttons that react to loading states
return Obx(() {
  final isLoading = TripStateService.instance.isLoading(tripId!, transitionType!);
  
  return ElevatedButton(
    onPressed: isLoading ? null : () {
      // Handle button press
      if (onPressed != null) {
        onPressed!();
        
        // Check for errors after action
        if (showErrorSnackbar && TripStateService.instance.lastError.isNotEmpty) {
          Get.snackbar(
            'Error',
            TripStateService.instance.lastError.value,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      }
    },
    child: isLoading 
      ? const CircularProgressIndicator()
      : Text('Submit'),
  );
});
```

## Navigation Patterns

### Basic Navigation
```dart
// Navigate to new screen
Get.to(() => TripDetailsPage(trip: trip));

// Named route navigation
Get.toNamed('/edit_driver_profile');
Get.toNamed('/full_day_reservation');

// Replace current screen (no back navigation)
Get.offAll(() => const UpdateRequiredScreen());

// Go back with optional result
Get.back();
Get.back(result: 'payment_completed');
```

### Route Configuration
```dart
// In MaterialApp - define named routes
routes: {
  '/choose_user_type': (context) => const ChooseUserTypeScreen(),
  '/edit_driver_profile': (context) => const DriverProfileForm(),
  '/settings/notifications': (context) => NotificationSettingsScreen(),
  '/full_day_reservation': (context) => FullDayReservationSetup(
    onNavigateToTab: (index) {
      final navigationState = Get.find<NavigationState>();
      if (navigationState.pageController != null) {
        navigationState.pageController!.animateToPage(
          index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    },
    mapController: Get.find<NavigationState>().mapController,
  ),
},
```

## Dialog Patterns

### Choice Dialog with Result
```dart
Future<String?> showPaymentMethodDialog(BuildContext context) async {
  final choice = await Get.dialog<String>(
    AlertDialog(
      title: Text('Choose Payment Method'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.money),
            title: Text('Cash Payment'),
            onTap: () => Get.back(result: PaymentMethod.cash.name),
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.phone_android),
            title: Text('Mobile Money Payment'),
            onTap: () => Get.back(result: PaymentMethod.mobile.name),
          ),
        ],
      ),
    ),
    barrierDismissible: true,
  );
  
  return choice;
}
```

### Loading Dialog Pattern
```dart
Future<void> performAsyncOperation() async {
  try {
    // Show loading dialog
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );
    
    // Perform operation
    await someAsyncOperation();
    
    // Close loading dialog
    Get.back();
    
  } catch (e) {
    // Close loading dialog if still showing
    if (Get.isDialogOpen ?? false) Get.back();
    
    // Show error
    Get.snackbar('Error', e.toString());
    rethrow;
  }
}
```

### Confirmation Dialog Pattern
```dart
Future<bool> showConfirmationDialog({
  required String title,
  required String message,
  required String confirmText,
  required String cancelText,
}) async {
  final result = await Get.dialog<bool>(
    AlertDialog(
      title: Text(title),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () => Get.back(result: false),
          child: Text(cancelText),
        ),
        TextButton(
          onPressed: () => Get.back(result: true),
          child: Text(confirmText),
        ),
      ],
    ),
    barrierDismissible: false,
  );
  
  return result ?? false;
}
```

## Snackbar Patterns

### Error Snackbar
```dart
void showErrorSnackbar(String message, {String title = 'Error'}) {
  Get.snackbar(
    title,
    message,
    backgroundColor: Colors.red,
    colorText: Colors.white,
    snackPosition: SnackPosition.BOTTOM,
    duration: const Duration(seconds: 3),
  );
}
```

### Success Snackbar
```dart
void showSuccessSnackbar(String message, {String title = 'Success'}) {
  Get.snackbar(
    title,
    message,
    backgroundColor: Colors.green,
    colorText: Colors.white,
    snackPosition: SnackPosition.TOP,
    duration: const Duration(seconds: 2),
  );
}
```

### Connection Status Snackbar
```dart
// Pattern for connection status updates
void updateConnectionStatus(bool isOnline) {
  if (isOnline) {
    Get.snackbar(
      'Connection Status',
      'You are connected to the Internet.',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  } else {
    Get.snackbar(
      'Connection Status',
      'Your Internet connection is not doing well right now.',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }
}
```

### Localized Snackbar Pattern
```dart
void showLocalizedError(BuildContext context, String errorKey) {
  Get.snackbar(
    AppLocalizations.of(context)!.mapScreen_error,
    AppLocalizations.of(context)!.getString(errorKey),
    backgroundColor: Colors.red,
    colorText: Colors.white,
  );
}
```

## Reactive Programming Patterns

### Workers for Side Effects

#### once() - React to First Change Only
```dart
// React only once when user logs in
once(authState.currentMobileUser, (MobileUser? mobileUser) {
  if (mobileUser != null && mobileUser.primaryUserType == null) {
    Get.toNamed('/choose_user_type');
  }
});
```

#### ever() - React to Every Change
```dart
// React to every change in app version
ever(appState.requiredMinimumVersion, (String minVersion) {
  if (_isUpdateRequired(appState.currentVersion.value, minVersion)) {
    Get.offAll(() => const UpdateRequiredScreen());
  }
});

// Complex reactive logic with state management
final w1 = ever(driverTripRequests, (List<Trip> trips) {
  if (drivingMode.value == UserType.driver) {
    final trip = driverTripRequests.firstWhereOrNull(
      (trip) => trip.status != TripStatus.preparing
    );

    // Auto-select first trip if none selected
    if (trips.isNotEmpty && selectedRequestTripId.value == null) {
      selectedRequestTripId.value = trips.first.id;
    } else if (trips.isEmpty) {
      selectedRequestTripId.value = null;
    }
  }
});
```

#### Worker Cleanup
```dart
class MyController extends GetxController {
  Worker? _userWorker;
  Worker? _statusWorker;

  @override
  void onInit() {
    super.onInit();

    // Store worker references for cleanup
    _userWorker = ever(authState.currentUser, (user) {
      // Handle user changes
    });

    _statusWorker = ever(connectionStatus, (status) {
      // Handle connection changes
    });
  }

  @override
  void onClose() {
    _userWorker?.dispose();
    _statusWorker?.dispose();
    super.onClose();
  }
}
```

### Debouncing Patterns

#### Timer-based Debouncing
```dart
class SearchController extends GetxController {
  Timer? _debounceTimer;

  void onSearchChanged(String query) {
    if (_debounceTimer?.isActive ?? false) {
      _debounceTimer!.cancel();
    }

    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _performSearch(query);
    });
  }

  @override
  void onClose() {
    _debounceTimer?.cancel();
    super.onClose();
  }
}
```

#### Position Update Debouncing
```dart
void _debounceUpdateCurrentPositionInFirebase(LatLng position) {
  if (_positionUpdateDebounceTimer?.isActive ?? false) {
    _positionUpdateDebounceTimer!.cancel();
  }

  _positionUpdateDebounceTimer = Timer(const Duration(milliseconds: 500), () {
    _updateCurrentPositionInFirebase(position);
  });
}
```

### Stream Integration with GetX

#### Firebase Stream Integration
```dart
class UserPreferencesService extends GetxService {
  final Rx<UserPreferences?> userPreferences = Rx<UserPreferences?>(null);
  StreamSubscription<DocumentSnapshot>? _preferencesSubscription;

  @override
  void onInit() {
    super.onInit();

    // React to auth state changes
    ever(_authState.currentMobileUser, (_) {
      _setupPreferencesListener();
    });

    _setupPreferencesListener();
  }

  void _setupPreferencesListener() {
    _preferencesSubscription?.cancel();

    final user = _authState.currentMobileUser.value;
    if (user != null) {
      _preferencesSubscription = _firestore
          .collection('mobile_users')
          .doc(user.uid)
          .collection('preferences')
          .doc('settings')
          .snapshots()
          .listen((snapshot) {
        if (snapshot.exists) {
          userPreferences.value = UserPreferences.fromMap(snapshot.data()!);
        }
      });
    }
  }

  @override
  void onClose() {
    _preferencesSubscription?.cancel();
    super.onClose();
  }
}
```

#### Trip State Reactive Pattern
```dart
// Subscribe to trip updates and react to status changes
currentTripSub = tripsColl.doc(trip.id).snapshots().listen((snapshot) {
  if (snapshot.exists) {
    currentRiderTrip.value = snapshot.data();
  } else {
    // Trip has been deleted
    clearCurrentRiderTrip();
    reset(stopCurrentTripSub: true);
  }
});
```

### Service Pattern with GetX

#### GetxService for Singleton Services
```dart
class TripStateService extends GetxService {
  static TripStateService get instance => Get.find();

  // Reactive state management
  final Map<String, RxBool> _transitionLoading = {};
  final RxString lastError = ''.obs;

  // Check loading state
  bool isLoading(String tripId, String transitionType) {
    final key = '${tripId}_$transitionType';
    return _transitionLoading[key]?.value ?? false;
  }

  // Set loading state
  void _setLoading(String tripId, String transitionType, bool loading) {
    final key = '${tripId}_$transitionType';
    if (!_transitionLoading.containsKey(key)) {
      _transitionLoading[key] = loading.obs;
    } else {
      _transitionLoading[key]!.value = loading;
    }
  }

  void clearError() => lastError.value = '';
}
```

## Error Handling Patterns

### Try-Catch with GetX Dialogs
```dart
Future<void> performOperation() async {
  try {
    // Show loading
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );

    final result = await apiCall();

    // Close loading
    Get.back();

    // Show success
    Get.snackbar('Success', 'Operation completed');

  } catch (e) {
    // Close loading if still showing
    if (Get.isDialogOpen ?? false) Get.back();

    // Show error
    Get.snackbar(
      'Error',
      'Operation failed: ${e.toString()}',
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );

    rethrow;
  }
}
```

### Reactive Error State
```dart
class ApiController extends GetxController {
  var isLoading = false.obs;
  var error = RxnString();
  var data = Rxn<ApiResponse>();

  Future<void> fetchData() async {
    try {
      isLoading.value = true;
      error.value = null;

      final response = await apiService.getData();
      data.value = response;

    } catch (e) {
      error.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }
}
```

## Best Practices Summary

1. **Always use proper lifecycle management** - Clean up streams, timers, and workers in onClose()
2. **Use appropriate Rx types** - Rxn for nullable, RxList for lists, etc.
3. **Don't make reactive widgets const** - This prevents rebuilding
4. **Use Get.find() consistently** - Find dependencies as class variables for reactive widgets
5. **Handle loading states** - Show loading indicators and disable interactions during async operations
6. **Provide user feedback** - Use snackbars for errors and success messages
7. **Use workers for side effects** - ever() for continuous reactions, once() for one-time reactions
8. **Implement proper error handling** - Always handle exceptions and provide user feedback
9. **Use debouncing for frequent updates** - Prevent excessive API calls or UI updates
10. **Follow the service pattern** - Use GetxService for singleton services with reactive state
