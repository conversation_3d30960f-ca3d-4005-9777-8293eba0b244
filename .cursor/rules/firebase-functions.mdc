---
description: 
globs: firebase/functions/**/*.ts
alwaysApply: false
---
# Firebase Functions Development Standards

This document outlines the configuration patterns and development standards for Firebase Functions v2 in the Fiaranow project.

## Function Configuration Standards

All Firebase functions must follow these specific configuration patterns for consistency with the existing codebase.

### 🌍 Common Configuration
- **Region**: `"europe-west3"` (all functions must use this region)
- **Memory**: `"512MiB"` (standard for most operations)
- **Timeout**: `5` seconds (for quick operations) or `30` seconds (for complex state transitions)
- **Concurrency**: `1000` (for most functions)

### 📋 Function-Specific Patterns

#### 1. onDocumentWritten/onDocumentDeleted (Firestore triggers)
```typescript
export const functionName = onDocumentWritten({
  document: "path/to/{document}",
  region: "europe-west3",
  timeoutSeconds: 5,
  memory: "512MiB",
  concurrency: 1000,
  maxInstances: 1,  // Prevent concurrent executions
}, async (event) => {
  // Function implementation
});
```

#### 2. onCall (HTTP callable functions)
```typescript
export const functionName = onCall({
  region: "europe-west3",
  timeoutSeconds: 5,  // or 30 for complex operations
  memory: "512MiB",
  concurrency: 1000,
}, async (request) => {
  // Function implementation
});
```

#### 3. onSchedule (scheduled functions)
```typescript
export const functionName = onSchedule({
  schedule: "every 24 hours",
  region: "europe-west3",
  timeoutSeconds: 60,
  memory: "512MiB",
  maxInstances: 1,
}, async (event) => {
  // Function implementation
});
```

#### 4. onRequest (HTTP request functions)
```typescript
export const functionName = onRequest({
  region: "europe-west3",
  timeoutSeconds: 60,
  memory: "256MiB"  // Lower memory for simple HTTP tasks
}, async (request, response) => {
  // Function implementation
});
```

### 📝 Import Patterns

#### Standard Imports
```typescript
import { onDocumentWritten } from "firebase-functions/v2/firestore";
import { onCall } from "firebase-functions/v2/https";
import { onSchedule } from "firebase-functions/v2/scheduler";
import { logger } from "firebase-functions/v2";
import { getFirestore, FieldValue } from "firebase-admin/firestore";
```

#### Utility Imports
```typescript
import { normalizeDataForFirestore } from "./utils";
import { getTenantCollection, ensureTenantId } from "./tenant_utils";
```

## Development Patterns

### Error Handling
```typescript
export const myFunction = onCall({
  region: "europe-west3",
  timeoutSeconds: 5,
  memory: "512MiB",
  concurrency: 1000,
}, async (request) => {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication is required.");
  }

  try {
    // Function logic here
    logger.info("Operation completed successfully", { data });
    return { success: true };
  } catch (error) {
    logger.error("Error in myFunction", { error });
    throw new HttpsError("internal", "Internal server error", { originalError: error });
  }
});
```

### Logging Standards
```typescript
// Use structured logging with context
logger.info("User authentication successful", { 
  uid: request.auth.uid,
  operation: "login"
});

logger.error("Database operation failed", {
  uid: request.auth.uid,
  tripId,
  error: error.message,
  stack: error.stack
});

// Use emoji prefixes for enhanced readability in development
logger.info("🚗 Driver accept request initiated", {
  tripId,
  driverUid,
  tenantId: effectiveTenantId
});
```

### Transaction Patterns
```typescript
// Follow the "reads-before-writes" pattern
await db.runTransaction(async (transaction) => {
  // 🔍 STEP 1: Perform all reads first
  const tripDoc = await transaction.get(tripRef);
  const userDoc = await transaction.get(userRef);
  
  if (!tripDoc.exists) {
    throw new HttpsError("not-found", "Trip not found");
  }
  
  // 🔍 STEP 2: Process data and prepare updates
  const tripData = tripDoc.data();
  const updateData = {
    status: 'updated',
    lastModified: FieldValue.serverTimestamp()
  };
  
  // 🔍 STEP 3: Perform all writes
  transaction.update(tripRef, updateData);
  transaction.update(userRef, { lastAction: 'trip_updated' });
});
```

### Multi-tenant Support
```typescript
export const myFunction = onCall({
  region: "europe-west3",
  timeoutSeconds: 5,
  memory: "512MiB",
  concurrency: 1000,
}, async (request) => {
  const { tenantId, ...otherData } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);
  
  // Use tenant-aware collection references
  const tripRef = getTenantCollection(effectiveTenantId, 'trips').doc(tripId);
  
  // Always log tenant context
  logger.info("Processing request", {
    tenantId: effectiveTenantId,
    uid: request.auth?.uid
  });
});
```

### Data Normalization
```typescript
// Always use normalizeDataForFirestore for updates
const updateData = normalizeDataForFirestore({
  status: 'completed',
  completedAt: FieldValue.serverTimestamp(),
  metadata: { reason: 'user_action' }
}, "functionName:collection:operation");

// Check if there's actual data to update
if (Object.keys(updateData).length > 0) {
  transaction.update(docRef, updateData);
}
```

## Performance Optimization

### Function Sizing Guidelines
- **Quick operations** (< 5 seconds): `timeoutSeconds: 5`, `memory: "512MiB"`
- **Complex state transitions** (< 30 seconds): `timeoutSeconds: 30`, `memory: "512MiB"`
- **Background processing** (< 60 seconds): `timeoutSeconds: 60`, `memory: "512MiB"`
- **Simple HTTP tasks**: `memory: "256MiB"`

### Concurrency Control
- **Document triggers**: Always use `maxInstances: 1` to prevent race conditions
- **HTTP functions**: Use `concurrency: 1000` unless specific limitations needed
- **Scheduled functions**: Use `maxInstances: 1` to prevent overlapping executions

### Memory Management
- Clean up resources in error handlers and completion paths
- Cancel timers and close streams in `onClose()` methods for services
- Use appropriate memory allocation based on function complexity

## Security Patterns

### Authentication Validation
```typescript
export const secureFunction = onCall({
  region: "europe-west3",
  timeoutSeconds: 5,
  memory: "512MiB",
  concurrency: 1000,
}, async (request) => {
  // Always check authentication first
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication is required.");
  }

  // Validate tenant access for admin functions
  const userDoc = await db.collection('admin_users').doc(request.auth.uid).get();
  if (!userDoc.exists || !userDoc.data()?.isActive) {
    throw new HttpsError("permission-denied", "Admin access required");
  }
});
```

### Input Validation
```typescript
export const validateInputFunction = onCall({
  region: "europe-west3",
  timeoutSeconds: 5,
  memory: "512MiB",
  concurrency: 1000,
}, async (request) => {
  const { tripId, driverUid, tenantId } = request.data;
  
  // Validate required parameters
  if (!tripId || !driverUid) {
    throw new HttpsError("invalid-argument", "Trip ID and driver UID are required.");
  }
  
  // Validate data types and formats
  if (typeof tripId !== 'string' || tripId.trim().length === 0) {
    throw new HttpsError("invalid-argument", "Invalid trip ID format.");
  }
});
```

## Testing and Deployment

### Local Development
- Always test functions in the Firebase emulator before deployment
- Use environment-specific configuration for development vs production
- Test with realistic data volumes and edge cases

### Deployment Checklist
- ✅ All functions use correct region (`europe-west3`)
- ✅ Memory and timeout settings are appropriate
- ✅ Error handling includes proper logging
- ✅ Multi-tenant support is implemented where needed
- ✅ Authentication and authorization checks are in place
- ✅ Input validation is comprehensive
- ✅ Transaction patterns follow reads-before-writes

These standards ensure consistency, performance, and reliability across all Firebase Functions in the Fiaranow multi-tenant architecture.
