---
description: 
globs: admin_sveltekit/src/**/*.svelte,admin_sveltekit/src/**/*.ts,admin_sveltekit/src/**/*.svelte.ts
alwaysApply: false
---
# General rules

- Always use Svelte 5's new runes syntax. Use $state, $props (instead of export let) and other modern syntax.
- For Storybook stories, favor the use of the fn() function instead of console.log().
- Use `import { page } from "$app/state";` instead of `import { page } from "$app/stores";`, then just use `page` instead of `$page`.

# Reactivity rules

- Use `onDestroy` instead of `$effect` for cleanup
- `$derived()` takes an expression, not a function
- **Don't overcomplicate reactivity**: Use `$derived` directly from reactive stores instead of `$state` + `$effect`
  - ❌ Wrong: Creating a `$state` variable and updating it with `$effect`
  - ✅ Correct: Using `$derived` to get values directly from stores/maps
  - Example:
    ```typescript
    // ❌ Overcomplicated
    let document = $state<Document | null>(null);
    $effect(() => {
      const doc = documentsMap.get(docId);
      if (doc) document = doc;
    });
    
    // ✅ Simple and reactive
    const document = $derived(documentsMap.get(docId) || null);
    ```
- Only use `$effect` for side effects (like syncing with external systems), not for deriving state
- When deriving multiple related values, use multiple `$derived` statements:
  ```typescript
  const document = $derived(documentsMap.get(docId) || null);
  const driver = $derived(
    document?.driverUID ? usersMap.get(document.driverUID) || null : null
  );
  ```

# Event handling

- Use the new syntax like `onclick` instead of the old one `on:click`

# Navigation

## Language-aware Navigation
- Always use `localizedGoto` from `$lib/utils` for programmatic navigation instead of direct methods
- Import with: `import { localizedGoto } from "$lib/utils"`
- This ensures the current language is preserved in the URL

### Examples:
```typescript
// ❌ Wrong - loses language context
onclick={() => (window.location.href = "/vehicles")}
onclick={() => goto("/vehicles")}

// ✅ Correct - preserves language
onclick={() => localizedGoto("/vehicles")}
```

### Common use cases:
- Button/Link navigation: `<Button onclick={() => localizedGoto("/path")}>Navigate</Button>`
- After form submission: `await localizedGoto("/success")`
- Conditional navigation: `if (condition) localizedGoto("/dashboard")`
- With query params: `localizedGoto("/search?q=term")`

### For static links in components:
- Use the `href` prop with Button components when possible, as it automatically handles localization
- For dynamic navigation or onclick handlers, always use `localizedGoto`

# UI Components

## Toast Notifications
- Always import toast from "svelte-sonner", not from custom components
- Use direct method calls like toast.success() and toast.error() rather than the object syntax
- Example: `toast.success("Operation completed successfully")` instead of `toast({ title: "Success", description: "...", variant: "success" })`

## Spinner Component
- Import Spinner as a default import: `import Spinner from "$lib/components/ui/Spinner.svelte"`
- Not as a named import: `import { Spinner } from "$lib/components/ui/Spinner.svelte"`
- When using in components, use `<Spinner className="..." />` for styling

## Card Component Scroll Area Pattern

### Purpose
To ensure consistent display of lists and content across the application, especially when dealing with dynamic content that may vary in length.

### Implementation
- For Card components that display lists or detailed content, implement a fixed height with overflow handling:
  ```html
  <Card.Content class="h-[calc(100vh-Xpx)] overflow-y-auto">
    <!-- Content here -->
  </Card.Content>
  ```
  where X is an appropriate offset value based on the page layout context:
  - Use `140px` for simpler layouts with minimal header content
  - Use `152px` for layouts with additional header elements or navigation
  - Adjust as needed for specific page requirements

### Benefits
- Prevents page layout shifts when content size changes
- Maintains consistent UI across different sections of the application
- Improves user experience by containing scrollable areas to logical components
- Avoids need for full page scrolling when only part of the content needs to be scrollable

### Examples
- Trips list in [+page.svelte](mdc:admin_sveltekit/src/routes/rides/trips/+page.svelte)
- Payments list in [+layout.svelte](mdc:admin_sveltekit/src/routes/finance/payments/+layout.svelte)
- Payment details in [+page.svelte](mdc:admin_sveltekit/src/routes/finance/payments/+page.svelte)