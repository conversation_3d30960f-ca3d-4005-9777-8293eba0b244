---
alwaysApply: true
---
# Logging Patterns & Error Handling

## Platform-Specific Documentation

Refer to comprehensive platform guides for detailed patterns:

- **Flutter App**: [flutter-logging.md](mdc:docs/logging/flutter-logging.md) - Hierarchical logging, Crashlytics, AgentLoggingService
- **Admin Panel**: [admin-logging.md](mdc:docs/logging/admin-logging.md) - Toast notifications, console logging, Firebase integration  
- **Firebase Functions**: [functions-logging.md](mdc:docs/logging/functions-logging.md) - Structured logging, audit trails, performance monitoring

## Critical Requirements

### ❌ NEVER Hide Real Errors
```dart
// ❌ BAD: Hiding actual error
catch (e) {
  throw Exception('Something went wrong');
}

// ✅ GOOD: Surface real error with context
catch (e, stackTrace) {
  _logger.severe('Operation failed: $e', e, stackTrace);
  throw Exception('Operation failed: ${e.toString()}');
}
```

### ✅ Always Use Structured Logging
Include relevant context: user ID, trip ID, tenant ID, operation details.

### ✅ Platform-Specific Patterns
- **Flutter**: Use `Logger('ClassName')`, GetX snackbars with `SnackPosition.TOP`
- **Admin**: Use `toast.error()` from svelte-sonner, console logging with emojis
- **Functions**: Use Firebase Functions v2 logger with structured data objects

### ✅ Environment Awareness
- **Development**: Detailed logs, mock operations, full error objects
- **Production**: Essential logs only, user-friendly messages, performance focus

## Quick Reference

```dart
// Flutter logging
final Logger _logger = Logger('ServiceName');
_logger.severe('Error description', error, stackTrace);

// Flutter user feedback  
Get.snackbar('Error', 'Message', snackPosition: SnackPosition.TOP);
```

```typescript
// Admin panel feedback
toast.error('Operation failed: ${errorMessage}');

// Firebase Functions logging
logger.error('❌ Operation failed', {
  tripId, tenantId, error: error.message, operation: 'function_name'
});
```
