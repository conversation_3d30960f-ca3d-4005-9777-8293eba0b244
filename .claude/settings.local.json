{"permissions": {"allow": ["Bash(./notify-user.sh:*)", "Bash(node:*)", "WebFetch(domain:docs.anthropic.com)", "mcp__sidekick__spawn_process", "mcp__sidekick__get_partial_process_output", "mcp__posthog-eu__list-errors", "mcp__posthog-eu__projects-get", "WebFetch(domain:pub.dev)", "WebFetch(domain:firebase.google.com)", "Bash(pod repo update:*)", "Bash(pod install:*)", "mcp__sidekick__get_full_process_output", "mcp__sidekick__get_process_status", "Bash(flutter analyze:*)", "<PERSON><PERSON>(docker compose:*)", "WebFetch(domain:www.algolia.com)", "Bash(npm:*)", "Bash(npx tsc:*)", "mcp__serena__initial_instructions", "mcp__serena__list_memories", "mcp__serena__search_for_pattern", "mcp__serena__find_file", "mcp__serena__list_dir", "mcp__serena__get_symbols_overview", "mcp__serena__find_symbol", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(dart run:*)"], "deny": []}}