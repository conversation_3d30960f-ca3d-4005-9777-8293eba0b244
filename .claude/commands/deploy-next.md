# Deploy Next Version

Deploy Fiaranow with specified targets: $ARGUMENTS (default: all)

Supported targets: 
- `all` (default) - Deploy everything
- `flutter` - Build and prepare Flutter app
- `admin` - Update admin dashboard version
- `hosting` - Build and deploy admin to Firebase hosting
- `firebase` or `functions` - Deploy Firebase functions
- `rules` - Deploy Firestore rules and indexes

## Current Status

- Current Flutter version: @fiaranow_flutter/pubspec.yaml (line containing "version:")
- Current Admin version: @admin_sveltekit/src/routes/version.ts

## Deployment Instructions

### 1. Version Management
- Read current version from `fiaranow_flutter/pubspec.yaml`
- Extract version name and build number (format: x.y.z+buildNumber)
- Propose next version by incrementing build number
- Ask user to confirm or provide different version
- Parse the confirmed version to extract components

### 2. Show Deployment Plan
Based on the targets provided in $ARGUMENTS (or "all" if none):
- Display a clear list of all actions that will be performed:
  - Version updates (Flutter app, Admin dashboard)
  - Build commands to be executed
  - Deployment commands that user will need to run
- Show the exact commands that will be run by Claude
- Show the deployment commands that user must run manually
- Request explicit user confirmation with "Yes" or "No"
- Stop immediately if user declines

### 3. Flutter App Build (if targets include "flutter" or "all")
- Update version in `fiaranow_flutter/pubspec.yaml` using Edit tool
- Execute `cd fiaranow_flutter && ./build-appbundle.sh` using Bash tool
- The script will create versioned .aab file and open the output directory
- Use the process tools from `sidekick` MCP tool to avoid timeouts
- Build will need to be checked periodically to see if it's done

### 4. Admin Version Update (if targets include "admin" or "all")
- Set admin version to "R{buildNumber}" (e.g., R27 for build 27)
- For admin-only deployments:
  - Check current admin version in `admin_sveltekit/src/routes/version.ts`
  - If version already starts with "R{buildNumber}", add/increment decimal
  - Example: R27 → R27.1 → R27.2
- Update `admin_sveltekit/src/routes/version.ts` using Edit tool

### 5. Firebase Hosting Deployment (if targets include "hosting" or "all")
- Build the admin dashboard:
  - Execute `cd admin_sveltekit && npm run build` using Bash tool
  - Wait for build to complete successfully
- Deploy to Firebase hosting:
  - Remind user to run `cd firebase && firebase deploy --only hosting`

### 6. Database Migrations Check
- Check `firebase/data-tools/migrate.sh` for any migrations that need to run against production
- If migrations exist, remind user to review and run them carefully

### 7. Firebase Backend Deployment
Based on targets:
- If "rules" or "all": Remind user to run `firebase deploy --only firestore:rules,firestore:indexes`
- If "functions", "firebase" or "all": Remind user to run `cd firebase/functions && firebase deploy --only functions`

### Important Notes
- NEVER run deployment commands automatically - only remind the user
- Always get explicit user confirmation before making changes
- Use TodoWrite to track deployment steps
- Handle version conflicts for admin-only deployments with decimal versions
- Ensure all file edits use proper tools (Read first, then Edit)
- Think hard as you deploy as a senior developer