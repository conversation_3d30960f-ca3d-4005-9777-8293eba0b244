# Admin SvelteKit Translation

## Context
This command translates hardcoded text in admin_sveltekit Svelte components to use Paraglide JS internationalization. Execute this as a **sub-agent** to conserve context window memory.

File(s) to translate:
$ARGUMENTS

## Critical Rules
- **FOCUS ONLY** on translation - no other modifications
- **WORK WITHIN** admin_sveltekit folder only
- **DO NOT** compile, test, or fix linting errors
- **DO NOT** modify files outside specified scope
- **SYNCHRONIZE** both language files simultaneously

## Target Setup
- **Languages**: `en-us` (English US), `fr-fr` (French France)
- **Message Files**: 
  - `admin_sveltekit/messages/en-us.json`
  - `admin_sveltekit/messages/fr-fr.json`
- **Import Pattern**: `import * as m from '$lib/paraglide/messages';`

## Translation Process

### 1. Identify Target Component
- Focus on ONE Svelte component (.svelte file) at a time
- Scan for hardcoded strings in HTML, attributes, and JavaScript

### 2. Create Message Keys
- **Naming Convention**: `componentName_elementPurpose` (camelCase)
- **Established Patterns from Codebase**:
  - **Page/Layout prefixes**: `mainPage_`, `tripsList_`, `clientsList_`, `driversList_`, `eventsLayout_`, `paymentsLayout_`
  - **Component prefixes**: `mobileUserDetails_`, `tripDetails_`, `paymentDetailsComponent_`, `adminUserDetailsComponent_`
  - **Form prefixes**: `adminConfigForm_`, `driverEdit_`
  - **Dialog/Modal prefixes**: Use component name + action (e.g., `profile_logoutConfirmDescription`)
  - **List item prefixes**: `mobileUserListItem_`, `adminUserListItem_`
- **Element Purpose Suffixes**:
  - `_title`, `_pageTitle`, `_description` for headings
  - `_button`, `_submitButton`, `_cancelButton` for buttons
  - `_label` for form labels
  - `_placeholder` for input placeholders
  - `_tooltip` for tooltips
  - `_errorToast`, `_successToast` for notifications
  - `_filter` for filter options
  - `_cardTitle` for card headers

### 3. Add to Message Files
**English (en-us.json)**:
```json
{
  "componentName_elementPurpose": "Original English Text"
}
```

**French (fr-fr.json)**:
```json
{
  "componentName_elementPurpose": "Texte français traduit"
}
```

### 4. Replace in Component
**Text Content**:
```svelte
<!-- Before -->
<h1>Page Title</h1>
<!-- After -->
<h1>{m.componentName_pageTitle()}</h1>
```

**Attributes**:
```svelte
<!-- Before -->
<input placeholder="Enter name">
<!-- After -->
<input placeholder={m.componentName_enterNamePlaceholder()}>
```

**Dynamic Parameters**:
```svelte
<!-- Message files -->
"welcomeMessage": "Welcome, {userName}!"
"welcomeMessage": "Bienvenue, {userName} !"

<!-- Component -->
{m.welcomeMessage({ userName: user.name })}
```

**Pluralization** (following established pattern):
```svelte
<!-- Message files (en-us.json) -->
"clientsList_countListed_one": "{count} client listed"
"clientsList_countListed_other": "{count} clients listed"

<!-- Message files (fr-fr.json) -->
"clientsList_countListed_one": "{count} client listé"
"clientsList_countListed_other": "{count} clients listés"

<!-- Component -->
{items.length === 1
  ? m.clientsList_countListed_one({ count: items.length })
  : m.clientsList_countListed_other({ count: items.length })}
```

## Existing Patterns
Based on codebase analysis:
- **Import**: `import * as m from '$lib/paraglide/messages';`
- **Usage**: `{m.keyName()}` for text content, `{m.keyName()}` for attributes
- **Parameters**: `{m.keyName({ param: value })}` for dynamic content
- **Plurals**: `_one` and `_other` suffixes with conditional logic
- **Schema**: Both JSON files include `"$schema": "https://inlang.com/schema/inlang-message-format"`
- **Key Structure**: All keys are synchronized between `en-us.json` and `fr-fr.json`

## Quality Checklist
- [ ] All hardcoded strings identified and replaced
- [ ] Keys follow naming convention
- [ ] Both language files updated with same keys
- [ ] Dynamic parameters properly passed
- [ ] Pluralization handled with conditionals
- [ ] Import statement added to component
- [ ] No compilation attempts made

## French Translation Guidelines
- Maintain formal tone ("vous" form)
- Preserve technical terms where appropriate
- Adapt UI terminology to French conventions
- Consider text length differences for UI layout

## Real-World Pattern Examples

**Established Key Patterns from Codebase**:
```json
// Page titles and descriptions
"tripsList_title": "Trips List"
"mainPage_navDashboard": "Dashboard"
"clientsList_pageTitle": "Clients List"

// Buttons and actions
"tripDetails_deleteButton": "Delete Trip"
"tripDetails_cancelButton": "Cancel Trip"
"profile_logoutConfirmButton": "Logout"

// Form elements
"driverEdit_brandLabel": "Brand"
"adminConfigForm_saveButton": "Save Changes"
"mobileUserDetails_phoneNumberLabel": "Phone Number"

// Status and states
"tripDetails_tripStatusCompleted": "Completed"
"adminUserListItem_statusActive": "Active"
"mobileUserDetails_driverStatusVerified": "Verified"

// Toast notifications
"tripDetails_deleteSuccessToast": "Trip deleted successfully"
"tripDetails_deleteErrorToast": "Failed to delete trip"

// Pluralization
"clientsList_countListed_one": "{count} client listed"
"clientsList_countListed_other": "{count} clients listed"

// Dynamic content
"paymentDetailsComponent_createdOn": "Created on {date}"
"mobileUserDetails_maxPassengersValue": "{count} passengers"
```

## Complete Migration Example

**Before (TripsList.svelte)**:
```svelte
<script lang="ts">
  // component logic
</script>

<h1>Trips List</h1>
<p>{tripCount} trips available</p>
<button>Refresh</button>
```

**After (TripsList.svelte)**:
```svelte
<script lang="ts">
  import * as m from '$lib/paraglide/messages';
  // component logic
</script>

<h1>{m.tripsList_title()}</h1>
<p>{m.tripsList_countAvailable({ tripCount })}</p>
<button>{m.tripsList_refreshButton()}</button>
```

**Message Files**:
```json
// en-us.json
{
  "$schema": "https://inlang.com/schema/inlang-message-format",
  "tripsList_title": "Trips List",
  "tripsList_countAvailable": "{tripCount} trips available",
  "tripsList_refreshButton": "Refresh"
}

// fr-fr.json
{
  "$schema": "https://inlang.com/schema/inlang-message-format",
  "tripsList_title": "Liste des trajets",
  "tripsList_countAvailable": "{tripCount} trajets disponibles",
  "tripsList_refreshButton": "Actualiser"
}
```
