# Flutter Translate

File(s):
`$ARGUMENTS`

Translate dart widget file(s) according to the rules below:

## Overview
This command translates hardcoded English text in Flutter Dart files to support internationalization using ARB files.

## Instructions

1. **Launch Translation Sub-Agent**
   - Use the Task tool to spawn a sub-agent for translation work to conserve context window
   - Pass these specific instructions to the sub-agent:

2. **Translation Process** (for sub-agent)
   
   a) **Extract English Text**
   - Identify all hardcoded English text in the specified Dart files
   - Focus on visible UI text (Text widgets, button labels, etc.)
   
   b) **Create English ARB entries**
   - Add entries to `fiaranow_flutter/lib/l10n/app_en.arb`
   - Use appropriate prefixes and identifiers following the existing pattern:
     * Use descriptive prefixes like `mapScreen_`, `driverProfileForm_`, `notification_`, `tripDetails_`, `home_`, `auth_`, etc.
     * Follow with a descriptive name: `mapScreen_confirmPickupLocationButton`, `driverProfileForm_vehicleBrand`
   - Include required metadata for each entry:
     * Always add `@keyName` metadata entry with `description` field
     * For entries with placeholders, include `placeholders` object with type and example values
     * Example metadata structure:
       ```json
       "@mapScreen_seatsAvailable": {
         "description": "Text showing number of seats available",
         "placeholders": {
           "maxPassengers": {
             "type": "int",
             "example": "4"
           }
         }
       }
       ```
   
   c) **Create French translations**
   - Translate English text to French as a native speaker
   - Add translations to `fiaranow_flutter/lib/l10n/app_fr.arb`
   - Include metadata ONLY for entries with placeholders (same structure as English file)
   - Simple text entries require no metadata in French file
   
   d) **Update Dart files**
   - Replace hardcoded text with localization calls
   - Add original English text as comments: `// "Original English text"`
   - Remove `const` specifiers where translations are used
   - Add import: `import 'l10n/app_localizations.dart';`
   
   e) **Complete translation**
   - Ensure ALL visible Text widgets are translated
   - Verify localization calls work correctly
   - No need to update the following files (they are generated automatically):
     - `fiaranow_flutter/lib/l10n/app_localizations_en.dart`
     - `fiaranow_flutter/lib/l10n/app_localizations_fr.dart`
     - `fiaranow_flutter/lib/l10n/app_localizations.dart`

## Critical Requirements
- Must translate ALL visible text, not partial
- Original text must be preserved as comments
- Proper ARB file structure with metadata for English (always include @keyName with description)
- French ARB file needs metadata only for entries with placeholders
- Follow existing key naming patterns with appropriate prefixes
- Import statement: `import 'l10n/app_localizations.dart';`

## Key Naming Pattern Examples
- Screen-specific: `mapScreen_`, `driverProfileForm_`, `phoneNumberForm_`, `tripDetails_`
- Feature-specific: `notification_`, `auth_`, `serviceStatusUpdate_`, `tripRejectionScreen_`
- General UI: `home_`, `mainPage_`, `menuDrawer_`, `chat_`
- Status/States: `tripStatus_`, `foregroundService_`, `navigationState_`