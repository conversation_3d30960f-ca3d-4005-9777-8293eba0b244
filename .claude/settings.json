{"permissions": {"allow": ["Bash(fvm flutter:*)"], "deny": []}, "hooks": {"PostToolUse": [{"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": "FILE_PATH=$(echo '$CLAUDE_TOOL_INPUT' | jq -r '.file_path // empty'); if [[ \"$FILE_PATH\" =~ ^admin_sveltekit/.*\\.(ts|js|svelte)$ ]]; then cd admin_sveltekit && echo '🎨 Formatting admin file: '$FILE_PATH && npx prettier --write --config .prettierrc \"$FILE_PATH\"; fi"}, {"type": "command", "command": "FILE_PATH=$(echo '$CLAUDE_TOOL_INPUT' | jq -r '.file_path // empty'); if [[ \"$FILE_PATH\" =~ ^firebase/functions/.*\\.(ts|js)$ ]]; then cd firebase/functions && echo '🎨 Formatting Firebase function: '$FILE_PATH && npx prettier --write --config .prettierrc \"$FILE_PATH\"; fi"}, {"type": "command", "command": "FILE_PATH=$(echo '$CLAUDE_TOOL_INPUT' | jq -r '.file_path // empty'); if [[ \"$FILE_PATH\" =~ ^admin_sveltekit/src/.*\\.(ts|svelte)$ ]]; then cd admin_sveltekit && echo '🧹 Running ESLint on admin file: '$FILE_PATH && npx eslint --fix --quiet \"$FILE_PATH\" 2>/dev/null || true; fi"}, {"type": "command", "command": "FILE_PATH=$(echo '$CLAUDE_TOOL_INPUT' | jq -r '.file_path // empty'); if [[ \"$FILE_PATH\" =~ ^firebase/functions/src/.*\\.(ts|js)$ ]]; then cd firebase/functions && echo '🧹 Running ESLint on Firebase function: '$FILE_PATH && npx eslint --fix --quiet \"$FILE_PATH\" 2>/dev/null || true; fi"}, {"type": "command", "command": "FILE_PATH=$(echo '$CLAUDE_TOOL_INPUT' | jq -r '.file_path // empty'); if [[ \"$FILE_PATH\" =~ ^admin_sveltekit/messages/.*\\.json$ ]]; then cd admin_sveltekit && echo '🚀 Processing translations for: '$FILE_PATH && npm run translations:process; fi"}, {"type": "command", "command": "FILE_PATH=$(echo '$CLAUDE_TOOL_INPUT' | jq -r '.file_path // empty'); if [[ \"$FILE_PATH\" =~ ^fiaranow_flutter/lib/.*\\.dart$ ]]; then cd fiaranow_flutter && echo '🎯 Formatting Flutter Dart file: '$FILE_PATH && fvm dart format --line-length=130 \"$FILE_PATH\"; fi"}]}]}}