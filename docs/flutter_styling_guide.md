# Flutter Styling Guide

This document outlines styling patterns and best practices for Flutter widgets in the Fiaranow app, ensuring consistent visual design and proper dark mode support.

## 🎨 Selection State Styling

### ✅ Recommended Pattern for Selectable Cards/List Items

```dart
final isSelected = /* your selection logic */;

Card(
  margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
  elevation: isSelected ? 0 : 1,
  shadowColor: Colors.transparent, // CRITICAL: Prevents shadow bleeding
  color: isSelected 
      ? Theme.of(context).brightness == Brightness.dark
          ? Theme.of(context).primaryColor.withValues(alpha: 0.35)
          : Theme.of(context).primaryColor.withValues(alpha: 0.15)
      : Theme.of(context).cardColor,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(8),
    side: isSelected 
        ? BorderSide(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white  // White border in dark mode
                : Theme.of(context).primaryColor, 
            width: 2
          )
        : BorderSide.none,
  ),
  child: // Your content here
)
```

### Key Principles:
- **No elevation for selected items** to avoid shadow bleeding
- **Transparent shadow colors** to prevent visual artifacts
- **Solid borders** for clear selection indication
- **Theme-aware border colors** (white in dark, primary in light)
- **Appropriate background opacity** (higher in dark mode for visibility)

## 🌓 Dark Mode Color Handling

### ✅ Text Color Patterns

```dart
// For secondary text (descriptions, timestamps, etc.)
Text(
  "Your text here",
  style: TextStyle(
    color: Theme.of(context).brightness == Brightness.dark
        ? Colors.grey[300]  // Light grey for dark mode
        : Colors.grey[600], // Dark grey for light mode
  ),
)

// For selection-aware text
Text(
  "Your text here",
  style: TextStyle(
    color: isSelected 
        ? Theme.of(context).brightness == Brightness.dark
            ? Colors.white
            : Theme.of(context).primaryColor
        : null, // Let theme handle default
    fontWeight: isSelected ? FontWeight.bold : null,
  ),
)
```

### ✅ Icon Color Patterns

```dart
Icon(
  Icons.your_icon,
  color: Theme.of(context).brightness == Brightness.dark
      ? Colors.grey[500]  // Lighter for visibility
      : Colors.grey[400], // Standard for light mode
)
```

### ❌ Anti-patterns to Avoid

```dart
// DON'T: Hard-coded colors that break in dark mode
color: Colors.grey[600]  // Invisible in dark mode

// DON'T: Using primary color text in dark mode selections
color: Theme.of(context).primaryColor // May be dark-on-dark

// DON'T: Fixed white/black without theme consideration
color: Colors.white // May be white-on-white in light mode
```

## 👤 Avatar Styling

### ✅ Consistent Avatar Pattern

```dart
CircleAvatar(
  radius: 24,
  backgroundColor: Theme.of(context).brightness == Brightness.dark
      ? Colors.cyan.shade300  // Consistent bright color
      : Theme.of(context).primaryColor,
  child: Text(
    "X",
    style: TextStyle(
      color: Theme.of(context).brightness == Brightness.dark
          ? Colors.black      // Dark text on bright background
          : Colors.white,     // White text on primary color
      fontSize: 18,
      fontWeight: FontWeight.bold,
    ),
  ),
)
```

### Key Principles:
- **Consistent colors** regardless of selection state
- **High contrast** combinations for readability
- **Bright colors in dark mode** for better visibility

## 📝 List Layout Best Practices

### ✅ Proper Spacing and Padding

```dart
// For list items with visual separation
Card(
  margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6), // Good spacing
  
  child: ListTile(
    contentPadding: const EdgeInsets.only(
      left: 16, 
      top: 8, 
      bottom: 8, 
      right: 4  // Reduced right padding for edge-positioned buttons
    ),
    // ... content
  ),
)

// For lists without separators (cleaner look)
ListView.builder(  // Use builder instead of separated
  itemBuilder: (context, index) {
    // Return your items with proper margin
  },
)
```

## 🚫 Critical Anti-patterns

### ❌ Shadow Bleeding Issues

```dart
// DON'T: High elevation with transparent backgrounds
elevation: 8,  // Too high
color: Theme.of(context).primaryColor.withValues(alpha: 0.1), // Too transparent

// DO: Low/no elevation with solid backgrounds
elevation: isSelected ? 0 : 1,
shadowColor: Colors.transparent,
color: isSelected ? solidColorBasedOnTheme : Theme.of(context).cardColor,
```

### ❌ Inconsistent Selection States

```dart
// DON'T: Change multiple visual properties for selection
fontSize: isSelected ? 20 : 18,  // Don't change sizes
radius: isSelected ? 28 : 24,    // Don't change dimensions

// DO: Use consistent sizing with color/border changes only
fontSize: 18,  // Consistent
color: isSelected ? selectionColor : defaultColor,  // Color change only
```

### ❌ Poor Dark Mode Support

```dart
// DON'T: Ignore theme brightness
color: Colors.grey[600],  // Fixed color

// DO: Theme-aware colors
color: Theme.of(context).brightness == Brightness.dark
    ? Colors.grey[300]
    : Colors.grey[600],
```

## 📚 Reference Examples

- **DriversList.dart**: Complete implementation of selectable cards with dark mode support
- **RoutesSelectionList.dart**: List layout with proper spacing and selection styling
- **NotificationListItem.dart**: Theme-aware text color patterns
- **ChatListScreen.dart**: Badge and timestamp styling for dark mode
- **VehicleManagementScreen.dart**: Avatar and empty state styling patterns

---

*This guide should be followed for all new UI components and used as reference when updating existing ones.*