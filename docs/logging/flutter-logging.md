# Flutter App Logging Guide

## Overview

The Flutter app uses a multi-layered logging system combining hierarchical logging, Firebase Crashlytics, Firebase Analytics, and custom structured event tracking for comprehensive error monitoring, user behavior analysis, and debugging.

## Core Components

### 1. Hierarchical Logging System

The app uses the `logging: ^1.3.0` package with hierarchical structure:

```dart
// Set up hierarchical logging in main.dart
hierarchicalLoggingEnabled = true;
Logger.root.level = Level.INFO;

// Per-class logger instances
final Logger _logger = Logger('ClassName');
```

#### Logger Usage Patterns

```dart
class MyService extends GetxController {
  final Logger _logger = Logger('MyService');
  
  @override
  void onInit() {
    _logger.info('Initializing MyService');
    super.onInit();
  }
  
  Future<void> performOperation() async {
    try {
      _logger.fine('Starting operation');
      // ... operation logic
      _logger.info('Operation completed successfully');
    } catch (e, stackTrace) {
      _logger.severe('Operation failed: $e', e, stackTrace);
      rethrow;
    }
  }
}
```

### 2. Firebase Crashlytics Integration

#### Global Error Capture
All uncaught errors are automatically sent to Crashlytics:

```dart
// In main.dart
FlutterError.onError = (errorDetails) {
  FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
};

PlatformDispatcher.instance.onError = (error, stack) {
  FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
  return true;
};
```

#### User Context Setting
Set user context when authenticated:

```dart
// In AuthState.dart
FirebaseCrashlytics.instance.setUserIdentifier(user.uid);
FirebaseCrashlytics.instance.setCustomKey('email', user.email ?? '');
FirebaseCrashlytics.instance.setCustomKey('name', user.displayName ?? '');
```

#### Manual Error Reporting
For specific error scenarios:

```dart
try {
  // risky operation
} catch (error, stackTrace) {
  _logger.severe('Service status change failed: $error', error, stackTrace);
  
  // Report to Crashlytics with context
  FirebaseCrashlytics.instance.recordError(
    error,
    stackTrace,
    reason: 'service_status_change_error',
    fatal: false,
  );
  
  rethrow;
}
```

### 3. AgentLoggingService - Structured Event Tracking

Custom service for tracking user actions and system events:

```dart
final agentLogging = Get.find<AgentLoggingService>();

// Track user actions
agentLogging.logAgentEvent('auth.sign_in', data: {
  'method': 'google',
  'timestamp': DateTime.now().toIso8601String()
});

// Track decisions
agentLogging.logAgentDecision('driver_selection', context: {
  'available_drivers': driverCount,
  'selection_criteria': criteria
});

// Track performance metrics
agentLogging.logAgentPerformance('trip_calculation_time', 
  calculationDuration.inMilliseconds.toDouble());

// Track errors with context
agentLogging.logAgentError('fcm_initialization_failed', 
  stackTrace: stackTrace, 
  context: {'platform': Platform.operatingSystem});
```

### 4. FCMErrorMonitor - Specialized Error Tracking

For Firebase Cloud Messaging errors with circuit breaker pattern:

```dart
// Report FCM errors
FCMErrorMonitor.reportError('token_refresh', error, stackTrace);

// Report successful operations
FCMErrorMonitor.reportSuccess('notification_sent');

// Check system health
final stats = FCMErrorMonitor.getErrorStats();
if (!stats['is_healthy']) {
  // Handle degraded service
}
```

### 5. Firebase Analytics - User Behavior & Event Tracking

Firebase Analytics tracks user interactions, feature usage, and business metrics with strict parameter type requirements.

#### ⚠️ Critical Parameter Type Rules

**IMPORTANT**: Firebase Analytics parameters must be String, long (int), or double only. Passing other types causes runtime crashes.

```dart
// ❌ WRONG - Will crash at runtime
FirebaseAnalytics.instance.logEvent(
  name: 'user_action',
  parameters: {
    'is_enabled': true,              // ❌ Boolean - will crash
    'user_count': userList.length,   // ❌ Int without conversion - may crash
    'user_data': userData,           // ❌ Object - will crash
  },
);

// ✅ CORRECT - Safe parameter types
FirebaseAnalytics.instance.logEvent(
  name: 'user_action',
  parameters: {
    'is_enabled': true.toString(),         // ✅ Boolean -> String
    'user_count': userList.length.toString(), // ✅ Int -> String
    'user_id': userData.id,                // ✅ String (direct)
    'score': userData.score.toDouble(),    // ✅ Double (direct)
    'timestamp': DateTime.now().toIso8601String(), // ✅ String
  },
);
```

#### Basic Usage Patterns

```dart
// Screen tracking
FirebaseAnalytics.instance.logScreenView(screenName: 'trip_details');

// User actions
FirebaseAnalytics.instance.logEvent(
  name: 'trip_created',
  parameters: {
    'trip_type': tripType.toString(),
    'payment_method': paymentMethod.name,
    'passenger_count': passengerCount.toString(),
    'estimated_cost': estimatedCost.toStringAsFixed(2),
    'user_id': currentUser.uid,
  },
);

// User properties
FirebaseAnalytics.instance.setUserProperty(
  name: 'user_type',
  value: userType.toString(),
);
```

#### Event Naming Conventions

Follow these patterns for consistent analytics:

```dart
// Action events: {object}_{action}
'trip_created', 'driver_selected', 'payment_completed'

// State change events: {object}_{state}_changed  
'service_status_changed', 'location_permission_changed'

// Error events: {operation}_error
'payment_processing_error', 'location_request_error'

// UI events: {widget}_{interaction}
'button_tapped', 'dialog_dismissed', 'screen_viewed'
```

#### Safe Parameter Conversion Patterns

```dart
class AnalyticsHelper {
  // Boolean conversion
  static String boolToString(bool value) => value.toString();
  
  // Nullable conversion
  static String nullableToString<T>(T? value, [String defaultValue = 'none']) {
    return value?.toString() ?? defaultValue;
  }
  
  // Enum conversion
  static String enumToString<T extends Enum>(T enumValue) => enumValue.name;
  
  // List length conversion
  static String lengthToString(List<dynamic> list) => list.length.toString();
}

// Usage in analytics calls
FirebaseAnalytics.instance.logEvent(
  name: 'feature_used',
  parameters: {
    'is_first_time': AnalyticsHelper.boolToString(isFirstTime),
    'user_type': AnalyticsHelper.nullableToString(userType),
    'status': AnalyticsHelper.enumToString(currentStatus),
    'item_count': AnalyticsHelper.lengthToString(items),
  },
);
```

#### Integration with Error Logging

Combine Analytics with other logging systems for comprehensive tracking:

```dart
class TripService extends GetxController {
  final Logger _logger = Logger('TripService');
  
  Future<void> completeTrip(String tripId) async {
    try {
      _logger.info('Completing trip: $tripId');
      
      await _api.completeTrip(tripId);
      
      // Log success to both systems
      _logger.info('Trip completed successfully: $tripId');
      FirebaseAnalytics.instance.logEvent(
        name: 'trip_completed',
        parameters: {
          'trip_id': tripId,
          'completion_time': DateTime.now().toIso8601String(),
          'success': 'true',
        },
      );
      
    } catch (e, stackTrace) {
      _logger.severe('Trip completion failed: $e', e, stackTrace);
      
      // Log error to Crashlytics
      FirebaseCrashlytics.instance.recordError(e, stackTrace, fatal: false);
      
      // Log error event to Analytics  
      FirebaseAnalytics.instance.logEvent(
        name: 'trip_completion_error',
        parameters: {
          'trip_id': tripId,
          'error_type': e.runtimeType.toString(),
          'error_message': e.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
      
      rethrow;
    }
  }
}
```

#### Performance Tracking

Track performance metrics with proper parameter types:

```dart
// Track operation duration
final stopwatch = Stopwatch()..start();
try {
  await performExpensiveOperation();
  
  stopwatch.stop();
  FirebaseAnalytics.instance.logEvent(
    name: 'operation_performance',
    parameters: {
      'operation': 'route_calculation',
      'duration_ms': stopwatch.elapsedMilliseconds.toString(),
      'success': 'true',
    },
  );
} catch (e) {
  stopwatch.stop();
  FirebaseAnalytics.instance.logEvent(
    name: 'operation_performance', 
    parameters: {
      'operation': 'route_calculation',
      'duration_ms': stopwatch.elapsedMilliseconds.toString(),
      'success': 'false',
      'error': e.toString(),
    },
  );
  rethrow;
}
```

#### User Journey Tracking

Track complete user flows with consistent event naming:

```dart
class TripBookingFlow {
  static void trackBookingStarted(String userType) {
    FirebaseAnalytics.instance.logEvent(
      name: 'booking_flow_started',
      parameters: {
        'user_type': userType,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }
  
  static void trackLocationSet(String locationType, String method) {
    FirebaseAnalytics.instance.logEvent(
      name: 'booking_location_set',
      parameters: {
        'location_type': locationType, // 'pickup' or 'destination'
        'input_method': method,        // 'search', 'map_tap', 'autocomplete'
        'step': '2',
      },
    );
  }
  
  static void trackDriverSelected(String driverId, String selectionMethod) {
    FirebaseAnalytics.instance.logEvent(
      name: 'booking_driver_selected',
      parameters: {
        'driver_id': driverId,
        'selection_method': selectionMethod, // 'auto', 'manual', 'list'
        'step': '3',
      },
    );
  }
  
  static void trackBookingCompleted(String tripId, double totalCost) {
    FirebaseAnalytics.instance.logEvent(
      name: 'booking_flow_completed',
      parameters: {
        'trip_id': tripId,
        'total_cost': totalCost.toStringAsFixed(2),
        'step': 'final',
      },
    );
  }
}

## Logging Levels and When to Use

### SEVERE (Logger.severe)
- **When**: Critical errors that affect user experience
- **Examples**: Authentication failures, payment processing errors, data corruption
- **Auto-forwarded**: To Crashlytics

```dart
_logger.severe('Payment processing failed for trip $tripId', error, stackTrace);
```

### WARNING (Logger.warning)
- **When**: Recoverable errors, deprecated usage, performance issues
- **Examples**: Network timeouts with retry, missing optional data
- **Auto-forwarded**: To Crashlytics

```dart
_logger.warning('Network request failed, retrying in ${delay.inSeconds}s');
```

### INFO (Logger.info)
- **When**: Important state changes, successful operations
- **Examples**: User login, trip completion, service state changes

```dart
_logger.info('🔧 Trip ${tripId} state changed from $oldState to $newState');
```

### FINE (Logger.fine)
- **When**: Detailed operation flow, debugging information
- **Examples**: Method entry/exit, intermediate calculations

```dart
_logger.fine('Calculating route with ${waypoints.length} waypoints');
```

## Error Handling Patterns

### 1. Service Layer Error Handling

```dart
class TripService extends GetxService {
  final Logger _logger = Logger('TripService');
  
  Future<bool> createTrip(TripData data) async {
    try {
      _logger.info('Creating trip: ${data.id}');
      
      final result = await _api.createTrip(data);
      
      _logger.info('Trip created successfully: ${data.id}');
      return true;
      
    } on ApiException catch (e, stackTrace) {
      _logger.severe('API error creating trip: ${e.message}', e, stackTrace);
      
      // Show user-friendly error
      Get.snackbar(
        'Error',
        'Failed to create trip: ${e.userMessage}',
        snackPosition: SnackPosition.TOP,
      );
      
      return false;
      
    } catch (e, stackTrace) {
      _logger.severe('Unexpected error creating trip: $e', e, stackTrace);
      
      // Report to Crashlytics
      FirebaseCrashlytics.instance.recordError(e, stackTrace, fatal: false);
      
      Get.snackbar(
        'Error',
        'An unexpected error occurred. Please try again.',
        snackPosition: SnackPosition.TOP,
      );
      
      return false;
    }
  }
}
```

### 2. UI Error Handling

```dart
class TripScreen extends StatefulWidget {
  @override
  _TripScreenState createState() => _TripScreenState();
}

class _TripScreenState extends State<TripScreen> {
  final Logger _logger = Logger('TripScreen');
  
  Future<void> _handleTripAction() async {
    if (!mounted) return;
    
    try {
      setState(() => _isLoading = true);
      
      await tripService.performAction();
      
      if (mounted) {
        setState(() => _isLoading = false);
        _logger.info('Trip action completed successfully');
      }
      
    } catch (e, stackTrace) {
      _logger.severe('Trip action failed: $e', e, stackTrace);
      
      if (mounted) {
        setState(() => _isLoading = false);
        
        // Show error to user - never hide real errors
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Action failed: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 8),
          ),
        );
      }
    }
  }
}
```

## Best Practices

### ✅ DO

1. **Use appropriate log levels**: SEVERE for critical errors, INFO for important events
2. **Include context**: User ID, trip ID, relevant parameters
3. **Log both success and failure**: Track operation outcomes
4. **Use structured data**: Include timestamps, user context, operation metadata
5. **Check mounted state**: Always verify widget is mounted before setState
6. **Show real errors**: Never hide actual runtime errors behind generic messages
7. **Convert Analytics parameters**: Always convert booleans, integers, and objects to strings
8. **Follow naming conventions**: Use consistent event and parameter naming patterns
9. **Track user journeys**: Log complete flows, not just individual actions
10. **Validate parameter limits**: Keep names under 40 chars, values under 100 chars

```dart
// Good: Structured logging with context
_logger.info('Driver status changed', {
  'driverId': driverId,
  'oldStatus': oldStatus,
  'newStatus': newStatus,
  'tenantId': tenantId,
  'timestamp': DateTime.now().toIso8601String()
});

// Good: Safe Analytics event
FirebaseAnalytics.instance.logEvent(
  name: 'driver_status_changed',
  parameters: {
    'driver_id': driverId,
    'old_status': oldStatus.toString(),
    'new_status': newStatus.toString(),
    'tenant_id': tenantId,
    'timestamp': DateTime.now().toIso8601String(),
  },
);
```

### ❌ DON'T

1. **Log sensitive data**: Never log passwords, tokens, personal data
2. **Overuse SEVERE level**: Reserve for actual critical errors
3. **Hide real errors**: Don't catch exceptions and show generic messages
4. **Log in production loops**: Avoid excessive logging in frequently called methods
5. **Forget error context**: Always include relevant debugging information
6. **Pass raw booleans/objects to Analytics**: Will cause runtime crashes
7. **Use reserved prefixes**: Avoid "firebase_", "google_", "ga_" in parameter names
8. **Exceed character limits**: Keep event names under 40 chars, parameters under 100
9. **Log PII in Analytics**: Never include personal identifiable information
10. **Ignore Analytics errors**: Always handle Analytics failures gracefully

```dart
// Bad: Hiding real error
try {
  await dangerousOperation();
} catch (e) {
  // ❌ Don't do this - hides actual error
  throw Exception('Something went wrong');
}

// Good: Surface real error with context
try {
  await dangerousOperation();
} catch (e, stackTrace) {
  _logger.severe('Dangerous operation failed: $e', e, stackTrace);
  throw Exception('Dangerous operation failed: ${e.toString()}');
}

// ❌ Bad: Analytics parameter types
FirebaseAnalytics.instance.logEvent(
  name: 'user_action',
  parameters: {
    'is_premium': user.isPremium,           // ❌ Boolean will crash
    'user_object': user,                    // ❌ Object will crash
    'firebase_custom_param': 'value',       // ❌ Reserved prefix
  },
);

// ✅ Good: Safe Analytics parameters
FirebaseAnalytics.instance.logEvent(
  name: 'user_action',
  parameters: {
    'is_premium': user.isPremium.toString(), // ✅ Boolean -> String
    'user_type': user.type.toString(),       // ✅ Extract safe property
    'custom_param': 'value',                 // ✅ No reserved prefix
  },
);
```

## Environment-Specific Configuration

### Debug Mode
- **Crashlytics collection**: Force enabled
- **Log level**: All levels shown
- **Console output**: Detailed formatting

### Release Mode
- **Crashlytics collection**: Automatic
- **Log level**: INFO and above
- **Console output**: Minimal

## Monitoring and Alerts

### Firebase Crashlytics Dashboard
- Monitor crash-free users percentage
- Track top crashes and errors
- Analyze user impact and trends

### Firebase Analytics Dashboard
- **Events**: Monitor custom events and conversion funnels
- **User Properties**: Track user segments and behavior patterns
- **Audience**: Analyze user demographics and engagement
- **Realtime**: View live user activity and current events
- **Funnels**: Track conversion rates through user flows
- **Cohorts**: Analyze user retention and lifetime value

### Custom Metrics via AgentLoggingService
- User journey tracking
- Performance bottlenecks
- Feature usage analytics

### Analytics Debugging

Enable Analytics debugging for development:

```dart
// In debug mode, enable Analytics debugging
if (kDebugMode) {
  FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
  // Use Firebase DebugView to see events in real-time
}
```

**Debugging Tools**:
- **DebugView**: Real-time event monitoring in Firebase console
- **Analytics Verification**: Verify events are being sent correctly
- **Parameter Validation**: Check parameter names and values comply with limits

## Common Patterns

### Network Request Logging
```dart
Future<T> makeRequest<T>(String endpoint) async {
  _logger.fine('Making request to $endpoint');
  
  try {
    final response = await http.get(Uri.parse(endpoint));
    _logger.info('Request successful: $endpoint (${response.statusCode})');
    return parseResponse<T>(response);
  } catch (e, stackTrace) {
    _logger.severe('Request failed: $endpoint - $e', e, stackTrace);
    rethrow;
  }
}
```

### State Change Logging
```dart
void updateTripStatus(TripStatus newStatus) {
  final oldStatus = _tripStatus.value;
  
  _logger.info('Trip status changing: $oldStatus -> $newStatus');
  
  _tripStatus.value = newStatus;
  
  // Log the change event
  agentLogging.logAgentEvent('trip.status_changed', data: {
    'tripId': tripId,
    'oldStatus': oldStatus.toString(),
    'newStatus': newStatus.toString(),
    'userId': currentUser.uid,
    'timestamp': DateTime.now().toIso8601String()
  });
}
```

This logging system provides comprehensive observability for debugging issues, monitoring app health, and understanding user behavior patterns. 