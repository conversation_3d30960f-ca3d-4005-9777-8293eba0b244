# Admin Panel Logging Guide (SvelteKit)

## Overview

The admin SvelteKit panel uses toast-based user feedback, console logging, and Firebase integration for error tracking and monitoring. The focus is on user experience with graceful error handling and real-time feedback.

## Core Components

### 1. Toast Notifications (svelte-sonner)

Primary user feedback mechanism for operations and errors:

```typescript
import { toast } from 'svelte-sonner';

// Success notifications
toast.success("Operation completed successfully");

// Error notifications  
toast.error("Failed to update status");

// Info notifications
toast.info("No changes detected");

// Warning notifications
toast.warning("Connection issues detected");
```

### 2. Console Logging

Structured console logging for development and debugging:

```typescript
// Development logging patterns
console.log('📝 Document changed, updating admin notes:', document.id);
console.error('Error updating document status:', error);
console.warn('Error accessing localStorage:', error);
console.info('🖼️ Image cache service initialized');
```

### 3. Execution Log Tracking

For monitoring scheduled functions and system operations:

```typescript
interface ExecutionLog {
  id: string;
  timestamp: string;
  success: boolean;
  executionTime: number;
  stats: {
    totalActiveTripCount: number;
    tenantsProcessed: number;
    tripsProcessed: number;
    errors: number;
    executionCount: number;
    checkedReservations: boolean;
    monitoredNotifications: boolean;
    driverTimeoutsChecked: boolean;
  };
  logs: string[];
  error?: string;
}
```

## Error Handling Patterns

### 1. Firebase Function Call Error Handling

```typescript
// In stores or components
try {
  await callAdminFunction('functionName', {
    tenantId: tenantStore.currentId,
    data: payload
  });
  
  toast.success('Operation completed successfully');
  
} catch (error) {
  console.error('Function call failed:', error);
  
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  toast.error(`Operation failed: ${errorMessage}`);
  
  // For critical operations, also log structured data
  const log: ExecutionLog = {
    id: `error-${Date.now()}`,
    timestamp: new Date().toISOString(),
    success: false,
    executionTime: Date.now() - startTime,
    logs: [`[ERROR] ${errorMessage}`],
    error: errorMessage,
    stats: { /* ... */ }
  };
}
```

### 2. Data Loading Error Handling

```typescript
// Firestore listener error handling
export function init() {
  try {
    const collectionRef = collection(fdb, tenantStore.getTenantPath('collection'));
    
    unsubscribe = onSnapshot(collectionRef, (snapshot) => {
      // Process data successfully
      _data = snapshot.docs.map(doc => processDocument(doc));
    }, (error) => {
      console.error('Error listening to collection:', error);
      toast.error('Failed to load data. Please refresh the page.');
    });
    
  } catch (error) {
    console.error('Error initializing collection listener:', error);
  }
}
```

### 3. Form Submission Error Handling

```typescript
async function handleSubmit() {
  if (isSubmitting) return;
  
  isSubmitting = true;
  
  try {
    await submitData(formData);
    
    toast.success('Data saved successfully');
    
    // Reset form or navigate
    formData = getDefaultFormData();
    
  } catch (error) {
    console.error('Form submission failed:', error);
    
    // Show specific error message
    const message = getErrorMessage(error);
    toast.error(`Save failed: ${message}`);
    
  } finally {
    isSubmitting = false;
  }
}

function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    // Parse Firebase errors for user-friendly messages
    if (error.message.includes('permission-denied')) {
      return 'You do not have permission to perform this action';
    }
    if (error.message.includes('not-found')) {
      return 'The requested resource was not found';
    }
    return error.message;
  }
  return 'An unexpected error occurred';
}
```

## Logging Best Practices

### ✅ DO

1. **Use appropriate toast types**: Success for completions, error for failures
2. **Log to console**: Include context and structured data
3. **Handle specific error types**: Provide meaningful error messages
4. **Track operation timing**: Include execution times for performance monitoring
5. **Use emoji prefixes**: Enhance console log readability (📝, 🖼️, ❌, ✅)

```typescript
// Good: Comprehensive error handling with user feedback
async function updateDocument(id: string, data: any) {
  const startTime = Date.now();
  
  try {
    console.log('📝 Updating document:', id);
    
    await updateDoc(doc(fdb, 'documents', id), data);
    
    const duration = Date.now() - startTime;
    console.log(`✅ Document updated successfully in ${duration}ms:`, id);
    
    toast.success('Document updated successfully');
    
  } catch (error) {
    console.error('❌ Document update failed:', error);
    toast.error(`Update failed: ${getErrorMessage(error)}`);
  }
}
```

### ❌ DON'T

1. **Silent failures**: Always provide user feedback for operations
2. **Generic error messages**: Avoid "Something went wrong" without specifics
3. **Excessive toasts**: Don't spam users with notifications
4. **Missing error context**: Include relevant IDs and operation details
5. **Blocking UI**: Always reset loading states in finally blocks

```typescript
// Bad: Silent failure
try {
  await riskyOperation();
} catch (error) {
  // ❌ User has no idea what happened
  console.error(error);
}

// Good: User feedback with context
try {
  await riskyOperation();
  toast.success('Operation completed');
} catch (error) {
  console.error('Risky operation failed:', error);
  toast.error('Operation failed. Please try again.');
}
```

## Environment-Specific Patterns

### Development Mode
- **Detailed console logging**: Full error objects and stack traces
- **Firebase emulator logging**: Connection status and emulator usage
- **Hot reload handling**: Graceful reconnection on development restarts

```typescript
// Development Firebase connection logging
if (import.meta.env.DEV && shouldUseEmulator()) {
  try {
    connectFirestoreEmulator(fdb, host, 8080);
    console.log('🔧 Connected to Firebase emulators');
  } catch (emulatorError) {
    console.warn('Error connecting to emulators:', emulatorError);
  }
}
```

### Production Mode
- **Minimal console output**: Essential errors only
- **User-focused messages**: Clear, actionable error descriptions
- **Performance monitoring**: Track operation timing and success rates

## Monitoring Patterns

### 1. System Health Monitoring

```typescript
// Clock drift detection with user notifications
const checkClockDrift = () => {
  try {
    const db = getDatabase();
    const offsetRef = ref(db, ".info/serverTimeOffset");

    onValue(offsetRef, (snapshot) => {
      const drift = Math.abs(snapshot.val() || 0);

      if (drift > 15000) {
        toast.error("System clock drift detected", {
          description: "Please check your system time",
          duration: 60000
        });
      }
    });
  } catch (error) {
    console.error("Error setting up clock drift detection:", error);
  }
};
```

### 2. Scheduled Function Monitoring

```typescript
class ScheduledFunctionsMonitor {
  private async executeFunction() {
    const startTime = Date.now();
    
    try {
      const result = await this.testCalculateTripCosts();
      
      const log: ExecutionLog = {
        id: `log-${Date.now()}`,
        timestamp: result.data.timestamp,
        success: result.data.success,
        executionTime: result.data.executionTime,
        stats: result.data.stats,
        logs: result.data.logs,
        error: result.data.error,
      };

      this.state.executionLogs = [log, ...this.state.executionLogs.slice(0, 24)];

      if (!result.data.success) {
        toast.error(`Function execution failed: ${result.data.error}`);
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      const log: ExecutionLog = {
        id: `log-${Date.now()}`,
        timestamp: new Date().toISOString(),
        success: false,
        executionTime: Date.now() - startTime,
        logs: [`[ERROR] Failed to execute function: ${errorMessage}`],
        error: errorMessage,
        stats: getDefaultStats()
      };

      this.state.executionLogs = [log, ...this.state.executionLogs.slice(0, 24)];
      toast.error(`Failed to execute function: ${errorMessage}`);
    }
  }
}
```

## Common Patterns

### 1. Data Synchronization with Error Recovery

```typescript
// Real-time data sync with graceful error handling
export function initializeDataSync() {
  if (!fdb) {
    console.error('Firebase not initialized');
    toast.error('Database connection failed');
    return;
  }

  const collectionRef = collection(fdb, tenantStore.getTenantPath('data'));
  
  unsubscribe = onSnapshot(collectionRef, 
    (snapshot) => {
      try {
        _data = snapshot.docs.map(doc => processDocument(doc));
        console.log(`📊 Loaded ${_data.length} items`);
      } catch (error) {
        console.error('Error processing data:', error);
        toast.error('Data processing failed');
      }
    },
    (error) => {
      console.error('Data sync error:', error);
      toast.error('Connection lost. Attempting to reconnect...');
      
      // Attempt to reconnect after delay
      setTimeout(() => initializeDataSync(), 5000);
    }
  );
}
```

### 2. Tenant-Aware Operations

```typescript
async function performTenantOperation(operation: string, data: any) {
  try {
    // Validate tenant access
    if (!tenantStore.validateCurrentTenantAccess()) {
      throw new Error('You do not have access to the current tenant');
    }

    console.log(`🏢 Performing ${operation} for tenant:`, tenantStore.currentId);
    
    const result = await callAdminFunction(operation, {
      tenantId: tenantStore.currentId,
      ...data
    });

    console.log(`✅ ${operation} completed successfully`);
    toast.success(`${operation} completed successfully`);
    
    return result;
    
  } catch (error) {
    console.error(`❌ ${operation} failed:`, error);
    toast.error(`${operation} failed: ${getErrorMessage(error)}`);
    throw error;
  }
}
```

### 3. Image Loading with Cache Management

```typescript
async function initializeImageCache() {
  try {
    await imageCacheService.initialize();
    console.log('🖼️ Image cache service initialized');
  } catch (error) {
    console.error('❌ Failed to initialize image cache service:', error);
    toast.warning('Image caching unavailable');
  }
}
```

## Performance Considerations

### 1. Toast Management
- **Avoid spam**: Use toast IDs to prevent duplicate notifications
- **Appropriate duration**: Short for success, longer for errors
- **Dismissible**: Allow users to dismiss notifications

### 2. Logging Optimization
- **Structured data**: Use consistent log formats for easier parsing
- **Conditional logging**: Reduce production console output
- **Async operations**: Don't block UI with logging operations

This logging approach ensures good user experience while providing developers with the information needed to debug issues and monitor system health. 