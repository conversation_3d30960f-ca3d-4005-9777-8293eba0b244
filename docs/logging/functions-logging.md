# Firebase Functions Logging Guide

## Overview

Firebase Functions use structured logging with environment-aware behavior, comprehensive error tracking, and audit trail maintenance. The logging system provides both operational monitoring and business intelligence through structured data storage.

## Core Components

### 1. Firebase Functions Logger

Native Firebase Functions v2 logger with structured data support:

```typescript
import { logger } from "firebase-functions/v2";

// Info level logging
logger.info('Trip state transition', {
  tripId,
  previousState,
  newState,
  userId,
  success: true
});

// Error level logging
logger.error('Error processing trip', {
  tripId,
  error: error.message,
  stack: error.stack
});

// Warning level logging
logger.warn(`Trip ${tripId} not found during timeout processing`);
```

### 2. Environment-Aware Logging

Production vs development logging behavior:

```typescript
import { isProduction } from "./environment";

export async function sendPushNotification(
  message: admin.messaging.Message,
  context?: string
): Promise<void> {
  if (isProduction()) {
    // Actually send in production
    await admin.messaging().send(message);
    logger.info(`📱 Push notification sent successfully`, {
      context,
      to: 'token' in message ? 'single_device' : 'topic',
      title: message.notification?.title
    });
  } else {
    // Log detailed info in development
    logger.info(`🔔 [DEV MODE] Push notification would have been sent:`, {
      context,
      environment: 'development',
      title: message.notification?.title,
      body: message.notification?.body,
      data: message.data,
      token: token ? `${token.substring(0, 20)}...` : 'N/A'
    });
  }
}
```

### 3. Audit Trail Logging

Comprehensive state transition and event logging to Firestore:

```typescript
export async function logStateTransition(
  tripId: string,
  previousState: TripStatus,
  newState: TripStatus,
  event: TripEvent,
  userId: string,
  userRole: 'passenger' | 'driver' | 'admin',
  tenantId: string,
  transaction: Transaction,
  error?: Error
): Promise<void> {
  const transitionLog: StateTransitionLog = {
    transitionId: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    previousState,
    newState,
    event: normalizeDataForFirestore(event, 'logStateTransition:event'),
    userId,
    userRole,
    timestamp: FieldValue.serverTimestamp(),
    success: !error,
    metadata: {
      eventType: event.type,
      userAgent: 'firebase-functions',
      functionVersion: process.env.K_REVISION || 'unknown'
    }
  };

  // Add error details if present
  if (error) {
    transitionLog.errorMessage = error.message;
    transitionLog.errorStack = error.stack;
  }

  // Store in Firestore subcollection
  const logRef = db.doc(`tenants/${tenantId}/trips/${tripId}/state_transitions/${transitionLog.transitionId}`);
  transaction.set(logRef, transitionLog);

  // Log to Firebase Functions logger for monitoring
  const logLevel = error ? 'error' : 'info';
  logger[logLevel]('Trip state transition', {
    tripId,
    transitionId: transitionLog.transitionId,
    previousState,
    newState,
    event: event.type,
    userId,
    userRole,
    success: transitionLog.success,
    error: error?.message
  });
}
```

## Logging Patterns by Function Type

### 1. HTTP Callable Functions

```typescript
export const myCallableFunction = onCall({
  region: "europe-west3",
  timeoutSeconds: 5,
  memory: "512MiB",
}, async (request) => {
  // Authentication check with logging
  if (!request.auth) {
    logger.warn('Unauthenticated request to myCallableFunction');
    throw new HttpsError("unauthenticated", "Authentication is required.");
  }

  const { tripId, tenantId } = request.data;
  const userId = request.auth.uid;

  logger.info('🚗 Function execution started', {
    functionName: 'myCallableFunction',
    userId,
    tripId,
    tenantId
  });

  try {
    // Function logic here
    const result = await performOperation(tripId, tenantId);
    
    logger.info('✅ Function execution completed', {
      functionName: 'myCallableFunction',
      userId,
      tripId,
      success: true,
      result: result.summary
    });
    
    return { success: true, data: result };
    
  } catch (error) {
    logger.error('❌ Function execution failed', {
      functionName: 'myCallableFunction',
      userId,
      tripId,
      error: error.message,
      stack: error.stack
    });
    
    throw new HttpsError("internal", "Internal server error", { 
      originalError: error.message 
    });
  }
});
```

### 2. Firestore Triggers

```typescript
export const onTripUpdated = onDocumentWritten({
  document: "/tenants/{tenantId}/trips/{tripId}",
  region: "europe-west3",
}, async (event) => {
  const { tenantId, tripId } = event.params;
  
  if (!event.data?.after.exists) {
    logger.info('Trip deleted, cleaning up', { tripId, tenantId });
    return;
  }

  const tripBefore = event.data?.before.data();
  const tripAfter = event.data?.after.data();

  // Log significant state changes
  if (tripBefore?.status !== tripAfter?.status) {
    logger.info('📍 Trip status changed via trigger', {
      tripId,
      tenantId,
      oldStatus: tripBefore?.status,
      newStatus: tripAfter?.status,
      trigger: 'firestore_document_written'
    });
  }

  try {
    await handleTripStatusChange(tripBefore, tripAfter, tripId, tenantId);
    
    logger.info('✅ Trip trigger processing completed', {
      tripId,
      tenantId,
      operation: 'status_change_handling'
    });
    
  } catch (error) {
    logger.error('❌ Trip trigger processing failed', {
      tripId,
      tenantId,
      error: error.message,
      stack: error.stack
    });
    
    // Don't rethrow - let other triggers proceed
  }
});
```

### 3. Scheduled Functions

```typescript
export const calculateTripCosts = onSchedule({
  schedule: "every 1 minutes",
  region: "europe-west3",
  timeoutSeconds: 540,
  memory: '512MiB'
}, async () => {
  logger.info('⏰ Starting scheduled trip cost calculation');

  try {
    // Get all active tenants
    const tenants = await getAllActiveTenants();
    let totalProcessed = 0;
    let totalErrors = 0;

    for (const tenantId of tenants) {
      try {
        const processed = await processTenantTrips(tenantId);
        totalProcessed += processed;
        
        logger.info('✅ Tenant processing completed', {
          tenantId,
          tripsProcessed: processed
        });
        
      } catch (error) {
        totalErrors++;
        logger.error('❌ Tenant processing failed', {
          tenantId,
          error: error.message
        });
      }
    }

    logger.info('⏰ Scheduled function completed', {
      tenantsProcessed: tenants.length,
      totalTripsProcessed: totalProcessed,
      totalErrors,
      success: totalErrors === 0
    });

  } catch (error) {
    logger.error('❌ Scheduled function failed', {
      error: error.message,
      stack: error.stack
    });
  }
});
```

## Error Handling and Logging

### 1. Transaction Error Handling

```typescript
async function performTransactionalOperation(tripId: string, tenantId: string) {
  return await db.runTransaction(async (transaction) => {
    try {
      // Read phase
      const tripRef = getTenantCollection(tenantId, 'trips').doc(tripId);
      const tripDoc = await transaction.get(tripRef);
      
      if (!tripDoc.exists) {
        logger.warn(`Trip ${tripId} not found in transaction`);
        throw new Error(`Trip ${tripId} not found`);
      }

      // Write phase
      const updateData = { status: 'updated', updatedAt: FieldValue.serverTimestamp() };
      transaction.update(tripRef, updateData);

      // Log the operation
      await logStateTransition(
        tripId, 
        tripDoc.data()!.status, 
        'updated',
        event,
        userId,
        userRole,
        tenantId,
        transaction
      );

      logger.info('✅ Transaction completed successfully', {
        tripId,
        tenantId,
        operation: 'status_update'
      });

      return { success: true };

    } catch (error) {
      logger.error('❌ Transaction failed', {
        tripId,
        tenantId,
        error: error.message,
        operation: 'status_update'
      });
      
      // Log the failed transition
      await logStateTransition(
        tripId,
        'unknown',
        'error',
        event,
        userId,
        userRole,
        tenantId,
        transaction,
        error as Error
      );

      throw error;
    }
  });
}
```

### 2. External API Error Handling

```typescript
async function callExternalAPI(endpoint: string, data: any) {
  const operationId = `api_${Date.now()}`;
  
  logger.info('🌐 External API call started', {
    operationId,
    endpoint,
    method: 'POST'
  });

  try {
    const response = await axios.post(endpoint, data, {
      timeout: 10000,
      headers: { 'Content-Type': 'application/json' }
    });

    logger.info('✅ External API call successful', {
      operationId,
      endpoint,
      statusCode: response.status,
      duration: response.headers['x-response-time']
    });

    return response.data;

  } catch (error) {
    if (error.response) {
      // API responded with error status
      logger.error('❌ External API error response', {
        operationId,
        endpoint,
        statusCode: error.response.status,
        errorData: error.response.data,
        headers: error.response.headers
      });
    } else if (error.request) {
      // Network error
      logger.error('❌ External API network error', {
        operationId,
        endpoint,
        error: error.message,
        code: error.code
      });
    } else {
      // Other error
      logger.error('❌ External API unexpected error', {
        operationId,
        endpoint,
        error: error.message
      });
    }

    throw error;
  }
}
```

## Structured Event Logging

### 1. Business Event Logging

```typescript
async function logBusinessEvent(
  eventType: string,
  tenantId: string,
  userId: string,
  data: Record<string, any>
) {
  const eventLog = {
    eventType,
    tenantId,
    userId,
    timestamp: FieldValue.serverTimestamp(),
    timestampDT: new Date(),
    data: normalizeDataForFirestore(data, `logBusinessEvent:${eventType}`)
  };

  try {
    await getTenantCollection(tenantId, 'business_events').add(eventLog);
    
    logger.info('📊 Business event logged', {
      eventType,
      tenantId,
      userId,
      dataKeys: Object.keys(data)
    });
    
  } catch (error) {
    logger.error('❌ Failed to log business event', {
      eventType,
      tenantId,
      userId,
      error: error.message
    });
  }
}

// Usage examples
await logBusinessEvent('trip_completed', tenantId, driverId, {
  tripId,
  duration: trip.duration,
  distance: trip.distance,
  cost: trip.finalCost,
  rating: trip.rating
});

await logBusinessEvent('driver_status_changed', tenantId, driverId, {
  oldStatus: 'inactive',
  newStatus: 'active',
  reason: 'shift_start',
  location: driver.currentLocation
});
```

### 2. Performance Monitoring

```typescript
async function monitorPerformance<T>(
  operation: string,
  context: Record<string, any>,
  fn: () => Promise<T>
): Promise<T> {
  const startTime = Date.now();
  const operationId = `${operation}_${Date.now()}`;
  
  logger.info(`⏱️ Performance monitoring started: ${operation}`, {
    operationId,
    ...context
  });

  try {
    const result = await fn();
    const duration = Date.now() - startTime;

    logger.info(`✅ Performance monitoring completed: ${operation}`, {
      operationId,
      duration,
      success: true,
      ...context
    });

    // Log performance metrics if duration is significant
    if (duration > 1000) {
      await logPerformanceMetric(operation, duration, context);
    }

    return result;

  } catch (error) {
    const duration = Date.now() - startTime;
    
    logger.error(`❌ Performance monitoring failed: ${operation}`, {
      operationId,
      duration,
      error: error.message,
      ...context
    });

    throw error;
  }
}

// Usage
const result = await monitorPerformance(
  'trip_cost_calculation',
  { tripId, tenantId },
  () => calculateTripCost(tripId, tenantId)
);
```

## Best Practices

### ✅ DO

1. **Use emoji prefixes**: Enhance log readability (🚗, ✅, ❌, 📱, ⏰)
2. **Include context**: Always log relevant IDs, tenant, user, operation
3. **Structure data**: Use consistent object formats for easier querying
4. **Log both success and failure**: Track operation outcomes
5. **Use appropriate levels**: INFO for normal operations, ERROR for failures
6. **Monitor performance**: Track execution times for optimization

```typescript
// Good: Structured logging with context
logger.info('🚗 Driver request processed', {
  tripId,
  driverId,
  tenantId,
  requestDuration: Date.now() - requestStartTime,
  outcome: 'accepted',
  driverLocation: { lat: driver.lat, lng: driver.lng }
});
```

### ❌ DON'T

1. **Log sensitive data**: Never log passwords, tokens, personal information
2. **Use console.log**: Always use the Firebase logger
3. **Skip error context**: Include relevant debugging information
4. **Ignore performance**: Don't overlook slow operations
5. **Use generic messages**: Provide specific, actionable information

```typescript
// Bad: Generic logging without context
logger.error('Something went wrong');

// Good: Specific error with context
logger.error('❌ Driver availability check failed', {
  driverId,
  tenantId,
  error: error.message,
  operation: 'availability_validation',
  criteria: { capacity: requiredCapacity, location: searchArea }
});
```

## Environment Configuration

### Development Mode
- **Detailed logging**: Full error objects and stack traces
- **Mock operations**: Log instead of executing external calls
- **Debug information**: Include intermediate calculation steps

### Production Mode
- **Optimized logging**: Essential information only
- **Error aggregation**: Group similar errors for analysis
- **Performance focus**: Monitor and alert on slow operations

## Monitoring and Alerting

### 1. Firebase Functions Logs Console
- **Real-time monitoring**: View logs as functions execute
- **Error filtering**: Focus on error and warning levels
- **Performance insights**: Identify slow operations

### 2. Custom Metrics
- **Business metrics**: Trip completion rates, driver response times
- **Technical metrics**: Function execution times, error rates
- **Tenant metrics**: Per-tenant performance and usage

### 3. Alert Configuration
```typescript
// Log critical errors that should trigger alerts
if (errorCount > threshold) {
  logger.error('🚨 CRITICAL: High error rate detected', {
    errorCount,
    threshold,
    timeWindow: '5 minutes',
    affectedTenants: tenantIds,
    alertLevel: 'CRITICAL'
  });
}
```

This logging system provides comprehensive observability for Firebase Functions, enabling effective debugging, monitoring, and business intelligence gathering. 