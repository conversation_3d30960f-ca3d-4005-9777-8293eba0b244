## Fiaranow Trip Lifecycle Overview

The trip lifecycle involves interactions between the Passenger (Flutter app), the Driver (Flutter app), Firebase Functions (backend logic), and potentially the Admin (SvelteKit app).

**1. Trip Initiation (Passenger or Admin)**

*   **Passenger (Flutter App):**
    1.  **Address Input:** Passenger opens the app (`MapScreen.dart`), inputs a pickup (`startLocation`) and destination (`arrivalLocation`).
    2.  **Route Calculation:** The app calls `getMultipleRoutesData` (Firebase Function via `firebase_cloud_functions.dart`) which uses Google Maps API (`navigation.ts`) to get route options, distance, duration, and estimated cost (calculated using `TripConfigurationModel`).
    3.  **Route & Payment Selection:** Passenger selects a preferred route and payment method (`payment_utils.dart` might show a dialog).
    4.  **Ride Type:**
        *   **Ride Now:** Passenger wants a trip immediately.
        *   **Reserve:** Passenger schedules a trip for a future time/date (`RouteReservationSetup.dart` or `FullDayReservationSetup.dart`).
        *   **Full Day Reservation:** A special type of reservation with fixed pricing options.
    5.  **Trip Creation:**
        *   A new `Trip` document is created in Firestore (`models/Trip.dart` -> `Trip.createTrip` or `Trip.reserveTrip` or `Trip.reserveFullDayTrip`).
        *   Initial status: `preparing` (for Ride Now) or `reserved` (for reservations).
        *   Key fields set: `uidPassenger`, `startLocation`, `arrivalLocation`, `routeData`, `tripConfiguration`, `customerRequestedPaymentMethod`, `pickupTime` (for reservations), `reservationType`, `fullDayPriceType`.

*   **Admin (SvelteKit App - Potentially):**
    *   While not explicitly shown for *creation*, an admin might initiate a trip on behalf of a user, likely setting it to `preparing` or `reserved`.

**2. Trip Preparation (Firebase Functions)**

*   **`onTripMutations` (`index.ts` -> `navigation_handlers.ts`):**
    *   When a trip's status is `preparing`:
        *   `handlePreparingStatus`: If `routeData` is missing (e.g., admin created), it's calculated. If the trip was previously assigned to a driver and is now back in `preparing`, the previous driver is released (`occupiedByTripId = null`) and added to `skippedDriverIds`.

**3. Driver Search & Assignment**

*   **Passenger (Ride Now - Flutter App):**
    1.  After confirming addresses and route, if "Ride Now" is chosen, the app might show nearby available drivers (`DriversList.dart` in `MapScreen.dart`).
    2.  Passenger selects a driver.
    3.  The app calls `trip.requestDriver()` (in `models/Trip.dart`), which updates the Firestore `Trip` document:
        *   `uidChosenDriver` is set to the selected driver's UID.
        *   `status` changes to `requestingDriver`.
        *   `driverLocation` (current location of the chosen driver) is set.
        *   `driverNotificationSent` is set to `false`.

*   **Admin (SvelteKit App):**
    1.  Admin can manually assign a driver to a trip (`routes/rides/trips/[tripId]/assign-driver/+page.svelte`).
    2.  This calls either:
        *   `adminRequestDriver` (Firebase Function): Sets `uidChosenDriver`, `status` to `requestingDriver`, `driverLocation`, `driver` (info), `driverNotificationSent: false`.
        *   `adminAssignDriver` (Firebase Function): Sets `uidChosenDriver`, `status` to `driverApproaching`, `driver` (info), clears `skippedDriverIds`, and importantly, sets `occupiedByTripId` on the `mobile_users` document for the driver.

*   **Firebase Functions (`onTripMutations`):**
    *   When `status` is `requestingDriver`:
        *   `handleRequestingDriverStatus`:
            *   Calculates `driverRouteData` (route for driver to pickup).
            *   Checks if the chosen driver (`uidChosenDriver`) is active (`isServiceActive` in `mobile_users`).
            *   If driver is inactive, trip reverts to `preparing`, `uidChosenDriver` is cleared.
            *   If active and has `fcmToken`, sends a push notification (`sendDriverNotification` in `notifications.ts`).
            *   Sets `driverNotificationSent` to `true`.

**4. Driver Acceptance (Driver - Flutter App & Firebase Functions)**

1.  **Notification:** Driver receives a push notification about the new trip request (`fcm.dart` in Flutter, `notifications.ts` in Firebase).
2.  **Accept/Reject:** Driver app shows the request (`DriverTripRequestsList.dart`).
    *   **Accept:** Driver calls `trip.driverAcceptRequest()` (in `models/Trip.dart`), which calls the `driverAcceptRequest` Firebase Function.
        *   `driverAcceptRequest` (Firebase Function):
            *   Checks if driver is already occupied.
            *   Updates `Trip` status to `driverApproaching`.
            *   Updates `driver` info on the trip.
            *   Sets `occupiedByTripId` on the driver's `mobile_users` document to this `tripId`.
    *   **Reject:** Driver calls `trip.driverRejectRequest()` (in `models/Trip.dart`).
        *   Updates `Trip` status back to `preparing`, clears `uidChosenDriver`, `driverRouteData`, `driverLocation`.
        *   Logs a `DRIVER_TRIP_REJECTED` event (`EventLog.dart`).

*   **Firebase Functions (`onTripMutations`):**
    *   When `status` changes to `driverApproaching` (and wasn't `driverApproaching` before):
        *   `handleDriverApproachingStatus`:
            *   Ensures the driver (`uidChosenDriver`) is marked as `occupiedByTripId` with *this* trip.
            *   **Crucially:** Finds any *other* trips where this driver was `uidChosenDriver` and status was `requestingDriver`, and reverts those other trips to `preparing` (freeing the driver from other pending requests).
            *   Sends a notification to the passenger (`sendPassengerNotification` for 'approaching').

**5. Driver En Route to Passenger (Driver & Passenger - Flutter Apps)**

1.  **Driver:** App shows route to passenger (`DriverTripControl.dart`). Driver's location is periodically updated to Firestore (`NavigationState.dart` -> `_updateCurrentPositionInFirebase`).
2.  **Passenger:** App shows driver approaching on the map (`PassengerTripControl.dart`), updates ETA.
3.  **Driver Arrives:** Driver taps "I have arrived". Flutter app calls `trip.driverAwaitPassenger()` (in `models/Trip.dart`).
    *   Updates `Trip` status to `driverAwaiting`.
    *   Sets `driverAwaitingTime`.

*   **Firebase Functions (`onTripMutations`):**
    *   When `status` changes to `driverAwaiting` (and wasn't `driverAwaiting` before):
        *   `handleDriverAwaitingStatus`: Sends a notification to the passenger (`sendPassengerNotification` for 'awaiting').

**6. Trip Start (Driver & Passenger - Flutter Apps)**

1.  **Mutual Start:** Both passenger and driver need to confirm the trip start.
    *   **Driver:** Taps "Start Trip". Flutter app calls `trip.startTripByDriver()` (in `models/Trip.dart`).
        *   Sets `driverStartTime` on `Trip`.
        *   Status becomes `inProgress` (if coming from `driverAwaiting`).
    *   **Passenger:** Taps "Start Trip". Flutter app calls `trip.startTripByPassenger()` (in `models/Trip.dart`).
        *   Sets `passengerStartTime` on `Trip`.
        *   Status becomes `inProgress` (if coming from `driverAwaiting`).
    *   Either party can start first - the status transitions to `inProgress` when the first party starts.
    *   The second party can then also mark their start, which sets their respective timestamp.
    *   The trip is considered fully started when both `driverStartTime` and `passengerStartTime` are set.
    *   `ForegroundServiceHandler.dart` (Flutter) likely starts a foreground service to keep location tracking active.
    *   `WakelockPlus` (Flutter) is enabled to keep the screen on.

**7. Trip In Progress (Driver & Passenger - Flutter Apps)**

1.  **Driver:**
    *   Navigates towards the destination.
    *   App logs GPS coordinates (`trip.recordTripLog` in `models/Trip.dart`).
    *   App periodically updates `costTotal`, `costDistance`, `costDuration`, `distanceTotalMeters` on the `Trip` document based on progress (`_updateTripCost` in `NavigationState.dart`).
2.  **Passenger:** App shows "Enjoy the ride!" or similar.

**8. Trip Completion (Driver - Flutter App)**

1.  **Driver Arrives at Destination:** Driver taps "Complete Trip".
2.  Flutter app calls `trip.driverCompleteTrip()` (in `models/Trip.dart`):
    *   Sets `finalRouteData` based on logged GPS points.
    *   Updates `Trip` status to `completed`.
    *   Sets `completedAt`.
    *   Calculates final `costTotal` based on actual distance/duration and `TripConfigurationModel`.
    *   `WakelockPlus` is disabled.
    *   `ForegroundServiceHandler.dart` stops the service.

**9. Payment & Post-Trip**

*   **Payment (Passenger/Admin):**
    1.  Passenger might pay via app (if mobile money integrated) or cash.
    2.  Admin can mark trip as paid (`PaymentForm.svelte` in Admin UI).
    3.  A `Payment` document is created (`models/Payment.dart`).
    4.  The `Trip` document's `paymentId` field is updated.
    5.  The `Trip` status is updated to `paid`.

*   **Firebase Functions (`onTripMutations`):**
    *   When `status` is `paid`:
        *   `handlePaidStatus`: If `driverDismissed` is true on the trip, it clears `occupiedByTripId` for the driver.

*   **Dismissal (Passenger & Driver - Flutter Apps):**
    1.  After trip is cancelled or paid, the close button becomes active in PassengerTripControl/DriverTripControl.
    2.  Passenger calls `TripStateService.dismissTrip` with `userType: 'passenger'` via PassengerTripControl.
    3.  Driver calls `TripStateService.dismissTrip` with `userType: 'driver'` via DriverTripControl.
    4.  Sets `passengerDismissed` or `driverDismissed` to `true` on the `Trip` document independently.
    5.  **Dismissal removes trips from Firestore listeners** but they remain accessible via history for on-demand viewing.
    6.  **Dismiss states are independent** - passenger and driver manage their own visibility.
    7.  **There is no undismiss functionality** - once dismissed, trips are only viewable on-demand.

*   **Reserved Trip Activation:**
    1.  **Reserved trips (`status: reserved`) DO NOT appear in Firestore listeners** until they become active.
    2.  **Activation occurs through two admin actions:**
        *   **Request Driver:** `reserved` → `requestingDriver` (driver receives notification, can reject)
        *   **Assign Driver:** `reserved` → `driverApproaching` (driver cannot reject, must proceed)
    3.  **Once active, the Firestore listener automatically picks up the trip** and overrides any on-demand viewing.
    4.  **Passengers see reserved trips only when they become active** (driver notification sent or driver approaching).

**10. Trip Deletion & Cleanup**

*   **Admin (SvelteKit App) or System:**
    *   An admin might delete a trip from the UI.
    *   A trip document is deleted from Firestore.
*   **Firebase Functions:**
    *   `onTripDeleted` (in `trip_operations.ts` and `index.ts` via `handleTripDeletion` in `navigation_handlers.ts`):
        *   `handleTripDeletion`: If a `uidChosenDriver` was on the deleted trip, and that driver's `occupiedByTripId` matched this trip, it clears the driver's `occupiedByTripId`.
        *   The separate `onDocumentDeleted` trigger in `trip_operations.ts` (also exported as `onTripDeleted`) specifically deletes the `logs` subcollection for that trip. *There seems to be a duplication of `onTripDeleted` functionality here.*

**Special Flows:**

*   **Trip Display Modes (Flutter Apps):**
    *   **Firestore Listeners (Live/Active Trips):**
        *   Only display truly active trips: `driverApproaching`, `driverAwaiting`, `inProgress`, `completed`, `paid` (until dismissed)
        *   **Reserved trips DO NOT appear** until admin assigns a driver (status becomes active)
        *   Active trips automatically override any on-demand viewing
        *   Query: `where('passengerDismissed', '==', false)` with local filtering for active statuses
    *   **On-Demand Viewing (History/TripDetailsPage):**
        *   Accessed from HistoryScreen or ChatScreen
        *   Can display ANY trip regardless of status or dismiss state
        *   **Fully read-only mode** - no mutations or Firebase calls
        *   **NEVER modifies dismiss states** - viewing doesn't affect Firestore listener visibility
        *   **No Firebase functions called** - purely local display operation
        *   Controls are disabled except close button and map centering
        *   **Read-only mode should NOT be indicated to users** - it's an internal implementation detail
        *   **Only restores visual state** (map positions, routes) without setting as current trip
        *   **Does NOT use restoreStateFromTrip** which would attempt to set dismissed trips as current
        *   Directly calls `showTripOnMap()` without any state changes
        *   Works for all trip types: reserved, completed, cancelled, paid, dismissed
        *   For drivers: works regardless of service status when viewing from history
        *   For passengers: works regardless of active trips when viewing from history
        *   Close button simply returns to previous screen
        *   Used for trip reference and navigation visualization

*   **Trip Cancellation (Passenger/Admin/Driver):**
    *   **Passenger/Admin:** Calls `cancelTrip` (Firebase Function). Sets status to `cancelled`, `cancelledAt`, `uidCancelledBy`. If a driver was assigned, their `occupiedByTripId` is cleared.
    *   **Driver Rejects Request:** (See step 4) Trip status goes to `preparing`.
    *   **Admin Disconnects Driver:** Calls `disconnectDriverFromTrip` (Firebase Function).
        *   If trip was `inProgress`, `driverAwaiting`, `driverApproaching`, status becomes `cancelled`.
        *   Otherwise, status becomes `preparing`, `uidChosenDriver` is cleared.
        *   Driver's `occupiedByTripId` is cleared.
*   **Skipped Drivers:**
    *   If a driver rejects or times out, or if an admin resets a trip from `requestingDriver` back to `preparing` (via `handlePreparingStatus`), the driver's UID is added to `skippedDriverIds`.
    *   Admin can manually manage this list via `updateSkippedDrivers` (Firebase Function).
*   **Full Day Reservation:**
    *   Created via `Trip.reserveFullDayTrip`.
    *   `reservationType` is `fullDay`.
    *   `fullDayPriceType` determines if gas is included.
    *   `costPrepaid` is set based on price type.
    *   The trip flow is similar, but cost calculation during "in progress" might differ or be fixed.

---

## Edge Cases & Potential Bugs

This is a complex system, so there are many potential edge cases:

1.  **API Key Exposure (CRITICAL SECURITY):**
    *   The Google Maps API key (`AIzaSyC5gn-4e1bZvCv4MVkopVvZAS642Up2EK8`) is hardcoded in `firebase/functions/lib/navigation.js`.
    *   Another Google Maps API key (`AIzaSyCYv7VWSjBh8_XziLq9QqwrjEygnA9uNmE`) is in `fiaranow_flutter/ios/Runner/AppDelegate.swift`.
    *   The admin panel also has `PUBLIC_GOOGLE_MAPS_API_KEY=AIzaSyC5gn-4e1bZvCv4MVkopVvZAS642Up2EK8` in `.env`.
    *   **Risk:** These keys can be extracted and abused, leading to unexpected charges or service disruption. They **MUST** be secured (e.g., restricted by IP/bundle ID, stored in secure environment variables for backend, or Firebase App Check for client-side).

2.  **Concurrency and Race Conditions:**
    *   **Driver Assignment:** Two admins trying to assign a driver to the same trip simultaneously. The transaction in `adminAssignDriver` helps, but the UI might not reflect the conflict immediately.
    *   **Driver Acceptance vs. Cancellation:**
        *   Driver accepts (`driverAcceptRequest`) at the exact moment an admin cancels the trip (`cancelTrip`) or disconnects the driver (`disconnectDriverFromTrip`). Transactions are used, but the sequence of operations and notifications could lead to confusion.
        *   Passenger cancels trip just as driver accepts.
    *   **`onTripMutations` Trigger:** If Firestore triggers `onTripMutations` multiple times for the same logical state change (e.g., due to rapid field updates or retries), the handlers need to be perfectly idempotent. The checks like `if (event.data?.before.data()?.status === 'driverApproaching') return;` are good but need to be comprehensive for all state transitions they manage.
    *   **Multiple `onTripDeleted` Functions:** There are two distinct `onTripDeleted` functions defined (`trip_operations.ts` and `navigation_handlers.ts` via `index.ts`). While one focuses on logs and the other on driver status, this could be confusing or lead to one being missed if re-exports are not perfect. It's better to consolidate or clearly delineate responsibilities.

3.  **State Mismatches & Stale Data:**
    *   **`occupiedByTripId`:** A driver's `occupiedByTripId` might point to a trip that is already cancelled, completed, or deleted if a cleanup function fails. `disconnectDriverFromTrip` attempts to handle stale `occupiedByTripId` if no active trips are found.
    *   **Trip Status vs. Driver Status:** Trip is `requestingDriver` but `uidChosenDriver` is null or the driver is not `isServiceActive`. `handleRequestingDriverStatus` tries to catch this.
    *   **Flutter App State:** If Firestore listeners in the Flutter app fail or are delayed, the UI might show an outdated trip status or driver location.
    *   **`cleanupStaleDeviceIds` Cron:** Relies on `lastSeen`. If `lastSeen` isn't updated reliably (e.g., app crashes before update), device IDs might not be cleaned up.

4.  **Notifications:**
    *   **Token Issues:** FCM token for driver/passenger might be missing, invalid, or outdated. `handleRequestingDriverStatus` and `handleDriverApproachingStatus` log warnings but the trip flow might stall.
    *   **Delivery Failure:** Notifications are "fire and forget." If a critical notification (like a new trip request) isn't delivered or seen, the trip won't proceed.
    *   **Driver Service Disabled:** If a driver toggles `isServiceActive` off *after* being selected but *before* `handleRequestingDriverStatus` runs, the notification might still be sent, or the check might pass then fail.

5.  **Data Integrity & Calculation:**
    *   **`normalizeDataForFirestore`:** While it aims to prevent `undefined` values, complex nested objects or Firestore-specific types could be mishandled if not explicitly accounted for. The current implementation seems robust for `undefined` but should be tested with all data structures.
    *   **Cost Calculation (`TripConfigurationModel`, `Trip.computeCost`):**
        *   Floating point inaccuracies in cost calculations.
        *   Division by zero if `durationHours` or `distanceKm` is unexpectedly zero in a formula where it's a divisor (though current formulas seem to avoid this).
        *   Logic for `costPerExtraWaitChunk` relies on `driverAwaitingTime` and `driverStartTime`. If these timestamps are incorrect or missing, the cost will be wrong.
    *   **`skippedDriverIds`:** If this array becomes excessively large, it could impact document size and query performance (though unlikely with typical trip volumes).
    *   **Timestamp Reliance:** Heavy reliance on `FieldValue.serverTimestamp()` vs. client-side `DateTime.now()`. While server timestamps are more reliable, differences can affect logic that compares times if not handled carefully (e.g., `clientAlarmTime` vs. server-generated `pickupTime`). The admin panel's clock drift detection is a good mitigation for admin actions.

6.  **Error Handling & Timeouts:**
    *   **Firebase Function Timeouts:** Most functions have a 5s timeout. If Google Maps API calls within `createRouteData` are slow, functions could timeout.
    *   **External API Failures:** Google Maps API downtime or errors would break route calculation.
    *   **Transaction Failures:** Firestore transactions can fail due to contention. The code generally throws `HttpsError`, but client-side retry logic might be needed for a better UX.

7.  **Flutter App Specifics:**
    *   **Permissions:** Location or notification permissions revoked by the user mid-trip or while the driver is "on service." The app needs to handle this gracefully (`PermissionsState.dart`).
    *   **Background Location (Driver):** OS killing the app or restricting background location updates for the driver would stop location tracking and updates to the trip. `ForegroundServiceHandler.dart` aims to mitigate this.
    *   **State Management (GetX):** Complex interactions between `AuthState`, `NavigationState`, `AppState` could lead to inconsistent states if not managed carefully, especially with async operations and Firestore listeners.
    *   **Wakelock:** If `WakelockPlus.disable()` fails to run (e.g., app crash), the screen might stay on unnecessarily.

8.  **Admin UI Operations:**
    *   Admin actions (assigning driver, cancelling trip) might conflict with passenger/driver actions if not carefully managed with transactions and status checks.
    *   UI might not immediately reflect the outcome of an admin action if Firestore listeners are delayed.

9.  **`driver_operations.ts` - `disconnectDriverFromTrip`:**
    *   The query for trips assigned to the driver uses `status` 'in' a list. If a new "active" status is added in the future and not updated here, those trips might not be handled correctly.
    *   If `tripDocs.empty` but `driverData.occupiedByTripId` is set, it clears the driver's status. This is good, but what if the trip *does* exist but the query somehow missed it (e.g., indexing delay, though unlikely for direct field queries)?

10. **`navigation_handlers.ts` - `handlePreparingStatus`:**
    *   If `previousTripData.uidChosenDriver` exists, it updates `occupiedByTripId` on that driver. If that driver document doesn't exist for some reason, the update will fail silently within the handler (though Firestore might log an error).

11. **Device ID Management (`cleanupStaleDeviceIds` & `AuthState`):**
    *   The cron job cleans up `deviceId` for inactive users.
    *   `AuthState` in Flutter generates a `deviceId` if not found in `SharedPreferences`. If a user reinstalls the app, they might get a new `deviceId`.
    *   The "Account Already in Use" logic relies on comparing the stored `deviceId` in Firestore with the current app's `deviceId`. This is generally robust but could have issues if `deviceId` generation/storage on the client has inconsistencies.

12. **Full Day Reservation Logic:**
    *   Cost calculation for full-day trips with "gas excluded" relies on `costDistance`. Ensure this is accurately tracked and updated if the driver logs significant mileage.
    *   The UI flow and driver instructions for full-day trips need to be very clear, as the "destination" is effectively "all day availability."

This overview and list of edge cases should provide a solid foundation for understanding the trip system and areas to focus on for testing and refinement. The hardcoded API keys are the most immediate and critical concern.