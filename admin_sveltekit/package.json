{"name": "admin-sveltekit", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test": "vitest", "translations:remove-duplicates": "node messages/remove-duplicates.js", "translations:compile": "npx @inlang/paraglide-js compile --project ./project.inlang --outdir ./src/lib/paraglide", "translations:process": "npm run translations:remove-duplicates && npm run translations:compile"}, "devDependencies": {"@exodus/schemasafe": "^1.3.0", "@lucide/svelte": "^0.525.0", "@sinclair/typebox": "^0.34.38", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.25.1", "@sveltejs/vite-plugin-svelte": "^6.1.0", "@types/google.maps": "^3.58.1", "@types/node": "^24.0.15", "@typeschema/class-validator": "^0.3.0", "@vinejs/vine": "^3.0.1", "arktype": "^2.1.20", "autoprefixer": "^10.4.21", "bits-ui": "^2.8.11", "class-validator": "^0.14.2", "clsx": "^2.1.1", "formsnap": "^2.0.1", "joi": "^17.13.3", "lucide-svelte": "^0.525.0", "mode-watcher": "^1.1.0", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "svelte": "^5.36.13", "svelte-check": "^4.3.0", "sveltekit-superforms": "^2.27.1", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "@tailwindcss/postcss": "^4.1.11", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "valibot": "^1.1.0", "vite": "^7.0.5", "vitest": "^3.2.4", "yup": "^1.6.1", "zod": "^4.0.5"}, "dependencies": {"@inlang/paraglide-sveltekit": "^0.16.1", "algoliasearch": "^5.34.0", "date-fns": "^4.1.0", "devalue": "^5.1.0", "firebase": "^12.0.0", "svelte-sonner": "^1.0.5"}}