<script lang="ts">
  import { onMount } from "svelte";
  import { getTags, type DriverTag } from "$lib/stores/driver_tags.svelte";
  import DriverTagBadge from "./DriverTagBadge.svelte";

  interface Props {
    selectedTags: string[];
    onTagsChange: (tags: string[]) => void;
    placeholder?: string;
    disabled?: boolean;
    allowCreation?: boolean;
  }

  let {
    selectedTags = [],
    onTagsChange,
    placeholder = "Add tags...",
    disabled = false,
    allowCreation = true,
  }: Props = $props();

  // Explicitly reference to avoid unused variable warning
  $effect(() => {
    void allowCreation;
  });

  let availableTags = $state<DriverTag[]>([]);
  let showDropdown = $state(false);
  let inputValue = $state("");
  let wrapperElement = $state<HTMLDivElement>();

  $effect(() => {
    availableTags = getTags();
  });

  let filteredTags = $derived(
    availableTags.filter(
      (tag) =>
        tag.name.toLowerCase().includes(inputValue.toLowerCase()) &&
        !selectedTags.includes(tag.name),
    ),
  );

  function handleWrapperClick(event: MouseEvent) {
    if (disabled) return;
    const target = event.target as HTMLElement;
    // Only handle clicks on the wrapper itself or the input
    if (target.tagName === "INPUT" || target === wrapperElement) {
      showDropdown = true;
    }
  }

  function handleDocumentClick(event: MouseEvent) {
    if (wrapperElement && !wrapperElement.contains(event.target as Node)) {
      showDropdown = false;
    }
  }

  function addTag(tagName: string) {
    const trimmedTag = tagName.trim();
    if (!trimmedTag || selectedTags.includes(trimmedTag)) return;

    const newTags = [...selectedTags, trimmedTag];
    onTagsChange(newTags);
    inputValue = "";
  }

  function removeTag(tagName: string) {
    const newTags = selectedTags.filter((t) => t !== tagName);
    onTagsChange(newTags);
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === "Escape") {
      showDropdown = false;
      inputValue = "";
    } else if (event.key === "ArrowDown" && !showDropdown) {
      showDropdown = true;
    }
  }

  function handleWrapperKeyDown(event: KeyboardEvent) {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      showDropdown = !showDropdown;
    }
  }

  onMount(() => {
    document.addEventListener("click", handleDocumentClick);
    return () => document.removeEventListener("click", handleDocumentClick);
  });
</script>

<div
  bind:this={wrapperElement}
  class="relative w-full flex flex-wrap gap-2 p-3 bg-muted/30 rounded-lg transition-all duration-200 focus-within:bg-muted/50 focus-within:shadow-sm hover:bg-muted/40 {disabled
    ? 'opacity-50 cursor-not-allowed'
    : ''}"
  onclick={handleWrapperClick}
  onkeydown={handleWrapperKeyDown}
  role="presentation"
>
  {#each selectedTags as tagName}
    {@const tagData = availableTags.find((t) => t.name === tagName)}
    <DriverTagBadge
      tag={tagData || tagName}
      size="sm"
      removable={!disabled}
      onRemove={() => removeTag(tagName)}
    />
  {/each}

  <input
    bind:value={inputValue}
    type="text"
    {placeholder}
    {disabled}
    onkeydown={handleKeyDown}
    role="combobox"
    aria-label={placeholder}
    aria-autocomplete="list"
    aria-controls={showDropdown ? "tag-dropdown" : undefined}
    aria-expanded={showDropdown}
    aria-haspopup="listbox"
    class="flex-1 min-w-[150px] bg-transparent text-sm outline-none border-0 ring-0 focus:ring-0 focus:outline-none placeholder:text-muted-foreground"
  />

  {#if showDropdown && !disabled && availableTags.length > 0}
    <div
      id="tag-dropdown"
      class="absolute z-10 w-full mt-2 bg-popover rounded-lg shadow-lg border-0 backdrop-blur-sm max-h-60 overflow-auto top-full left-0"
      role="listbox"
      aria-label="Available tags"
    >
      {#if filteredTags.length > 0}
        {#each filteredTags as tag}
          <button
            type="button"
            onclick={() => addTag(tag.name)}
            class="w-full px-3 py-2 text-left hover:bg-accent/80 hover:text-accent-foreground flex items-center justify-between group transition-colors duration-150"
            role="option"
            aria-label="Select tag {tag.name}"
            aria-selected="false"
          >
            <DriverTagBadge {tag} size="sm" />
            {#if tag.usageCount > 0}
              <span
                class="text-xs text-muted-foreground group-hover:text-accent-foreground"
              >
                {tag.usageCount} driver{tag.usageCount !== 1 ? "s" : ""}
              </span>
            {/if}
          </button>
        {/each}
      {:else if inputValue}
        <div class="px-3 py-2 text-sm text-muted-foreground">
          No matching tags found
        </div>
      {/if}
    </div>
  {/if}
</div>
