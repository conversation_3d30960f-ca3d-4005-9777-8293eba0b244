<script lang="ts">
  import { imageCacheService } from "$lib/services/ImageCacheService";
  import { Button } from "$lib/components/ui/button";
  import * as Card from "$lib/components/ui/card";
  import { Badge } from "$lib/components/ui/badge";
  import { toast } from "svelte-sonner";
  import { Trash2, RefreshCw, Database } from "lucide-svelte";

  let cacheStatus = $state<{ size: number; count: number } | null>(null);
  let isLoading = $state(false);
  let isClearing = $state(false);

  async function loadCacheStatus() {
    isLoading = true;
    try {
      cacheStatus = await imageCacheService.getCacheStatus();
    } catch (error) {
      console.error("Failed to load cache status:", error);
      toast.error("Failed to load cache status");
    } finally {
      isLoading = false;
    }
  }

  async function clearCache() {
    isClearing = true;
    try {
      const success = await imageCacheService.clearImageCache();
      if (success) {
        toast.success("Image cache cleared successfully");
        await loadCacheStatus(); // Refresh status
      } else {
        toast.error("Failed to clear image cache");
      }
    } catch (error) {
      console.error("Failed to clear cache:", error);
      toast.error("Failed to clear image cache");
    } finally {
      isClearing = false;
    }
  }

  // Load cache status on component mount
  $effect(() => {
    if (imageCacheService.isInitialized()) {
      loadCacheStatus();
    }
  });
</script>

<Card.Root>
  <Card.Header>
    <Card.Title class="flex items-center gap-2">
      <Database class="h-5 w-5" />
      Image Cache Management
    </Card.Title>
    <Card.Description>
      Manage the aggressive image caching system that caches images for 1 week
    </Card.Description>
  </Card.Header>
  <Card.Content class="space-y-4">
    {#if !imageCacheService.isInitialized()}
      <div class="text-center py-4">
        <p class="text-muted-foreground">Image cache service not initialized</p>
      </div>
    {:else}
      <div class="flex items-center justify-between">
        <div class="space-y-1">
          <p class="text-sm font-medium">Cache Status</p>
          {#if isLoading}
            <p class="text-sm text-muted-foreground">Loading...</p>
          {:else if cacheStatus}
            <div class="flex items-center gap-2">
              <Badge variant="secondary">
                {cacheStatus.count} images
              </Badge>
              <Badge variant="outline">
                {imageCacheService.formatCacheSize(cacheStatus.size)}
              </Badge>
            </div>
          {:else}
            <p class="text-sm text-muted-foreground">No cache data available</p>
          {/if}
        </div>
        <div class="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onclick={loadCacheStatus}
            disabled={isLoading}
          >
            <RefreshCw class="h-4 w-4 mr-1" />
            Refresh
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onclick={clearCache}
            disabled={isClearing || !cacheStatus || cacheStatus.count === 0}
          >
            <Trash2 class="h-4 w-4 mr-1" />
            {isClearing ? "Clearing..." : "Clear Cache"}
          </Button>
        </div>
      </div>

      <div class="text-xs text-muted-foreground space-y-1">
        <p>• Images are cached for 1 week regardless of server headers</p>
        <p>• Includes Firebase Storage and Google Cloud Storage images</p>
        <p>• Cache is automatically managed and cleaned up</p>
      </div>
    {/if}
  </Card.Content>
</Card.Root>
