<script lang="ts">
  import { onMount } from "svelte";
  import {
    getTags,
    ensureDriverTag,
    type DriverTag,
  } from "$lib/stores/driver_tags.svelte";
  import { updateUser } from "$lib/stores/mobile_users.svelte";
  import DriverTagBadge from "./DriverTagBadge.svelte";
  import { toast } from "svelte-sonner";
  import * as Card from "$lib/components/ui/card";
  import { Plus } from "lucide-svelte";

  interface Props {
    driverUid: string;
    currentTags: string[];
  }

  let { driverUid, currentTags = [] }: Props = $props();

  // Create a reactive copy of currentTags to avoid mutation warnings
  let tags = $derived(currentTags);

  let availableTags = $state<DriverTag[]>([]);
  let showDropdown = $state(false);
  let inputValue = $state("");
  let inputElement = $state<HTMLInputElement>();
  let dropdownElement = $state<HTMLDivElement>();
  let isUpdating = $state(false);

  $effect(() => {
    availableTags = getTags();
  });

  let filteredTags = $derived(
    availableTags.filter(
      (tag) =>
        tag.name.toLowerCase().includes(inputValue.toLowerCase()) &&
        !tags.includes(tag.name),
    ),
  );

  let showCreateOption = $derived(
    inputValue.trim() &&
      !availableTags.some(
        (tag) => tag.name.toLowerCase() === inputValue.toLowerCase(),
      ) &&
      !tags.includes(inputValue.trim()),
  );

  function handleInputFocus() {
    showDropdown = true;
  }

  function handleClickOutside(event: MouseEvent) {
    if (
      inputElement &&
      !inputElement.contains(event.target as Node) &&
      dropdownElement &&
      !dropdownElement.contains(event.target as Node)
    ) {
      showDropdown = false;
    }
  }

  async function addTag(tagName: string) {
    if (isUpdating) return;

    const trimmedTag = tagName.trim();
    if (!trimmedTag || tags.includes(trimmedTag)) return;

    try {
      isUpdating = true;

      // Ensure the tag exists (will auto-create if needed)
      await ensureDriverTag(trimmedTag);

      // Update the driver's tags
      const newTags = [...tags, trimmedTag];
      await updateUser(driverUid, { driverTags: newTags });

      inputValue = "";
      showDropdown = false;
      toast.success(`Added tag "${trimmedTag}" to driver`);
    } catch (error) {
      console.error("Error adding tag:", error);
      toast.error("Failed to add tag");
    } finally {
      isUpdating = false;
    }
  }

  async function removeTag(tagName: string) {
    if (isUpdating) return;

    try {
      isUpdating = true;

      // Update the driver's tags
      const newTags = tags.filter((t) => t !== tagName);
      await updateUser(driverUid, { driverTags: newTags });

      toast.success(`Removed tag "${tagName}" from driver`);
    } catch (error) {
      console.error("Error removing tag:", error);
      toast.error("Failed to remove tag");
    } finally {
      isUpdating = false;
    }
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === "Enter" && inputValue.trim()) {
      event.preventDefault();
      addTag(inputValue);
    } else if (event.key === "Escape") {
      showDropdown = false;
      inputValue = "";
    }
  }

  onMount(() => {
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  });
</script>

<Card.Root>
  <Card.Header>
    <Card.Title class="flex items-center gap-2">
      <span>🏷️ Driver Tags</span>
    </Card.Title>
    <Card.Description>
      Manage tags for this driver. Tags help categorize and filter drivers.
    </Card.Description>
  </Card.Header>
  <Card.Content>
    <div class="space-y-4">
      <!-- Current Tags -->
      {#if tags.length > 0}
        <div>
          <h4 class="text-sm font-medium mb-2">Current Tags</h4>
          <div class="flex flex-wrap gap-2">
            {#each tags as tagName}
              {@const tagData = availableTags.find((t) => t.name === tagName)}
              <DriverTagBadge
                tag={tagData || tagName}
                size="md"
                removable={!isUpdating}
                onRemove={() => removeTag(tagName)}
              />
            {/each}
          </div>
        </div>
      {:else}
        <p class="text-sm text-muted-foreground">
          No tags assigned to this driver yet.
        </p>
      {/if}

      <!-- Add New Tag -->
      <div class="relative">
        <h4 class="text-sm font-medium mb-2">Add Tag</h4>
        <div
          class="flex items-center gap-2 p-3 bg-muted/30 rounded-lg transition-all duration-200 focus-within:bg-muted/50 focus-within:shadow-sm hover:bg-muted/40 {isUpdating
            ? 'opacity-50 cursor-not-allowed'
            : ''}"
        >
          <Plus class="w-4 h-4 text-muted-foreground" />
          <input
            bind:this={inputElement}
            bind:value={inputValue}
            type="text"
            placeholder="Type to search or create new tag..."
            disabled={isUpdating}
            onfocus={handleInputFocus}
            onkeydown={handleKeyDown}
            class="flex-1 bg-transparent text-sm outline-none border-0 ring-0 focus:ring-0 focus:outline-none placeholder:text-muted-foreground"
          />
        </div>

        {#if showDropdown && !isUpdating && (filteredTags.length > 0 || showCreateOption)}
          <div
            bind:this={dropdownElement}
            class="absolute z-10 w-full mt-2 bg-popover rounded-lg shadow-lg border-0 backdrop-blur-sm max-h-60 overflow-auto"
          >
            {#each filteredTags as tag}
              <button
                type="button"
                onclick={() => addTag(tag.name)}
                class="w-full px-3 py-2 text-left hover:bg-accent/80 hover:text-accent-foreground flex items-center justify-between group transition-colors duration-150"
              >
                <DriverTagBadge {tag} size="sm" />
                {#if tag.usageCount > 0}
                  <span
                    class="text-xs text-muted-foreground group-hover:text-accent-foreground"
                  >
                    {tag.usageCount} driver{tag.usageCount !== 1 ? "s" : ""}
                  </span>
                {/if}
              </button>
            {/each}

            {#if showCreateOption}
              <button
                type="button"
                onclick={() => addTag(inputValue)}
                class="w-full px-3 py-2 text-left hover:bg-accent/80 hover:text-accent-foreground flex items-center gap-2 text-sm border-t border-muted/40 transition-colors duration-150"
              >
                <Plus class="w-4 h-4 text-muted-foreground" />
                <span>Create new tag "<strong>{inputValue}</strong>"</span>
              </button>
            {/if}
          </div>
        {/if}
      </div>
    </div>
  </Card.Content>
</Card.Root>
