<script lang="ts">
  let { amount, locale = navigator.language } = $props<{
    amount: number;
    locale?: string;
  }>();

  function formatAriary(value: number, userLocale: string): string {
    // Round to avoid floating point precision issues
    const roundedValue = Math.round(value);

    // Format with locale, then replace commas with spaces
    const formatted = roundedValue
      .toLocaleString(userLocale)
      .replace(/,/g, " ");

    return `${formatted} Ar`;
  }
</script>

<span>
  {formatAriary(amount, locale)}
</span>
