<script lang="ts">
	import type { HTMLInputAttributes } from "svelte/elements";
	import type { InputEvents } from "./index.js";
	import { cn } from "$lib/utils.js";

	type $$Events = InputEvents;

	let {
		value = $bindable(),
		class: className,
		readonly,
		onblur,
		onchange,
		onclick,
		onfocus,
		onfocusin,
		onfocusout,
		onkeydown,
		onkeypress,
		onkeyup,
		onmouseover,
		onmouseenter,
		onmouseleave,
		onmousemove,
		onpaste,
		oninput,
		onwheel,
		...restProps
	} = $props<HTMLInputAttributes & { value?: any }>();
</script>

<input
	class={cn(
		"border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
		className,
	)}
	bind:value
	{readonly}
	{onblur}
	{onchange}
	{onclick}
	{onfocus}
	{onfocusin}
	{onfocusout}
	{onkeydown}
	{onkeypress}
	{onkeyup}
	{onmouseover}
	{onmouseenter}
	{onmouseleave}
	{onmousemove}
	{onpaste}
	{oninput}
	{onwheel}
	{...restProps}
/>
