<script lang="ts">
  import {
    EventLogType,
    getEventTypeLabel,
  } from "$lib/stores/event_logs.svelte";
  import type { EventLog } from "$lib/stores/event_logs.svelte";
  import * as m from "$lib/paraglide/messages";

  interface Props {
    eventType: EventLogType | string;
    className?: string;
  }

  const { eventType, className = "" }: Props = $props();

  // Get color classes based on event type
  function getColorClasses(type: EventLogType | string): string {
    if (type === EventLogType.DRIVER_SERVICE_STATUS_UPDATE) {
      return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";
    } else if (type === EventLogType.DRIVER_TRIP_REJECTED) {
      return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300";
    } else if (
      type === "adminTripCostOverride" ||
      type === EventLogType.ADMIN_TRIP_COST_OVERRIDE
    ) {
      return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300";
    }
    // Default color for unknown types
    return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";
  }

  // Get display label
  function getLabel(type: EventLogType | string): string {
    // Use localized messages for known types
    if (type === EventLogType.DRIVER_SERVICE_STATUS_UPDATE) {
      return m.eventsLayout_serviceStatusEvent();
    } else if (type === EventLogType.DRIVER_TRIP_REJECTED) {
      return m.eventsLayout_tripRejectedEvent();
    }
    // Use the formatted label for all other types
    return getEventTypeLabel(type);
  }
</script>

<span
  class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium {getColorClasses(
    eventType,
  )} {className}"
>
  {getLabel(eventType)}
</span>
