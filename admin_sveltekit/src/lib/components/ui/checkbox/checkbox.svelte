<script lang="ts">
	import { cn } from "$lib/utils.js";
	import type { HTMLInputAttributes } from "svelte/elements";
	import { Check } from "lucide-svelte";

	let {
		checked = $bindable(false),
		onCheckedChange,
		class: className,
		...restProps
	} = $props<
		Omit<HTMLInputAttributes, "type"> & {
			checked?: boolean;
			onCheckedChange?: (checked: boolean) => void;
		}
	>();

	function handleChange(event: Event) {
		const target = event.target as HTMLInputElement;
		checked = target.checked;
		onCheckedChange?.(checked);
	}
</script>

<div class={cn("relative", className)}>
	<input
		type="checkbox"
		class={cn(
			"peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
			"data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",
		)}
		bind:checked
		onchange={handleChange}
		{...restProps}
	/>
	{#if checked}
		<Check
			class="absolute left-0 top-0 h-4 w-4 p-[1px] text-primary-foreground pointer-events-none"
		/>
	{/if}
</div>
