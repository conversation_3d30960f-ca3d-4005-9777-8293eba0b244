<script lang="ts">
    import * as Dialog from "$lib/components/ui/dialog";
    import { Button } from "$lib/components/ui/button";
    import Spinner from "$lib/components/ui/Spinner.svelte";

    let {
        open,
        title,
        description,
        onConfirm,
        onCancel,
        loading = false,
    } = $props<{
        open: boolean;
        title: string;
        description: string;
        onConfirm: () => void;
        onCancel: () => void;
        loading?: boolean;
    }>();
</script>

<Dialog.Root {open} onOpenChange={(isOpen: boolean) => !isOpen && onCancel()}>
    <Dialog.Content class="sm:max-w-[425px]">
        <Dialog.Header>
            <Dialog.Title>{title}</Dialog.Title>
            <Dialog.Description>
                {description}
            </Dialog.Description>
        </Dialog.Header>
        <Dialog.Footer>
            <Button variant="outline" onclick={onCancel} disabled={loading}
                >Cancel</Button
            >
            <Button
                variant="destructive"
                onclick={onConfirm}
                disabled={loading}
            >
                {#if loading}
                    <Spinner className="mr-2 h-6 w-4 text-current" />
                    <span class="ml-2">Processing...</span>
                {:else}
                    Confirm
                {/if}
            </Button>
        </Dialog.Footer>
    </Dialog.Content>
</Dialog.Root>
