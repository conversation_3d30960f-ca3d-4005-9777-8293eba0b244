<script lang="ts">
    import { tenantStore, type Tenant } from "$lib/stores/tenant.svelte";
    import { ChevronDown } from "lucide-svelte";

    let showDropdown = $state(false);
    let dropdownRef = $state<HTMLDivElement | undefined>();

    $effect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (dropdownRef && !dropdownRef.contains(event.target as Node)) {
                showDropdown = false;
            }
        }

        if (showDropdown) {
            document.addEventListener("click", handleClickOutside);
            return () => {
                document.removeEventListener("click", handleClickOutside);
            };
        }
    });

    async function selectTenant(tenant: Tenant) {
        await tenantStore.switchTenant(tenant.id);
        showDropdown = false;
        // Reload the page to refresh all data with new tenant context
        window.location.reload();
    }
</script>

{#if tenantStore.hasMultipleTenants}
    <div class="relative" bind:this={dropdownRef}>
        <button
            onclick={() => (showDropdown = !showDropdown)}
            class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
            <span>{tenantStore.current?.name || "Select Tenant"}</span>
            <ChevronDown class="w-4 h-4" />
        </button>

        {#if showDropdown}
            <div
                class="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
            >
                <div class="py-1">
                    {#each tenantStore.tenants as tenant}
                        <button
                            onclick={() => selectTenant(tenant)}
                            class="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled={tenant.id === tenantStore.currentId}
                        >
                            <div class="flex items-center justify-between">
                                <span>{tenant.name}</span>
                                {#if tenant.id === tenantStore.currentId}
                                    <span class="text-xs text-gray-500"
                                        >Current</span
                                    >
                                {/if}
                            </div>
                            {#if !tenant.isActive}
                                <span class="text-xs text-red-600"
                                    >Inactive</span
                                >
                            {/if}
                        </button>
                    {/each}
                </div>
            </div>
        {/if}
    </div>
{/if}
