<script lang="ts">
  import { Search } from 'lucide-svelte';
  import * as m from '$lib/paraglide/messages';

  interface Props {
    value?: string;
    placeholder?: string;
    onSearch?: (query: string) => void;
    class?: string;
  }

  let {
    value = $bindable(''),
    placeholder = m.algoliaSearch_defaultSearchPlaceholder(),
    onSearch,
    class: className = '',
  }: Props = $props();

  let timeoutId: NodeJS.Timeout;

  function handleInput(event: Event) {
    const target = event.target as HTMLInputElement;
    value = target.value;

    // Debounce search
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      onSearch?.(value);
    }, 300);
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      clearTimeout(timeoutId);
      onSearch?.(value);
    }
  }
</script>

<div class="relative {className}">
  <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
  <input
    type="search"
    {value}
    {placeholder}
    oninput={handleInput}
    onkeydown={handleKeyDown}
    class="w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
  />
</div>
