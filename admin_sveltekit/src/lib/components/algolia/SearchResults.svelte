<script lang="ts">
  import type { Hit } from 'algoliasearch';
  import Spinner from '$lib/components/ui/Spinner.svelte';
  import * as m from '$lib/paraglide/messages';

  interface Props {
    hits: Hit<any>[];
    loading?: boolean;
    renderHit?: (hit: Hit<any>) => any;
    onHitClick?: (hit: Hit<any>) => void;
    emptyMessage?: string;
    hasQuery?: boolean;
    class?: string;
  }

  let {
    hits = [],
    loading = false,
    renderHit,
    onHitClick,
    emptyMessage = m.algoliaSearch_noResultsFound(),
    hasQuery = true,
    class: className = '',
  }: Props = $props();
</script>

<div class="search-results {className}">
  {#if loading}
    <div class="flex items-center justify-center py-8">
      <Spinner />
    </div>
  {:else if hasQuery && hits.length === 0}
    <div class="text-center py-8 text-muted-foreground">
      {emptyMessage}
    </div>
  {:else if hasQuery}
    <div class="space-y-2">
      {#each hits as hit (hit.objectID)}
        <button
          type="button"
          class="w-full text-left p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
          onclick={() => onHitClick?.(hit)}
        >
          {#if renderHit}
            {@html renderHit(hit)}
          {:else}
            <pre class="text-xs">{JSON.stringify(hit, null, 2)}</pre>
          {/if}
        </button>
      {/each}
    </div>
  {/if}
</div>
