<script lang="ts">
  import { algoliaClient, getIndexName, getTenantFilteredParams } from '$lib/algolia';
  import SearchBox from './SearchBox.svelte';
  import SearchResults from './SearchResults.svelte';
  import type { Hit } from 'algoliasearch';
  import { toast } from 'svelte-sonner';
  import * as m from '$lib/paraglide/messages';

  interface Props {
    indexName: string;
    searchableAttributes?: string[];
    attributesToRetrieve?: string[];
    hitsPerPage?: number;
    renderHit?: (hit: Hit<any>) => any;
    onHitClick?: (hit: Hit<any>) => void;
    placeholder?: string;
    emptyMessage?: string;
    additionalFilters?: string;
    class?: string;
  }

  let {
    indexName,
    searchableAttributes,
    attributesToRetrieve,
    hitsPerPage = 20,
    renderHit,
    onHitClick,
    placeholder = m.algoliaSearch_defaultSearchPlaceholder(),
    emptyMessage = m.algoliaSearch_noResultsFound(),
    additionalFilters,
    class: className = '',
  }: Props = $props();

  let searchQuery = $state('');
  let hits = $state<Hit<any>[]>([]);
  let loading = $state(false);

  async function performSearch(query: string) {
    if (!algoliaClient) {
      toast.error(m.algoliaSearch_notConfigured());
      return;
    }

    loading = true;
    const fullIndexName = getIndexName(indexName);

    try {
      const { filters } = getTenantFilteredParams(additionalFilters);

      // Build the search request with proper v5 format
      // All parameters go directly in the request object
      const searchRequest: any = {
        indexName: fullIndexName,
        query,
        hitsPerPage,
      };

      // Add optional parameters only if they exist
      if (filters) {
        searchRequest.filters = filters;
      }

      if (attributesToRetrieve) {
        searchRequest.attributesToRetrieve = attributesToRetrieve;
      }

      // Use searchableAttributes if provided
      if (searchableAttributes && searchableAttributes.length > 0) {
        searchRequest.restrictSearchableAttributes = searchableAttributes;
      }

      const response = await algoliaClient.search({
        requests: [searchRequest],
      });

      const result = response.results[0];
      if (result && 'hits' in result) {
        hits = result.hits || [];
      } else {
        hits = [];
      }
    } catch (error: any) {
      // Check if it's an index not found error
      if (error?.message?.includes('does not exist')) {
        console.warn(`Algolia index "${fullIndexName}" does not exist yet. It will be created when data is synced.`);
        // Don't show error toast for missing index
      } else if (error?.message?.includes('not in searchableAttributes')) {
        // This error occurs when the index exists but searchableAttributes are not configured
        console.warn(
          `Algolia index "${fullIndexName}" needs configuration. Run: node firebase/data-tools/on-demand/algolia-configure-indexes.js`
        );
        toast.warning(m.algoliaSearch_indexNotConfigured());
      } else {
        console.error('Search error:', error);
        toast.error(m.algoliaSearch_searchFailed());
      }
      hits = [];
    } finally {
      loading = false;
    }
  }

  // Only perform search when there's an actual query
  $effect(() => {
    if (searchQuery.trim()) {
      performSearch(searchQuery);
    } else {
      hits = [];
    }
  });
</script>

<div class="algolia-search {className}">
  <div class="mb-4">
    <SearchBox bind:value={searchQuery} {placeholder} onSearch={performSearch} />
  </div>

  <SearchResults {hits} {loading} {renderHit} {onHitClick} {emptyMessage} hasQuery={searchQuery.trim().length > 0} />
</div>
