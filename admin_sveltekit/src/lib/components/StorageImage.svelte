<script lang="ts">
  import { onMount } from "svelte";
  import { getDownloadUrl } from "$lib/utils/storage";

  type Props = {
    pathOrUrl: string;
    alt?: string;
    class?: string;
    style?: string;
    onclick?: (event: MouseEvent) => void;
  };

  let {
    pathOrUrl,
    alt = "",
    class: className = "",
    style = "",
    onclick,
  }: Props = $props();

  let imageUrl = $state<string | null>(null);
  let loading = $state(true);
  let error = $state(false);

  onMount(async () => {
    await loadImage();
  });

  async function loadImage() {
    loading = true;
    error = false;

    try {
      imageUrl = await getDownloadUrl(pathOrUrl);
    } catch (err) {
      console.error("Failed to load image:", err);
      error = true;
    } finally {
      loading = false;
    }
  }

  $effect(() => {
    if (pathOrUrl) {
      loadImage();
    }
  });
</script>

{#if loading}
  <div class="bg-gray-200 animate-pulse {className}" {style}>
    <div class="flex items-center justify-center h-full">
      <svg
        class="w-8 h-8 text-gray-400"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
        />
      </svg>
    </div>
  </div>
{:else if error}
  <div class="bg-gray-200 {className}" {style}>
    <div class="flex items-center justify-center h-full">
      <svg
        class="w-8 h-8 text-red-400"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
    </div>
  </div>
{:else if imageUrl}
  {#if onclick}
    <button
      type="button"
      {onclick}
      class="focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      aria-label={alt || "Click to view image"}
    >
      <img
        src={imageUrl}
        {alt}
        class={className}
        {style}
        onerror={() => {
          error = true;
        }}
      />
    </button>
  {:else}
    <img
      src={imageUrl}
      {alt}
      class={className}
      {style}
      onerror={() => {
        error = true;
      }}
    />
  {/if}
{/if}
