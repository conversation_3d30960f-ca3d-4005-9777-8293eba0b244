<script lang="ts">
  import type { DriverTag } from '$lib/stores/driver_tags.svelte';
  
  interface Props {
    tag: string | DriverTag;
    size?: 'sm' | 'md' | 'lg';
    removable?: boolean;
    onRemove?: () => void;
  }
  
  let { tag, size = 'md', removable = false, onRemove }: Props = $props();
  
  let tagData = $derived(
    typeof tag === 'string' 
      ? { name: tag, color: '#6b7280' } // default gray color for string tags
      : tag
  );
  
  let sizeClasses = $derived({
    sm: 'text-xs px-2 py-0.5',
    md: 'text-sm px-2.5 py-1',
    lg: 'text-base px-3 py-1.5'
  }[size]);
  
  function handleRemove(e: MouseEvent) {
    e.stopPropagation();
    onRemove?.();
  }
</script>

<span 
  class="inline-flex items-center gap-1 rounded-full font-medium {sizeClasses}"
  style="background-color: {tagData.color}20; color: {tagData.color}; border: 1px solid {tagData.color}40;"
>
  <span>{tagData.name}</span>
  {#if removable}
    <button
      type="button"
      onclick={handleRemove}
      class="ml-1 hover:opacity-75 transition-opacity"
      aria-label="Remove tag"
    >
      <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
      </svg>
    </button>
  {/if}
</span>