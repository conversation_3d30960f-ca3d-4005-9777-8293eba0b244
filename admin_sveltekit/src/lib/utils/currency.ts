export function formatAriary(value: number, locale: string = navigator.language): string {
  // Round to avoid floating point precision issues
  const roundedValue = Math.round(value);

  // Format with locale, then replace commas with spaces
  const formatted = roundedValue.toLocaleString(locale).replace(/,/g, ' ');

  return `${formatted} Ar`;
}

// Helper function to format currency values for display in JavaScript contexts
// (e.g., toast messages, string interpolation)
export function formatCurrency(value: number): string {
  return formatAriary(value);
} 