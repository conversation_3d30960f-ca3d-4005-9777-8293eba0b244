/**
 * Format a date in long format based on locale
 * English: 2025-march-15 5:49:40pm
 * French: 15-mars-2025 17:49:40
 */
export function formatLongDateTime(date: Date, locale: string = 'en'): string {
  const months = {
    en: ['january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december'],
    fr: ['janvier', 'février', 'mars', 'avril', 'mai', 'juin', 'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre']
  };

  const year = date.getFullYear();
  const month = months[locale as keyof typeof months][date.getMonth()];
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');

  if (locale === 'fr') {
    return `${day}-${month}-${year} ${hours.toString().padStart(2, '0')}:${minutes}:${seconds}`;
  }

  // English format with AM/PM
  const period = hours >= 12 ? 'pm' : 'am';
  const hours12 = hours % 12 || 12;
  return `${year}-${month}-${day} ${hours12}:${minutes}:${seconds}${period}`;
}

/**
 * Format a date in short format based on locale
 * English: mar-15, 5:49pm
 * French: 15 mars à 17:49
 */
export function formatShortDateTime(date: Date, locale: string = 'en'): string {
  const months = {
    en: ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'],
    fr: ['janvier', 'février', 'mars', 'avril', 'mai', 'juin', 'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre']
  };

  const month = months[locale as keyof typeof months][date.getMonth()];
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, '0');

  if (locale === 'fr') {
    return `${day} ${month} à ${hours.toString().padStart(2, '0')}:${minutes}`;
  }

  // English format with AM/PM
  const period = hours >= 12 ? 'pm' : 'am';
  const hours12 = hours % 12 || 12;
  return `${month}-${day} at ${hours12}:${minutes}${period}`;
}

/**
 * Format a time in HH:MM format
 * @param date Date object to format
 * @param locale Optional locale (defaults to 'en')
 * @returns Formatted time string
 */
export function formatTime(date: Date, locale: string = 'en'): string {
  const hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, '0');
  
  if (locale === 'fr') {
    return `${hours.toString().padStart(2, '0')}:${minutes}`;
  }
  
  // English format with AM/PM
  const period = hours >= 12 ? 'PM' : 'AM';
  const hours12 = hours % 12 || 12;
  return `${hours12}:${minutes} ${period}`;
}

/**
 * Format a date and time in a standard format
 * @param date Date object to format
 * @param locale Optional locale (defaults to 'en')
 * @returns Formatted date and time string
 */
export function formatDateTime(date: Date, locale: string = 'en'): string {
  return formatShortDateTime(date, locale);
}
