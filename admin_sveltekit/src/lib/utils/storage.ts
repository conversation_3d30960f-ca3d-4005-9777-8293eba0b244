import { ref, getDownloadURL } from "firebase/storage";
import { storage } from "$lib/firebase.client";

/**
 * Extract the storage path from a full URL or return the path as-is
 */
export function getStoragePath(urlOrPath: string): string {
  // If it's already just a path, return it
  if (!urlOrPath.startsWith("http://") && !urlOrPath.startsWith("https://") && !urlOrPath.startsWith("gs://")) {
    return urlOrPath;
  }

  // Handle gs:// URLs
  if (urlOrPath.startsWith("gs://")) {
    const url = new URL(urlOrPath);
    // Remove the bucket name and return just the path
    return url.pathname.startsWith("/") ? url.pathname.substring(1) : url.pathname;
  }

  // Handle http(s):// URLs from Firebase Storage
  try {
    const url = new URL(urlOrPath);
    // Extract path from Firebase Storage URL format
    // e.g., http://10.0.2.2:9199/v0/b/bucket/o/path%2Fto%2Ffile?alt=media
    const pathMatch = url.pathname.match(/\/o\/([^?]+)/);
    if (pathMatch) {
      // Decode the URL-encoded path
      return decodeURIComponent(pathMatch[1]);
    }
  } catch (e) {
    console.error("Error parsing storage URL:", e);
  }

  // If we can't parse it, return as-is
  return urlOrPath;
}

/**
 * Get a Firebase Storage reference from a path
 */
export function getStorageRef(path: string) {
  if (!storage) {
    throw new Error("Firebase storage is not initialized");
  }
  return ref(storage, path);
}

/**
 * Get the download URL for a storage path
 */
export async function getDownloadUrl(pathOrUrl: string): Promise<string> {
  // If it's already a full URL and not from the emulator, return it
  if (pathOrUrl.startsWith("https://") && !pathOrUrl.includes("10.0.2.2")) {
    return pathOrUrl;
  }

  // Extract the path if it's a URL
  const path = getStoragePath(pathOrUrl);

  try {
    const storageRef = getStorageRef(path);
    return await getDownloadURL(storageRef);
  } catch (error) {
    console.error("Error getting download URL for path", path, error);
    throw error;
  }
}

/**
 * Check if a string is a full URL
 */
export function isFullUrl(value: string): boolean {
  return value.startsWith("http://") ||
    value.startsWith("https://") ||
    value.startsWith("gs://");
} 