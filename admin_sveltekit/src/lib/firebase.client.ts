import { initializeApp, type FirebaseApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider, type Auth } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator, type Firestore } from 'firebase/firestore';
import { getFunctions, connectFunctionsEmulator, httpsCallable, type Functions } from 'firebase/functions';
import { getStorage, connectStorageEmulator, type FirebaseStorage } from 'firebase/storage';

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  databaseURL: import.meta.env.VITE_FIREBASE_DATABASE_URL,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID,
};

interface FirebaseServices {
  app: FirebaseApp | null;
  auth: Auth | null;
  fdb: Firestore | null;
  functions: Functions | null;
  storage: FirebaseStorage | null;
}

let app: FirebaseApp | null = null;
let auth: Auth | null = null;
let provider: GoogleAuthProvider | null = null;
let fdb: Firestore | null = null;
let functions: Functions | null = null;
let storage: FirebaseStorage | null = null;

export async function initializeFirebase(): Promise<FirebaseServices> {
  if (app) return { app, auth, fdb, functions, storage };

  return new Promise((resolve, reject) => {
    if (typeof window !== 'undefined') {
      try {
        const shouldUseEmulator = () => {
          try {
            if (localStorage.getItem('useEmulator') === 'false') return false;
            if (import.meta.env.DEV) return localStorage.getItem('useEmulator') !== 'false';
          } catch (error) {
            console.warn('Error accessing localStorage:', error);
          }
          return false;
        };

        if (!firebaseConfig.projectId || !firebaseConfig.apiKey) {
          throw new Error('Missing required Firebase configuration');
        }

        app = initializeApp(firebaseConfig);
        auth = getAuth(app);
        provider = new GoogleAuthProvider();
        fdb = getFirestore(app);
        functions = getFunctions(app, 'europe-west3');
        storage = getStorage(app);

        if (import.meta.env.DEV && shouldUseEmulator()) {
          console.log('🔧 Attempting to connect to emulators...');
          const host = 'localhost';
          try {
            connectFirestoreEmulator(fdb, host, 8080);
            connectFunctionsEmulator(functions, host, 5001);
            connectStorageEmulator(storage, host, 9199);
            console.log('🔧 Connected to Firebase emulators');
          } catch (emulatorError) {
            console.error('❌ Error connecting to emulators:', emulatorError);
          }
        } else {
          console.log('🌐 Using production Firebase services');
        }

        console.log('🔥 Firebase initialized successfully');
        resolve({ app, auth, fdb, functions, storage });
      } catch (error) {
        console.error('Error initializing Firebase:', error);
        reject(error);
      }
    } else {
      resolve({ app: null, auth: null, fdb: null, functions: null, storage: null });
    }
  });
}

export const db = fdb;

export async function callFunction<T = any, R = any>(functionName: string, data?: T): Promise<R> {
  if (!functions) throw new Error('Firebase Functions not initialized');
  const functionCall = httpsCallable<T, R>(functions, functionName);
  const result = await functionCall(data);
  return result.data;
}

export async function callAdminFunction<T = any, R = any>(
  functionName: string,
  data: T & { tenantId: string },
  requiredRole: number = 0
): Promise<R> {
  const { tenantStore } = await import('$lib/stores/tenant.svelte');
  if (!tenantStore.validateCurrentTenantAccess()) throw new Error('No access to tenant');
  if (!tenantStore.hasMinimumRole(requiredRole)) throw new Error('Insufficient permissions');
  if (data.tenantId !== tenantStore.currentId) throw new Error('Tenant ID mismatch');
  return callFunction<T, R>(functionName, data);
}

export { app, auth, provider, fdb, functions, storage };
