import { browser } from '$app/environment';
import { initializeFirebase } from '$lib/firebase.client';
import type { Firestore } from 'firebase/firestore';
import { getFirebaseUser } from './auth.svelte';

export interface Tenant {
	id: string;
	name: string;
	isActive: boolean;
	settings: Record<string, any>;
}

export interface TenantAccess {
	tenantId: string;
	isActive: boolean;
	role: number; // 0=MANAGER, 1=ADMIN, 2=SUPER_ADMIN
	assignedAt: Date;
	assignedBy: string;
}

class TenantStore {
	private currentTenant = $state<Tenant | null>(null);
	private availableTenants = $state<Tenant[]>([]);
	private tenantAccess = $state<TenantAccess[]>([]);
	private loading = $state(false);
	private error = $state<string | null>(null);
	private initialized = $state(false);
	private fdb: Firestore | null = null;

	// Default tenant ID
	private readonly DEFAULT_TENANT_ID = 'fiaranow';

	constructor() {
		this.initialize();
	}

	private async initialize() {
		if (this.initialized || !browser) return;
		this.initialized = true;

		try {
			const { fdb } = await initializeFirebase();
			this.fdb = fdb;

			// Only load tenant data if user is authenticated
			// For unauthenticated users, we'll set a default tenant without Firebase calls
			const user = getFirebaseUser();
			if (user && typeof user === 'object') {
				const savedTenantId = localStorage.getItem('selectedTenantId');
				if (savedTenantId) {
					await this.loadTenant(savedTenantId);
				} else {
					await this.loadTenant(this.DEFAULT_TENANT_ID);
				}
			} else {
				// Set default tenant for unauthenticated users
				this.currentTenant = {
					id: this.DEFAULT_TENANT_ID,
					name: 'Fiaranow',
					isActive: true,
					settings: {}
				};
			}
		} catch (error) {
			console.error('Error initializing tenant store:', error);
			this.error = 'Failed to initialize tenant store';
		}
	}

	get current() {
		return this.currentTenant;
	}

	get currentId() {
		return this.currentTenant?.id || this.DEFAULT_TENANT_ID;
	}

	get tenants() {
		return this.availableTenants;
	}

	get access() {
		return this.tenantAccess;
	}

	get isLoading() {
		return this.loading;
	}

	get errorMessage() {
		return this.error;
	}

	get hasMultipleTenants() {
		return this.availableTenants.length > 1;
	}

	get currentAccess() {
		return this.tenantAccess.find((a) => a.tenantId === this.currentId);
	}

	get isSuperAdmin() {
		return this.currentAccess?.role === 2;
	}

	get isAdmin() {
		return this.currentAccess?.role === 1 || this.currentAccess?.role === 2;
	}

	get isManager() {
		return this.currentAccess?.role === 0 || this.isAdmin;
	}

	async loadTenant(tenantId: string) {
		if (!tenantId) {
			console.warn('Tenant ID is required');
			return;
		}

		this.loading = true;
		this.error = null;

		try {
			const availableTenant = this.availableTenants.find((t) => t.id === tenantId);
			if (availableTenant) {
				this.currentTenant = availableTenant;
			} else {
				// Ensure Firebase is initialized
				if (!this.fdb) {
					const { fdb } = await initializeFirebase();
					this.fdb = fdb;
				}

				if (!this.fdb) {
					throw new Error('Failed to initialize Firestore');
				}

				const { doc, getDoc } = await import('firebase/firestore');
				
				try {
					const tenantDoc = await getDoc(doc(this.fdb, 'tenants', tenantId));
					
					if (tenantDoc && tenantDoc.exists()) {
						const tenantData = tenantDoc.data();
						if (!tenantData || typeof tenantData !== 'object') {
							console.warn(`Tenant data is invalid for ${tenantId}:`, tenantData);
							throw new Error('Tenant data is invalid');
						}
						
						this.currentTenant = {
							id: tenantId,
							name: tenantData.name || tenantId,
							isActive: tenantData.isActive !== false,
							settings: tenantData.settings || {}
						};
					} else {
						// Create default tenant
						this.currentTenant = {
							id: tenantId,
							name: tenantId === this.DEFAULT_TENANT_ID ? 'Fiaranow' : tenantId,
							isActive: true,
							settings: {}
						};
					}
				} catch (firestoreError) {
					console.warn(`Firestore error for tenant ${tenantId}:`, firestoreError);
					// Create fallback tenant on Firestore errors
					this.currentTenant = {
						id: tenantId,
						name: tenantId === this.DEFAULT_TENANT_ID ? 'Fiaranow' : tenantId,
						isActive: true,
						settings: {}
					};
				}
			}
			
			if (browser) {
				localStorage.setItem('selectedTenantId', tenantId);
			}
		} catch (error) {
			console.error('Error loading tenant:', error);
			this.error = `Failed to load tenant: ${error instanceof Error ? error.message : 'Unknown error'}`;
		} finally {
			this.loading = false;
		}
	}

	async switchTenant(tenantId: string) {
		const hasAccess = this.tenantAccess.some((a) => a.tenantId === tenantId && a.isActive);
		if (!hasAccess && this.tenantAccess.length > 0) {
			this.error = 'You do not have access to this tenant';
			return;
		}
		await this.loadTenant(tenantId);
	}

	async loadUserTenantAccess(adminUid: string) {
		if (!adminUid) {
			console.warn('No admin UID provided - user may not be authenticated');
			return;
		}

		// Check if user is authenticated before making Firebase calls
		const user = getFirebaseUser();
		if (!user || typeof user !== 'object') {
			console.info('User not authenticated, skipping tenant access loading');
			return;
		}

		this.loading = true;
		this.error = null;

		try {
			// Ensure Firebase is initialized
			if (!this.fdb) {
				const { fdb } = await initializeFirebase();
				this.fdb = fdb;
			}

			if (!this.fdb) {
				throw new Error('Failed to initialize Firestore');
			}

			const { collection, getDocs, doc, getDoc } = await import('firebase/firestore');
			const tenantAccessRef = collection(this.fdb, 'admin_users', adminUid, 'tenants');
			const tenantAccessSnapshot = await getDocs(tenantAccessRef);

			this.tenantAccess = tenantAccessSnapshot.docs.map((docRef) => {
				const data = docRef.data();
				if (!data) {
					throw new Error('Tenant access data is null');
				}
				
				return {
					tenantId: data.tenantId || '',
					isActive: data.isActive === true,
					role: data.role || 0,
					assignedAt: data.assignedAt?.toDate() || new Date(),
					assignedBy: data.assignedBy || 'unknown'
				};
			});

			if (this.tenantAccess.length === 0) {
				this.tenantAccess = [
					{
						tenantId: this.DEFAULT_TENANT_ID,
						isActive: true,
						role: 1,
						assignedAt: new Date(),
						assignedBy: 'system'
					}
				];
			}

			this.availableTenants = [];
			for (const access of this.tenantAccess) {
				if (access.isActive && access.tenantId) {
					try {
						const tenantDoc = await getDoc(doc(this.fdb!, 'tenants', access.tenantId));
						if (tenantDoc.exists()) {
							const tenantData = tenantDoc.data();
							if (tenantData) {
								this.availableTenants.push({
									id: access.tenantId,
									name: tenantData.name || access.tenantId,
									isActive: tenantData.isActive !== false,
									settings: tenantData.settings || {}
								});
							}
						} else {
							// Create default tenant entry if document doesn't exist
							this.availableTenants.push({
								id: access.tenantId,
								name: access.tenantId === this.DEFAULT_TENANT_ID ? 'Fiaranow' : access.tenantId,
								isActive: true,
								settings: {}
							});
						}
					} catch (tenantError) {
						console.error(`Error loading tenant ${access.tenantId}:`, tenantError);
						// Add fallback tenant entry
						this.availableTenants.push({
							id: access.tenantId,
							name: access.tenantId === this.DEFAULT_TENANT_ID ? 'Fiaranow' : access.tenantId,
							isActive: true,
							settings: {}
						});
					}
				}
			}

			if (!this.currentTenant && this.availableTenants.length > 0) {
				await this.loadTenant(this.availableTenants[0].id);
			}
		} catch (error) {
			console.error('Error loading user tenant access:', error);
			this.error = 'Failed to load tenant access';
		} finally {
			this.loading = false;
		}
	}

	getTenantPath(collectionName: string) {
		return `tenants/${this.currentId}/${collectionName}`;
	}

	validateCurrentTenantAccess(): boolean {
		if (!this.currentTenant) return false;
		const access = this.tenantAccess.find((a) => a.tenantId === this.currentTenant!.id);
		return access?.isActive === true;
	}

	getCurrentTenantRole(): number | null {
		if (!this.currentTenant) return null;
		const access = this.tenantAccess.find((a) => a.tenantId === this.currentTenant!.id);
		return access?.role ?? null;
	}

	hasMinimumRole(minRole: number): boolean {
		const currentRole = this.getCurrentTenantRole();
		return currentRole !== null && currentRole >= minRole;
	}

	clear() {
		this.currentTenant = null;
		this.availableTenants = [];
		this.tenantAccess = [];
		this.error = null;
		this.initialized = false;
		if (browser) {
			localStorage.removeItem('selectedTenantId');
		}
	}
}

export const tenantStore = new TenantStore();

export function getTenantCollection(collection: string) {
	return tenantStore.getTenantPath(collection);
}