import {
  collection,
  doc,
  onSnapshot,
  query,
  where,
  orderBy,
  type QuerySnapshot,
  type DocumentData,
  type Timestamp,
} from 'firebase/firestore';
import { fdb } from '$lib/firebase.client';
import { tenantStore, getTenantCollection } from './tenant.svelte';
import { callAdminFunction } from '$lib/firebase.client';

export interface Vehicle {
  id?: string;
  brand: string;
  model: string;
  color: string;
  year: number;
  registrationNumber: string;
  maxPassengers: number;
  isActive: boolean;
  ownerUID?: string;

  // Metadata
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;

  // Derived fields
  displayName?: string;
  ownerName?: string;
}

// Private state
let _vehicles = $state<Vehicle[]>([]);
let _vehiclesMap = $state<Map<string, Vehicle>>(new Map());
let _vehicleLinkings = $state<Map<string, any>>(new Map()); // Vehicle ID -> Linking data
let _loading = $state(true);
let _error = $state<string | null>(null);

// Filter state
let _showInactive = $state(false);
let _ownerFilter = $state<string | null>(null);

// Public getters
export function getVehicles() {
  return _vehicles;
}

export function getVehiclesMap() {
  return _vehiclesMap;
}

export function getLoading() {
  return _loading;
}

export function getError() {
  return _error;
}

export function getShowInactive() {
  return _showInactive;
}

export function getOwnerFilter() {
  return _ownerFilter;
}

export function getVehicleLinkingsMap() {
  return _vehicleLinkings;
}

// Filter setters
export function setShowInactive(show: boolean) {
  _showInactive = show;
}

export function setOwnerFilter(ownerUID: string | null) {
  _ownerFilter = ownerUID;
}

let unsubscribe: (() => void) | null = null;
let unsubscribeLinkings: (() => void) | null = null;

export function init() {
  if (unsubscribe) return;

  const tenantId = tenantStore.currentId;
  if (!tenantId) {
    _error = 'No tenant selected';
    _loading = false;
    return;
  }

  _loading = true;
  _error = null;

  if (!fdb) {
    _error = 'Firestore not initialized';
    _loading = false;
    return;
  }

  try {
    // Query global vehicles collection - always get ALL vehicles for client-side filtering
    const vehiclesRef = collection(fdb, 'vehicles');
    const vehiclesQuery = query(vehiclesRef, orderBy('createdAt', 'desc'));

    // Query tenant-specific vehicle linkings - get ALL linkings (active and inactive)
    const linkingsRef = collection(fdb, getTenantCollection('vehicles_linking'));
    const linkingsQuery = query(linkingsRef, orderBy('linkedAt', 'desc'));

    // Subscribe to vehicles
    unsubscribe = onSnapshot(
      vehiclesQuery,
      (snapshot: QuerySnapshot<DocumentData>) => {
        // Clear the map before updating to ensure reactivity
        _vehiclesMap.clear();

        _vehicles = snapshot.docs.map((doc) => {
          const data = doc.data();
          const vehicle: Vehicle = {
            id: doc.id,
            brand: data.brand,
            model: data.model,
            color: data.color,
            year: data.year,
            registrationNumber: data.registrationNumber,
            maxPassengers: data.maxPassengers || 4,
            isActive: data.isActive ?? true,
            ownerUID: data.ownerUID,
            createdAt: (data.createdAt as Timestamp).toDate(),
            updatedAt: (data.updatedAt as Timestamp).toDate(),
            createdBy: data.createdBy,
            displayName: `${data.brand} ${data.model} (${data.year})`,
          };

          _vehiclesMap.set(doc.id, vehicle);
          return vehicle;
        });

        // Force reactivity by creating a new Map instance
        _vehiclesMap = new Map(_vehiclesMap);
        _loading = false;
      },
      (error) => {
        console.error('Error loading vehicles:', error);
        _error = error.message;
        _loading = false;
      }
    );

    // Subscribe to vehicle linkings for tenant awareness
    unsubscribeLinkings = onSnapshot(
      linkingsQuery,
      (snapshot: QuerySnapshot<DocumentData>) => {
        _vehicleLinkings.clear();
        snapshot.docs.forEach((doc) => {
          const data = doc.data();
          _vehicleLinkings.set(data.vehicleId, {
            id: doc.id,
            ...data,
            linkedAt: (data.linkedAt as Timestamp).toDate(),
          });
        });

        // Force reactivity by creating a new Map instance
        _vehicleLinkings = new Map(_vehicleLinkings);
      },
      (error) => {
        console.error('Error loading vehicle linkings:', error);
      }
    );
  } catch (error) {
    console.error('Error initializing vehicles:', error);
    _error = error instanceof Error ? error.message : 'Unknown error';
    _loading = false;
  }
}

export function destroy() {
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }
  if (unsubscribeLinkings) {
    unsubscribeLinkings();
    unsubscribeLinkings = null;
  }
  _vehicles = [];
  _vehiclesMap.clear();
  _vehicleLinkings.clear();
  _loading = true;
  _error = null;
  // Don't reset filter states when refreshing - preserve user preferences
  // _showInactive = false;
  // _ownerFilter = null;
}

// Complete reset - use when navigating away or when you want to clear all states
export function reset() {
  destroy();
  _showInactive = false;
  _ownerFilter = null;
}

// Filtered vehicles
export function getFilteredVehicles() {
  let filtered = _vehicles;

  // Filter by tenant - only show vehicles linked to current tenant
  filtered = filtered.filter((vehicle) => {
    const linking = _vehicleLinkings.get(vehicle.id!);
    // Show vehicle if it has any linking to current tenant (active or inactive linking)
    // This ensures inactive vehicles can still be viewed when "Show inactive" is checked
    return linking !== undefined;
  });

  // Filter by active/inactive status
  if (!_showInactive) {
    filtered = filtered.filter((vehicle) => vehicle.isActive);
  }

  // Filter by owner
  if (_ownerFilter) {
    filtered = filtered.filter((vehicle) => vehicle.ownerUID === _ownerFilter);
  }

  return filtered;
}

// Get vehicles by owner
export function getVehiclesByOwner(ownerUID: string) {
  return _vehicles.filter((vehicle) => vehicle.ownerUID === ownerUID);
}

// Get tenant-owned vehicles
export function getTenantOwnedVehicles() {
  return _vehicles.filter((vehicle) => {
    if (vehicle.ownerUID) return false; // Not tenant-owned if has owner
    const linking = _vehicleLinkings.get(vehicle.id!);
    return linking && linking.isActive && linking.isOwnedByTenant;
  });
}

// Get user-owned vehicles
export function getUserOwnedVehicles() {
  return _vehicles.filter((vehicle) => {
    if (!vehicle.ownerUID) return false; // Not user-owned if no owner
    const linking = _vehicleLinkings.get(vehicle.id!);
    return linking && linking.isActive && !linking.isOwnedByTenant;
  });
}

// Vehicle operations
export async function createVehicle(vehicleData: Partial<Vehicle>) {
  try {
    const tenantId = tenantStore.currentId;
    if (!tenantId) throw new Error('No tenant selected');

    return await callAdminFunction(
      'createVehicle',
      {
        tenantId,
        vehicleData: {
          brand: vehicleData.brand,
          model: vehicleData.model,
          color: vehicleData.color,
          year: vehicleData.year,
          registrationNumber: vehicleData.registrationNumber,
          maxPassengers: vehicleData.maxPassengers || 4,
        },
      },
      1
    ); // Requires ADMIN role
  } catch (error) {
    console.error('Error creating vehicle:', error);
    throw error;
  }
}

export async function updateVehicleStatus(vehicleId: string, isActive: boolean) {
  try {
    const tenantId = tenantStore.currentId;
    if (!tenantId) throw new Error('No tenant selected');

    return await callAdminFunction(
      'updateVehicleStatus',
      {
        tenantId,
        vehicleId,
        isActive,
      },
      1
    ); // Requires ADMIN role
  } catch (error) {
    console.error('Error updating vehicle status:', error);
    throw error;
  }
}
