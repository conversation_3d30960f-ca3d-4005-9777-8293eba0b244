import { fdb } from '$lib/firebase.client';
import { collection, query, onSnapshot, where, orderBy, updateDoc, doc, type Unsubscribe } from 'firebase/firestore';
import { getTenantCollection, tenantStore } from './tenant.svelte';

export enum FeedbackType {
  TRIP = 'trip',
  APPLICATION = 'application',
}

export enum FeedbackStatus {
  NEW = 'newFeedback',
  SEEN = 'seen',
  ADDRESSED = 'addressed',
  ARCHIVED = 'archived',
}

export interface Feedback {
  id: string;
  uid: string;
  type: FeedbackType;
  tripId?: string;
  rating?: number;
  message: string;
  images: string[];
  createdAt: Date;
  status: FeedbackStatus;
  chatSessionId?: string;
}

let feedbacks = $state<Feedback[]>([]);
let loading = $state(true);
let unsubscribe: Unsubscribe | null = null;
let currentTenantId: string | null = null;

export function init() {
  // Get current tenant ID
  const tenantId = tenantStore.currentId;

  // If tenant hasn't changed and we already have a subscription, don't re-initialize
  if (unsubscribe && currentTenantId === tenantId) return;

  // Clean up existing subscription if tenant changed
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }

  currentTenantId = tenantId;
  loading = true;

  if (!fdb) {
    console.error('Firestore not initialized');
    loading = false;
    return;
  }

  const q = query(collection(fdb, getTenantCollection('feedbacks')), orderBy('createdAt', 'desc'));

  unsubscribe = onSnapshot(q, (snapshot) => {
    feedbacks = snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        uid: data.uid,
        type: data.type as FeedbackType,
        tripId: data.tripId,
        rating: data.rating,
        message: data.message,
        images: data.images || [],
        createdAt: data.createdAt?.toDate() || new Date(),
        status: data.status as FeedbackStatus,
        chatSessionId: data.chatSessionId,
      } as Feedback;
    });
    loading = false;
  });
}

export function destroy() {
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }
  feedbacks = [];
  loading = true;
  currentTenantId = null;
}

// Re-initialize store when tenant changes (called after tenant switch)
export function reinitialize() {
  destroy();
  init();
}

export function getFeedbacks() {
  return feedbacks;
}

export function getFeedbackById(id: string) {
  return feedbacks.find((f) => f.id === id);
}

export function getFeedbacksByStatus(status: FeedbackStatus) {
  return feedbacks.filter((f) => f.status === status);
}

export function getFeedbacksByType(type: FeedbackType) {
  return feedbacks.filter((f) => f.type === type);
}

export function isLoading() {
  return loading;
}

export async function updateFeedbackStatus(feedbackId: string, status: FeedbackStatus) {
  if (!fdb) throw new Error('Firestore not initialized');
  try {
    await updateDoc(doc(fdb, getTenantCollection('feedbacks'), feedbackId), {
      status,
    });
  } catch (error) {
    console.error('Error updating feedback status:', error);
    throw error;
  }
}

export async function linkFeedbackToChat(feedbackId: string, chatSessionId: string) {
  if (!fdb) throw new Error('Firestore not initialized');
  try {
    await updateDoc(doc(fdb, getTenantCollection('feedbacks'), feedbackId), {
      chatSessionId,
    });
  } catch (error) {
    console.error('Error linking feedback to chat:', error);
    throw error;
  }
}
