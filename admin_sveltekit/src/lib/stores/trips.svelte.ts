import {
  collection,
  query,
  orderBy,
  limit,
  onSnapshot,
  type QueryDocumentSnapshot,
  type DocumentReference,
  doc,
  deleteDoc,
  getDocs,
  getDoc,
} from 'firebase/firestore';
import { fdb, callFunction, callAdminFunction } from '$lib/firebase.client';
import type { TripConfiguration } from './configurations.svelte';
import { decodePolyline } from '$lib/utils/polyline';
import type { MobileUser } from './mobile_users.svelte';
import { tenantStore, getTenantCollection } from './tenant.svelte';
import { getUsersMap } from './mobile_users.svelte';

export enum TripStatus {
  Preparing = 'preparing',
  RequestingDriver = 'requestingDriver',
  Reserved = 'reserved',
  DriverApproaching = 'driverApproaching',
  DriverAwaiting = 'driverAwaiting',
  InProgress = 'inProgress',
  Completed = 'completed',
  Cancelled = 'cancelled',
  Paid = 'paid',
}

export enum InTakeSource {
  Immediate = 'immediate',
  Reservation = 'reservation',
}

export enum ReservationType {
  Scheduled = 'scheduled',
  FullDay = 'fullDay',
}

export enum FullDayPriceType {
  Fixed = 'fixed',
  GasExcluded = 'gasExcluded',
}

export interface TripLocation {
  lat: number;
  lon: number;
}

export interface LatLngBounds {
  southwest: {
    lat: number;
    lng: number;
  };
  northeast: {
    lat: number;
    lng: number;
  };
}

export interface RouteData {
  distance: number;
  durationSec: number;
  polyline?: string;
  bounds: LatLngBounds;
  mapsPolylines: Array<{ lat: number; lng: number }>;
}

export interface RouteDataIds {
  main?: string;
  driver?: string;
  final?: string;
  overviews?: string[];
  selectedOverviewId?: string;
}

export interface Trip {
  id: string;
  uidPassenger: string;
  uidChosenDriver?: string;
  createdAt: Date;
  status: TripStatus;
  completedAt?: Date;
  cancelledAt?: Date;
  uidCancelledBy?: string;
  ref: DocumentReference;
  startLocation?: TripLocation;
  arrivalLocation?: TripLocation;
  driverLocation?: TripLocation;
  distanceTotalMeters?: number;
  routeData?: RouteData;
  driverRouteData?: RouteData;
  finalRouteData?: RouteData;
  paymentMethod?: string;
  customerRequestedPaymentMethod?: string;
  driverStartTime?: Date;
  passengerStartTime?: Date;
  driverAwaitingTime?: Date;
  passenger: {
    displayName?: string;
    phoneNumber?: string;
    photoURL?: string;
    [key: string]: any;
  };
  driver?: {
    displayName?: string;
    phoneNumber?: string;
    photoURL?: string;
    [key: string]: any;
  };
  driverDismissed: boolean;
  passengerDismissed: boolean;
  skippedDriverIds: string[];
  driverNotificationSent?: boolean;
  inTakeSource?: InTakeSource;
  clientAlarmTime?: Date;
  pickupTime?: Date;
  costPrepaid?: number;
  costTotal?: number;
  costDuration?: number;
  costDistance?: number;
  estimatedCost?: number; // Initial estimate shown to user
  realCost?: number; // Actual calculated cost based on logs
  realCostCurrency?: string; // Currency for real cost (default: 'MGA')
  costCappedAt10Percent?: boolean; // Flag if cost was capped
  adminOverrideCost?: number; // Manual override by admin
  adminOverrideReason?: string; // Reason for override
  startLocationName?: string;
  arrivalLocationName?: string;
  tripConfiguration: TripConfiguration;
  reservationType?: ReservationType;
  fullDayPriceType?: FullDayPriceType;
  routeDataIds?: RouteDataIds;
  passengerCount?: number;

  _timeTillPickupSec?: number;
  _isLive: boolean;
}

// Private state
let _trips = $state<Trip[]>([]);
let unsubscribe: (() => void) | null = null;
let updateTimeTillPickupInterval: NodeJS.Timeout;

// Public getter
export function getTrips() {
  return _trips;
}

function processRouteData(data: any): RouteData | undefined {
  if (!data) return undefined;

  const routeData: RouteData = {
    distance: data.distance,
    durationSec: data.durationSec,
    polyline: data.polyline,
    bounds: data.bounds,
    mapsPolylines: data.polyline ? decodePolyline(data.polyline) : [],
  };

  return routeData;
}

// Load route data from separate collection if routeDataIds exist
async function loadRouteDataIfNeeded(trip: Trip): Promise<void> {
  if (!trip.routeDataIds) return;
  if (!fdb) {
    console.error('Firestore not initialized');
    return;
  }

  // Load main route data if ID exists but embedded data doesn't
  if (trip.routeDataIds.main && !trip.routeData) {
    const routeDoc = await getDoc(doc(fdb, getTenantCollection('route_data'), trip.routeDataIds.main));
    if (routeDoc.exists()) {
      const data = routeDoc.data();
      trip.routeData = processRouteData(data.routeData);
    }
  }

  // Load driver route data if ID exists but embedded data doesn't
  if (trip.routeDataIds.driver && !trip.driverRouteData) {
    const routeDoc = await getDoc(doc(fdb, getTenantCollection('route_data'), trip.routeDataIds.driver));
    if (routeDoc.exists()) {
      const data = routeDoc.data();
      trip.driverRouteData = processRouteData(data.routeData);
    }
  }

  // Load final route data if ID exists but embedded data doesn't
  if (trip.routeDataIds.final && !trip.finalRouteData) {
    const routeDoc = await getDoc(doc(fdb, getTenantCollection('route_data'), trip.routeDataIds.final));
    if (routeDoc.exists()) {
      const data = routeDoc.data();
      trip.finalRouteData = processRouteData(data.routeData);
    }
  }
}

function updateTimeTillPickup() {
  _trips = _trips.map((trip) => ({
    ...trip,
    _timeTillPickupSec:
      trip.inTakeSource === InTakeSource.Reservation && trip.pickupTime
        ? Math.floor((trip.pickupTime.getTime() - new Date().getTime()) / 1000)
        : undefined,
  }));
}

export function init() {
  if (unsubscribe) return; // Already initialized
  if (!fdb) {
    console.error('Firestore not initialized');
    return;
  }

  const tripsRef = collection(fdb, getTenantCollection('trips'));
  const q = query(tripsRef, orderBy('createdAt', 'desc'), limit(50));

  unsubscribe = onSnapshot(q, async (snapshot) => {
    const trips = await Promise.all(
      snapshot.docs.map(async (doc: QueryDocumentSnapshot) => {
        const data = doc.data();
        const tripData = {
          id: doc.id,
          ref: doc.ref,
          ...data,
          createdAt: data.createdAt?.toDate(),
          completedAt: data.completedAt?.toDate(),
          cancelledAt: data.cancelledAt?.toDate(),
          driverStartTime: data.driverStartTime?.toDate(),
          passengerStartTime: data.passengerStartTime?.toDate(),
          driverAwaitingTime: data.driverAwaitingTime?.toDate(),
          clientAlarmTime: data.clientAlarmTime?.toDate(),
          pickupTime: data.pickupTime?.toDate(),
          driverDismissed: data.driverDismissed ?? false,
          passengerDismissed: data.passengerDismissed ?? false,
          skippedDriverIds: data.skippedDriverIds ?? [],
          routeData: processRouteData(data.routeData),
          driverRouteData: processRouteData(data.driverRouteData),
          finalRouteData: processRouteData(data.finalRouteData),

          // Ensure all cost fields are properly converted to numbers
          costPrepaid: data.costPrepaid != null ? Number(data.costPrepaid) : undefined,
          costTotal: data.costTotal != null ? Number(data.costTotal) : undefined,
          costDuration: data.costDuration != null ? Number(data.costDuration) : undefined,
          costDistance: data.costDistance != null ? Number(data.costDistance) : undefined,
          distanceTotalMeters: data.distanceTotalMeters != null ? Number(data.distanceTotalMeters) : undefined,

          // Handle reservation type and full day price type
          reservationType: data.reservationType ? (data.reservationType as ReservationType) : undefined,
          fullDayPriceType: data.fullDayPriceType ? (data.fullDayPriceType as FullDayPriceType) : undefined,
          inTakeSource: data.inTakeSource ? (data.inTakeSource as InTakeSource) : undefined,
          routeDataIds: data.routeDataIds,
          passengerCount: data.passengerCount != null ? Number(data.passengerCount) : 1,

          _timeTillPickupSec:
            data.pickupTime && data.inTakeSource === 'reservation'
              ? Math.floor((data.pickupTime.toDate().getTime() - new Date().getTime()) / 1000)
              : undefined,
          _isLive: [TripStatus.DriverApproaching, TripStatus.DriverAwaiting, TripStatus.InProgress].includes(data.status),
        } as Trip;

        // Load route data from separate collection if needed
        await loadRouteDataIfNeeded(tripData);

        return tripData;
      })
    );

    _trips = trips;

    // Start the interval to update _timeTillPickupSec
    if (updateTimeTillPickupInterval) clearInterval(updateTimeTillPickupInterval);
    updateTimeTillPickupInterval = setInterval(updateTimeTillPickup, 10000);
  });
}

export function destroy() {
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }
  if (updateTimeTillPickupInterval) {
    clearInterval(updateTimeTillPickupInterval);
  }
}

export async function cancelTrip(tripId: string, uidCancelledBy: string): Promise<void> {
  try {
    await callAdminFunction(
      'cancelTrip',
      {
        tripId,
        uidCancelledBy,
        tenantId: tenantStore.currentId,
      },
      0
    ); // MANAGER role required
  } catch (error) {
    console.error('Error cancelling trip:', error);
    throw error;
  }
}

// State machine transition functions

export async function confirmPayment(tripId: string): Promise<void> {
  try {
    await callAdminFunction(
      'transitionTripState',
      {
        tripId,
        tenantId: tenantStore.currentId,
        event: { type: 'CONFIRM_PAYMENT' },
      },
      0
    ); // MANAGER role required
  } catch (error) {
    console.error('Error confirming payment:', error);
    throw error;
  }
}

export async function requestDriver(
  tripId: string,
  driverUid: string,
  driverLocation: { lat: number; lon: number }
): Promise<void> {
  try {
    await callAdminFunction(
      'requestDriverTransition',
      {
        tripId,
        tenantId: tenantStore.currentId,
        driverUid,
        driverLocation,
      },
      0
    ); // MANAGER role required
  } catch (error) {
    console.error('Error requesting driver:', error);
    throw error;
  }
}

export async function completeTrip(tripId: string, finalRouteData?: any, costData?: any): Promise<void> {
  try {
    await callAdminFunction(
      'completeTripTransition',
      {
        tripId,
        tenantId: tenantStore.currentId,
        finalRouteData,
        costData,
      },
      0
    ); // MANAGER role required
  } catch (error) {
    console.error('Error completing trip:', error);
    throw error;
  }
}

export async function adminRequestDriver(
  tripId: string,
  selectedDriver: MobileUser,
  driverLocation: { lat: number; lon: number }
): Promise<void> {
  try {
    await callAdminFunction(
      'adminRequestDriver',
      {
        tripId,
        tenantId: tenantStore.currentId,
        driverUid: selectedDriver.uid,
        driverLocation,
      },
      1
    ); // ADMIN role required
  } catch (error) {
    console.error('Error requesting driver:', error);
    throw error;
  }
}

export async function adminAssignDriver(tripId: string, selectedDriver: MobileUser): Promise<number> {
  try {
    const response = await callAdminFunction<{ tripId: string; driverUid: string; tenantId: string }, { result: number }>(
      'adminAssignDriver',
      {
        tripId,
        tenantId: tenantStore.currentId,
        driverUid: selectedDriver.uid,
      },
      1
    ); // ADMIN role required

    return response.result;
  } catch (error) {
    console.error('Error assigning driver:', error);
    throw error;
  }
}

export async function deleteTrip(tripId: string) {
  if (!fdb) throw new Error('Firestore not initialized');
  const tripRef = doc(fdb, getTenantCollection('trips'), tripId);
  const logsRef = collection(fdb, `${getTenantCollection('trips')}/${tripId}/logs`);
  const logs = await getDocs(logsRef);
  logs.forEach((doc) => {
    deleteDoc(doc.ref);
  });
  await deleteDoc(tripRef);
}

export async function updateSkippedDrivers(tripId: string, skippedDriverIds: string[], operation: 'set' | 'remove' = 'set') {
  try {
    await callFunction('updateSkippedDrivers', {
      tripId,
      tenantId: tenantStore.currentId,
      skippedDriverIds,
      operation,
    });
  } catch (error) {
    console.error('Error updating skipped drivers:', error);
    throw error;
  }
}

// Utility functions for pickup time display
export function isPickupPhaseComplete(status: TripStatus): boolean {
  return [TripStatus.DriverAwaiting, TripStatus.InProgress, TripStatus.Completed, TripStatus.Cancelled, TripStatus.Paid].includes(
    status
  );
}

export function formatTimeUntilPickup(seconds: number | undefined, _formatDateFn: (date: Date) => string): string {
  if (seconds === undefined) return '';

  const absSeconds = Math.abs(seconds);
  const hours = Math.floor(absSeconds / 3600);
  const minutes = Math.floor((absSeconds % 3600) / 60);
  const secs = Math.floor(absSeconds % 60);

  if (seconds < 0) {
    if (hours > 0) return `${hours}h ${minutes}m ago`;
    if (minutes > 0) return `${minutes}m ${secs}s ago`;
    return `${secs}s ago`;
  }

  if (absSeconds < 60) {
    return `in ${secs}s`;
  } else if (hours === 0) {
    return `in ${minutes}m ${secs}s`;
  }
  return `in ${hours}h ${minutes}m`;
}

export function getPickupTimeStyle(seconds: number | undefined, status: TripStatus): string {
  if (seconds === undefined) return '';

  // If more than 10 minutes in the past or trip is past pickup phase, show grey
  if (seconds < -600 || isPickupPhaseComplete(status)) {
    return 'color: rgb(156 163 175);'; // Gray-400
  }

  if (seconds < 0) {
    return 'color: rgb(249 115 22);'; // Orange-500
  }

  // Calculate color interpolation between green and orange
  // 2400 seconds (40 minutes) is fully green
  // 0 seconds is fully orange
  const progress = Math.min(Math.max(seconds / 2400, 0), 1);

  // Interpolate between orange (rgb(249, 115, 22)) and green (rgb(34, 197, 94))
  const r = Math.round(249 - (249 - 34) * progress);
  const g = Math.round(115 + (197 - 115) * progress);
  const b = Math.round(22 + (94 - 22) * progress);

  return `color: rgb(${r}, ${g}, ${b}); transition: color 0.5s ease-in-out;`;
}

let followedTripId = $state<string | null>(null);

export function getFollowedTripId() {
  return followedTripId;
}

export function doFollowTrip(tripId: string) {
  followedTripId = tripId;
}

export function stopFollowingTrip() {
  followedTripId = null;
}

// Get driver position from mobile_users store instead of separate tracking
export function getLatestDriverPosition(driverUid: string): TripLocation | undefined {
  const driver = getUsersMap().get(driverUid);
  if (driver && driver.lat && driver.lon) {
    return { lat: driver.lat, lon: driver.lon };
  }
  return undefined;
}

export function getLatestTripPosition(tripId: string): TripLocation | undefined {
  const trip = _trips.find((t) => t.id === tripId);
  if (trip?.uidChosenDriver) {
    return getLatestDriverPosition(trip.uidChosenDriver);
  }
  return undefined;
}

// Deprecated functions - kept for compatibility but do nothing
export function startTrackingTripPosition(tripId: string) {
  // No longer needed - positions come from mobile_users store
}

export function startTrackingDriverPosition(driverUid: string) {
  // No longer needed - positions come from mobile_users store
}

export function stopTrackingTripPosition(tripId: string) {
  // No longer needed - positions come from mobile_users store
}

export function stopTrackingDriverPosition(driverUid: string) {
  // No longer needed - positions come from mobile_users store
}

export function stopTrackingAllPositions() {
  // No longer needed - positions come from mobile_users store
}

/**
 * Admin function to manually override trip cost
 */
export async function adminOverrideTripCost(tripId: string, newCost: number, reason?: string): Promise<void> {
  try {
    await callAdminFunction(
      'adminOverrideTripCost',
      {
        tripId,
        newCost,
        reason: reason || 'Manual admin adjustment',
        tenantId: tenantStore.currentId,
      },
      1
    ); // ADMIN role required
  } catch (error) {
    console.error('Error overriding trip cost:', error);
    throw error;
  }
}
