import type { TripLocation } from './trips.svelte';
import type { MobileUser } from './mobile_users.svelte';

// Helper function to build custom marker content
function buildCustomMarkerContent(options: any): HTMLElement {
  const content = document.createElement('div');
  content.style.position = 'relative';

  if (options.icon) {
    const img = document.createElement('img');
    img.src = options.icon.url;

    if (options.icon.scaledSize) {
      img.style.width = `${options.icon.scaledSize.width}px`;
      img.style.height = `${options.icon.scaledSize.height}px`;
    }

    // Apply opacity if specified
    if (options.opacity !== undefined) {
      img.style.opacity = options.opacity.toString();
    }

    content.appendChild(img);
  } else if (options.label) {
    // Create a marker with label
    const pin = document.createElement('div');
    pin.style.width = '30px';
    pin.style.height = '40px';
    pin.style.position = 'relative';
    pin.style.cursor = 'pointer';

    // Apply opacity to the entire pin if specified
    if (options.opacity !== undefined) {
      pin.style.opacity = options.opacity.toString();
    }

    // Create pin background
    const pinBg = document.createElement('div');
    pinBg.style.width = '30px';
    pinBg.style.height = '30px';
    pinBg.style.borderRadius = '50% 50% 50% 0';
    pinBg.style.background = options.color || '#ea4335';
    pinBg.style.position = 'absolute';
    pinBg.style.transform = 'rotate(-45deg)';
    pinBg.style.left = '50%';
    pinBg.style.top = '50%';
    pinBg.style.margin = '-20px 0 0 -15px';

    // Create label text
    const label = document.createElement('div');
    label.textContent = options.label;
    label.style.position = 'absolute';
    label.style.color = 'white';
    label.style.fontSize = '14px';
    label.style.fontWeight = 'bold';
    label.style.top = '8px';
    label.style.left = '50%';
    label.style.transform = 'translateX(-50%)';
    label.style.zIndex = '1';

    pin.appendChild(pinBg);
    pin.appendChild(label);
    content.appendChild(pin);
  } else {
    // Create a default marker pin when no icon or label
    const pin = document.createElement('div');
    pin.style.width = '30px';
    pin.style.height = '40px';
    pin.style.position = 'relative';
    pin.style.cursor = 'pointer';

    // Apply opacity if specified
    if (options.opacity !== undefined) {
      pin.style.opacity = options.opacity.toString();
    }

    // Create pin background
    const pinBg = document.createElement('div');
    pinBg.style.width = '30px';
    pinBg.style.height = '30px';
    pinBg.style.borderRadius = '50% 50% 50% 0';
    pinBg.style.background = options.color || '#ea4335';
    pinBg.style.position = 'absolute';
    pinBg.style.transform = 'rotate(-45deg)';
    pinBg.style.left = '50%';
    pinBg.style.top = '50%';
    pinBg.style.margin = '-20px 0 0 -15px';

    // Add initials if provided
    if (options.initials) {
      const initials = document.createElement('div');
      initials.textContent = options.initials;
      initials.style.position = 'absolute';
      initials.style.color = 'white';
      initials.style.fontSize = '12px';
      initials.style.fontWeight = 'bold';
      initials.style.top = '8px';
      initials.style.left = '50%';
      initials.style.transform = 'translateX(-50%)';
      initials.style.zIndex = '1';
      initials.style.lineHeight = '1';
      pin.appendChild(initials);
    }

    pin.appendChild(pinBg);
    content.appendChild(pin);
  }

  return content;
}

interface MapElements {
  polylines: Map<string, google.maps.Polyline>;
  markers: Map<string, google.maps.marker.AdvancedMarkerElement>;
  circles: Map<string, google.maps.Circle>;
}

interface MapStore {
  map: google.maps.Map | null;
  elements: MapElements;
}

// Private state
let _store = $state<MapStore>({
  map: null,
  elements: {
    polylines: new Map(),
    markers: new Map(),
    circles: new Map(),
  },
});

// Public getter
export function getMapStore() {
  return _store;
}

export function setMap(map: google.maps.Map) {
  _store.map = map;
  return map;
}

export function addPolyline(
  id: string,
  path: google.maps.LatLngLiteral[] | google.maps.LatLng[],
  options: google.maps.PolylineOptions = {}
) {
  if (!_store.map) return null;

  // Remove existing polyline with same ID if it exists
  const existingPolyline = _store.elements.polylines.get(id);
  if (existingPolyline) {
    existingPolyline.setMap(null);
  }

  // Create and add new polyline
  const polyline = new google.maps.Polyline({
    path,
    map: _store.map,
    ...options,
  });

  // Immediately show polyline
  polyline.setMap(_store.map);

  _store.elements.polylines.set(id, polyline);
  return polyline;
}

export function removePolyline(id: string) {
  const polyline = _store.elements.polylines.get(id);
  if (polyline) {
    polyline.setMap(null);
    _store.elements.polylines.delete(id);
  }
}

export function clearPolylines() {
  // Remove all polylines from the map
  _store.elements.polylines.forEach((polyline) => {
    polyline.setMap(null);
  });
  // Clear the polylines map
  _store.elements.polylines.clear();
}

export function centerMap(lat: number, lng: number, zoom?: number) {
  if (!_store.map) return;

  _store.map.panTo({ lat, lng });
  if (zoom !== undefined) {
    _store.map.setZoom(zoom);
  }
}

export function smoothCenterMap(lat: number, lng: number, zoom?: number) {
  if (!_store.map) return;

  // Use panTo for smooth movement
  const center = new google.maps.LatLng(lat, lng);
  _store.map.panTo(center);

  if (zoom !== undefined) {
    _store.map.setZoom(zoom);
  }
}

export function fitBounds(bounds: google.maps.LatLngBoundsLiteral, padding: number = 50) {
  if (!_store.map) return;

  const googleBounds = new google.maps.LatLngBounds(
    new google.maps.LatLng(bounds.south, bounds.west),
    new google.maps.LatLng(bounds.north, bounds.east)
  );

  _store.map.fitBounds(googleBounds, padding);
}

export function addMarker(id: string, position: google.maps.LatLngLiteral | TripLocation, options: any = {}) {
  if (!_store.map) return null;

  const existingMarker = _store.elements.markers.get(id);
  const markerPosition = 'lon' in position ? new google.maps.LatLng(position.lat, position.lon) : position;

  if (existingMarker) {
    existingMarker.position = markerPosition;
    if (options.title !== undefined) existingMarker.title = options.title;

    // Update content if icon, label, opacity, or initials changed
    if (options.icon || options.label || options.opacity !== undefined || options.initials) {
      existingMarker.content = buildCustomMarkerContent(options);
    }

    return existingMarker;
  } else {
    const markerOptions: any = {
      position: markerPosition,
      map: _store.map,
      title: options.title,
    };

    // Add custom content for icon, label, or initials
    if (options.icon || options.label || options.initials || options.opacity !== undefined) {
      markerOptions.content = buildCustomMarkerContent(options);
    }

    const marker = new google.maps.marker.AdvancedMarkerElement(markerOptions);

    _store.elements.markers.set(id, marker);
    return marker;
  }
}

export function removeMarker(id: string) {
  const marker = _store.elements.markers.get(id);
  if (marker) {
    marker.map = null;
    _store.elements.markers.delete(id);
  }
}

export function clearMarkers() {
  // Remove all markers from the map
  _store.elements.markers.forEach((marker) => {
    marker.map = null;
  });
  // Clear the markers map
  _store.elements.markers.clear();
}

export function updateMobileUsersMarkers(prefix: string, users: MobileUser[], options: any = {}) {
  if (!_store.map) return;

  // Get all existing marker IDs for this prefix
  const existingMarkerIds = new Set<string>();
  _store.elements.markers.forEach((_, id) => {
    if (id.startsWith(prefix)) {
      existingMarkerIds.add(id);
    }
  });

  // Process each user
  users.forEach((user) => {
    if (!user.lat || !user.lon) return;

    const markerId = `${prefix}${user.uid}`;
    existingMarkerIds.delete(markerId); // Remove from set as we process it

    const position = new google.maps.LatLng(user.lat, user.lon);
    const markerTitle = user.displayName || user.phoneNumber || '';

    const existingMarker = _store.elements.markers.get(markerId);
    if (existingMarker) {
      // Update existing marker
      existingMarker.position = position;
      existingMarker.title = markerTitle;
      existingMarker.map = _store.map;

      // Build options including user initials
      const contentOptions = { ...options };
      if ((user as any).initials) {
        contentOptions.initials = (user as any).initials;
      }

      // Update content if icon, opacity, or initials changed
      if (options.icon || options.opacity !== undefined || contentOptions.initials) {
        existingMarker.content = buildCustomMarkerContent(contentOptions);
      }
    } else {
      // Create new marker
      const markerOptions: any = {
        position,
        map: _store.map,
        title: markerTitle,
      };

      // Build options including user initials
      const contentOptions = { ...options };
      if ((user as any).initials) {
        contentOptions.initials = (user as any).initials;
      }

      // Add custom content for icon, opacity, or initials
      if (options.icon || options.opacity !== undefined || contentOptions.initials) {
        markerOptions.content = buildCustomMarkerContent(contentOptions);
      }

      const marker = new google.maps.marker.AdvancedMarkerElement(markerOptions);
      _store.elements.markers.set(markerId, marker);
    }
  });

  // Remove any remaining markers with this prefix that weren't in the users list
  existingMarkerIds.forEach((id) => {
    const marker = _store.elements.markers.get(id);
    if (marker) {
      marker.map = null;
      _store.elements.markers.delete(id);
    }
  });
}

export function removeMobileUsersMarkers(prefix: string) {
  // Find and remove all markers that start with the given prefix
  _store.elements.markers.forEach((marker, id) => {
    if (id.startsWith(prefix)) {
      marker.map = null;
      _store.elements.markers.delete(id);
    }
  });
}

export function addCircle(id: string, center: google.maps.LatLngLiteral, options: google.maps.CircleOptions = {}) {
  if (!_store.map) return null;

  const existingCircle = _store.elements.circles.get(id);
  if (existingCircle) {
    existingCircle.setMap(null);
  }

  const circle = new google.maps.Circle({
    center,
    map: _store.map,
    ...options,
  });

  // Immediately show circle
  circle.setMap(_store.map);

  _store.elements.circles.set(id, circle);
  return circle;
}

export function removeCircle(id: string) {
  const circle = _store.elements.circles.get(id);
  if (circle) {
    circle.setMap(null);
    _store.elements.circles.delete(id);
  }
}

export function reset() {
  // Clear all polylines
  _store.elements.polylines.forEach((polyline) => polyline.setMap(null));
  _store.elements.polylines.clear();

  // Clear all markers
  _store.elements.markers.forEach((marker) => (marker.map = null));
  _store.elements.markers.clear();

  // Clear all circles
  _store.elements.circles.forEach((circle) => circle.setMap(null));
  _store.elements.circles.clear();
}

export function cleanup() {
  _store.elements.polylines.forEach((polyline) => polyline.setMap(null));
  _store.elements.markers.forEach((marker) => (marker.map = null));
  _store.elements.circles.forEach((circle) => circle.setMap(null));
  _store.map = null;
}

export function addMarkerAndCenter(
  id: string,
  position: google.maps.LatLngLiteral | TripLocation,
  options: google.maps.MarkerOptions = {},
  shouldCenter: boolean = false
) {
  if (!_store.map) return null;

  const marker = addMarker(id, position, options);

  if (shouldCenter && marker) {
    if ('lon' in position) {
      // TripLocation
      smoothCenterMap(position.lat, position.lon);
    } else {
      // google.maps.LatLngLiteral
      smoothCenterMap(position.lat, position.lng);
    }
  }

  return marker;
}
