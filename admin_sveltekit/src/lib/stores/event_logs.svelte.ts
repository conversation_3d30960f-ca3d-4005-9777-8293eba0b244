import { onSnapshot, type DocumentReference, type Timestamp, collection, query, orderBy, limit } from 'firebase/firestore';
import { fdb } from '$lib/firebase.client';
import { tenantStore } from './tenant.svelte';

export enum EventLogType {
  DRIVER_SERVICE_STATUS_UPDATE = 'DRIVER_SERVICE_STATUS_UPDATE',
  DRIVER_TRIP_REJECTED = 'DRIVER_TRIP_REJECTED',
  ADMIN_TRIP_COST_OVERRIDE = 'adminTripCostOverride',
}

export enum ServiceStatusReasonType {
  MORNING_SERVICE_START = 'MORNING_SERVICE_START',
  EVENING_SERVICE_START = 'EVENING_SERVICE_START',
  LUNCH_BREAK = 'LUNCH_BREAK',
  PRAYER_BREAK = 'PRAYER_BREAK',
  FUEL_REFILL = 'FUEL_REFILL',
  VEHICLE_MAINTENANCE = 'VEHICLE_MAINTENANCE',
  END_OF_SHIFT = 'END_OF_SHIFT',
  EMERGENCY_STOP = 'EMERGENCY_STOP',
  SWITCH_ACTIVITY = 'SWITCH_ACTIVITY',
  APP_RELAUNCH = 'APP_RELAUNCH',
  CUSTOM = 'CUSTOM',
}

export enum TripRejectionReasonType {
  VEHICLE_MALFUNCTION = 'VEHICLE_MALFUNCTION',
  TOO_FAR_PICKUP = 'TOO_FAR_PICKUP',
  HEAVY_TRAFFIC = 'HEAVY_TRAFFIC',
  UNSAFE_AREA = 'UNSAFE_AREA',
  ENDING_SHIFT_SOON = 'ENDING_SHIFT_SOON',
  VEHICLE_CLEANING = 'VEHICLE_CLEANING',
  PASSENGER_CAPACITY_FULL = 'PASSENGER_CAPACITY_FULL',
  BATTERY_LOW = 'BATTERY_LOW',
  WEATHER_CONDITIONS = 'WEATHER_CONDITIONS',
  CUSTOM = 'CUSTOM',
}

export interface Driver {
  uid: string;
  displayName?: string;
  photoURL?: string;
  email?: string;
}

export interface Trip {
  id: string;
  arrivalLocationName?: string;
  startLocationName?: string;
  originalCost?: number;
  newCost?: number;
  realCost?: number;
}

export interface EventLog {
  id: string;
  uid: string;
  type: EventLogType | string;
  timestamp: Date;
  driver: Driver;
  trip?: Trip;
  reason?: string;
  serviceStatusReasonType?: ServiceStatusReasonType;
  tripRejectionReasonType?: TripRejectionReasonType;
  metadata?: Record<string, any>;
  ref: DocumentReference;
}

// Private state
let _eventLogs = $state<EventLog[]>([]);
let _eventLogsMap = $state<Map<string, EventLog>>(new Map());

// Public getters
export function getEventLogs() {
  return _eventLogs;
}

export function getEventLogsMap() {
  return _eventLogsMap;
}

let unsubscribe: (() => void) | null = null;

export function init() {
  if (unsubscribe) return;
  if (!fdb) {
    console.error('Firestore not initialized');
    return;
  }

  const eventLogsRef = collection(fdb, tenantStore.getTenantPath('event_logs'));
  const eventLogsQuery = query(eventLogsRef, orderBy('timestamp', 'desc'), limit(100));

  unsubscribe = onSnapshot(eventLogsQuery, (snapshot) => {
    _eventLogs = snapshot.docs.map((doc) => {
      const data = doc.data();
      const eventLog = {
        id: doc.id,
        uid: data.uid,
        type: data.type as EventLogType | string,
        timestamp: (data.timestamp as Timestamp).toDate(),
        driver: data.driver
          ? {
              uid: data.driver.uid || data.uid,
              displayName: data.driver.displayName,
              photoURL: data.driver.photoURL,
              email: data.driver.email,
            }
          : {
              uid: data.uid,
              displayName: undefined,
              photoURL: undefined,
              email: undefined,
            },
        trip: data.trip
          ? {
              id: data.trip.id,
              arrivalLocationName: data.trip.arrivalLocationName,
              startLocationName: data.trip.startLocationName,
              originalCost: data.trip.originalCost,
              newCost: data.trip.newCost,
              realCost: data.trip.realCost,
            }
          : undefined,
        reason: data.reason,
        serviceStatusReasonType: data.serviceStatusReasonType as ServiceStatusReasonType | undefined,
        tripRejectionReasonType: data.tripRejectionReasonType as TripRejectionReasonType | undefined,
        metadata: data.metadata,
        ref: doc.ref,
      } as EventLog;

      // Update the lookup map
      _eventLogsMap.set(eventLog.id, eventLog);
      return eventLog;
    });
  });
}

export function destroy() {
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }
}

export function getEventTypeLabel(type: EventLogType | string): string {
  // Handle special cases first
  if (type === 'adminTripCostOverride' || type === EventLogType.ADMIN_TRIP_COST_OVERRIDE) {
    return 'Admin Trip Cost Override';
  }

  // Default formatting for other types
  return type
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}
