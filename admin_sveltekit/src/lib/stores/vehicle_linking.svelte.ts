import {
  collection,
  onSnapshot,
  query,
  where,
  orderBy,
  type QuerySnapshot,
  type DocumentData,
  type Timestamp,
} from 'firebase/firestore';
import { fdb } from '$lib/firebase.client';
import { tenantStore } from './tenant.svelte';
import { callAdminFunction } from '$lib/firebase.client';
import { getVehiclesMap } from './vehicles.svelte';
import { getUsersMap } from './mobile_users.svelte';

export interface VehicleLinking {
  id?: string;
  vehicleId: string;
  tenantId: string;

  // Tenant-specific vehicle state
  tenantRemark?: string;
  tenantApproved?: boolean;
  currentDriverId?: string;

  // Linking metadata
  linkedAt: Date;
  linkedBy: string;
  isActive: boolean;

  // For tenant-owned vehicles
  isOwnedByTenant: boolean;

  // Derived fields
  vehicle?: any;
  currentDriver?: any;
}

// Private state
let _linkings = $state<VehicleLinking[]>([]);
let _linkingsMap = $state<Map<string, VehicleLinking>>(new Map());
let _loading = $state(true);
let _error = $state<string | null>(null);

// Public getters
export function getLinkings() {
  return _linkings;
}

export function getLinkingsMap() {
  return _linkingsMap;
}

export function getLoading() {
  return _loading;
}

export function getError() {
  return _error;
}

let unsubscribe: (() => void) | null = null;

export function init() {
  if (unsubscribe) return;

  _loading = true;
  _error = null;

  const tenantId = tenantStore.currentId;
  if (!tenantId) {
    _error = 'No tenant selected';
    _loading = false;
    return;
  }

  if (!fdb) {
    _error = 'Firestore not initialized';
    _loading = false;
    return;
  }

  try {
    const linkingsRef = collection(fdb, tenantStore.getTenantPath('vehicles_linking'));
    const q = query(linkingsRef, where('isActive', '==', true), orderBy('linkedAt', 'desc'));

    unsubscribe = onSnapshot(
      q,
      (snapshot: QuerySnapshot<DocumentData>) => {
        const vehiclesMap = getVehiclesMap();
        const usersMap = getUsersMap();

        // Clear the map before updating to ensure removed documents are not retained
        _linkingsMap.clear();

        _linkings = snapshot.docs.map((doc) => {
          const data = doc.data();
          const linking: VehicleLinking = {
            id: doc.id,
            vehicleId: data.vehicleId,
            tenantId: data.tenantId,
            tenantRemark: data.tenantRemark,
            tenantApproved: data.tenantApproved,
            currentDriverId: data.currentDriverId,
            linkedAt: (data.linkedAt as Timestamp).toDate(),
            linkedBy: data.linkedBy,
            isActive: data.isActive,
            isOwnedByTenant: data.isOwnedByTenant,
            vehicle: vehiclesMap.get(data.vehicleId),
            currentDriver: data.currentDriverId ? usersMap.get(data.currentDriverId) : null,
          };

          _linkingsMap.set(doc.id, linking);
          return linking;
        });

        // Force reactivity update by creating a new Map
        _linkingsMap = new Map(_linkingsMap);

        _loading = false;
      },
      (error) => {
        console.error('Error loading vehicle linkings:', error);
        _error = error.message;
        _loading = false;
      }
    );
  } catch (error) {
    console.error('Error initializing vehicle linkings:', error);
    _error = error instanceof Error ? error.message : 'Unknown error';
    _loading = false;
  }
}

export function destroy() {
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }
  _linkings = [];
  _linkingsMap.clear();
  _loading = true;
  _error = null;
}

// Get linkings by status
export function getApprovedLinkings() {
  return _linkings.filter((linking) => linking.tenantApproved === true);
}

export function getPendingApprovalLinkings() {
  return _linkings.filter((linking) => linking.tenantApproved === false || linking.tenantApproved === undefined);
}

export function getAssignedLinkings() {
  return _linkings.filter((linking) => linking.currentDriverId);
}

export function getUnassignedLinkings() {
  return _linkings.filter((linking) => !linking.currentDriverId && linking.tenantApproved);
}

// Get linking by vehicle ID
export function getLinkingByVehicleId(vehicleId: string) {
  return _linkings.find((linking) => linking.vehicleId === vehicleId);
}

// Get linkings by driver
export function getLinkingsByDriver(driverUID: string) {
  return _linkings.filter((linking) => linking.currentDriverId === driverUID);
}

// Vehicle linking operations
export async function linkVehicleToTenant(vehicleId: string) {
  try {
    const tenantId = tenantStore.currentId;
    if (!tenantId) throw new Error('No tenant selected');

    return await callAdminFunction(
      'linkVehicleToTenant',
      {
        tenantId,
        vehicleId,
      },
      1
    ); // Requires ADMIN role
  } catch (error) {
    console.error('Error linking vehicle to tenant:', error);
    throw error;
  }
}

export async function approveUserVehicle(linkingId: string, approved: boolean, remark?: string) {
  try {
    const tenantId = tenantStore.currentId;
    if (!tenantId) throw new Error('No tenant selected');

    return await callAdminFunction(
      'approveUserVehicle',
      {
        tenantId,
        linkingId,
        approved,
        remark,
      },
      1
    ); // Requires ADMIN role
  } catch (error) {
    console.error('Error approving user vehicle:', error);
    throw error;
  }
}

export async function assignVehicleToDriver(linkingId: string, driverUID: string) {
  try {
    const tenantId = tenantStore.currentId;
    if (!tenantId) throw new Error('No tenant selected');

    const linking = _linkingsMap.get(linkingId);
    if (!linking) throw new Error('Vehicle linking not found');

    return await callAdminFunction(
      'assignVehicleToDriver',
      {
        tenantId,
        vehicleId: linking.vehicleId,
        linkingId,
        driverUID,
      },
      1
    ); // Requires ADMIN role
  } catch (error) {
    console.error('Error assigning vehicle to driver:', error);
    throw error;
  }
}

export async function unassignVehicleFromDriver(linkingId: string) {
  try {
    const tenantId = tenantStore.currentId;
    if (!tenantId) throw new Error('No tenant selected');

    const linking = _linkingsMap.get(linkingId);
    if (!linking || !linking.currentDriverId) throw new Error('No active assignment found');

    return await callAdminFunction(
      'unassignVehicleFromDriver',
      {
        tenantId,
        vehicleId: linking.vehicleId,
        linkingId,
        driverUID: linking.currentDriverId,
      },
      1
    ); // Requires ADMIN role
  } catch (error) {
    console.error('Error unassigning vehicle from driver:', error);
    throw error;
  }
}

export async function unlinkVehicleFromTenant(linkingId: string) {
  try {
    const tenantId = tenantStore.currentId;
    if (!tenantId) throw new Error('No tenant selected');

    const linking = _linkingsMap.get(linkingId);
    if (!linking) throw new Error('Vehicle linking not found');

    return await callAdminFunction(
      'unlinkVehicleFromTenant',
      {
        tenantId,
        vehicleId: linking.vehicleId,
        linkingId,
      },
      1
    ); // Requires ADMIN role
  } catch (error) {
    console.error('Error unlinking vehicle from tenant:', error);
    throw error;
  }
}
