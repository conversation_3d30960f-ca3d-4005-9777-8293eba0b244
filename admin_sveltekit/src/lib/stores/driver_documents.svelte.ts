import {
  collection,
  collectionGroup,
  doc,
  onSnapshot,
  query,
  where,
  orderBy,
  type QuerySnapshot,
  type DocumentData,
  type Timestamp,
} from 'firebase/firestore';
import { fdb } from '$lib/firebase.client';
import { tenantStore } from './tenant.svelte';
import { callAdminFunction } from '$lib/firebase.client';

export enum DocumentType {
  license = 'license',
  insurance = 'insurance',
  vehicleRegistration = 'vehicleRegistration',
  nationalId = 'nationalId',
  other = 'other',
}

export enum DocumentStatus {
  pendingReview = 'pendingReview',
  approved = 'approved',
  rejected = 'rejected',
  expired = 'expired',
  expiringSoon = 'expiringSoon',
}

export interface DriverDocument {
  id?: string;
  documentType: DocumentType;
  documentName: string;
  fileURL: string;
  expiryDate: Date;
  status: DocumentStatus;
  notes?: string;
  uploadedAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
  adminNotes?: string;
  tenantIDs: string[];

  // Derived fields
  driverUID?: string;
  driverName?: string;
}

// Private state
let _documents = $state<DriverDocument[]>([]);
let _documentsMap = $state<Map<string, DriverDocument>>(new Map());
let _loading = $state(true);
let _error = $state<string | null>(null);

// Filter state
let _statusFilter = $state<DocumentStatus | null>(null);
let _typeFilter = $state<DocumentType | null>(null);
let _driverFilter = $state<string | null>(null);

// Public getters
export function getDocuments() {
  return _documents;
}

export function getDocumentsMap() {
  return _documentsMap;
}

export function getLoading() {
  return _loading;
}

export function getError() {
  return _error;
}

export function getStatusFilter() {
  return _statusFilter;
}

export function getTypeFilter() {
  return _typeFilter;
}

export function getDriverFilter() {
  return _driverFilter;
}

// Filter setters
export function setStatusFilter(status: DocumentStatus | null) {
  _statusFilter = status;
}

export function setTypeFilter(type: DocumentType | null) {
  _typeFilter = type;
}

export function setDriverFilter(driverUID: string | null) {
  _driverFilter = driverUID;
}

let unsubscribe: (() => void) | null = null;

function updateDocumentStatuses() {
  const now = new Date();
  let hasChanges = false;

  _documents = _documents.map((doc) => {
    const daysUntilExpiry = Math.floor((doc.expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    let newStatus = doc.status;
    if (daysUntilExpiry <= 0 && doc.status === DocumentStatus.approved) {
      newStatus = DocumentStatus.expired;
      hasChanges = true;
    } else if (daysUntilExpiry <= 30 && daysUntilExpiry > 0 && doc.status === DocumentStatus.approved) {
      newStatus = DocumentStatus.expiringSoon;
      hasChanges = true;
    }

    if (newStatus !== doc.status) {
      return { ...doc, status: newStatus };
    }
    return doc;
  });

  // Update map only if there were changes
  if (hasChanges) {
    _documentsMap.clear();
    _documents.forEach((doc) => {
      if (doc.id) _documentsMap.set(doc.id, doc);
    });
    // Force reactivity by creating a new Map instance
    _documentsMap = new Map(_documentsMap);
  }
}

export function init() {
  if (unsubscribe) return;

  _loading = true;
  _error = null;

  const tenantId = tenantStore.currentId;
  if (!tenantId) {
    _error = 'No tenant selected';
    _loading = false;
    return;
  }

  if (!fdb) {
    _error = 'Firestore not initialized';
    _loading = false;
    return;
  }

  try {
    // Use collection group query to get all driver documents
    let q = query(
      collectionGroup(fdb, 'driver_documents'),
      where('tenantIDs', 'array-contains', tenantId),
      orderBy('uploadedAt', 'desc')
    );

    unsubscribe = onSnapshot(
      q,
      (snapshot: QuerySnapshot<DocumentData>) => {
        _documents = [];
        // Clear the map before updating to ensure removed documents are not retained
        _documentsMap.clear();

        snapshot.docs.forEach((doc) => {
          const data = doc.data();
          const parentPath = doc.ref.parent.parent?.path || '';
          const driverUID = parentPath.split('/').pop() || '';

          const document: DriverDocument = {
            id: doc.id,
            documentType: data.documentType as DocumentType,
            documentName: data.documentName,
            fileURL: data.fileURL,
            expiryDate: (data.expiryDate as Timestamp).toDate(),
            status: data.status as DocumentStatus,
            notes: data.notes,
            uploadedAt: (data.uploadedAt as Timestamp).toDate(),
            reviewedAt: data.reviewedAt ? (data.reviewedAt as Timestamp).toDate() : undefined,
            reviewedBy: data.reviewedBy,
            adminNotes: data.adminNotes,
            tenantIDs: data.tenantIDs || [],
            driverUID,
          };

          _documents.push(document);
          _documentsMap.set(doc.id, document);
        });

        // Update statuses based on expiry dates
        updateDocumentStatuses();

        // Force reactivity by creating a new Map instance
        _documentsMap = new Map(_documentsMap);

        _loading = false;
      },
      (error) => {
        console.error('Error loading driver documents:', error);
        _error = error.message;
        _loading = false;
      }
    );

    // Update statuses periodically
    const intervalId = setInterval(updateDocumentStatuses, 60000); // Every minute

    // Clean up interval on destroy
    const originalUnsubscribe = unsubscribe;
    unsubscribe = () => {
      originalUnsubscribe();
      clearInterval(intervalId);
    };
  } catch (error) {
    console.error('Error initializing driver documents:', error);
    _error = error instanceof Error ? error.message : 'Unknown error';
    _loading = false;
  }
}

export function destroy() {
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }
  _documents = [];
  _documentsMap.clear();
  _loading = true;
  _error = null;
  _statusFilter = null;
  _typeFilter = null;
  _driverFilter = null;
}

// Filtered documents
export function getFilteredDocuments() {
  let filtered = _documents;

  if (_statusFilter) {
    filtered = filtered.filter((doc) => doc.status === _statusFilter);
  }

  if (_typeFilter) {
    filtered = filtered.filter((doc) => doc.documentType === _typeFilter);
  }

  if (_driverFilter) {
    filtered = filtered.filter((doc) => doc.driverUID === _driverFilter);
  }

  return filtered;
}

// Get documents by status
export function getDocumentsByStatus(status: DocumentStatus) {
  return _documents.filter((doc) => doc.status === status);
}

// Get expiring documents
export function getExpiringDocuments(daysThreshold: number = 30) {
  const now = new Date();
  return _documents.filter((doc) => {
    const daysUntilExpiry = Math.floor((doc.expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= daysThreshold && daysUntilExpiry > 0 && doc.status === DocumentStatus.approved;
  });
}

// Document operations
export async function updateDocumentStatus(documentId: string, driverUID: string, status: DocumentStatus, adminNotes?: string) {
  try {
    const tenantId = tenantStore.currentId;
    if (!tenantId) throw new Error('No tenant selected');

    return await callAdminFunction(
      'updateDocumentStatus',
      {
        tenantId,
        documentId,
        driverUID,
        status,
        adminNotes,
      },
      1
    ); // Requires ADMIN role
  } catch (error) {
    console.error('Error updating document status:', error);
    throw error;
  }
}

// Get document type display name
export function getDocumentTypeDisplayName(type: DocumentType): string {
  switch (type) {
    case DocumentType.license:
      return 'Driver License';
    case DocumentType.insurance:
      return 'Insurance';
    case DocumentType.vehicleRegistration:
      return 'Vehicle Registration';
    case DocumentType.nationalId:
      return 'National ID';
    case DocumentType.other:
      return 'Other';
  }
}

// Get document status display name
export function getDocumentStatusDisplayName(status: DocumentStatus): string {
  switch (status) {
    case DocumentStatus.pendingReview:
      return 'Pending Review';
    case DocumentStatus.approved:
      return 'Approved';
    case DocumentStatus.rejected:
      return 'Rejected';
    case DocumentStatus.expired:
      return 'Expired';
    case DocumentStatus.expiringSoon:
      return 'Expiring Soon';
  }
}

// Get status color
export function getDocumentStatusColor(status: DocumentStatus): string {
  switch (status) {
    case DocumentStatus.approved:
      return 'text-green-600 bg-green-100';
    case DocumentStatus.pendingReview:
      return 'text-orange-600 bg-orange-100';
    case DocumentStatus.rejected:
      return 'text-red-600 bg-red-100';
    case DocumentStatus.expired:
      return 'text-red-900 bg-red-200';
    case DocumentStatus.expiringSoon:
      return 'text-amber-700 bg-amber-100';
  }
}

// Get document by ID - reactive getter
export function getDocumentById(documentId: string): DriverDocument | null {
  return _documentsMap.get(documentId) || null;
}
