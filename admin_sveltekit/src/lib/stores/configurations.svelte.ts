import { collection, onSnapshot, type QueryDocumentSnapshot, type DocumentReference, updateDoc, doc } from 'firebase/firestore';
import { fdb } from '$lib/firebase.client';
import { getTenantCollection } from './tenant.svelte';

export interface TripConfiguration {
  costPerKilometer: number;
  costPerHour: number;
  minimumTripCost: number;
  waitTimeAfterExtraPayment: number;
  costPerExtraWaitChunk: number;
  cancelCostPreStart: number;
  nearbyDriverListedRadiusMeters: number;
  maxPassengerCount: number;
  hideInProgressCosts: boolean;
}

export interface ChatConfiguration {
  showAdminNamesInChat: boolean;
  defaultChatCategories: string[];
  autoArchiveChatAfterDays: number;
  enableChatNotifications: boolean;
  enableAutoReplyForOffHours: boolean;
  autoReplyMessage: string;
  supportHours: number[]; // [startHour, endHour] in 24h format
  supportDays: number[]; // [0-6] where 0 is Sunday
  maxImagesPerMessage: number;
  maxImageSizeMB: number;
}

export interface PassengerNotificationConfiguration {
  enableRingtoneByDefault: boolean;
  enableDriverMovingNotification: boolean;
  enableDriverArrivedNotification: boolean;
  enableTripPaidNotification: boolean;
  enableReservationReminders: boolean;
  reservationReminderTimes: number[]; // [60, 15] minutes before
}

export interface Configuration {
  key: string;
  value: string | number | TripConfiguration | ChatConfiguration | PassengerNotificationConfiguration;
  ref: DocumentReference;
}

const TRIP_CONFIGURATION_ID = 'tripConfiguration';
const CHAT_CONFIGURATION_ID = 'chat';
const PASSENGER_NOTIFICATION_CONFIGURATION_ID = 'passengerNotifications';

export function getDefaultTripConfiguration(): TripConfiguration {
  return {
    costPerKilometer: 4000,
    costPerHour: 25000,
    minimumTripCost: 15000,
    waitTimeAfterExtraPayment: 10,
    costPerExtraWaitChunk: 5000,
    cancelCostPreStart: 10000,
    nearbyDriverListedRadiusMeters: 1700,
    maxPassengerCount: 8,
    hideInProgressCosts: false,
  };
}

export function getDefaultChatConfiguration(): ChatConfiguration {
  return {
    showAdminNamesInChat: false,
    defaultChatCategories: ['supportGeneral', 'supportTrip', 'supportPayment', 'supportTechnical', 'feedbackFollowup'],
    autoArchiveChatAfterDays: 30,
    enableChatNotifications: true,
    enableAutoReplyForOffHours: false,
    autoReplyMessage:
      'Thank you for contacting us. Our support team is currently offline. We will respond to your message during our business hours.',
    supportHours: [8, 18], // 8 AM to 6 PM
    supportDays: [1, 2, 3, 4, 5], // Monday to Friday
    maxImagesPerMessage: 5,
    maxImageSizeMB: 5,
  };
}

export function getDefaultPassengerNotificationConfiguration(): PassengerNotificationConfiguration {
  return {
    enableRingtoneByDefault: true,
    enableDriverMovingNotification: true,
    enableDriverArrivedNotification: true,
    enableTripPaidNotification: true,
    enableReservationReminders: true,
    reservationReminderTimes: [60, 15], // 60 and 15 minutes before
  };
}

export function getTitleOfConfigurationId(id: string): string {
  switch (id) {
    case TRIP_CONFIGURATION_ID:
      return 'Trip Configuration';
    case CHAT_CONFIGURATION_ID:
      return 'Chat Configuration';
    case PASSENGER_NOTIFICATION_CONFIGURATION_ID:
      return 'Passenger Notifications';
    default:
      return id;
  }
}

// Private state
let _configurations = $state<Configuration[]>([]);
let unsubscribe: (() => void) | null = null;

// Public getter
export function getConfigurations() {
  return _configurations;
}

const parseTripConfiguration = (data: any): TripConfiguration => {
  if (!data) return getDefaultTripConfiguration();
  return {
    costPerKilometer: Number(data.costPerKilometer),
    costPerHour: Number(data.costPerHour),
    minimumTripCost: Number(data.minimumTripCost),
    waitTimeAfterExtraPayment: Number(data.waitTimeAfterExtraPayment),
    costPerExtraWaitChunk: Number(data.costPerExtraWaitChunk),
    cancelCostPreStart: Number(data.cancelCostPreStart),
    nearbyDriverListedRadiusMeters: Number(data.nearbyDriverListedRadiusMeters ?? 1700),
    maxPassengerCount: Number(data.maxPassengerCount ?? 8),
    hideInProgressCosts: Boolean(data.hideInProgressCosts ?? false),
  };
};

const parseChatConfiguration = (data: any): ChatConfiguration => {
  if (!data) return getDefaultChatConfiguration();
  const defaults = getDefaultChatConfiguration();
  return {
    showAdminNamesInChat: data.showAdminNamesInChat ?? defaults.showAdminNamesInChat,
    defaultChatCategories: Array.isArray(data.defaultChatCategories)
      ? data.defaultChatCategories
      : defaults.defaultChatCategories,
    autoArchiveChatAfterDays: Number(data.autoArchiveChatAfterDays ?? defaults.autoArchiveChatAfterDays),
    enableChatNotifications: data.enableChatNotifications ?? defaults.enableChatNotifications,
    enableAutoReplyForOffHours: data.enableAutoReplyForOffHours ?? defaults.enableAutoReplyForOffHours,
    autoReplyMessage: data.autoReplyMessage ?? defaults.autoReplyMessage,
    supportHours:
      Array.isArray(data.supportHours) && data.supportHours.length >= 2 ? data.supportHours.map(Number) : defaults.supportHours,
    supportDays: Array.isArray(data.supportDays) ? data.supportDays.map(Number) : defaults.supportDays,
    maxImagesPerMessage: Number(data.maxImagesPerMessage ?? defaults.maxImagesPerMessage),
    maxImageSizeMB: Number(data.maxImageSizeMB ?? defaults.maxImageSizeMB),
  };
};

const parsePassengerNotificationConfiguration = (data: any): PassengerNotificationConfiguration => {
  if (!data) return getDefaultPassengerNotificationConfiguration();
  const defaults = getDefaultPassengerNotificationConfiguration();
  return {
    enableRingtoneByDefault: data.enableRingtoneByDefault ?? defaults.enableRingtoneByDefault,
    enableDriverMovingNotification: data.enableDriverMovingNotification ?? defaults.enableDriverMovingNotification,
    enableDriverArrivedNotification: data.enableDriverArrivedNotification ?? defaults.enableDriverArrivedNotification,
    enableTripPaidNotification: data.enableTripPaidNotification ?? defaults.enableTripPaidNotification,
    enableReservationReminders: data.enableReservationReminders ?? defaults.enableReservationReminders,
    reservationReminderTimes:
      Array.isArray(data.reservationReminderTimes) && data.reservationReminderTimes.length > 0
        ? data.reservationReminderTimes.map(Number)
        : defaults.reservationReminderTimes,
  };
};

export function init() {
  if (unsubscribe) return; // Already initialized
  if (!fdb) {
    console.error('Firestore not initialized');
    return;
  }

  const configurationsRef = collection(fdb, getTenantCollection('configurations'));
  unsubscribe = onSnapshot(configurationsRef, (snapshot) => {
    _configurations = snapshot.docs.map((doc: QueryDocumentSnapshot) => {
      const data = doc.data();
      let value = data.value;

      // Special handling for configurations
      if (doc.id === TRIP_CONFIGURATION_ID) {
        value = parseTripConfiguration(data.value);
      } else if (doc.id === CHAT_CONFIGURATION_ID) {
        value = parseChatConfiguration(data.value);
      } else if (doc.id === PASSENGER_NOTIFICATION_CONFIGURATION_ID) {
        value = parsePassengerNotificationConfiguration(data.value);
      }

      return {
        key: doc.id,
        value,
        ref: doc.ref,
      } as Configuration;
    });

    // Check if passengerNotifications configuration exists
    const hasPassengerNotifications = snapshot.docs.some((doc) => doc.id === PASSENGER_NOTIFICATION_CONFIGURATION_ID);

    // If it doesn't exist, add a default one to the list
    if (!hasPassengerNotifications) {
      const defaultNotificationConfig: Configuration = {
        key: PASSENGER_NOTIFICATION_CONFIGURATION_ID,
        value: getDefaultPassengerNotificationConfiguration(),
        // Create a document reference that will be used when saving
        ref: doc(configurationsRef, PASSENGER_NOTIFICATION_CONFIGURATION_ID) as DocumentReference,
      };
      _configurations = [..._configurations, defaultNotificationConfig];
    }
  });
}

export function cleanup() {
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }
}

export function getTripConfiguration() {
  const tripConfig = _configurations.find((c) => c.key === TRIP_CONFIGURATION_ID);
  return (tripConfig?.value as TripConfiguration) ?? getDefaultTripConfiguration();
}

export function getChatConfiguration() {
  const chatConfig = _configurations.find((c) => c.key === CHAT_CONFIGURATION_ID);
  return (chatConfig?.value as ChatConfiguration) ?? getDefaultChatConfiguration();
}

export function getPassengerNotificationConfiguration() {
  const passengerConfig = _configurations.find((c) => c.key === PASSENGER_NOTIFICATION_CONFIGURATION_ID);
  return (passengerConfig?.value as PassengerNotificationConfiguration) ?? getDefaultPassengerNotificationConfiguration();
}

export async function save(configuration: Configuration) {
  try {
    // For trip configuration, ensure all values are numbers
    if (configuration.key === TRIP_CONFIGURATION_ID) {
      const tripConfig = configuration.value as TripConfiguration;
      configuration.value = {
        costPerKilometer: Number(tripConfig.costPerKilometer),
        costPerHour: Number(tripConfig.costPerHour),
        minimumTripCost: Number(tripConfig.minimumTripCost),
        waitTimeAfterExtraPayment: Number(tripConfig.waitTimeAfterExtraPayment),
        costPerExtraWaitChunk: Number(tripConfig.costPerExtraWaitChunk),
        cancelCostPreStart: Number(tripConfig.cancelCostPreStart),
        nearbyDriverListedRadiusMeters: Number(tripConfig.nearbyDriverListedRadiusMeters),
        maxPassengerCount: Number(tripConfig.maxPassengerCount),
        hideInProgressCosts: Boolean(tripConfig.hideInProgressCosts),
      };
    } else if (configuration.key === CHAT_CONFIGURATION_ID) {
      // For chat configuration, ensure proper formatting
      const chatConfig = configuration.value as ChatConfiguration;
      configuration.value = {
        showAdminNamesInChat: Boolean(chatConfig.showAdminNamesInChat),
        defaultChatCategories: chatConfig.defaultChatCategories,
        autoArchiveChatAfterDays: Number(chatConfig.autoArchiveChatAfterDays),
        enableChatNotifications: Boolean(chatConfig.enableChatNotifications),
        enableAutoReplyForOffHours: Boolean(chatConfig.enableAutoReplyForOffHours),
        autoReplyMessage: String(chatConfig.autoReplyMessage),
        supportHours: chatConfig.supportHours.map(Number),
        supportDays: chatConfig.supportDays.map(Number),
        maxImagesPerMessage: Number(chatConfig.maxImagesPerMessage),
        maxImageSizeMB: Number(chatConfig.maxImageSizeMB),
      };
    } else if (configuration.key === PASSENGER_NOTIFICATION_CONFIGURATION_ID) {
      // For passenger notification configuration, ensure proper formatting
      const passengerConfig = configuration.value as PassengerNotificationConfiguration;
      configuration.value = {
        enableRingtoneByDefault: Boolean(passengerConfig.enableRingtoneByDefault),
        enableDriverMovingNotification: Boolean(passengerConfig.enableDriverMovingNotification),
        enableDriverArrivedNotification: Boolean(passengerConfig.enableDriverArrivedNotification),
        enableTripPaidNotification: Boolean(passengerConfig.enableTripPaidNotification),
        enableReservationReminders: Boolean(passengerConfig.enableReservationReminders),
        reservationReminderTimes: passengerConfig.reservationReminderTimes.map(Number),
      };
    } else {
      // For generic configurations, ensure value is either string or number
      const value = configuration.value;
      configuration.value = typeof value === 'string' ? value : Number(value);
    }

    // Try to update the document
    try {
      await updateDoc(configuration.ref, { value: configuration.value });
    } catch (error: any) {
      // If the document doesn't exist, create it
      if (error.code === 'not-found') {
        const { setDoc } = await import('firebase/firestore');
        await setDoc(configuration.ref, { value: configuration.value });
      } else {
        throw error;
      }
    }
    return true;
  } catch (error) {
    console.error('Error saving configuration:', error);
    return false;
  }
}
