import type { User } from 'firebase/auth';
import { doc, updateDoc, serverTimestamp } from 'firebase/firestore';

// Private state
let _user = $state<User | boolean>(false); // false = loading, true = signed out, User = signed in
let lastSeenInterval: NodeJS.Timeout | null = null;

// Public getter
export function getFirebaseUser() {
	return _user;
}

const updateLastSeen = async (currentUser: User) => {
	try {
		// Import Firestore functions dynamically to ensure proper initialization
		const { initializeFirebase } = await import('$lib/firebase.client');

		const { fdb } = await initializeFirebase();
		if (!fdb) {
			console.warn('Firestore not initialized, skipping last seen update');
			return;
		}

		const userRef = doc(fdb, 'admin_users', currentUser.uid);
		await updateDoc(userRef, {
			lastSeen: serverTimestamp(),
			lastSeenDT: new Date()
		});
	} catch (error) {
		console.error('Error updating last seen:', error);
	}
};

// Initialize auth state listener
// Use setTimeout to ensure Firebase client is initialized
setTimeout(async () => {
	try {
		const { initializeFirebase } = await import('$lib/firebase.client');
		const { auth } = await initializeFirebase();

		if (!auth) {
			console.warn('Firebase Auth not initialized');
			_user = true; // Set to signed out state
			return;
		}

		auth.onAuthStateChanged(async (firebaseUser: User | null) => {
			if (firebaseUser === null) {
				_user = true;
				if (lastSeenInterval) {
					clearInterval(lastSeenInterval);
					lastSeenInterval = null;
				}
				return;
			}

			// Start periodic last seen updates
			if (!lastSeenInterval) {
				await updateLastSeen(firebaseUser);
				lastSeenInterval = setInterval(() => updateLastSeen(firebaseUser), 15000);
			}

			_user = firebaseUser;
		});
	} catch (error) {
		console.error('Error setting up auth state listener:', error);
		_user = true; // Set to signed out state if there's an error
	}
}, 100); // Small delay to ensure Firebase is initialized
