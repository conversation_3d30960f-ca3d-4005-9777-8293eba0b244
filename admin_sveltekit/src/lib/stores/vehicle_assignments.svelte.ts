import {
  collection,
  doc,
  onSnapshot,
  query,
  where,
  orderBy,
  limit,
  type QuerySnapshot,
  type DocumentData,
  type Timestamp,
} from 'firebase/firestore';
import { fdb } from '$lib/firebase.client';
import { tenantStore } from './tenant.svelte';
import { getVehiclesMap } from './vehicles.svelte';
import { getUsersMap } from './mobile_users.svelte';

export enum AssignmentReason {
  adminAssignment = 'adminAssignment',
  driverSwitch = 'driverSwitch',
  vehicleMaintenance = 'vehicleMaintenance',
  driverUnavailable = 'driverUnavailable',
}

export interface VehicleAssignment {
  id?: string;
  vehicleId: string;
  vehicleLinkingId: string;
  driverUID: string;
  tenantId: string;

  createdAt: Date;
  assignedAt: Date;
  assignedBy: string;
  unassignedAt?: Date;
  unassignedBy?: string;

  reason: AssignmentReason;
  notes?: string;

  isActive: boolean;

  // Derived fields
  vehicle?: any;
  driver?: any;
}

// Private state
let _assignments = $state<VehicleAssignment[]>([]);
let _assignmentsMap = $state<Map<string, VehicleAssignment>>(new Map());
let _loading = $state(true);
let _error = $state<string | null>(null);

// Filter state
let _vehicleFilter = $state<string | null>(null);
let _driverFilter = $state<string | null>(null);
let _showOnlyActive = $state(true);

// Public getters
export function getAssignments() {
  return _assignments;
}

export function getAssignmentsMap() {
  return _assignmentsMap;
}

export function getLoading() {
  return _loading;
}

export function getError() {
  return _error;
}

export function getVehicleFilter() {
  return _vehicleFilter;
}

export function getDriverFilter() {
  return _driverFilter;
}

export function getShowOnlyActive() {
  return _showOnlyActive;
}

// Filter setters
export function setVehicleFilter(vehicleId: string | null) {
  _vehicleFilter = vehicleId;
}

export function setDriverFilter(driverUID: string | null) {
  _driverFilter = driverUID;
}

export function setShowOnlyActive(show: boolean) {
  _showOnlyActive = show;
}

let unsubscribe: (() => void) | null = null;

export function init() {
  if (unsubscribe) return;

  _loading = true;
  _error = null;

  const tenantId = tenantStore.currentId;
  if (!tenantId) {
    _error = 'No tenant selected';
    _loading = false;
    return;
  }

  if (!fdb) {
    _error = 'Firestore not initialized';
    _loading = false;
    return;
  }

  try {
    const assignmentsRef = collection(fdb, 'vehicle_assignments');
    let q = query(assignmentsRef, where('tenantId', '==', tenantId), orderBy('createdAt', 'desc'), limit(500));

    if (_showOnlyActive) {
      q = query(assignmentsRef, where('tenantId', '==', tenantId), where('isActive', '==', true), orderBy('createdAt', 'desc'));
    }

    unsubscribe = onSnapshot(
      q,
      (snapshot: QuerySnapshot<DocumentData>) => {
        const vehiclesMap = getVehiclesMap();
        const usersMap = getUsersMap();

        _assignments = snapshot.docs.map((doc) => {
          const data = doc.data();
          const assignment: VehicleAssignment = {
            id: doc.id,
            vehicleId: data.vehicleId,
            vehicleLinkingId: data.vehicleLinkingId,
            driverUID: data.driverUID,
            tenantId: data.tenantId,
            createdAt: (data.createdAt as Timestamp).toDate(),
            assignedAt: (data.assignedAt as Timestamp).toDate(),
            assignedBy: data.assignedBy,
            unassignedAt: data.unassignedAt ? (data.unassignedAt as Timestamp).toDate() : undefined,
            unassignedBy: data.unassignedBy,
            reason: data.reason as AssignmentReason,
            notes: data.notes,
            isActive: data.isActive,
            vehicle: vehiclesMap.get(data.vehicleId),
            driver: usersMap.get(data.driverUID),
          };

          _assignmentsMap.set(doc.id, assignment);
          return assignment;
        });

        _loading = false;
      },
      (error) => {
        console.error('Error loading vehicle assignments:', error);
        _error = error.message;
        _loading = false;
      }
    );
  } catch (error) {
    console.error('Error initializing vehicle assignments:', error);
    _error = error instanceof Error ? error.message : 'Unknown error';
    _loading = false;
  }
}

export function destroy() {
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }
  _assignments = [];
  _assignmentsMap.clear();
  _loading = true;
  _error = null;
  _vehicleFilter = null;
  _driverFilter = null;
  _showOnlyActive = true;
}

// Get assignments for a specific vehicle
export async function getAssignmentsByVehicle(vehicleId: string): Promise<VehicleAssignment[]> {
  // We don't need tenant filtering for vehicle assignments as they're global
  if (!vehicleId) return [];

  return new Promise((resolve, reject) => {
    try {
      if (!fdb) {
        reject(new Error('Firestore not initialized'));
        return;
      }

      const q = query(collection(fdb, 'vehicle_assignments'), where('vehicleId', '==', vehicleId), orderBy('createdAt', 'desc'));

      const unsubscribeQuery = onSnapshot(
        q,
        (snapshot: QuerySnapshot<DocumentData>) => {
          const vehiclesMap = getVehiclesMap();
          const usersMap = getUsersMap();

          const assignments = snapshot.docs.map((doc) => {
            const data = doc.data();
            return {
              id: doc.id,
              vehicleId: data.vehicleId,
              vehicleLinkingId: data.vehicleLinkingId,
              driverUID: data.driverUID,
              tenantId: data.tenantId,
              createdAt: data.createdAt?.toDate() || new Date(),
              assignedAt: data.assignedAt?.toDate() || new Date(),
              assignedBy: data.assignedBy,
              unassignedAt: data.unassignedAt?.toDate(),
              unassignedBy: data.unassignedBy,
              reason: data.reason as AssignmentReason,
              notes: data.notes,
              isActive: data.isActive,
              vehicle: vehiclesMap.get(data.vehicleId),
              driver: usersMap.get(data.driverUID),
            };
          });

          unsubscribeQuery();
          resolve(assignments);
        },
        (error) => {
          reject(error);
        }
      );
    } catch (error) {
      reject(error);
    }
  });
}

// Filtered assignments
export function getFilteredAssignments() {
  let filtered = _assignments;

  if (_vehicleFilter) {
    filtered = filtered.filter((assignment) => assignment.vehicleId === _vehicleFilter);
  }

  if (_driverFilter) {
    filtered = filtered.filter((assignment) => assignment.driverUID === _driverFilter);
  }

  return filtered;
}

// Get active assignment for a vehicle
export function getActiveAssignmentForVehicle(vehicleId: string) {
  return _assignments.find((assignment) => assignment.vehicleId === vehicleId && assignment.isActive);
}

// Get active assignment for a driver
export function getActiveAssignmentForDriver(driverUID: string) {
  return _assignments.find((assignment) => assignment.driverUID === driverUID && assignment.isActive);
}

// Get assignment history for a vehicle
export function getAssignmentHistoryForVehicle(vehicleId: string) {
  return _assignments
    .filter((assignment) => assignment.vehicleId === vehicleId)
    .sort((a, b) => b.assignedAt.getTime() - a.assignedAt.getTime());
}

// Get assignment history for a driver
export function getAssignmentHistoryForDriver(driverUID: string) {
  return _assignments
    .filter((assignment) => assignment.driverUID === driverUID)
    .sort((a, b) => b.assignedAt.getTime() - a.assignedAt.getTime());
}

// Get assignment reason display name
export function getAssignmentReasonDisplayName(reason: AssignmentReason): string {
  switch (reason) {
    case AssignmentReason.adminAssignment:
      return 'Admin Assignment';
    case AssignmentReason.driverSwitch:
      return 'Driver Switch';
    case AssignmentReason.vehicleMaintenance:
      return 'Vehicle Maintenance';
    case AssignmentReason.driverUnavailable:
      return 'Driver Unavailable';
  }
}
