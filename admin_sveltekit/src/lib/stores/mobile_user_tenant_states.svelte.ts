import { collection, query, where, onSnapshot, type Unsubscribe, collectionGroup } from 'firebase/firestore';
import { fdb } from '$lib/firebase.client';
import { tenantStore } from './tenant.svelte';

export interface RatingStats {
  totalTrips: number;
  totalRating: number;
  averageRating: number;
  lastRatingDate?: Date;
  distribution: Record<number, number>;
}

export interface MobileUserTenantState {
  uid: string;
  tenantId: string;
  isActive: boolean;
  currentVehicleLinkingId?: string;
  driverTags: string[];
  isDriverConfirmed?: string;
  isServiceActive: boolean;
  ratingStats?: RatingStats;
  joinedAt: Date;
  lastActiveAt: Date;
}

let _tenantStates = $state<Map<string, MobileUserTenantState>>(new Map());
let unsubscribe: Unsubscribe | null = null;
let currentTenantId: string | null = null;

export function getTenantStates() {
  return _tenantStates;
}

export function getDriverRating(driverUid: string): RatingStats | undefined {
  return _tenantStates.get(driverUid)?.ratingStats;
}

export function init() {
  const tenantId = tenantStore.currentId;

  // If tenant hasn't changed and we already have a subscription, don't re-initialize
  if (unsubscribe && currentTenantId === tenantId) return;

  // Clean up existing subscription if tenant changed
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }

  currentTenantId = tenantId;

  if (!fdb) {
    console.error('Firestore not initialized');
    return;
  }

  // 🔥 Query tenant_states subcollection across all mobile users for the current tenant
  const q = query(collectionGroup(fdb, 'tenant_states'), where('tenantId', '==', tenantId), where('isActive', '==', true));

  unsubscribe = onSnapshot(
    q,
    (snapshot) => {
      _tenantStates.clear();

      snapshot.forEach((doc) => {
        const data = doc.data();
        _tenantStates.set(data.uid, {
          ...data,
          joinedAt: data.joinedAt?.toDate(),
          lastActiveAt: data.lastActiveAt?.toDate(),
          ratingStats: data.ratingStats
            ? {
                ...data.ratingStats,
                lastRatingDate: data.ratingStats.lastRatingDate?.toDate(),
              }
            : undefined,
        } as MobileUserTenantState);
      });

      console.log(`📊 Loaded ${_tenantStates.size} tenant states for tenant: ${tenantId}`);
    },
    (error) => {
      console.error('Error loading tenant states:', error);
    }
  );
}

export function destroy() {
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }
  _tenantStates.clear();
  currentTenantId = null;
}

// Re-initialize store when tenant changes
export function reinitialize() {
  destroy();
  init();
}
