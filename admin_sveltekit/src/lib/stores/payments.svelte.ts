import {
  collection,
  query,
  orderBy,
  limit,
  onSnapshot,
  type QueryDocumentSnapshot,
  type DocumentReference,
  doc,
  setDoc,
} from 'firebase/firestore';
import { fdb } from '$lib/firebase.client';
import { tenantStore } from './tenant.svelte';

export enum PaymentMethod {
  Cash = 'cash',
  Mobile = 'mobile',
  Card = 'card',
  Balance = 'balance',
  Other = 'other',
}

export enum PaymentStatus {
  Pending = 'pending',
  Processing = 'processing',
  ReceivedByDriver = 'receivedByDriver',
  Completed = 'completed',
  Failed = 'failed',
  Refunded = 'refunded',
  PartiallyRefunded = 'partiallyRefunded',
  Disputed = 'disputed',
}

export function formatPaymentStatus(status: PaymentStatus): string {
  // Split by capital letters and join with space
  return status
    .split(/(?=[A-Z])/)
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

export interface Payment {
  id: string;
  tripId: string;
  customerId: string;
  driverId?: string;
  customerRequestedPaymentMethod?: PaymentMethod;
  customerRequestedPaymentMethodRemark?: string;
  finalPaymentMethod?: PaymentMethod;
  finalPaymentMethodRemark?: string;
  amount: number;
  amountDue?: number;
  discount?: number;
  discountReason?: string;
  remark?: string;
  status: PaymentStatus;
  createdAt: Date;
  processedAt?: Date;
  completedAt?: Date;
  processedByUid?: string;
  completedByUid?: string;
  transactionId?: string;
  receiptNumber?: string;
  metadata?: Record<string, any>;
  ref?: DocumentReference;
}

// Private state
let _payments = $state<Payment[]>([]);

// Public getter
export function getPayments() {
  return _payments;
}

function processPaymentFromFirestore(doc: QueryDocumentSnapshot): Payment {
  const data = doc.data();
  return {
    id: doc.id,
    tripId: data.tripId,
    customerId: data.customerId,
    driverId: data.driverId,
    customerRequestedPaymentMethod: data.customerRequestedPaymentMethod as PaymentMethod,
    customerRequestedPaymentMethodRemark: data.customerRequestedPaymentMethodRemark,
    finalPaymentMethod: data.finalPaymentMethod as PaymentMethod,
    finalPaymentMethodRemark: data.finalPaymentMethodRemark,
    amount: data.amount,
    amountDue: data.amountDue,
    discount: data.discount,
    discountReason: data.discountReason,
    remark: data.remark,
    status: data.status as PaymentStatus,
    createdAt: data.createdAt?.toDate(),
    processedAt: data.processedAt?.toDate(),
    completedAt: data.completedAt?.toDate(),
    processedByUid: data.processedByUid,
    completedByUid: data.completedByUid,
    transactionId: data.transactionId,
    receiptNumber: data.receiptNumber,
    metadata: data.metadata,
    ref: doc.ref,
  };
}

function processPaymentToFirestore(payment: Partial<Payment> & { tripId: string; customerId: string; amount: number }) {
  const processedPayment: Record<string, any> = {
    tripId: payment.tripId,
    customerId: payment.customerId,
    amount: payment.amount,
    status: payment.status?.toString() ?? PaymentStatus.Pending,
    createdAt: payment.createdAt ?? new Date(),
  };

  if (payment.driverId) processedPayment.driverId = payment.driverId;
  if (payment.customerRequestedPaymentMethod)
    processedPayment.customerRequestedPaymentMethod = payment.customerRequestedPaymentMethod;
  if (payment.customerRequestedPaymentMethodRemark)
    processedPayment.customerRequestedPaymentMethodRemark = payment.customerRequestedPaymentMethodRemark;
  if (payment.finalPaymentMethod) processedPayment.finalPaymentMethod = payment.finalPaymentMethod;
  if (payment.finalPaymentMethodRemark) processedPayment.finalPaymentMethodRemark = payment.finalPaymentMethodRemark;
  if (payment.amountDue) processedPayment.amountDue = payment.amountDue;
  if (payment.discount) processedPayment.discount = payment.discount;
  if (payment.discountReason) processedPayment.discountReason = payment.discountReason;
  if (payment.remark) processedPayment.remark = payment.remark;
  if (payment.processedAt) processedPayment.processedAt = payment.processedAt;
  if (payment.completedAt) processedPayment.completedAt = payment.completedAt;
  if (payment.processedByUid) processedPayment.processedByUid = payment.processedByUid;
  if (payment.completedByUid) processedPayment.completedByUid = payment.completedByUid;
  if (payment.transactionId) processedPayment.transactionId = payment.transactionId;
  if (payment.receiptNumber) processedPayment.receiptNumber = payment.receiptNumber;
  if (payment.metadata) processedPayment.metadata = payment.metadata;

  return processedPayment;
}

let unsubscribe: (() => void) | null = null;

export function init() {
  if (unsubscribe) return; // Already initialized
  if (!fdb) {
    console.error('Firestore not initialized');
    return;
  }

  const paymentsRef = collection(fdb, tenantStore.getTenantPath('payments'));
  const q = query(paymentsRef, orderBy('createdAt', 'desc'), limit(100));

  unsubscribe = onSnapshot(
    q,
    (snapshot) => {
      _payments = snapshot.docs.map(processPaymentFromFirestore);
    },
    (error) => {
      console.error('Error fetching payments:', error);
      _payments = [];
    }
  );
}

export function destroy() {
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }
}

export async function createPayment({
  tripId,
  customerId,
  driverId,
  finalMethod,
  finalMethodRemark,
  amount,
  remark,
  metadata,
}: {
  tripId: string;
  customerId: string;
  driverId?: string;
  finalMethod: PaymentMethod;
  finalMethodRemark?: string;
  amount: number;
  remark?: string;
  metadata?: Record<string, any>;
}) {
  if (!fdb) throw new Error('Firestore not initialized');
  const paymentsRef = collection(fdb, tenantStore.getTenantPath('payments'));
  const paymentRef = doc(paymentsRef);
  const payment = processPaymentToFirestore({
    tripId,
    customerId,
    driverId,
    finalPaymentMethod: finalMethod,
    finalPaymentMethodRemark: finalMethodRemark,
    amount,
    remark,
    metadata,
    status: PaymentStatus.Pending,
    createdAt: new Date(),
  });

  await setDoc(paymentRef, payment);

  return paymentRef;
}

export async function updatePaymentStatus(paymentId: string, status: PaymentStatus, processedByUid?: string) {
  if (!fdb) throw new Error('Firestore not initialized');
  const paymentsRef = collection(fdb, tenantStore.getTenantPath('payments'));
  const paymentRef = doc(paymentsRef, paymentId);
  const updateData: Record<string, any> = { status };

  if (status === PaymentStatus.Processing) {
    updateData.processedAt = new Date();
    updateData.processedByUid = processedByUid;
  } else if (status === PaymentStatus.Completed) {
    updateData.completedAt = new Date();
    updateData.completedByUid = processedByUid;
  }

  await setDoc(paymentRef, updateData, { merge: true });
}
