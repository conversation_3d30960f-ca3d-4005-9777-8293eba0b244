import {
  collection,
  doc,
  onSnapshot,
  query,
  where,
  setDoc,
  updateDoc,
  deleteDoc,
  type QuerySnapshot,
  type DocumentData,
  serverTimestamp,
  increment,
} from 'firebase/firestore';
import { fdb } from '$lib/firebase.client';
import { tenantStore } from './tenant.svelte';
import { callAdminFunction } from '$lib/firebase.client';

export interface DriverTag {
  id?: string;
  name: string;
  color: string;
  description?: string;
  isActive: boolean;
  createdAt?: Date;
  createdBy?: string;
  usageCount: number;
}

// Private state
let _tags = $state<DriverTag[]>([]);
let _tagsMap = $state<Map<string, DriverTag>>(new Map());
let _loading = $state(true);
let _error = $state<string | null>(null);

// Public getters
export function getTags() {
  return _tags;
}

export function getTagsMap() {
  return _tagsMap;
}

export function getLoading() {
  return _loading;
}

export function getError() {
  return _error;
}

let unsubscribe: (() => void) | null = null;

export function init() {
  if (unsubscribe) return;

  _loading = true;
  _error = null;

  const tenantId = tenantStore.currentId;
  if (!tenantId) {
    _error = 'No tenant selected';
    _loading = false;
    return;
  }

  if (!fdb) {
    _error = 'Firestore not initialized';
    _loading = false;
    return;
  }

  try {
    const tagsRef = collection(fdb, tenantStore.getTenantPath('driver_tags'));
    const q = query(tagsRef, where('isActive', '==', true));

    unsubscribe = onSnapshot(
      q,
      (snapshot: QuerySnapshot<DocumentData>) => {
        _tags = snapshot.docs.map((doc) => {
          const data = doc.data();
          const tag = {
            id: doc.id,
            name: data.name,
            color: data.color,
            description: data.description,
            isActive: data.isActive,
            createdAt: data.createdAt?.toDate(),
            createdBy: data.createdBy,
            usageCount: data.usageCount || 0,
          } as DriverTag;

          _tagsMap.set(doc.id, tag);
          return tag;
        });

        _loading = false;
      },
      (error) => {
        console.error('Error loading driver tags:', error);
        _error = error.message;
        _loading = false;
      }
    );
  } catch (error) {
    console.error('Error initializing driver tags:', error);
    _error = error instanceof Error ? error.message : 'Unknown error';
    _loading = false;
  }
}

export function destroy() {
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }
  _tags = [];
  _tagsMap.clear();
  _loading = true;
  _error = null;
}

// CRUD operations
export async function createDriverTag(tagData: Partial<DriverTag>) {
  try {
    const tenantId = tenantStore.currentId;
    if (!tenantId) throw new Error('No tenant selected');

    // Check if user has minimum ADMIN role
    if (!tenantStore.hasMinimumRole(1)) {
      throw new Error('Insufficient permissions');
    }

    return await callAdminFunction(
      'createDriverTag',
      {
        tenantId,
        name: tagData.name,
        color: tagData.color,
        description: tagData.description,
      },
      1
    ); // Requires ADMIN role
  } catch (error) {
    console.error('Error creating driver tag:', error);
    throw error;
  }
}

export async function updateDriverTag(tagId: string, updates: Partial<DriverTag>) {
  try {
    const tenantId = tenantStore.currentId;
    if (!tenantId) throw new Error('No tenant selected');

    if (!tenantStore.hasMinimumRole(1)) {
      throw new Error('Insufficient permissions');
    }

    return await callAdminFunction(
      'updateDriverTag',
      {
        tenantId,
        tagId,
        updates,
      },
      1
    ); // Requires ADMIN role
  } catch (error) {
    console.error('Error updating driver tag:', error);
    throw error;
  }
}

export async function deleteDriverTag(tagId: string) {
  try {
    const tenantId = tenantStore.currentId;
    if (!tenantId) throw new Error('No tenant selected');

    if (!tenantStore.hasMinimumRole(1)) {
      throw new Error('Insufficient permissions');
    }

    return await callAdminFunction(
      'deleteDriverTag',
      {
        tenantId,
        tagId,
      },
      1
    ); // Requires ADMIN role
  } catch (error) {
    console.error('Error deleting driver tag:', error);
    throw error;
  }
}

// Auto-creation when a new tag is used
export async function ensureDriverTag(tagName: string): Promise<DriverTag> {
  const existingTag = _tags.find((t) => t.name === tagName);
  if (existingTag) return existingTag;

  // Generate a color for the new tag
  const colors = [
    '#ff0000', // red - reserved for "external"
    '#3b82f6', // blue
    '#10b981', // green
    '#f59e0b', // amber
    '#8b5cf6', // purple
    '#ec4899', // pink
    '#14b8a6', // teal
    '#f97316', // orange
    '#6366f1', // indigo
    '#84cc16', // lime
  ];

  // Use red for "external" tag, otherwise pick a color based on hash
  const color =
    tagName.toLowerCase() === 'external'
      ? colors[0]
      : colors[(Math.abs(tagName.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % (colors.length - 1)) + 1];

  const newTag = await createDriverTag({
    name: tagName,
    color,
    description: `Auto-created tag: ${tagName}`,
    isActive: true,
    usageCount: 0,
  });

  return newTag as DriverTag;
}

// Update usage count
export async function incrementTagUsage(tagId: string, delta: number = 1) {
  try {
    const tenantId = tenantStore.currentId;
    if (!tenantId) throw new Error('No tenant selected');
    if (!fdb) throw new Error('Firestore not initialized');

    const tagRef = doc(fdb, tenantStore.getTenantPath('driver_tags'), tagId);
    await updateDoc(tagRef, {
      usageCount: increment(delta),
    });
  } catch (error) {
    console.error('Error updating tag usage count:', error);
    throw error;
  }
}
