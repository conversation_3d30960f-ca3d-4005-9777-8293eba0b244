import { fdb } from '$lib/firebase.client';
import {
  collection,
  query,
  onSnapshot,
  where,
  orderBy,
  updateDoc,
  doc,
  addDoc,
  getDocs,
  getDoc,
  limit,
  startAfter,
  type Unsubscribe,
  type QueryDocumentSnapshot,
  Timestamp,
} from 'firebase/firestore';
import { getTenantCollection } from './tenant.svelte';

export enum ChatCategory {
  SUPPORT_GENERAL = 'supportGeneral',
  SUPPORT_TRIP = 'supportTrip',
  SUPPORT_PAYMENT = 'supportPayment',
  SUPPORT_TECHNICAL = 'supportTechnical',
  FEEDBACK_FOLLOWUP = 'feedbackFollowup',
}

export enum ChatStatus {
  ACTIVE = 'active',
  RESOLVED = 'resolved',
  ARCHIVED = 'archived',
}

export enum SenderType {
  PASSENGER = 'passenger',
  ADMIN = 'admin',
}

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  SYSTEM = 'system',
}

export interface ChatSession {
  id: string;
  participantUids: string[];
  title: string;
  category: ChatCategory;
  status: ChatStatus;
  createdAt: Date;
  lastMessageAt: Date;
  feedbackId?: string;
  isAdminInitiated: boolean;
  unreadCount?: number;
  lastMessage?: string;
}

export interface ChatMessage {
  id: string;
  senderUid: string;
  senderType: SenderType;
  message: string;
  timestamp: Date;
  readBy: Record<string, Date>;
  messageType: MessageType;
  imageUrl?: string;
}

let chatSessions = $state<ChatSession[]>([]);
let currentMessages = $state<ChatMessage[]>([]);
let loading = $state(true);
let messagesLoading = $state(false);
let sessionsUnsubscribe: Unsubscribe | null = null;
let messagesUnsubscribe: Unsubscribe | null = null;
let currentSessionId = $state<string | null>(null);
let lastMessageDoc: QueryDocumentSnapshot | null = null;

export function init() {
  if (sessionsUnsubscribe) return;
  if (!fdb) {
    console.error('Firestore not initialized');
    return;
  }

  const q = query(collection(fdb, getTenantCollection('chat_sessions')), orderBy('lastMessageAt', 'desc'));

  sessionsUnsubscribe = onSnapshot(q, (snapshot) => {
    chatSessions = snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        participantUids: data.participantUids || [],
        title: data.title,
        category: data.category as ChatCategory,
        status: data.status as ChatStatus,
        createdAt: data.createdAt?.toDate() || new Date(),
        lastMessageAt: data.lastMessageAt?.toDate() || new Date(),
        feedbackId: data.feedbackId,
        isAdminInitiated: data.isAdminInitiated || false,
        unreadCount: data.unreadCount,
        lastMessage: data.lastMessage,
      } as ChatSession;
    });
    loading = false;
  });
}

export function destroy() {
  if (sessionsUnsubscribe) {
    sessionsUnsubscribe();
    sessionsUnsubscribe = null;
  }
  cleanupMessages();
  chatSessions = [];
  loading = true;
}

// Clean up messages and their listener without destroying sessions
export function cleanupMessages() {
  if (messagesUnsubscribe) {
    messagesUnsubscribe();
    messagesUnsubscribe = null;
  }

  currentMessages = [];
  messagesLoading = false;
  currentSessionId = null;
  lastMessageDoc = null;
}

export function getChatSessions() {
  return chatSessions;
}

export function getChatSessionById(id: string) {
  return chatSessions.find((s) => s.id === id);
}

// Fallback function to get session directly from Firestore if not found in local array
export async function getChatSessionByIdDirect(id: string): Promise<ChatSession | null> {
  if (!fdb) {
    console.error('Firestore not initialized');
    return null;
  }
  try {
    const docRef = doc(fdb, getTenantCollection('chat_sessions'), id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        id: docSnap.id,
        participantUids: data.participantUids || [],
        title: data.title,
        category: data.category as ChatCategory,
        status: data.status as ChatStatus,
        createdAt: data.createdAt?.toDate() || new Date(),
        lastMessageAt: data.lastMessageAt?.toDate() || new Date(),
        feedbackId: data.feedbackId,
        isAdminInitiated: data.isAdminInitiated || false,
        unreadCount: data.unreadCount,
        lastMessage: data.lastMessage,
      } as ChatSession;
    }
    return null;
  } catch (error) {
    console.error('Error fetching session directly:', error);
    return null;
  }
}

export function getCurrentMessages() {
  return currentMessages;
}

export function isLoading() {
  return loading;
}

export function isMessagesLoading() {
  return messagesLoading;
}

export async function loadChatMessages(sessionId: string, reset = false) {
  // Clean up if switching to a different session or reset is requested
  if (reset || sessionId !== currentSessionId) {
    // Immediately clear messages to prevent showing old data
    currentMessages = [];
    lastMessageDoc = null;

    // Unsubscribe from previous messages listener
    if (messagesUnsubscribe) {
      messagesUnsubscribe();
      messagesUnsubscribe = null;
    }
  }

  currentSessionId = sessionId;
  messagesLoading = true;

  if (!fdb) {
    console.error('Firestore not initialized');
    messagesLoading = false;
    return;
  }

  try {
    // Load initial messages (50)
    let q = query(
      collection(fdb, getTenantCollection('chat_sessions'), sessionId, 'chat_messages'),
      orderBy('timestamp', 'desc'),
      limit(50)
    );

    if (lastMessageDoc && !reset && fdb) {
      q = query(
        collection(fdb, getTenantCollection('chat_sessions'), sessionId, 'chat_messages'),
        orderBy('timestamp', 'desc'),
        startAfter(lastMessageDoc),
        limit(50)
      );
    }

    const snapshot = await getDocs(q);

    const newMessages = snapshot.docs.map((doc) => {
      const data = doc.data();
      const readBy: Record<string, Date> = {};
      if (data.readBy) {
        Object.entries(data.readBy).forEach(([uid, timestamp]) => {
          readBy[uid] = (timestamp as Timestamp).toDate();
        });
      }

      return {
        id: doc.id,
        senderUid: data.senderUid,
        senderType: data.senderType as SenderType,
        message: data.message,
        timestamp: data.timestamp?.toDate() || new Date(),
        readBy,
        messageType: data.messageType as MessageType,
        imageUrl: data.imageUrl,
      } as ChatMessage;
    });

    if (snapshot.docs.length > 0) {
      lastMessageDoc = snapshot.docs[snapshot.docs.length - 1];
    }

    // Only append messages if we're still on the same session
    if (currentSessionId === sessionId) {
      currentMessages = [...currentMessages, ...newMessages];
    } else {
      return;
    }

    // Set up real-time listener for last 5 messages
    if ((!messagesUnsubscribe || reset) && fdb) {
      const realtimeQuery = query(
        collection(fdb, getTenantCollection('chat_sessions'), sessionId, 'chat_messages'),
        orderBy('timestamp', 'desc'),
        limit(5)
      );

      messagesUnsubscribe = onSnapshot(realtimeQuery, (snapshot) => {
        // Only process if we're still on the same session
        if (currentSessionId !== sessionId) {
          return;
        }

        const realtimeMessages = snapshot.docs.map((doc) => {
          const data = doc.data();
          const readBy: Record<string, Date> = {};
          if (data.readBy) {
            Object.entries(data.readBy).forEach(([uid, timestamp]) => {
              readBy[uid] = (timestamp as Timestamp).toDate();
            });
          }

          return {
            id: doc.id,
            senderUid: data.senderUid,
            senderType: data.senderType as SenderType,
            message: data.message,
            timestamp: data.timestamp?.toDate() || new Date(),
            readBy,
            messageType: data.messageType as MessageType,
            imageUrl: data.imageUrl,
          } as ChatMessage;
        });

        // Merge with existing messages, avoiding duplicates
        const messageIds = new Set(currentMessages.map((m) => m.id));
        const newRealtimeMessages = realtimeMessages.filter((m) => !messageIds.has(m.id));

        if (newRealtimeMessages.length > 0) {
          currentMessages = [...newRealtimeMessages, ...currentMessages].sort(
            (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
          );
        }
      });
    }
  } catch (error) {
    console.error('❌ Error loading chat messages:', error);
    throw error;
  } finally {
    messagesLoading = false;
  }
}

export async function sendMessage(
  sessionId: string,
  message: string,
  senderUid: string,
  senderType: SenderType,
  imageUrl?: string
) {
  if (!fdb) {
    console.error('Firestore not initialized');
    throw new Error('Firestore not initialized');
  }
  try {
    const messageData = {
      senderUid,
      senderType,
      message,
      timestamp: Timestamp.now(),
      readBy: { [senderUid]: Timestamp.now() },
      messageType: imageUrl ? MessageType.IMAGE : MessageType.TEXT,
      ...(imageUrl && { imageUrl }),
    };

    await addDoc(collection(fdb, getTenantCollection('chat_sessions'), sessionId, 'chat_messages'), messageData);

    // Update session's last message
    await updateDoc(doc(fdb, getTenantCollection('chat_sessions'), sessionId), {
      lastMessageAt: Timestamp.now(),
      lastMessage: message,
    });
  } catch (error) {
    console.error('Error sending message:', error);
    throw error;
  }
}

export async function markMessageAsRead(sessionId: string, messageId: string, uid: string) {
  if (!fdb) {
    console.error('Firestore not initialized');
    throw new Error('Firestore not initialized');
  }
  try {
    const messageRef = doc(fdb, getTenantCollection('chat_sessions'), sessionId, 'chat_messages', messageId);
    await updateDoc(messageRef, {
      [`readBy.${uid}`]: Timestamp.now(),
    });
  } catch (error) {
    console.error('Error marking message as read:', error);
    throw error;
  }
}

export async function updateChatStatus(sessionId: string, status: ChatStatus) {
  if (!fdb) {
    console.error('Firestore not initialized');
    throw new Error('Firestore not initialized');
  }
  try {
    await updateDoc(doc(fdb, getTenantCollection('chat_sessions'), sessionId), {
      status,
    });
  } catch (error) {
    console.error('Error updating chat status:', error);
    throw error;
  }
}

export async function createChatSession(
  title: string,
  category: ChatCategory,
  participantUids: string[],
  isAdminInitiated: boolean,
  feedbackId?: string
) {
  if (!fdb) {
    console.error('Firestore not initialized');
    throw new Error('Firestore not initialized');
  }
  try {
    const sessionData = {
      participantUids,
      title,
      category,
      status: ChatStatus.ACTIVE,
      createdAt: Timestamp.now(),
      lastMessageAt: Timestamp.now(),
      isAdminInitiated,
      ...(feedbackId && { feedbackId }),
    };

    const docRef = await addDoc(collection(fdb, getTenantCollection('chat_sessions')), sessionData);
    return docRef.id;
  } catch (error) {
    console.error('Error creating chat session:', error);
    throw error;
  }
}

export function getCategoryDisplayName(category: ChatCategory): string {
  switch (category) {
    case ChatCategory.SUPPORT_GENERAL:
      return 'General Support';
    case ChatCategory.SUPPORT_TRIP:
      return 'Trip Support';
    case ChatCategory.SUPPORT_PAYMENT:
      return 'Payment Support';
    case ChatCategory.SUPPORT_TECHNICAL:
      return 'Technical Support';
    case ChatCategory.FEEDBACK_FOLLOWUP:
      return 'Feedback Follow-up';
  }
}
