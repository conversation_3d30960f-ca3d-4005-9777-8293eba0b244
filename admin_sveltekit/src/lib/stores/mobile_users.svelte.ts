import {
  onSnapshot,
  type DocumentReference,
  updateDoc,
  type Timestamp,
  doc,
  collection,
  type FieldValue,
  query,
  where,
} from 'firebase/firestore';
import { fdb } from '$lib/firebase.client';
import { tenantStore } from './tenant.svelte';

export interface Language {
  languageCode: string;
  countryCode: string;
}

export enum UserType {
  Rider,
  Driver,
}

export interface MobileUser {
  uid: string;
  email?: string;
  displayName?: string;
  phoneNumber?: string;
  photoURL?: string;
  lastSeen?: Date;
  primaryUserType?: UserType;
  primaryLanguage?: Language;
  isServiceActiveByTenant?: Record<string, boolean>;
  ref: DocumentReference;
  isDriverConfirmed?: string | FieldValue; // UID of the Admin who has approved the driver account
  lat?: number;
  lon?: number;
  occupiedByTripId?: string;
  _isOnline?: boolean;
  driverTags?: string[];
  tenantIDs?: string[];
}

// Private state
let _users = $state<MobileUser[]>([]);
let _usersMap = $state<Map<string, MobileUser>>(new Map());

// Public getters
export function getUsers() {
  return _users;
}

export function getUsersMap() {
  return _usersMap;
}

let unsubscribe: (() => void) | null = null;
let updateOnlineStatusInterval: NodeJS.Timeout;
let currentTenantId: string | null = null;

function updateOnlineStatus() {
  const now = new Date().getTime();
  _users = _users.map((user) => ({
    ...user,
    _isOnline: user.lastSeen ? now - user.lastSeen.getTime() < 60000 : false,
  }));

  // Update map with new online status
  _users.forEach((user) => _usersMap.set(user.uid, user));
}

export async function init() {
  // Import Firebase client dynamically to ensure initialization
  const { initializeFirebase } = await import('$lib/firebase.client');

  // Initialize Firebase and get database instance
  const { fdb: firebaseDb } = await initializeFirebase();
  if (!firebaseDb) {
    console.error('Firebase not initialized in mobile_users store');
    return;
  }

  // Get current tenant ID - default to 'fiaranow' for backward compatibility
  const tenantId = tenantStore.currentId;

  // If tenant hasn't changed and we already have a subscription, don't re-initialize
  if (unsubscribe && currentTenantId === tenantId) return;

  // Clean up existing subscription if tenant changed
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }

  currentTenantId = tenantId;

  try {
    // Query mobile users that belong to the current tenant
    // Users belong to a tenant if tenantIDs array contains the tenant ID
    // For backward compatibility, if tenantId is 'fiaranow', also query users without tenantIDs
    const mobileUsersRef =
      tenantId === 'fiaranow'
        ? collection(firebaseDb, 'mobile_users') // Show all users for default tenant (backward compatibility)
        : query(collection(firebaseDb, 'mobile_users'), where('tenantIDs', 'array-contains', tenantId));

    unsubscribe = onSnapshot(mobileUsersRef, (snapshot) => {
      _users = snapshot.docs.map((doc) => {
        const data = doc.data();
        const lastSeen = data.lastSeen ? (data.lastSeen as Timestamp).toDate() : undefined;
        const user = {
          uid: data.uid,
          email: data.email,
          displayName: data.displayName,
          phoneNumber: data.phoneNumber,
          photoURL: data.photoURL,
          primaryUserType: data.primaryUserType,
          primaryLanguage: data.primaryLanguage
            ? {
                languageCode: data.primaryLanguage.languageCode,
                countryCode: data.primaryLanguage.countryCode,
              }
            : undefined,
          lastSeen,
          _isOnline: lastSeen ? new Date().getTime() - lastSeen.getTime() < 60000 : false,
          isServiceActiveByTenant: data.isServiceActiveByTenant as Record<string, boolean> | undefined,
          ref: doc.ref,
          isDriverConfirmed: data.isDriverConfirmed,
          lat: data.lat,
          lon: data.lon,
          occupiedByTripId: data.occupiedByTripId,
          driverTags: data.driverTags ? (data.driverTags as string[]) : undefined,
          tenantIDs: data.tenantIDs ? (data.tenantIDs as string[]) : undefined,
        } as MobileUser;

        // Update the lookup map
        _usersMap.set(user.uid, user);
        return user;
      });
    });

    updateOnlineStatusInterval = setInterval(updateOnlineStatus, 20000);
  } catch (error) {
    console.error('Error initializing mobile_users store:', error);
  }
}

export function destroy() {
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }
  if (updateOnlineStatusInterval) {
    clearInterval(updateOnlineStatusInterval);
  }
  currentTenantId = null;
}

// Re-initialize store when tenant changes (called after tenant switch)
export function reinitialize() {
  destroy();
  init();
}

export async function updateUser(userId: string, data: Partial<MobileUser>) {
  if (!fdb) throw new Error('Firebase not initialized');

  const userRef = doc(collection(fdb, 'mobile_users'), userId);
  await updateDoc(userRef, {
    ...data,
  });
}

export function getDisplayName(user: MobileUser): string {
  return user.displayName ?? user.email ?? user.phoneNumber ?? 'Unknown User';
}

export function getInitials(name: string): string {
  return name
    .split(' ')
    .map((word) => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);
}
