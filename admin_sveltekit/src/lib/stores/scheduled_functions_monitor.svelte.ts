import { httpsCallable } from 'firebase/functions';
import { functions } from '$lib/firebase.client';
import { toast } from 'svelte-sonner';

interface ExecutionLog {
  id: string;
  timestamp: string;
  success: boolean;
  executionTime: number;
  stats: {
    totalActiveTripCount: number;
    tenantsProcessed: number;
    tripsProcessed: number;
    errors: number;
    executionCount: number;
    checkedReservations: boolean;
    monitoredNotifications: boolean;
    driverTimeoutsChecked: boolean;
  };
  logs: string[];
  error?: string;
}

interface ScheduledFunctionsMonitorState {
  isMonitoring: boolean;
  isExecuting: boolean;
  executionLogs: ExecutionLog[];
  intervalId: number | null;
}

class ScheduledFunctionsMonitor {
  private state = $state<ScheduledFunctionsMonitorState>({
    isMonitoring: false,
    isExecuting: false,
    executionLogs: [],
    intervalId: null,
  });

  private testCalculateTripCosts = functions ? httpsCallable(functions, 'testCalculateTripCosts') : null;

  // Getters
  get isMonitoring() {
    return this.state.isMonitoring;
  }

  get isExecuting() {
    return this.state.isExecuting;
  }

  get executionLogs() {
    return this.state.executionLogs;
  }

  get latestLog() {
    return this.state.executionLogs[0];
  }

  // Start monitoring
  startMonitoring() {
    if (this.state.isMonitoring) return;

    this.state.isMonitoring = true;

    // Execute immediately
    this.executeFunction();

    // Set up interval for every 20 seconds
    const intervalId = window.setInterval(() => {
      if (!this.state.isExecuting) {
        this.executeFunction();
      }
    }, 20000);

    this.state.intervalId = intervalId;
    toast.success('Started monitoring scheduled functions');
  }

  // Stop monitoring
  stopMonitoring() {
    if (!this.state.isMonitoring) return;

    if (this.state.intervalId !== null) {
      window.clearInterval(this.state.intervalId);
      this.state.intervalId = null;
    }

    this.state.isMonitoring = false;
    toast.success('Stopped monitoring scheduled functions');
  }

  // Execute the function
  private async executeFunction() {
    // Double-check to prevent race conditions
    if (this.state.isExecuting) return;

    // Atomically set executing state
    const wasExecuting = this.state.isExecuting;
    this.state.isExecuting = true;

    if (wasExecuting) {
      // Another execution slipped through
      this.state.isExecuting = false;
      return;
    }

    const startTime = Date.now();

    try {
      if (!this.testCalculateTripCosts) {
        throw new Error('Firebase Functions not initialized');
      }
      const result = await this.testCalculateTripCosts();
      const data = result.data as any;

      const log: ExecutionLog = {
        id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        timestamp: data.timestamp,
        success: data.success,
        executionTime: data.executionTime,
        stats: data.stats,
        logs: data.logs,
        error: data.error,
      };

      // Add to logs (keep only last 25)
      this.state.executionLogs = [log, ...this.state.executionLogs.slice(0, 24)];

      if (!data.success) {
        toast.error(`Function execution failed: ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      const log: ExecutionLog = {
        id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        success: false,
        executionTime: Date.now() - startTime,
        stats: {
          totalActiveTripCount: 0,
          tenantsProcessed: 0,
          tripsProcessed: 0,
          errors: 1,
          executionCount: 0,
          checkedReservations: false,
          monitoredNotifications: false,
          driverTimeoutsChecked: false,
        },
        logs: [`[ERROR] Failed to execute function: ${errorMessage}`],
        error: errorMessage,
      };

      this.state.executionLogs = [log, ...this.state.executionLogs.slice(0, 24)];

      toast.error(`Failed to execute function: ${errorMessage}`);
    } finally {
      this.state.isExecuting = false;
    }
  }

  // Clear logs
  clearLogs() {
    this.state.executionLogs = [];
    toast.success('Cleared execution logs');
  }

  // Clean up on destroy
  destroy() {
    this.stopMonitoring();
  }
}

// Create singleton instance
let monitorInstance: ScheduledFunctionsMonitor | null = null;

export function getScheduledFunctionsMonitor(): ScheduledFunctionsMonitor {
  if (!monitorInstance) {
    monitorInstance = new ScheduledFunctionsMonitor();
  }
  return monitorInstance;
}
