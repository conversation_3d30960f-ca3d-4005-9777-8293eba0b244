import {
  collection,
  onSnapshot,
  type DocumentReference,
  updateDoc,
  type Timestamp,
  doc,
  getDoc,
  serverTimestamp,
  setDoc,
  getDocs,
  query,
  where,
} from 'firebase/firestore';
import { fdb } from '$lib/firebase.client';
import type { User } from 'firebase/auth';
import { tenantStore, type TenantAccess } from './tenant.svelte';

export interface AdminUser {
  uid: string;
  email: string;
  displayName: string;
  photoURL: string | null;
  isActive: boolean;
  lastSeen: Date;
  ref: DocumentReference;

  _isOnline?: boolean;
}

// Private state
let _users = $state<AdminUser[]>([]);
let _currentAdminUser = $state<AdminUser | null | undefined>(undefined); // undefined = loading, null = not found, AdminUser = found
let unsubscribe: (() => void) | null = null;
let currentAdminUnsubscribe: (() => void) | null = null;
let updateOnlineStatusInterval: NodeJS.Timeout | null = null;

// Getters
export function getUsers() {
  return _users;
}

export function getCurrentAdminUser() {
  return _currentAdminUser;
}

export function clearCurrentAdminUser() {
  _currentAdminUser = undefined;
  if (currentAdminUnsubscribe) {
    currentAdminUnsubscribe();
    currentAdminUnsubscribe = null;
  }
}

// Helper to check if Firebase is properly initialized
async function waitForFirebaseInit(maxAttempts = 10): Promise<boolean> {
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      if (fdb) {
        return true;
      }
    } catch (error) {
      console.warn(`Firebase initialization attempt ${attempt + 1} failed:`, error);
    }

    // Wait 100ms before next attempt
    await new Promise((resolve) => setTimeout(resolve, 100));
  }
  return false;
}

function updateOnlineStatus() {
  _users = _users.map((user) => ({
    ...user,
    _isOnline: user.lastSeen ? new Date().getTime() - user.lastSeen.getTime() < 60000 : false,
  }));
}

export function init() {
  if (unsubscribe) return;

  // Check if Firebase is initialized
  if (!fdb) {
    console.error('Firebase not initialized in admin_users store');
    return;
  }

  try {
    const adminUsersRef = collection(fdb, 'admin_users');
    unsubscribe = onSnapshot(adminUsersRef, (snapshot) => {
      _users = snapshot.docs.map((doc) => {
        const data = doc.data();
        const lastSeen = data.lastSeen ? (data.lastSeen as Timestamp).toDate() : new Date();
        return {
          uid: data.uid,
          email: data.email,
          displayName: data.displayName,
          photoURL: data.photoURL,
          isActive: data.isActive ?? true,
          lastSeen,
          _isOnline: lastSeen ? new Date().getTime() - lastSeen.getTime() < 60000 : false,
          ref: doc.ref,
        } as AdminUser;
      });
    });

    // Start periodic online status updates
    updateOnlineStatusInterval = setInterval(updateOnlineStatus, 20000);
  } catch (error) {
    console.error('Error initializing admin_users store:', error);
  }
}

export function destroy() {
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }
  if (updateOnlineStatusInterval) {
    clearInterval(updateOnlineStatusInterval);
  }
  clearCurrentAdminUser();
}

export function getDisplayName(user: AdminUser): string {
  return user.displayName ?? user.email ?? 'Unknown Admin';
}

export async function updateUser(userId: string, data: Partial<AdminUser>) {
  if (!fdb) throw new Error('Firebase not initialized');

  const adminUsersRef = collection(fdb, 'admin_users');
  const userRef = doc(adminUsersRef, userId);
  await updateDoc(userRef, data);
}

export async function toggleUserActivation(uid: string) {
  if (!fdb) throw new Error('Firebase not initialized');

  const userRef = doc(fdb, 'admin_users', uid);
  const userDoc = await getDoc(userRef);

  if (!userDoc.exists()) {
    throw new Error('Admin user not found');
  }

  const currentState = userDoc.data().isActive;
  await updateDoc(userRef, {
    isActive: !currentState,
    lastModified: serverTimestamp(),
  });
}

// Fetch admin user by uid, also populates the current admin user state
export async function fetchAdminUser(uid: string) {
  if (!fdb) throw new Error('Firebase not initialized');

  // Clear any existing listener
  if (currentAdminUnsubscribe) {
    currentAdminUnsubscribe();
    currentAdminUnsubscribe = null;
  }

  const userRef = doc(fdb, 'admin_users', uid);

  return new Promise<AdminUser | null>((resolve, reject) => {
    // Set up real-time listener
    currentAdminUnsubscribe = onSnapshot(
      userRef,
      (doc) => {
        if (!doc.exists()) {
          _currentAdminUser = null;
          resolve(null);
          return;
        }

        const data = doc.data();
        const lastSeen = data.lastSeen ? (data.lastSeen as Timestamp).toDate() : new Date();

        const adminUser = {
          uid: data.uid,
          email: data.email,
          displayName: data.displayName,
          photoURL: data.photoURL,
          isActive: data.isActive ?? true,
          lastSeen,
          _isOnline: lastSeen ? new Date().getTime() - lastSeen.getTime() < 60000 : false,
          ref: doc.ref,
        } as AdminUser;

        _currentAdminUser = adminUser;
        resolve(adminUser);
      },
      (error) => {
        console.error('Error fetching admin user:', error);
        _currentAdminUser = null;
        reject(error);
      }
    );
  });
}

export async function syncUserProfile(user: User) {
  if (!fdb) throw new Error('Firebase not initialized');

  const userRef = doc(fdb, 'admin_users', user.uid);

  try {
    const userDoc = await getDoc(userRef);
    if (!userDoc.exists()) {
      // Create new admin profile
      await setDoc(userRef, {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName || user.email || '', // Use email as fallback
        photoURL: user.photoURL,
        isActive: false, // New admins are inactive by default
        lastSeen: serverTimestamp(),
      });
      console.log('Created new user profile:', user.uid);
    } else {
      // Update existing profile with latest auth info
      await updateDoc(userRef, {
        email: user.email,
        displayName: user.displayName || user.email || '', // Use email as fallback
        photoURL: user.photoURL,
        lastSeen: serverTimestamp(),
      });
      console.log('Synced user profile:', user.uid);
    }
  } catch (error) {
    console.error('Error syncing user profile:', error);
    throw error;
  }
}

// Load user's tenant access and initialize tenant store
export async function loadUserTenantAccess(uid: string) {
  try {
    // Wait for Firebase to be initialized
    const firebaseReady = await waitForFirebaseInit();
    if (!firebaseReady) {
      throw new Error('Firebase not initialized after waiting');
    }

    if (!fdb) throw new Error('Firebase not initialized');

    // Query tenant access sub-collection
    const tenantAccessRef = collection(fdb, 'admin_users', uid, 'tenants');
    const tenantAccessSnapshot = await getDocs(tenantAccessRef);

    const tenantAccess: TenantAccess[] = [];
    tenantAccessSnapshot.forEach((doc) => {
      const data = doc.data();
      tenantAccess.push({
        tenantId: doc.id,
        isActive: data.isActive,
        role: data.role,
        assignedAt: data.assignedAt?.toDate() || new Date(),
        assignedBy: data.assignedBy,
      });
    });

    // Load tenant access into the store
    await tenantStore.loadUserTenantAccess(uid);

    return tenantAccess;
  } catch (error) {
    console.error('Error loading tenant access:', error);
    throw error;
  }
}
