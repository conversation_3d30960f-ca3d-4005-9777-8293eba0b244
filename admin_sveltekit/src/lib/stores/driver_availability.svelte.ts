import { callFunction } from '$lib/firebase.client';
import { tenantStore } from './tenant.svelte';

export interface AvailableDriver {
  uid: string;
  displayName?: string;
  photoURL?: string;
  phoneNumber?: string;
  lat?: number;
  lon?: number;
  maxPassengers: number;
  vehicleId?: string;
  vehicleLinkingId?: string;
  vehicleInfo?: {
    brand: string;
    model: string;
    color: string;
    registrationNumber: string;
  };
}

// Private state
let _availableDrivers = $state<AvailableDriver[]>([]);
let _isLoading = $state(false);
let _error = $state<string | null>(null);

// Public getters
export function getAvailableDrivers() {
  return _availableDrivers;
}

export function isLoading() {
  return _isLoading;
}

export function getError() {
  return _error;
}

/**
 * Fetch available drivers from the backend
 */
export async function fetchAvailableDrivers(requiredCapacity: number = 1): Promise<void> {
  _isLoading = true;
  _error = null;
  
  try {
    const response = await callFunction('getAvailableDriversForAdmin', {
      tenantId: tenantStore.currentId,
      requiredCapacity
    });
    
    if (response.success && response.drivers) {
      _availableDrivers = response.drivers;
    } else {
      throw new Error('Failed to fetch available drivers');
    }
  } catch (error: any) {
    console.error('Error fetching available drivers:', error);
    _error = error.message || 'Failed to fetch available drivers';
    _availableDrivers = [];
  } finally {
    _isLoading = false;
  }
}

/**
 * Check if a specific driver is available
 */
export async function checkDriverAvailability(
  driverUID: string, 
  requiredCapacity: number = 1
): Promise<boolean> {
  try {
    const response = await callFunction('checkDriverAvailability', {
      tenantId: tenantStore.currentId,
      driverUID,
      requiredCapacity
    });
    
    return response.success && response.isAvailable;
  } catch (error: any) {
    console.error('Error checking driver availability:', error);
    return false;
  }
}

/**
 * Clear the available drivers list
 */
export function clearAvailableDrivers() {
  _availableDrivers = [];
  _error = null;
}