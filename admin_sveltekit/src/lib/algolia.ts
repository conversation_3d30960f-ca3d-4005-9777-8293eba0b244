import { algoliasearch } from 'algoliasearch';
import {
  PUBLIC_ALGOLIA_APP_ID_DEV,
  PUBLIC_ALGOLIA_SEARCH_KEY_DEV,
  PUBLIC_ALGOLIA_APP_ID_PROD,
  PUBLIC_ALGOLIA_SEARCH_KEY_PROD,
} from '$env/static/public';
import { dev } from '$app/environment';
import { tenantStore } from '$lib/stores/tenant.svelte';

// Select appropriate Algolia credentials based on environment
const ALGOLIA_APP_ID = dev ? PUBLIC_ALGOLIA_APP_ID_DEV : PUBLIC_ALGOLIA_APP_ID_PROD;
const ALGOLIA_SEARCH_KEY = dev ? PUBLIC_ALGOLIA_SEARCH_KEY_DEV : PUBLIC_ALGOLIA_SEARCH_KEY_PROD;

// Initialize Algolia client with search-only key
export const algoliaClient = ALGOLIA_APP_ID && ALGOLIA_SEARCH_KEY ? algoliasearch(ALGOLIA_APP_ID, ALGOLIA_SEARCH_KEY) : null;

// Helper to get index name with tenant
export function getIndexName(collection: string): string {
  const tenantId = tenantStore.currentId;
  return tenantId ? `${collection}_${tenantId}` : collection;
}

// Helper to create tenant-filtered search parameters
export function getTenantFilteredParams(additionalFilters?: string): any {
  const tenantId = tenantStore.currentId;
  const tenantFilter = tenantId ? `tenantId:${tenantId}` : '';
  const filters = [tenantFilter, additionalFilters].filter(Boolean).join(' AND ');

  return {
    filters: filters || undefined,
  };
}
