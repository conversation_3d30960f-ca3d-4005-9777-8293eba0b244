import { doc, setDoc, updateDoc, deleteDoc, serverTimestamp, type Firestore } from "firebase/firestore";
import { getTenantCollection } from "$lib/stores/tenant.svelte";

export interface ChatPresence {
  userId: string;
  sessionId: string;
  userType: "admin" | "user";
  lastHeartbeat: any; // Firestore Timestamp
  isActive: boolean;
}

export class ChatPresenceManager {
  private static HEARTBEAT_INTERVAL_MS = 10000; // 10 seconds
  private static PRESENCE_TTL_BUFFER_SECONDS = 60; // 1 minute TTL buffer

  private userId: string;
  private userType: "admin" | "user";
  private currentSessionId: string | null = null;
  private heartbeatInterval: number | null = null;
  private db: Firestore;

  constructor(db: Firestore, userId: string, userType: "admin" | "user" = "admin") {
    this.db = db;
    this.userId = userId;
    this.userType = userType;
  }

  /**
   * Enter a chat session and start tracking presence
   */
  async enterChatSession(sessionId: string): Promise<void> {
    try {
      // Cancel any existing heartbeat
      this.stopHeartbeat();
      this.currentSessionId = sessionId;

      // Get reference to presence document (using userId as doc ID)
      const presenceRef = doc(this.db, getTenantCollection("chat_presence"), this.userId);

      // Calculate expiration time (current time + TTL buffer)
      const expiresAt = new Date(Date.now() + (ChatPresenceManager.PRESENCE_TTL_BUFFER_SECONDS * 1000));

      // Set or update presence document
      await setDoc(presenceRef, {
        userId: this.userId,
        sessionId: sessionId,
        userType: this.userType,
        lastHeartbeat: serverTimestamp(),
        isActive: true,
        expiresAt: expiresAt
      }, { merge: true }); // Use merge to handle existing documents

      // Start heartbeat
      this.startHeartbeat(presenceRef);

      // Set up page visibility handler
      this.setupVisibilityHandler();

      // Set up beforeunload handler
      this.setupUnloadHandler();
    } catch (error) {
      console.error("Error entering chat session:", error);
      // Don't throw - presence tracking should not break chat functionality
    }
  }

  /**
   * Leave the chat session and stop tracking presence
   */
  async leaveChatSession(): Promise<void> {
    try {
      // Stop heartbeat immediately
      this.stopHeartbeat();
      this.currentSessionId = null;

      // Delete presence document immediately for instant notification resumption
      const presenceRef = doc(this.db, getTenantCollection("chat_presence"), this.userId);
      await deleteDoc(presenceRef);

      // Remove event listeners
      this.removeEventListeners();
    } catch (error) {
      console.error("Error leaving chat session:", error);
      // Don't throw - presence tracking should not break chat functionality
    }
  }

  /**
   * Start the heartbeat timer
   */
  private startHeartbeat(presenceRef: any): void {
    this.stopHeartbeat();

    // Update immediately
    this.updateHeartbeat(presenceRef);

    // Then set interval for future updates
    this.heartbeatInterval = window.setInterval(() => {
      if (this.currentSessionId) {
        this.updateHeartbeat(presenceRef);
      }
    }, ChatPresenceManager.HEARTBEAT_INTERVAL_MS);
  }

  /**
   * Stop the heartbeat timer
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      window.clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Update the heartbeat timestamp
   */
  private async updateHeartbeat(presenceRef: any): Promise<void> {
    try {
      // Update heartbeat and extend expiration
      const expiresAt = new Date(Date.now() + (ChatPresenceManager.PRESENCE_TTL_BUFFER_SECONDS * 1000));
      await updateDoc(presenceRef, {
        lastHeartbeat: serverTimestamp(),
        expiresAt: expiresAt
      });
    } catch (error) {
      console.error("Error updating heartbeat:", error);
      // Don't stop the heartbeat - try again next interval
    }
  }

  /**
   * Handle page visibility changes
   */
  private visibilityHandler = async () => {
    if (!this.currentSessionId) return;

    const presenceRef = doc(this.db, getTenantCollection("chat_presence"), this.userId);

    if (document.hidden) {
      // Page is hidden - mark as inactive
      this.stopHeartbeat();
      try {
        await updateDoc(presenceRef, {
          isActive: false
        });
      } catch (error) {
        console.error("Error updating presence on visibility change:", error);
      }
    } else {
      // Page is visible - mark as active and restart heartbeat
      try {
        const expiresAt = new Date(Date.now() + (ChatPresenceManager.PRESENCE_TTL_BUFFER_SECONDS * 1000));
        await updateDoc(presenceRef, {
          isActive: true,
          lastHeartbeat: serverTimestamp(),
          expiresAt: expiresAt
        });
        this.startHeartbeat(presenceRef);
      } catch (error) {
        console.error("Error updating presence on visibility change:", error);
      }
    }
  };

  /**
   * Handle page unload (browser close/navigation)
   */
  private unloadHandler = () => {
    if (!this.currentSessionId) return;

    // Use sendBeacon for reliable cleanup on page unload
    const presenceRef = doc(this.db, getTenantCollection("chat_presence"), this.userId);

    // Try to delete the document synchronously (best effort)
    // Note: This might not always complete, which is why we have the cleanup function
    navigator.sendBeacon?.(`/api/chat-presence/cleanup?userId=${this.userId}`);
  };

  /**
   * Set up visibility change handler
   */
  private setupVisibilityHandler(): void {
    document.addEventListener("visibilitychange", this.visibilityHandler);
  }

  /**
   * Set up page unload handler
   */
  private setupUnloadHandler(): void {
    window.addEventListener("beforeunload", this.unloadHandler);
    window.addEventListener("pagehide", this.unloadHandler);
  }

  /**
   * Remove all event listeners
   */
  private removeEventListeners(): void {
    document.removeEventListener("visibilitychange", this.visibilityHandler);
    window.removeEventListener("beforeunload", this.unloadHandler);
    window.removeEventListener("pagehide", this.unloadHandler);
  }

  /**
   * Clean up resources
   */
  dispose(): void {
    this.stopHeartbeat();
    this.removeEventListeners();
    this.currentSessionId = null;
  }
}