import { browser } from '$app/environment';

class ImageCacheService {
  private registration: ServiceWorkerRegistration | null = null;
  private initialized = false;

  async initialize() {
    if (!browser || this.initialized || !('serviceWorker' in navigator)) {
      return;
    }

    try {
      // Register the cache service worker
      this.registration = await navigator.serviceWorker.register('/cache-service-worker.js', {
        scope: '/',
      });

      // Listen for updates
      this.registration.addEventListener('updatefound', () => {});

      this.initialized = true;
    } catch (error) {}
  }

  async clearImageCache(): Promise<boolean> {
    if (!this.registration || !this.registration.active) {
      return false;
    }

    try {
      // Send message to service worker to clear cache
      const messageChannel = new MessageChannel();

      return new Promise((resolve) => {
        messageChannel.port1.onmessage = (event) => {
          resolve(event.data.success);
        };

        this.registration!.active!.postMessage({ type: 'CLEAR_IMAGE_CACHE' }, [messageChannel.port2]);
      });
    } catch (error) {
      return false;
    }
  }

  async getCacheStatus(): Promise<{ size: number; count: number } | null> {
    if (!browser || !('caches' in window)) {
      return null;
    }

    try {
      const cache = await caches.open('fiaranow-google-photos-v1');
      const keys = await cache.keys();

      // Estimate cache size (rough calculation)
      let totalSize = 0;
      for (const request of keys) {
        const response = await cache.match(request);
        if (response) {
          const blob = await response.blob();
          totalSize += blob.size;
        }
      }

      return {
        count: keys.length,
        size: totalSize,
      };
    } catch (error) {
      return null;
    }
  }

  formatCacheSize(bytes: number): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  getRegistration(): ServiceWorkerRegistration | null {
    return this.registration;
  }
}

// Export singleton instance
export const imageCacheService = new ImageCacheService();
