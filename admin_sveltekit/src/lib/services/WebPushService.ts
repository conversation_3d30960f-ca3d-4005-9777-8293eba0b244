import { getMessaging, getToken, onMessage, type Messaging } from 'firebase/messaging';
import { doc, updateDoc, getDoc } from 'firebase/firestore';
import { fdb } from '$lib/firebase.client';
import { toast } from 'svelte-sonner';

class WebPushService {
  private messaging: Messaging | null = null;
  private initialized = false;
  private currentToken: string | null = null;

  async initialize() {
    if (this.initialized) return;

    try {
      // Dynamically import firebase app to avoid SSR issues
      const { initializeApp, getApps } = await import('firebase/app');

      // Check if app is already initialized
      const app =
        getApps().length > 0
          ? getApps()[0]
          : initializeApp({
              apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
              authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
              projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
              storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
              messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
              appId: import.meta.env.VITE_FIREBASE_APP_ID,
            });

      this.messaging = getMessaging(app);
      this.initialized = true;

      // Register service worker
      if ('serviceWorker' in navigator) {
        try {
          await navigator.serviceWorker.register('/firebase-messaging-sw.js');
          // Service Worker registered successfully
        } catch (error) {
          console.error('Service Worker registration failed:', error);
        }
      }

      // Listen for foreground messages
      this.setupForegroundMessageHandler();
    } catch (error) {
      console.error('Failed to initialize WebPushService:', error);
    }
  }

  private setupForegroundMessageHandler() {
    if (!this.messaging) return;

    onMessage(this.messaging, (payload) => {
      // Foreground message received

      // Show toast notification for foreground messages
      const title = payload.notification?.title || 'New Notification';
      const body = payload.notification?.body || 'You have a new notification';
      const data = payload.data;

      let actionUrl: string | null = null;
      let actionLabel = 'View';

      // Determine action based on notification type
      if (data) {
        switch (data.type) {
          case 'chat_message':
            actionUrl = data.chatSessionId ? `/support/chats/${data.chatSessionId}` : '/support/chats';
            actionLabel = 'View Chat';
            break;
          case 'trip_booking':
          case 'trip_cancelled':
            actionUrl = data.tripId ? `/rides/trips/${data.tripId}/details` : '/rides/trips';
            actionLabel = 'View Trip';
            break;
          case 'feedback_submitted':
            actionUrl = data.feedbackId ? `/support/feedbacks/${data.feedbackId}` : '/support/feedbacks';
            actionLabel = 'View Feedback';
            break;
        }
      }

      toast(body, {
        description: title,
        action: actionUrl
          ? {
              label: actionLabel,
              onClick: () => {
                window.location.href = actionUrl;
              },
            }
          : undefined,
      });
    });
  }

  async requestPermission(): Promise<boolean> {
    if (!this.messaging) {
      console.error('WebPushService not initialized');
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      // Notification permission status: permission
      return permission === 'granted';
    } catch (error) {
      console.error('Failed to request notification permission:', error);
      return false;
    }
  }

  async getToken(vapidKey?: string): Promise<string | null> {
    if (!this.messaging) {
      console.error('WebPushService not initialized');
      return null;
    }

    try {
      // Use VAPID key if provided, otherwise Firebase will use the default
      const token = await getToken(this.messaging, {
        vapidKey: vapidKey || import.meta.env.VITE_FIREBASE_VAPID_KEY,
      });

      if (token) {
        // FCM token obtained successfully
        this.currentToken = token;
        return token;
      } else {
        // No registration token available
        return null;
      }
    } catch (error) {
      console.error('An error occurred while retrieving token:', error);
      return null;
    }
  }

  async saveTokenToUser(userId: string, token: string): Promise<void> {
    if (!fdb) throw new Error('Firebase not initialized');

    try {
      const userRef = doc(fdb, 'admin_users', userId);

      // Simply overwrite the single FCM token
      await updateDoc(userRef, {
        fcmToken: token,
        lastTokenUpdate: new Date(),
        // Clean up legacy array field if it exists
        fcmTokens: null,
      });

      // FCM token saved to user document
    } catch (error) {
      console.error('Failed to save FCM token:', error);
      throw error;
    }
  }

  async removeTokenFromUser(userId: string): Promise<void> {
    if (!fdb) throw new Error('Firebase not initialized');

    try {
      const userRef = doc(fdb, 'admin_users', userId);
      await updateDoc(userRef, {
        fcmToken: null,
        fcmTokens: null, // Clean up legacy field
      });
      // FCM token removed from user document
    } catch (error) {
      console.error('Failed to remove FCM token:', error);
      throw error;
    }
  }

  async initializeForUser(userId: string): Promise<boolean> {
    try {
      // Initialize the service
      await this.initialize();

      // Request permission
      const hasPermission = await this.requestPermission();
      if (!hasPermission) {
        // Notification permission denied
        return false;
      }

      // Get FCM token
      const token = await this.getToken();
      if (!token) {
        console.error('Failed to get FCM token');
        return false;
      }

      // Save token to user document
      await this.saveTokenToUser(userId, token);

      return true;
    } catch (error) {
      console.error('Failed to initialize web push for user:', error);
      return false;
    }
  }

  getCurrentToken(): string | null {
    return this.currentToken;
  }

  isInitialized(): boolean {
    return this.initialized;
  }
}

// Export singleton instance
export const webPushService = new WebPushService();
