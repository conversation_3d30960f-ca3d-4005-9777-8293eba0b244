<script lang="ts">
  import type { Payment } from "$lib/stores/payments.svelte";
  import {
    PaymentStatus,
    PaymentMethod,
    updatePaymentStatus,
    formatPaymentStatus,
  } from "$lib/stores/payments.svelte";
  import * as Card from "$lib/components/ui/card/index.js";
  import { CopyIcon } from "lucide-svelte";
  import AriaryCurrency from "$lib/components/ui/AriaryCurrency.svelte";
  import { getUsersMap, getDisplayName } from "$lib/stores/mobile_users.svelte";
  import * as Dialog from "$lib/components/ui/dialog";
  import { Button } from "$lib/components/ui/button";
  import { toast } from "svelte-sonner";
  import { getFirebaseUser } from "$lib/stores/auth.svelte";
  import type { User } from "firebase/auth";
  import { formatLongDateTime } from "$lib/utils/datetime";
  import * as m from '$lib/paraglide/messages';

  let { payment } = $props<{ payment: Payment }>();
  let showStatusDialog = $state(false);
  let processingStatus = $state(false);

  let usersMap = $derived(getUsersMap());
  const firebaseUser = $derived(getFirebaseUser());

  function formatDate(date: Date): string {
    return formatLongDateTime(date);
  }

  function getInitials(name: string): string {
    return name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  }

  function formatPaymentMethod(method: PaymentMethod): string {
    return method.charAt(0).toUpperCase() + method.slice(1);
  }

  let customer = $derived(
    payment.customerId ? usersMap.get(payment.customerId) : null,
  );
  let driver = $derived(
    payment.driverId ? usersMap.get(payment.driverId) : null,
  );

  // Get all available payment statuses as an array
  const paymentStatuses = $derived(Object.values(PaymentStatus));

  async function handleStatusChange(newStatus: PaymentStatus) {
    if (processingStatus) return;

    processingStatus = true;
    try {
      const uid =
        typeof firebaseUser === "object"
          ? (firebaseUser as unknown as User).uid
          : "admin";

      await updatePaymentStatus(payment.id, newStatus, uid);
      toast.success(
        m.paymentDetailsComponent_updateStatusSuccessToast({ status: newStatus }),
      );
      showStatusDialog = false;

      // Update the local payment object to reflect the change
      payment = {
        ...payment,
        status: newStatus,
        ...(newStatus === PaymentStatus.Processing
          ? { processedAt: new Date(), processedByUid: uid }
          : {}),
        ...(newStatus === PaymentStatus.Completed
          ? { completedAt: new Date(), completedByUid: uid }
          : {}),
      };
    } catch (error) {
      console.error("Failed to update payment status:", error);
      toast.error(m.paymentDetailsComponent_updateStatusErrorToast());
    } finally {
      processingStatus = false;
    }
  }
</script>

<Card.Root>
  <Card.Header>
    <div class="flex justify-between items-start">
      <div>
        <Card.Title>{m.paymentDetailsComponent_title()}</Card.Title>
        <Card.Description>
          {m.paymentDetailsComponent_createdOn({ date: formatDate(payment.createdAt) })}
          <div
            class="text-muted-foreground text-xs mt-1 flex items-center gap-1"
          >
            ID: {payment.id}
            <button
              class="hover:text-primary-foreground transition-colors"
              onclick={() => navigator.clipboard.writeText(payment.id)}
            >
              <CopyIcon class="w-4 h-4" />
            </button>
          </div>
        </Card.Description>
      </div>
      <div class="flex items-center gap-2">
        <span
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
          class:bg-yellow-100={payment.status === PaymentStatus.Pending}
          class:text-yellow-800={payment.status === PaymentStatus.Pending}
          class:bg-blue-100={payment.status === PaymentStatus.Processing}
          class:text-blue-800={payment.status === PaymentStatus.Processing}
          class:bg-green-100={payment.status === PaymentStatus.Completed}
          class:text-green-800={payment.status === PaymentStatus.Completed}
          class:bg-red-100={payment.status === PaymentStatus.Failed}
          class:text-red-800={payment.status === PaymentStatus.Failed}
          class:bg-purple-100={payment.status ===
            PaymentStatus.ReceivedByDriver}
          class:text-purple-800={payment.status ===
            PaymentStatus.ReceivedByDriver}
          class:bg-orange-100={payment.status === PaymentStatus.Refunded ||
            payment.status === PaymentStatus.PartiallyRefunded}
          class:text-orange-800={payment.status === PaymentStatus.Refunded ||
            payment.status === PaymentStatus.PartiallyRefunded}
          class:bg-gray-100={payment.status === PaymentStatus.Disputed}
          class:text-gray-800={payment.status === PaymentStatus.Disputed}
        >
          {formatPaymentStatus(payment.status)}
        </span>
        <Button
          variant="outline"
          size="sm"
          onclick={() => (showStatusDialog = true)}
        >
          {m.paymentDetailsComponent_changeStatusButton()}
        </Button>
      </div>
    </div>
  </Card.Header>
  <Card.Content class="space-y-8">
    <div class="grid grid-cols-2 gap-8">
      {#if customer}
        <div>
          <h4 class="font-medium mb-2">{m.paymentDetailsComponent_customerHeading()}</h4>
          <div class="flex items-center gap-3">
            {#if customer.photoURL}
              <img
                src={customer.photoURL}
                alt={getDisplayName(customer)}
                class="w-12 h-12 rounded-full object-cover"
              />
            {:else}
              <div
                class="w-12 h-12 rounded-full bg-primary text-primary-foreground flex items-center justify-center"
              >
                {getInitials(getDisplayName(customer))}
              </div>
            {/if}
            <div>
              <p class="font-medium">{getDisplayName(customer)}</p>
              {#if customer.phoneNumber}
                <p class="text-sm text-muted-foreground">
                  {customer.phoneNumber}
                </p>
              {/if}
            </div>
          </div>
        </div>
      {/if}

      {#if driver}
        <div>
          <h4 class="font-medium mb-2">{m.paymentDetailsComponent_driverHeading()}</h4>
          <div class="flex items-center gap-3">
            {#if driver.photoURL}
              <img
                src={driver.photoURL}
                alt={getDisplayName(driver)}
                class="w-12 h-12 rounded-full object-cover"
              />
            {:else}
              <div
                class="w-12 h-12 rounded-full bg-primary text-primary-foreground flex items-center justify-center"
              >
                {getInitials(getDisplayName(driver))}
              </div>
            {/if}
            <div>
              <p class="font-medium">{getDisplayName(driver)}</p>
              {#if driver.phoneNumber}
                <p class="text-sm text-muted-foreground">
                  {driver.phoneNumber}
                </p>
              {/if}
            </div>
          </div>
        </div>
      {/if}
    </div>

    <div>
      <h4 class="font-medium mb-2">{m.paymentDetailsComponent_paymentInfoHeading()}</h4>
      <div class="space-y-2">
        <div class="flex justify-between">
          <span class="text-muted-foreground">{m.paymentDetailsComponent_amountLabel()}</span>
          <span><AriaryCurrency amount={payment.amount} /></span>
        </div>

        {#if payment.amountDue}
          <div class="flex justify-between">
            <span class="text-muted-foreground">{m.paymentDetailsComponent_amountDueLabel()}</span>
            <span><AriaryCurrency amount={payment.amountDue} /></span>
          </div>
        {/if}

        {#if payment.discount}
          <div class="flex justify-between">
            <span class="text-muted-foreground">{m.paymentDetailsComponent_discountLabel()}</span>
            <div class="text-right">
              <span
                >-<AriaryCurrency amount={payment.discount} /></span
              >
              {#if payment.discountReason}
                <p class="text-xs text-muted-foreground">
                  {m.paymentDetailsComponent_discountReasonLabel({ reason: payment.discountReason })}
                </p>
              {/if}
            </div>
          </div>
        {/if}

        {#if payment.customerRequestedPaymentMethod}
          <div class="flex justify-between">
            <span class="text-muted-foreground">{m.paymentDetailsComponent_requestedMethodLabel()}</span>
            <div class="text-right">
              <div>
                {formatPaymentMethod(payment.customerRequestedPaymentMethod)}
              </div>
              {#if payment.customerRequestedPaymentMethodRemark}
                <p class="text-xs text-muted-foreground">
                  {payment.customerRequestedPaymentMethodRemark}
                </p>
              {/if}
            </div>
          </div>
        {/if}

        {#if payment.finalPaymentMethod}
          <div class="flex justify-between">
            <span class="text-muted-foreground">{m.paymentDetailsComponent_paidWithMethodLabel({ method: formatPaymentMethod(payment.finalPaymentMethod) })}</span>
            <span>{formatPaymentMethod(payment.finalPaymentMethod)}</span>
          </div>
          {#if payment.finalPaymentMethodRemark}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.paymentDetailsComponent_paidWithMethodRemarkLabel()}</span>
              <span>{payment.finalPaymentMethodRemark}</span>
            </div>
          {/if}
        {/if}

        {#if payment.receiptNumber}
          <div class="flex justify-between">
            <span class="text-muted-foreground">{m.paymentDetailsComponent_receiptNumberLabel()}</span>
            <span>{payment.receiptNumber}</span>
          </div>
        {/if}

        {#if payment.transactionId}
          <div class="flex justify-between">
            <span class="text-muted-foreground">{m.paymentDetailsComponent_transactionIdLabel()}</span>
            <span>{payment.transactionId}</span>
          </div>
        {/if}

        {#if payment.remark}
          <div class="flex justify-between">
            <span class="text-muted-foreground">{m.paymentDetailsComponent_remarkLabel()}</span>
            <span>{payment.remark}</span>
          </div>
        {/if}
      </div>
    </div>

    {#if payment.methodDetails}
      <div>
        <h4 class="font-medium mb-2">{m.paymentDetailsComponent_paymentMethodDetailsHeading()}</h4>
        <div class="space-y-2">
          {#each Object.entries(payment.methodDetails) as [key, value]}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{key}</span>
              <span>{value}</span>
            </div>
          {/each}
        </div>
      </div>
    {/if}

    <div>
      <h4 class="font-medium mb-2">{m.paymentDetailsComponent_paymentTimelineHeading()}</h4>
      <div class="space-y-2">
        <div class="flex justify-between">
          <span class="text-muted-foreground">{m.paymentDetailsComponent_receivedLabel()}</span>
          <span>{formatDate(payment.createdAt)}</span>
        </div>

        {#if payment.processedAt}
          <div class="flex justify-between">
            <span class="text-muted-foreground">{m.paymentDetailsComponent_processedLabel()}</span>
            <div class="text-right">
              <span>{formatDate(payment.processedAt)}</span>
              {#if payment.processedByUid}
                <p class="text-xs text-muted-foreground">
                  {m.paymentDetailsComponent_byUserLabel({ user: payment.processedByUid })}
                </p>
              {/if}
            </div>
          </div>
        {/if}

        {#if payment.completedAt}
          <div class="flex justify-between">
            <span class="text-muted-foreground">{m.paymentDetailsComponent_completedLabel()}</span>
            <div class="text-right">
              <span>{formatDate(payment.completedAt)}</span>
              {#if payment.completedByUid}
                <p class="text-xs text-muted-foreground">
                  {m.paymentDetailsComponent_byUserLabel({ user: payment.completedByUid })}
                </p>
              {/if}
            </div>
          </div>
        {/if}
      </div>
    </div>

    {#if payment.metadata && Object.keys(payment.metadata).length > 0}
      <div>
        <h4 class="font-medium mb-2">{m.paymentDetailsComponent_additionalInfoHeading()}</h4>
        <div class="space-y-2">
          {#each Object.entries(payment.metadata) as [key, value]}
            <div class="flex justify-between">
              <span class="text-muted-foreground">
                {key
                  // First split camelCase by inserting spaces
                  .replace(/([A-Z])/g, " $1")
                  // Then split by underscores and trim
                  .split(/[\s_]+/)
                  // Capitalize first letter of each word
                  .map(
                    (word) =>
                      word.charAt(0).toUpperCase() +
                      word.slice(1).toLowerCase(),
                  )
                  .join(" ")}
              </span>
              <span>
                {typeof value === "string"
                  ? value
                      .replace(/([A-Z])/g, " $1")
                      .split(/[\s_]+/)
                      .map(
                        (word) =>
                          word.charAt(0).toUpperCase() +
                          word.slice(1).toLowerCase(),
                      )
                      .join(" ")
                  : value}
              </span>
            </div>
          {/each}
        </div>
      </div>
    {/if}
  </Card.Content>
</Card.Root>

<Dialog.Root
  open={showStatusDialog}
  onOpenChange={(open) => (showStatusDialog = open)}
>
  <Dialog.Content class="max-w-md">
    <Dialog.Header>
      <Dialog.Title>{m.paymentDetailsComponent_changeStatusDialogTitle()}</Dialog.Title>
      <Dialog.Description>
        {m.paymentDetailsComponent_changeStatusDialogDescription()}
      </Dialog.Description>
    </Dialog.Header>
    <div class="mt-4 space-y-4">
      <div class="grid grid-cols-1 gap-2">
        {#each paymentStatuses as status}
          <Button
            variant={payment.status === status ? "default" : "outline"}
            class="justify-start"
            disabled={processingStatus || payment.status === status}
            onclick={() => handleStatusChange(status)}
          >
            <span
              class="w-3 h-3 rounded-full mr-2"
              class:bg-yellow-500={status === PaymentStatus.Pending}
              class:bg-blue-500={status === PaymentStatus.Processing}
              class:bg-green-500={status === PaymentStatus.Completed}
              class:bg-red-500={status === PaymentStatus.Failed}
              class:bg-purple-500={status ===
                PaymentStatus.ReceivedByDriver}
              class:bg-orange-500={status === PaymentStatus.Refunded ||
                status === PaymentStatus.PartiallyRefunded}
              class:bg-gray-500={status === PaymentStatus.Disputed}
            ></span>
            {formatPaymentStatus(status)}
          </Button>
        {/each}
      </div>
    </div>
    <Dialog.Footer>
      <Button variant="outline" onclick={() => (showStatusDialog = false)}>
        {m.paymentDetailsComponent_cancelButton()}
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
