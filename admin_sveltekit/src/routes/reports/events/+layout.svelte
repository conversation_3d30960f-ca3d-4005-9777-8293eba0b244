<script module lang="ts">
  let showServiceStatusOnly = $state(false);
  let showTripRejectedOnly = $state(false);
</script>

<script lang="ts">
  import * as m from "$lib/paraglide/messages";
  import * as Card from "$lib/components/ui/card/index.js";
  import {
    getEventLogs,
    init as initEventLogs,
    EventLogType,
  } from "$lib/stores/event_logs.svelte";
  import type { EventLog } from "$lib/stores/event_logs.svelte";
  import { onMount, onDestroy } from "svelte";
  import { localizedGoto } from "$lib/utils";
  import { page } from "$app/state";
  import {
    getUsersMap,
    getDisplayName,
    init as initMobileUsers,
  } from "$lib/stores/mobile_users.svelte";
  import { formatShortDateTime } from "$lib/utils/datetime";
  import { Toggle } from "$lib/components/ui/toggle/index.js";
  import EventTypeBadge from "$lib/components/ui/EventTypeBadge.svelte";

  let { children } = $props();

  onMount(() => {
    initEventLogs();
    initMobileUsers();
  });

  onDestroy(() => {
    // Don't destroy the stores, keep for reuse
  });

  function handleEventClick(event: EventLog) {
    localizedGoto(`/reports/events/${event.id}`);
  }

  function getInitials(name: string): string {
    return name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  }

  let selectedEventId = $derived(page.params.eventId);
  let usersMap = $derived(getUsersMap());
  let eventLogs = $derived(getEventLogs());

  let filteredEvents = $derived(
    eventLogs.filter((event) => {
      // Service Status filter
      if (
        showServiceStatusOnly &&
        event.type !== EventLogType.DRIVER_SERVICE_STATUS_UPDATE
      )
        return false;

      // Trip Rejected filter
      if (
        showTripRejectedOnly &&
        event.type !== EventLogType.DRIVER_TRIP_REJECTED
      )
        return false;

      return true;
    }),
  );
</script>

<div class="p-4">
  <div class="flex h-full gap-4">
    <!-- Left section - List -->
    <div class="w-1/3">
      <Card.Root>
        <Card.Header>
          <div class="flex items-start justify-between">
            <div>
              <Card.Title>{m.eventsLayout_title()}</Card.Title>
              <Card.Description>{m.eventsLayout_description()}</Card.Description
              >
            </div>
            <div class="flex items-center gap-2">
              <Toggle
                pressed={showServiceStatusOnly}
                onPressedChange={(pressed) => (showServiceStatusOnly = pressed)}
                variant="outline"
                size="sm"
                class="data-[state=on]:bg-blue-500"
              >
                {m.eventsLayout_serviceStatusFilter()}
              </Toggle>
              <Toggle
                pressed={showTripRejectedOnly}
                onPressedChange={(pressed) => (showTripRejectedOnly = pressed)}
                variant="outline"
                size="sm"
                class="data-[state=on]:bg-red-500"
              >
                {m.eventsLayout_tripRejectedFilter()}
              </Toggle>
            </div>
          </div>
        </Card.Header>
        <Card.Content class="h-[calc(100vh-152px)] overflow-y-auto">
          <div class="space-y-2">
            {#each filteredEvents as event (event.id)}
              <div
                class="flex items-center justify-between rounded-lg border p-3 hover:bg-muted/50 cursor-pointer {selectedEventId ===
                event.id
                  ? 'bg-muted'
                  : ''}"
                onclick={() => handleEventClick(event)}
                onkeydown={(e) => e.key === "Enter" && handleEventClick(event)}
                role="button"
                tabindex="0"
              >
                <div class="flex items-center gap-4">
                  {#if event.driver}
                    {#if event.driver.uid}
                      {@const user = usersMap.get(event.driver.uid)}
                      {#if user}
                        <div class="relative">
                          {#if user.photoURL}
                            <img
                              src={user.photoURL}
                              alt={getDisplayName(user)}
                              class="w-10 h-10 rounded-full"
                            />
                          {:else}
                            <div
                              class="w-10 h-10 rounded-full bg-primary text-primary-foreground flex items-center justify-center"
                            >
                              {getInitials(getDisplayName(user))}
                            </div>
                          {/if}
                        </div>
                        <div>
                          <p class="font-medium">
                            {getDisplayName(user)}
                          </p>
                          <div
                            class="flex items-center gap-2 text-sm text-muted-foreground"
                          >
                            <EventTypeBadge eventType={event.type} />
                          </div>
                        </div>
                      {:else}
                        <div class="relative">
                          <div
                            class="w-10 h-10 rounded-full bg-primary text-primary-foreground flex items-center justify-center"
                          >
                            {getInitials(
                              event.driver.displayName ||
                                m.eventsLayout_unknownUser(),
                            )}
                          </div>
                        </div>
                        <div>
                          <p class="font-medium">
                            {event.driver.displayName ||
                              m.eventsLayout_unknownDriver()}
                          </p>
                          <div
                            class="flex items-center gap-2 text-sm text-muted-foreground"
                          >
                            <EventTypeBadge eventType={event.type} />
                          </div>
                        </div>
                      {/if}
                    {/if}
                  {/if}
                </div>
                <div class="text-sm text-muted-foreground">
                  {formatShortDateTime(event.timestamp)}
                </div>
              </div>
            {/each}
            {#if !filteredEvents.length}
              <p class="text-sm text-muted-foreground">
                {m.eventsLayout_noEventsFound()}
              </p>
            {/if}
          </div>
        </Card.Content>
      </Card.Root>
    </div>

    <!-- Right section - Content -->
    <div class="flex-1">
      {@render children()}
    </div>
  </div>
</div>
