<script lang="ts">
  import * as m from "$lib/paraglide/messages";
  import * as Card from "$lib/components/ui/card/index.js";
  import { page } from "$app/state";
  import {
    getEventLogsMap,
    EventLogType,
    ServiceStatusReasonType,
    TripRejectionReasonType,
    getEventTypeLabel,
  } from "$lib/stores/event_logs.svelte";
  import type { EventLog } from "$lib/stores/event_logs.svelte";
  import {
    getUsersMap,
    getDisplayName,
    getInitials,
  } from "$lib/stores/mobile_users.svelte";
  import {
    getUsers as getAdminUsers,
    getDisplayName as getAdminDisplayName,
    init as initAdminUsers,
  } from "$lib/stores/admin_users.svelte";
  import { formatLongDateTime } from "$lib/utils/datetime";
  import { formatCurrency } from "$lib/utils/currency";
  import { onMount } from "svelte";
  import EventTypeBadge from "$lib/components/ui/EventTypeBadge.svelte";

  let eventId = $derived(page.params.eventId);
  let eventLogsMap = $derived(getEventLogsMap());
  let usersMap = $derived(getUsersMap());
  let adminUsers = $derived(getAdminUsers());

  let event = $derived(
    eventId && eventLogsMap ? eventLogsMap.get(eventId) : null,
  );

  onMount(() => {
    initAdminUsers();
  });

  function getReasonLabel(event: EventLog): string {
    if (
      event.type === EventLogType.DRIVER_SERVICE_STATUS_UPDATE &&
      event.serviceStatusReasonType
    ) {
      return formatServiceStatusReason(event.serviceStatusReasonType);
    } else if (
      event.type === EventLogType.DRIVER_TRIP_REJECTED &&
      event.tripRejectionReasonType
    ) {
      return formatTripRejectionReason(event.tripRejectionReasonType);
    }
    return event.reason || m.eventDetail_noReasonProvided();
  }

  function formatServiceStatusReason(
    reasonType: ServiceStatusReasonType,
  ): string {
    switch (reasonType) {
      case ServiceStatusReasonType.MORNING_SERVICE_START:
        return "Morning Service Start";
      case ServiceStatusReasonType.EVENING_SERVICE_START:
        return "Evening Service Start";
      case ServiceStatusReasonType.LUNCH_BREAK:
        return "Lunch Break";
      case ServiceStatusReasonType.PRAYER_BREAK:
        return "Prayer Break";
      case ServiceStatusReasonType.FUEL_REFILL:
        return "Fuel Refill";
      case ServiceStatusReasonType.VEHICLE_MAINTENANCE:
        return "Vehicle Maintenance";
      case ServiceStatusReasonType.END_OF_SHIFT:
        return "End of Shift";
      case ServiceStatusReasonType.EMERGENCY_STOP:
        return "Emergency Stop";
      case ServiceStatusReasonType.SWITCH_ACTIVITY:
        return "Switch Activity";
      case ServiceStatusReasonType.APP_RELAUNCH:
        return "App Relaunch";
      case ServiceStatusReasonType.CUSTOM:
        return "Custom";
      default:
        return reasonType;
    }
  }

  function formatTripRejectionReason(
    reasonType: TripRejectionReasonType,
  ): string {
    switch (reasonType) {
      case TripRejectionReasonType.VEHICLE_MALFUNCTION:
        return "Vehicle Malfunction";
      case TripRejectionReasonType.TOO_FAR_PICKUP:
        return "Too Far Pickup";
      case TripRejectionReasonType.HEAVY_TRAFFIC:
        return "Heavy Traffic";
      case TripRejectionReasonType.UNSAFE_AREA:
        return "Unsafe Area";
      case TripRejectionReasonType.ENDING_SHIFT_SOON:
        return "Ending Shift Soon";
      case TripRejectionReasonType.VEHICLE_CLEANING:
        return "Vehicle Cleaning";
      case TripRejectionReasonType.PASSENGER_CAPACITY_FULL:
        return "Passenger Capacity Full";
      case TripRejectionReasonType.BATTERY_LOW:
        return "Battery Low";
      case TripRejectionReasonType.WEATHER_CONDITIONS:
        return "Weather Conditions";
      case TripRejectionReasonType.CUSTOM:
        return "Custom";
      default:
        return reasonType;
    }
  }

  function formatMetadataKey(key: string): string {
    // Convert camelCase to Title Case
    return key
      .replace(/([A-Z])/g, " $1")
      .replace(/^./, (str) => str.toUpperCase())
      .trim();
  }

  function formatMetadataValue(key: string, value: any): string {
    // Handle admin UIDs
    if (key === "adminUid" && typeof value === "string") {
      const admin = adminUsers.find((u) => u.uid === value);
      return admin ? getAdminDisplayName(admin) : value;
    }

    // Handle cost values
    if (
      (key.toLowerCase().includes("cost") ||
        key.toLowerCase().includes("price")) &&
      typeof value === "number"
    ) {
      return formatCurrency(value);
    }

    // Handle timestamps
    if (value instanceof Date) {
      return formatLongDateTime(value);
    }

    // Handle other values
    if (typeof value === "object") {
      return JSON.stringify(value, null, 2);
    }

    return String(value);
  }
</script>

<Card.Root>
  <Card.Header>
    <Card.Title>{m.eventDetail_title()}</Card.Title>
    <Card.Description>{m.eventDetail_description()}</Card.Description>
  </Card.Header>
  <Card.Content>
    {#if event}
      <div class="grid grid-cols-2 gap-6">
        <!-- Left Column -->
        <div class="space-y-6">
          <!-- Event Type -->
          <div class="space-y-1">
            <h3 class="text-sm font-medium">
              {m.eventDetail_eventTypeLabel()}
            </h3>
            <div class="rounded-md bg-muted p-3">
              <EventTypeBadge eventType={event.type} />
            </div>
          </div>

          <!-- Driver Info (for driver events) or Admin Info (for admin events) -->
          <div class="space-y-1">
            <h3 class="text-sm font-medium">
              {event.type === "adminTripCostOverride" ||
              event.type === EventLogType.ADMIN_TRIP_COST_OVERRIDE
                ? "Admin"
                : m.eventDetail_driverLabel()}
            </h3>
            <div class="rounded-md bg-muted p-3">
              {#if event.type === "adminTripCostOverride" || event.type === EventLogType.ADMIN_TRIP_COST_OVERRIDE}
                {@const admin = adminUsers.find((u) => u.uid === event.uid)}
                {#if admin}
                  <div class="flex items-center gap-3">
                    {#if admin.photoURL}
                      <img
                        src={admin.photoURL}
                        alt={getAdminDisplayName(admin)}
                        class="w-10 h-10 rounded-full"
                      />
                    {:else}
                      <div
                        class="w-10 h-10 rounded-full bg-primary text-primary-foreground flex items-center justify-center"
                      >
                        {getInitials(getAdminDisplayName(admin))}
                      </div>
                    {/if}
                    <div>
                      <p class="font-medium">{getAdminDisplayName(admin)}</p>
                      <p class="text-sm text-muted-foreground">{admin.email}</p>
                    </div>
                  </div>
                {:else}
                  <p class="text-sm text-muted-foreground">
                    Admin ID: {event.uid}
                  </p>
                {/if}
              {:else if event.driver}
                {@const user = usersMap.get(event.driver.uid)}
                <div class="flex items-center gap-3">
                  {#if user && user.photoURL}
                    <img
                      src={user.photoURL}
                      alt={user
                        ? getDisplayName(user)
                        : event.driver.displayName ||
                          m.eventsLayout_unknownDriver()}
                      class="w-10 h-10 rounded-full"
                    />
                  {:else}
                    <div
                      class="w-10 h-10 rounded-full bg-primary text-primary-foreground flex items-center justify-center"
                    >
                      {user
                        ? getInitials(getDisplayName(user))
                        : getInitials(
                            event.driver.displayName ||
                              m.eventsLayout_unknownUser(),
                          )}
                    </div>
                  {/if}
                  <div>
                    <p class="font-medium">
                      {user
                        ? getDisplayName(user)
                        : event.driver.displayName ||
                          m.eventsLayout_unknownDriver()}
                    </p>
                    <p class="text-sm text-muted-foreground">
                      {user && user.phoneNumber
                        ? user.phoneNumber
                        : user && user.email
                          ? user.email
                          : ""}
                    </p>
                  </div>
                </div>
              {/if}
            </div>
          </div>

          <!-- Timestamp -->
          <div class="space-y-1">
            <h3 class="text-sm font-medium">
              {m.eventDetail_timestampLabel()}
            </h3>
            <div class="rounded-md bg-muted p-3">
              <p>{formatLongDateTime(event.timestamp)}</p>
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-6">
          <!-- Reason -->
          <div class="space-y-1">
            <h3 class="text-sm font-medium">{m.eventDetail_reasonLabel()}</h3>
            <div class="rounded-md bg-muted p-3">
              <p>{getReasonLabel(event)}</p>
              {#if event.reason && ((event.type === EventLogType.DRIVER_SERVICE_STATUS_UPDATE && event.serviceStatusReasonType === ServiceStatusReasonType.CUSTOM) || (event.type === EventLogType.DRIVER_TRIP_REJECTED && event.tripRejectionReasonType === TripRejectionReasonType.CUSTOM))}
                <p class="text-sm text-muted-foreground mt-1">
                  {m.eventDetail_customReasonPrefix()}
                  {event.reason}
                </p>
              {/if}
            </div>
          </div>

          <!-- Trip Info (if applicable) -->
          {#if event.trip}
            <div class="space-y-1">
              <h3 class="text-sm font-medium">
                {m.eventDetail_tripInfoLabel()}
              </h3>
              <div class="rounded-md bg-muted p-3">
                <p class="font-medium">
                  {m.eventDetail_tripIdPrefix()}
                  {event.trip.id}
                </p>
                {#if event.trip.startLocationName}
                  <p class="text-sm">
                    {m.eventDetail_tripFromPrefix()}
                    {event.trip.startLocationName}
                  </p>
                {/if}
                {#if event.trip.arrivalLocationName}
                  <p class="text-sm">
                    {m.eventDetail_tripToPrefix()}
                    {event.trip.arrivalLocationName}
                  </p>
                {/if}
                {#if event.type === "adminTripCostOverride" || event.type === EventLogType.ADMIN_TRIP_COST_OVERRIDE}
                  {#if event.trip.originalCost !== undefined}
                    <p class="text-sm mt-2">
                      <span class="font-medium">Original Cost:</span>
                      {formatCurrency(event.trip.originalCost)}
                    </p>
                  {/if}
                  {#if event.trip.newCost !== undefined}
                    <p class="text-sm">
                      <span class="font-medium">New Cost:</span>
                      {formatCurrency(event.trip.newCost)}
                    </p>
                  {/if}
                  {#if event.trip.realCost !== undefined}
                    <p class="text-sm">
                      <span class="font-medium">Calculated Cost:</span>
                      {formatCurrency(event.trip.realCost)}
                    </p>
                  {/if}
                {/if}
              </div>
            </div>
          {/if}

          <!-- Additional Metadata (if available) -->
          {#if event.metadata && Object.keys(event.metadata).length > 0}
            <div class="space-y-1">
              <h3 class="text-sm font-medium">
                {m.eventDetail_additionalInfoLabel()}
              </h3>
              <div class="rounded-md bg-muted p-3 space-y-2">
                {#each Object.entries(event.metadata) as [key, value]}
                  <div class="flex justify-between items-start">
                    <span class="text-sm font-medium"
                      >{formatMetadataKey(key)}</span
                    >
                    <span class="text-sm text-muted-foreground text-right ml-2">
                      {formatMetadataValue(key, value)}
                    </span>
                  </div>
                {/each}
              </div>
            </div>
          {/if}
        </div>
      </div>
    {:else}
      <div class="flex items-center justify-center h-48">
        <p class="text-muted-foreground">{m.eventDetail_eventNotFound()}</p>
      </div>
    {/if}
  </Card.Content>
</Card.Root>
