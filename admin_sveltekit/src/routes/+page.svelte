<script lang="ts">
  import { auth, provider } from "$lib/firebase.client";
  import { signInWithPopup } from "firebase/auth";
  import { getFirebaseUser } from "$lib/stores/auth.svelte";
  import MainPageOutlet from "./MainPageOutlet.svelte";

  let signingIn = $state(false);
  let error = $state<string | null>(null);

  const firebaseUser = $derived(getFirebaseUser());

  const signIn = async () => {
    signingIn = true;
    error = null;
    try {
      if (!provider || !auth) {
        throw new Error("Firebase auth not initialized");
      }
      provider.setCustomParameters({
        prompt: "select_account",
      });
      await signInWithPopup(auth, provider);
    } catch (err) {
      error = "Authentication failed. Please try again.";
      console.error(err);
    } finally {
      signingIn = false;
    }
  };
</script>

<MainPageOutlet
  user={firebaseUser}
  loading={signingIn}
  {error}
  onSignInClick={signIn}
/>
