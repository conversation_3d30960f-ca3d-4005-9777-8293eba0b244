<script lang="ts">
  import { page } from "$app/state";
  import { onMount, onDestroy } from "svelte";
  import * as Card from "$lib/components/ui/card/index.js";
  import { Button } from "$lib/components/ui/button";
  import { Badge } from "$lib/components/ui/badge";
  import { Textarea } from "$lib/components/ui/textarea/index.js";
  import { Label } from "$lib/components/ui/label";
  import * as Select from "$lib/components/ui/select/index.js";
  import Spinner from "$lib/components/ui/Spinner.svelte";
  import {
    User,
    Check,
    X,
    AlertTriangle,
    History,
    Building,
  } from "lucide-svelte";
  import { format } from "date-fns";
  import { toast } from "svelte-sonner";
  import * as m from "$lib/paraglide/messages";

  import {
    getVehiclesMap,
    getVehicleLinkingsMap,
    updateVehicleStatus,
  } from "$lib/stores/vehicles.svelte";

  import {
    getLinkings,
    linkVehicleToTenant,
    approveUserVehicle,
    assignVehicleToDriver,
    unassignVehicleFromDriver,
    init as initLinkings,
  } from "$lib/stores/vehicle_linking.svelte";

  import {
    init as initAssignments,
    destroy as destroyAssignments,
    getAssignmentHistoryForVehicle,
    getAssignmentReasonDisplayName,
  } from "$lib/stores/vehicle_assignments.svelte";

  import { getUsers, getUsersMap } from "$lib/stores/mobile_users.svelte";

  const vehicleId = $derived(page.params.vehicleId);
  const vehiclesMap = $derived(getVehiclesMap());
  const linkingsMap = $derived(getVehicleLinkingsMap());
  const usersMap = $derived(getUsersMap());
  const allDrivers = $derived(
    getUsers().filter((u) => u.primaryUserType === 1 && u.isDriverConfirmed),
  );

  const activeLinkings = $derived(getLinkings());
  const assignedDriverIds = $derived(
    new Set(
      activeLinkings
        .filter(
          (linking) =>
            linking.currentDriverId && linking.vehicleId !== vehicleId, // Exclude current vehicle
        )
        .map((linking) => linking.currentDriverId!),
    ),
  );

  const availableDrivers = $derived(
    allDrivers.filter((driver) => !assignedDriverIds.has(driver.uid)),
  );

  let vehicle = $derived(vehiclesMap.get(vehicleId) || null);
  let linking = $derived(linkingsMap.get(vehicleId) || null);
  let assignments = $derived(getAssignmentHistoryForVehicle(vehicleId));

  // Make these reactive so they update when linking changes
  let owner = $derived(
    vehicle?.ownerUID ? usersMap.get(vehicle.ownerUID) : null,
  );
  let currentDriver = $derived(
    linking?.currentDriverId ? usersMap.get(linking.currentDriverId) : null,
  );

  let adminRemark = $state("");
  let selectedDriverId = $state("");
  let isUpdating = $state(false);

  onMount(() => {
    initAssignments();
    initLinkings();
  });

  onDestroy(() => {
    destroyAssignments();
    // Don't destroy linkings - it's managed by the layout
  });

  $effect(() => {
    if (linking) {
      adminRemark = linking.tenantRemark || "";
      // Only set selectedDriverId if there's no current driver (for the assignment select)
      selectedDriverId = "";
    }
  });

  async function handleApproval(approved: boolean) {
    if (!linking || isUpdating) return;

    isUpdating = true;
    try {
      await approveUserVehicle(linking.id!, approved, adminRemark || undefined);
      toast.success(
        approved
          ? m.vehicleDetails_vehicleApprovedToast()
          : m.vehicleDetails_vehicleRejectedToast(),
      );
    } catch (error) {
      console.error("Error updating vehicle approval:", error);
      toast.error(m.vehicleDetails_updateVehicleApprovalFailedToast());
    } finally {
      isUpdating = false;
    }
  }

  async function handleLinkToTenant() {
    if (!vehicle || isUpdating) return;

    isUpdating = true;
    try {
      await linkVehicleToTenant(vehicle.id!);
      toast.success(m.vehicleDetails_vehicleLinkedToast());
    } catch (error) {
      console.error("Error linking vehicle:", error);
      toast.error(m.vehicleDetails_linkVehicleFailedToast());
    } finally {
      isUpdating = false;
    }
  }

  async function handleAssignDriver() {
    if (!linking || !selectedDriverId || isUpdating) return;

    isUpdating = true;
    try {
      await assignVehicleToDriver(linking.id!, selectedDriverId);
      toast.success(m.vehicleDetails_driverAssignedToast());
      selectedDriverId = "";
    } catch (error) {
      console.error("Error assigning driver:", error);
      toast.error(m.vehicleDetails_assignDriverFailedToast());
    } finally {
      isUpdating = false;
    }
  }

  async function handleUnassignDriver() {
    if (!linking || isUpdating) return;

    isUpdating = true;
    try {
      await unassignVehicleFromDriver(linking.id!);
      toast.success(m.vehicleDetails_driverUnassignedToast());
    } catch (error) {
      console.error("Error unassigning driver:", error);
      toast.error(m.vehicleDetails_unassignDriverFailedToast());
    } finally {
      isUpdating = false;
    }
  }

  async function handleToggleStatus() {
    if (!vehicle || isUpdating) return;

    isUpdating = true;
    try {
      await updateVehicleStatus(vehicle.id!, !vehicle.isActive);
      toast.success(
        vehicle.isActive
          ? m.vehicleDetails_vehicleDeactivatedToast()
          : m.vehicleDetails_vehicleActivatedToast(),
      );
    } catch (error) {
      console.error("Error updating vehicle status:", error);
      toast.error(m.vehicleDetails_updateVehicleStatusFailedToast());
    } finally {
      isUpdating = false;
    }
  }
</script>

<svelte:head>
  <title>{m.vehicleDetails_pageTitle()}</title>
</svelte:head>

<Card.Root class="h-[calc(100vh-90px)]">
  <Card.Content class="h-full overflow-y-auto p-6">
    {#if !vehicle}
      <div class="py-12">
        <div class="flex items-center justify-center">
          <Spinner className="h-8 w-8" />
        </div>
      </div>
    {:else}
      <div class="mb-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold">
              {vehicle.displayName || `${vehicle.brand} ${vehicle.model}`}
            </h1>
            <div class="flex items-center gap-2 mt-1">
              <p class="text-muted-foreground">
                {vehicle.color} • {vehicle.registrationNumber}
              </p>
              {#if vehicle.isActive}
                <Badge class="bg-green-100 text-green-800"
                  >{m.vehicleDetails_active()}</Badge
                >
              {:else}
                <Badge variant="secondary">{m.vehicleDetails_inactive()}</Badge>
              {/if}
              {#if linking}
                {#if !linking.tenantApproved}
                  <Badge class="bg-yellow-100 text-yellow-800"
                    >{m.vehicleDetails_pendingApproval()}</Badge
                  >
                {:else if linking.currentDriverId}
                  <Badge class="bg-blue-100 text-blue-800"
                    >{m.vehicleDetails_assigned()}</Badge
                  >
                {:else}
                  <Badge variant="outline">{m.vehicleDetails_available()}</Badge
                  >
                {/if}
              {:else}
                <Badge variant="secondary">{m.vehicleDetails_notLinked()}</Badge
                >
              {/if}
            </div>
          </div>
          <div class="flex items-center gap-3">
            <Button variant="outline" href="/vehicles/assignments/{vehicleId}">
              <History class="h-4 w-4 mr-2" />
              {m.vehicleDetails_assignmentHistory()}
            </Button>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Vehicle Details -->
        <Card.Root>
          <Card.Header>
            <Card.Title>{m.vehicleDetails_vehicleDetailsTitle()}</Card.Title>
          </Card.Header>
          <Card.Content>
            <div class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <Label class="text-muted-foreground"
                    >{m.vehicleDetails_brandLabel()}</Label
                  >
                  <p class="font-medium">{vehicle.brand}</p>
                </div>
                <div>
                  <Label class="text-muted-foreground"
                    >{m.vehicleDetails_modelLabel()}</Label
                  >
                  <p class="font-medium">{vehicle.model}</p>
                </div>
                <div>
                  <Label class="text-muted-foreground"
                    >{m.vehicleDetails_yearLabel()}</Label
                  >
                  <p class="font-medium">{vehicle.year}</p>
                </div>
                <div>
                  <Label class="text-muted-foreground"
                    >{m.vehicleDetails_colorLabel()}</Label
                  >
                  <p class="font-medium">{vehicle.color}</p>
                </div>
                <div>
                  <Label class="text-muted-foreground"
                    >{m.vehicleDetails_registrationLabel()}</Label
                  >
                  <p class="font-medium">{vehicle.registrationNumber}</p>
                </div>
                <div>
                  <Label class="text-muted-foreground"
                    >{m.vehicleDetails_maxPassengersLabel()}</Label
                  >
                  <p class="font-medium">{vehicle.maxPassengers}</p>
                </div>
              </div>

              <div class="pt-4 border-t">
                <div>
                  <Label class="text-muted-foreground"
                    >{m.vehicleDetails_createdLabel()}</Label
                  >
                  <p class="font-medium">
                    {format(vehicle.createdAt, "MMM d, yyyy h:mm a")}
                  </p>
                </div>
              </div>

              <div class="pt-4 border-t">
                <Button
                  variant={vehicle.isActive ? "destructive" : "default"}
                  onclick={handleToggleStatus}
                  disabled={isUpdating}
                  class="w-full"
                >
                  {#if isUpdating}
                    <Spinner className="mr-2 h-4 w-4" />
                  {/if}
                  {vehicle.isActive
                    ? m.vehicleDetails_deactivateVehicle()
                    : m.vehicleDetails_activateVehicle()}
                </Button>
              </div>
            </div>
          </Card.Content>
        </Card.Root>

        <!-- Ownership & Assignment -->
        <Card.Root>
          <Card.Header>
            <Card.Title
              >{m.vehicleDetails_ownershipAssignmentTitle()}</Card.Title
            >
          </Card.Header>
          <Card.Content>
            <div class="space-y-4">
              {#if owner}
                <div>
                  <Label class="text-muted-foreground"
                    >{m.vehicleDetails_ownerLabel()}</Label
                  >
                  <div class="flex items-center gap-2 mt-1">
                    <User class="h-4 w-4" />
                    <p class="font-medium">
                      {owner.displayName ||
                        owner.email ||
                        m.vehicleDetails_unknown()}
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    href="/rides/drivers/{owner.uid}/details"
                    class="w-full mt-2"
                  >
                    {m.vehicleDetails_viewOwnerProfile()}
                  </Button>
                </div>
              {:else}
                <div>
                  <Label class="text-muted-foreground"
                    >{m.vehicleDetails_ownershipLabel()}</Label
                  >
                  <div class="flex items-center gap-2 mt-1">
                    <Building class="h-4 w-4" />
                    <p class="font-medium">
                      {m.vehicleDetails_tenantOwnedVehicle()}
                    </p>
                  </div>
                </div>
              {/if}

              {#if currentDriver}
                <div class="pt-4 border-t">
                  <Label class="text-muted-foreground"
                    >{m.vehicleDetails_currentDriverLabel()}</Label
                  >
                  <div class="flex items-center gap-2 mt-1">
                    <User class="h-4 w-4 text-green-600" />
                    <p class="font-medium">
                      {currentDriver.displayName ||
                        currentDriver.email ||
                        m.vehicleDetails_unknown()}
                    </p>
                  </div>
                  <div class="flex gap-2 mt-2">
                    <Button
                      variant="outline"
                      href="/rides/drivers/{currentDriver.uid}/details"
                      class="flex-1"
                    >
                      {m.vehicleDetails_viewDriver()}
                    </Button>
                    <Button
                      variant="destructive"
                      onclick={handleUnassignDriver}
                      disabled={isUpdating}
                      class="flex-1"
                    >
                      {m.vehicleDetails_unassign()}
                    </Button>
                  </div>
                </div>
              {:else if linking?.tenantApproved}
                <div class="pt-4 border-t">
                  <Label for="driver-select"
                    >{m.vehicleDetails_assignDriverLabel()}</Label
                  >
                  <Select.Root
                    type="single"
                    bind:value={selectedDriverId}
                    disabled={isUpdating}
                  >
                    <Select.Trigger class="mt-1">
                      {#if selectedDriverId}
                        {@const selectedDriver = availableDrivers.find(
                          (d) => d.uid === selectedDriverId,
                        )}
                        {selectedDriver?.displayName ||
                          selectedDriver?.email ||
                          selectedDriver?.phoneNumber ||
                          m.vehicleDetails_unknownDriver()}
                      {:else}
                        <span class="text-muted-foreground"
                          >{m.vehicleDetails_selectDriverPlaceholder()}</span
                        >
                      {/if}
                    </Select.Trigger>
                    <Select.Content>
                      {#each availableDrivers as driver}
                        <Select.Item value={driver.uid}>
                          {driver.displayName ||
                            driver.email ||
                            driver.phoneNumber}
                        </Select.Item>
                      {/each}
                    </Select.Content>
                  </Select.Root>
                  <Button
                    onclick={handleAssignDriver}
                    disabled={!selectedDriverId || isUpdating}
                    class="w-full mt-2"
                  >
                    {#if isUpdating}
                      <Spinner className="mr-2 h-4 w-4" />
                    {/if}
                    {m.vehicleDetails_assignDriver()}
                  </Button>
                </div>
              {/if}
            </div>
          </Card.Content>
        </Card.Root>
      </div>

      <!-- Approval Section (for user-owned vehicles) -->
      {#if owner && linking && !linking.tenantApproved}
        <Card.Root class="mt-6">
          <Card.Header>
            <Card.Title class="flex items-center gap-2 text-yellow-600">
              <AlertTriangle class="h-5 w-5" />
              {m.vehicleDetails_vehicleApprovalRequired()}
            </Card.Title>
          </Card.Header>
          <Card.Content>
            <div class="space-y-4">
              <div>
                <Label for="admin-remark"
                  >{m.vehicleDetails_adminRemarkLabel()}</Label
                >
                <Textarea
                  id="admin-remark"
                  bind:value={adminRemark}
                  placeholder={m.vehicleDetails_adminRemarkPlaceholder()}
                  rows={3}
                  disabled={isUpdating}
                />
              </div>

              <div class="flex gap-3">
                <Button
                  variant="default"
                  onclick={() => handleApproval(true)}
                  disabled={isUpdating}
                  class="flex-1"
                >
                  {#if isUpdating}
                    <Spinner className="mr-2 h-4 w-4" />
                  {:else}
                    <Check class="h-4 w-4 mr-2" />
                  {/if}
                  {m.vehicleDetails_approveVehicle()}
                </Button>

                <Button
                  variant="destructive"
                  onclick={() => handleApproval(false)}
                  disabled={isUpdating}
                  class="flex-1"
                >
                  {#if isUpdating}
                    <Spinner className="mr-2 h-4 w-4" />
                  {:else}
                    <X class="h-4 w-4 mr-2" />
                  {/if}
                  {m.vehicleDetails_rejectVehicle()}
                </Button>
              </div>
            </div>
          </Card.Content>
        </Card.Root>
      {/if}

      <!-- Link to Tenant (if not linked) -->
      {#if !linking}
        <Card.Root class="mt-6">
          <Card.Header>
            <Card.Title>{m.vehicleDetails_linkToTenantTitle()}</Card.Title>
          </Card.Header>
          <Card.Content>
            <p class="text-muted-foreground mb-4">
              {m.vehicleDetails_linkToTenantDescription()}
            </p>
            <Button
              onclick={handleLinkToTenant}
              disabled={isUpdating}
              class="w-full"
            >
              {#if isUpdating}
                <Spinner className="mr-2 h-4 w-4" />
              {/if}
              {m.vehicleDetails_linkVehicleToTenant()}
            </Button>
          </Card.Content>
        </Card.Root>
      {/if}

      <!-- Assignment History -->
      {#if assignments.length > 0}
        <Card.Root class="mt-6">
          <Card.Header>
            <Card.Title class="flex items-center gap-2">
              <History class="h-5 w-5" />
              {m.vehicleDetails_assignmentHistoryTitle()}
            </Card.Title>
          </Card.Header>
          <Card.Content>
            <div class="space-y-4">
              {#each assignments as assignment}
                {@const driver = usersMap.get(assignment.driverUID)}
                <div class="pb-4 border-b last:border-0 last:pb-0">
                  <div class="flex items-start justify-between">
                    <div>
                      <p class="font-medium">
                        {driver?.displayName ||
                          driver?.email ||
                          m.vehicleDetails_unknownDriver()}
                      </p>
                      <p class="text-sm text-muted-foreground">
                        {getAssignmentReasonDisplayName(assignment.reason)}
                      </p>
                      <p class="text-xs text-muted-foreground mt-1">
                        {format(assignment.assignedAt, "MMM d, yyyy h:mm a")}
                        {#if assignment.unassignedAt}
                          - {format(
                            assignment.unassignedAt,
                            "MMM d, yyyy h:mm a",
                          )}
                        {/if}
                      </p>
                      {#if assignment.notes}
                        <p class="text-sm mt-2">{assignment.notes}</p>
                      {/if}
                    </div>
                    <Badge
                      variant={assignment.isActive ? "default" : "secondary"}
                    >
                      {assignment.isActive
                        ? m.vehicleDetails_current()
                        : m.vehicleDetails_past()}
                    </Badge>
                  </div>
                </div>
              {/each}
            </div>
          </Card.Content>
        </Card.Root>
      {/if}
    {/if}
  </Card.Content>
</Card.Root>
