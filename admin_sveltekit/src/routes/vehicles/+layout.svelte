<script lang="ts">
  import { onMount, onDestroy } from "svelte";
  import * as Card from "$lib/components/ui/card/index.js";
  import { But<PERSON> } from "$lib/components/ui/button";
  import { Badge } from "$lib/components/ui/badge";
  import { Input } from "$lib/components/ui/input";
  import { Toggle } from "$lib/components/ui/toggle";
  import Spinner from "$lib/components/ui/Spinner.svelte";
  import { Car, Search, Plus, User, Building } from "lucide-svelte";
  import { page } from "$app/state";
  import { localizedGoto } from "$lib/utils";
  import * as m from "$lib/paraglide/messages";

  import {
    init as initVehicles,
    destroy as destroyVehicles,
    getFilteredVehicles,
    getLoading as getVehiclesLoading,
    getError as getVehiclesError,
    getShowInactive,
    setShowInactive,
    type Vehicle,
  } from "$lib/stores/vehicles.svelte";

  import {
    init as initLinkings,
    destroy as destroyLinkings,
    getLinkings,
    getPendingApprovalLinkings,
    getAssignedLinkings,
    type VehicleLinking,
  } from "$lib/stores/vehicle_linking.svelte";

  import {
    getUsersMap,
    init as initMobileUsers,
    destroy as destroyMobileUsers,
  } from "$lib/stores/mobile_users.svelte";

  let searchQuery = $state("");
  let showInactive = $derived(getShowInactive());

  onMount(() => {
    initVehicles();
    initLinkings();
    initMobileUsers();
  });

  onDestroy(() => {
    // Don't destroy the stores, keep for reuse
  });

  let vehiclesLoading = $derived(getVehiclesLoading());
  let vehiclesError = $derived(getVehiclesError());
  let vehicles = $derived(getFilteredVehicles());
  let linkings = $derived(getLinkings());
  let pendingApprovals = $derived(getPendingApprovalLinkings());
  let assignedLinkings = $derived(getAssignedLinkings());
  let usersMap = $derived(getUsersMap());
  let selectedVehicleId = $derived(page.params.vehicleId || null);

  let filteredVehicles = $derived(
    vehicles.filter((vehicle) => {
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const ownerName = vehicle.ownerUID
          ? usersMap.get(vehicle.ownerUID)?.displayName || ""
          : "";
        return (
          vehicle.brand.toLowerCase().includes(query) ||
          vehicle.model.toLowerCase().includes(query) ||
          vehicle.color.toLowerCase().includes(query) ||
          vehicle.registrationNumber.toLowerCase().includes(query) ||
          ownerName.toLowerCase().includes(query)
        );
      }
      return true;
    }),
  );

  function handleShowInactiveChange(checked: boolean) {
    setShowInactive(checked);
  }

  function getVehicleLinking(vehicleId: string) {
    return linkings.find((l) => l.vehicleId === vehicleId);
  }

  function getVehicleStatus(vehicle: Vehicle) {
    const linking = getVehicleLinking(vehicle.id!);
    if (!linking)
      return {
        status: "not-linked",
        label: m.vehiclesLayout_statusNotLinked(),
        color: "bg-gray-100 text-gray-800",
      };
    if (!linking.tenantApproved)
      return {
        status: "pending",
        label: m.vehiclesLayout_statusPending(),
        color: "bg-yellow-100 text-yellow-800",
      };
    if (linking.currentDriverId)
      return {
        status: "assigned",
        label: m.vehiclesLayout_statusAssigned(),
        color: "bg-green-100 text-green-800",
      };
    return {
      status: "available",
      label: m.vehiclesLayout_statusAvailable(),
      color: "bg-blue-100 text-blue-800",
    };
  }

  const { children } = $props();
</script>

<svelte:head>
  <title>{m.vehiclesLayout_pageTitle()}</title>
</svelte:head>

<div class="p-4">
  <div class="flex h-full gap-4">
    <!-- Left section - Vehicles list with stats -->
    <div class="w-1/3">
      <Card.Root class="h-[calc(100vh-90px)]">
        <Card.Header>
          <div class="flex items-start justify-between">
            <div>
              <Card.Title>{m.vehiclesLayout_title()}</Card.Title>
              <Card.Description
                >{m.vehiclesLayout_description()}</Card.Description
              >
            </div>
            <Button size="sm" href="/vehicles/add">
              <Plus class="h-4 w-4 mr-1" />
              {m.vehiclesLayout_addButton()}
            </Button>
          </div>
        </Card.Header>
        <Card.Content class="h-[calc(100vh-152px)] overflow-y-auto">
          {#if vehiclesError}
            <div class="text-destructive mb-4">
              {m.vehiclesLayout_errorPrefix()}{vehiclesError}
            </div>
          {/if}

          <!-- Summary Stats -->
          <div class="grid grid-cols-4 gap-2 mb-4">
            <div class="bg-muted/50 rounded-lg p-2 text-center">
              <div class="text-lg font-bold">{vehicles.length}</div>
              <div class="text-xs text-muted-foreground">
                {m.vehiclesLayout_totalLabel()}
              </div>
            </div>
            <div class="bg-yellow-50 rounded-lg p-2 text-center">
              <div class="text-lg font-bold text-yellow-600">
                {pendingApprovals.length}
              </div>
              <div class="text-xs text-muted-foreground">
                {m.vehiclesLayout_pendingLabel()}
              </div>
            </div>
            <div class="bg-green-50 rounded-lg p-2 text-center">
              <div class="text-lg font-bold text-green-600">
                {assignedLinkings.length}
              </div>
              <div class="text-xs text-muted-foreground">
                {m.vehiclesLayout_assignedLabel()}
              </div>
            </div>
            <div class="bg-blue-50 rounded-lg p-2 text-center">
              <div class="text-lg font-bold text-blue-600">
                {linkings.filter((l) => l.tenantApproved && !l.currentDriverId)
                  .length}
              </div>
              <div class="text-xs text-muted-foreground">
                {m.vehiclesLayout_availableLabel()}
              </div>
            </div>
          </div>

          <!-- Search input -->
          <div class="relative mb-3">
            <Search
              class="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"
            />
            <Input
              type="text"
              placeholder={m.vehiclesLayout_searchPlaceholder()}
              bind:value={searchQuery}
              class="pl-9"
            />
          </div>

          <!-- Show inactive checkbox -->
          <div class="flex items-center gap-2 mb-4">
            <input
              type="checkbox"
              id="show-inactive"
              checked={showInactive}
              onchange={(e) =>
                handleShowInactiveChange(
                  (e.target as HTMLInputElement).checked,
                )}
              class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
            />
            <label
              for="show-inactive"
              class="text-sm font-medium leading-none cursor-pointer select-none"
            >
              {m.vehiclesLayout_showInactiveLabel()}
            </label>
          </div>

          <!-- Vehicles count -->
          <div class="flex justify-between items-center mb-4">
            <span class="text-sm text-muted-foreground">
              {filteredVehicles.length === 1
                ? m.vehiclesLayout_vehicleCount_one({
                    count: filteredVehicles.length,
                  })
                : m.vehiclesLayout_vehicleCount_other({
                    count: filteredVehicles.length,
                  })}
            </span>
          </div>

          <!-- Vehicles list -->
          <div class="space-y-2">
            {#if vehiclesLoading}
              <div class="flex items-center justify-center py-8">
                <Spinner className="h-6 w-6" />
              </div>
            {:else if filteredVehicles.length === 0}
              <div class="text-center py-8">
                <Car class="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                <p class="text-sm text-muted-foreground">
                  {m.vehiclesLayout_noVehiclesFound()}
                </p>
              </div>
            {:else}
              {#each filteredVehicles as vehicle}
                {@const linking = getVehicleLinking(vehicle.id!)}
                {@const status = getVehicleStatus(vehicle)}
                {@const owner = vehicle.ownerUID
                  ? usersMap.get(vehicle.ownerUID)
                  : null}
                {@const driver = linking?.currentDriverId
                  ? usersMap.get(linking.currentDriverId)
                  : null}

                <button
                  onclick={() => localizedGoto(`/vehicles/${vehicle.id}`)}
                  class="w-full text-left p-3 rounded-lg hover:bg-muted transition-colors {selectedVehicleId ===
                  vehicle.id
                    ? 'bg-muted'
                    : ''}"
                >
                  <div class="flex items-start justify-between gap-2">
                    <div class="flex-1 min-w-0">
                      <h4 class="font-medium truncate">
                        {vehicle.displayName ||
                          `${vehicle.brand} ${vehicle.model}`}
                      </h4>
                      <p class="text-sm text-muted-foreground truncate">
                        {vehicle.color} • {vehicle.registrationNumber}
                      </p>
                      <div
                        class="flex items-center gap-1 mt-1 text-xs text-muted-foreground"
                      >
                        {#if owner}
                          <User class="h-3 w-3" />
                          <span class="truncate"
                            >{owner.displayName ||
                              owner.email ||
                              m.vehiclesLayout_userLabel()}</span
                          >
                        {:else}
                          <Building class="h-3 w-3" />
                          <span>{m.vehiclesLayout_tenantLabel()}</span>
                        {/if}
                        {#if driver && driver.uid !== owner?.uid}
                          <span class="mx-1">•</span>
                          <span class="truncate text-green-600">
                            {driver.displayName ||
                              driver.email ||
                              m.vehiclesLayout_driverLabel()}
                          </span>
                        {/if}
                      </div>
                      <div class="flex items-center gap-2 mt-2">
                        <Badge class={status.color + " text-xs"}>
                          {status.label}
                        </Badge>
                        {#if !vehicle.isActive}
                          <Badge variant="secondary" class="text-xs">
                            {m.vehiclesLayout_statusInactive()}
                          </Badge>
                        {/if}
                      </div>
                    </div>
                    <div class="text-xs text-muted-foreground">
                      {vehicle.maxPassengers}p
                    </div>
                  </div>
                </button>
              {/each}
            {/if}
          </div>
        </Card.Content>
      </Card.Root>
    </div>

    <!-- Right section - Content -->
    <div class="flex-1">
      {@render children()}
    </div>
  </div>
</div>
