<script lang="ts">
  import { page } from "$app/state";
  import { onMount, onDestroy } from "svelte";
  import * as Card from "$lib/components/ui/card/index.js";
  import { Button } from "$lib/components/ui/button";
  import { Badge } from "$lib/components/ui/badge";
  import Spinner from "$lib/components/ui/Spinner.svelte";
  import {
    ArrowLeft,
    User,
    Calendar,
    Clock,
    FileText,
    UserCheck,
    UserX,
    Car,
  } from "lucide-svelte";
  import { format, formatDistanceToNow } from "date-fns";
  import { localizedGoto } from "$lib/utils";
  import * as m from "$lib/paraglide/messages";

  import {
    init,
    destroy,
    getAssignmentsByVehicle,
    getLoading,
    getError,
    getAssignmentReasonDisplayName,
    type VehicleAssignment,
  } from "$lib/stores/vehicle_assignments.svelte";

  import { getVehiclesMap } from "$lib/stores/vehicles.svelte";
  import { getUsersMap } from "$lib/stores/mobile_users.svelte";

  const vehicleId = $derived(page.params.vehicleId);
  const vehiclesMap = $derived(getVehiclesMap());
  const usersMap = $derived(getUsersMap());

  let vehicle = $state<any>(null);
  let assignments = $state<VehicleAssignment[]>([]);
  let loading = $state(true);
  let error = $state<string | null>(null);

  onMount(() => {
    init();
    loadVehicleAssignments();
  });

  onDestroy(() => {
    destroy();
  });

  $effect(() => {
    vehicle = vehiclesMap.get(vehicleId);
  });

  $effect(() => {
    loading = getLoading();
    error = getError();
  });

  async function loadVehicleAssignments() {
    try {
      // Get assignments for this specific vehicle
      const vehicleAssignments = await getAssignmentsByVehicle(vehicleId);

      // Enrich with driver information
      assignments = vehicleAssignments.map((assignment) => ({
        ...assignment,
        driver: usersMap.get(assignment.driverUID),
      }));
    } catch (err) {
      console.error("Error loading vehicle assignments:", err);
      error = m.vehicleAssignments_loadAssignmentHistoryFailed();
    }
  }

  function getDuration(assignment: VehicleAssignment) {
    if (assignment.isActive) {
      return m.vehicleAssignments_activeForDuration({
        duration: formatDistanceToNow(assignment.assignedAt),
      });
    }
    if (assignment.unassignedAt) {
      const duration =
        assignment.unassignedAt.getTime() - assignment.assignedAt.getTime();
      const days = Math.floor(duration / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (duration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
      );

      if (days > 0) {
        return m.vehicleAssignments_durationDaysHours({
          days: days.toString(),
          dayPlural: days !== 1 ? "s" : "",
          hours: hours.toString(),
          hourPlural: hours !== 1 ? "s" : "",
        });
      }
      return m.vehicleAssignments_durationHours({
        hours: hours.toString(),
        plural: hours !== 1 ? "s" : "",
      });
    }
    return m.vehicleAssignments_durationNA();
  }

  function getStatusColor(assignment: VehicleAssignment) {
    return assignment.isActive ? "bg-green-500" : "bg-gray-400";
  }
</script>

<svelte:head>
  <title>{m.vehicleAssignments_pageTitle()}</title>
</svelte:head>

<Card.Root class="h-[calc(100vh-90px)] m-6">
  <Card.Content class="h-full overflow-y-auto p-6">
    <!-- Header -->
    <div class="mb-6">
      <Button
        variant="ghost"
        onclick={() => localizedGoto("/vehicles/" + vehicleId)}
        class="mb-4"
      >
        <ArrowLeft class="h-4 w-4 mr-2" />
        {m.vehicleAssignments_backToVehicleDetails()}
      </Button>

      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold">
            {m.vehicleAssignments_assignmentHistoryTitle()}
          </h1>
          {#if vehicle}
            <p class="text-muted-foreground mt-2">
              {vehicle.displayName} • {vehicle.registrationNumber}
            </p>
          {/if}
        </div>

        {#if vehicle}
          <Button href="/vehicles/{vehicleId}">
            <Car class="h-4 w-4 mr-2" />
            {m.vehicleAssignments_viewVehicleDetails()}
          </Button>
        {/if}
      </div>
    </div>

    {#if error}
      <div
        class="mb-6 p-4 border rounded-lg bg-destructive/10 text-destructive"
      >
        {m.vehicleAssignments_errorPrefix()}{error}
      </div>
    {/if}

    <!-- Assignment Timeline -->
    {#if loading}
      <div class="py-12">
        <div class="flex items-center justify-center">
          <Spinner className="h-8 w-8" />
        </div>
      </div>
    {:else if assignments.length === 0}
      <div class="py-12">
        <div class="flex flex-col items-center justify-center text-center">
          <Clock class="h-12 w-12 text-muted-foreground mb-4" />
          <h3 class="text-lg font-semibold mb-2">
            {m.vehicleAssignments_noAssignmentHistoryTitle()}
          </h3>
          <p class="text-muted-foreground">
            {m.vehicleAssignments_noAssignmentHistoryDescription()}
          </p>
        </div>
      </div>
    {:else}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100%-140px)]">
        <!-- Summary Card -->
        <Card.Root class="h-full">
          <Card.Header>
            <Card.Title>{m.vehicleAssignments_summaryTitle()}</Card.Title>
          </Card.Header>
          <Card.Content class="h-[calc(100%-80px)] overflow-y-auto">
            <div class="space-y-4">
              <div>
                <p class="text-sm text-muted-foreground">
                  {m.vehicleAssignments_totalAssignmentsLabel()}
                </p>
                <p class="text-2xl font-bold">{assignments.length}</p>
              </div>
              <div>
                <p class="text-sm text-muted-foreground">
                  {m.vehicleAssignments_currentStatusLabel()}
                </p>
                <Badge
                  variant={assignments[0]?.isActive ? "default" : "secondary"}
                >
                  {assignments[0]?.isActive
                    ? m.vehicleDetails_assigned()
                    : m.vehicleDetails_available()}
                </Badge>
              </div>
              {#if assignments[0]?.isActive}
                <div>
                  <p class="text-sm text-muted-foreground">
                    {m.vehicleAssignments_currentDriverLabel()}
                  </p>
                  <p class="font-medium">
                    {assignments[0].driver?.displayName ||
                      assignments[0].driver?.email ||
                      m.vehicleAssignments_unknownDriver()}
                  </p>
                </div>
              {/if}
            </div>
          </Card.Content>
        </Card.Root>

        <!-- Timeline -->
        <Card.Root class="h-full">
          <Card.Header>
            <Card.Title
              >{m.vehicleAssignments_assignmentTimelineTitle()}</Card.Title
            >
            <Card.Description>
              {m.vehicleAssignments_assignmentTimelineDescription()}
            </Card.Description>
          </Card.Header>
          <Card.Content class="h-[calc(100%-80px)] overflow-y-auto">
            <div class="relative">
              <!-- Timeline line -->
              <div class="absolute left-4 top-8 bottom-0 w-0.5 bg-border"></div>

              <!-- Timeline items -->
              <div class="space-y-8">
                {#each assignments as assignment, index}
                  <div class="relative flex gap-6">
                    <!-- Timeline dot -->
                    <div class="relative z-10">
                      <div
                        class={`w-8 h-8 rounded-full ${getStatusColor(assignment)} flex items-center justify-center`}
                      >
                        {#if assignment.isActive}
                          <div class="w-3 h-3 bg-white rounded-full"></div>
                        {/if}
                      </div>
                    </div>

                    <!-- Content -->
                    <div class="flex-1 pb-8">
                      <Card.Root>
                        <Card.Content class="pt-6">
                          <div class="flex items-start justify-between gap-4">
                            <div class="flex-1">
                              <!-- Driver Info -->
                              <div class="flex items-center gap-3 mb-3">
                                <User class="h-5 w-5 text-muted-foreground" />
                                <div>
                                  <h3 class="font-semibold">
                                    {assignment.driver?.displayName ||
                                      assignment.driver?.email ||
                                      m.vehicleAssignments_unknownDriver()}
                                  </h3>
                                  <p class="text-sm text-muted-foreground">
                                    {assignment.driver?.phoneNumber ||
                                      m.vehicleAssignments_noPhoneNumber()}
                                  </p>
                                </div>
                              </div>

                              <!-- Assignment Details -->
                              <div class="space-y-2">
                                <div class="flex items-center gap-2 text-sm">
                                  <Calendar
                                    class="h-4 w-4 text-muted-foreground"
                                  />
                                  <span class="font-medium"
                                    >{m.vehicleAssignments_assignedLabel()}</span
                                  >
                                  <span
                                    >{format(
                                      assignment.assignedAt,
                                      "MMM d, yyyy h:mm a",
                                    )}</span
                                  >
                                </div>

                                {#if !assignment.isActive && assignment.unassignedAt}
                                  <div class="flex items-center gap-2 text-sm">
                                    <Calendar
                                      class="h-4 w-4 text-muted-foreground"
                                    />
                                    <span class="font-medium"
                                      >{m.vehicleAssignments_unassignedLabel()}</span
                                    >
                                    <span
                                      >{format(
                                        assignment.unassignedAt,
                                        "MMM d, yyyy h:mm a",
                                      )}</span
                                    >
                                  </div>
                                {/if}

                                <div class="flex items-center gap-2 text-sm">
                                  <Clock
                                    class="h-4 w-4 text-muted-foreground"
                                  />
                                  <span class="font-medium"
                                    >{m.vehicleAssignments_durationLabel()}</span
                                  >
                                  <span>{getDuration(assignment)}</span>
                                </div>

                                <div class="flex items-center gap-2 text-sm">
                                  <FileText
                                    class="h-4 w-4 text-muted-foreground"
                                  />
                                  <span class="font-medium"
                                    >{m.vehicleAssignments_reasonLabel()}</span
                                  >
                                  <span
                                    >{getAssignmentReasonDisplayName(
                                      assignment.reason,
                                    )}</span
                                  >
                                </div>

                                {#if assignment.notes}
                                  <div class="mt-3 p-3 bg-muted rounded-lg">
                                    <p class="text-sm italic">
                                      "{assignment.notes}"
                                    </p>
                                  </div>
                                {/if}
                              </div>

                              <!-- Admin Info -->
                              <div
                                class="mt-4 pt-4 border-t flex items-center justify-between text-sm text-muted-foreground"
                              >
                                <div class="flex items-center gap-2">
                                  <UserCheck class="h-4 w-4" />
                                  <span
                                    >{m.vehicleAssignments_assignedByAdmin()}</span
                                  >
                                </div>

                                {#if !assignment.isActive && assignment.unassignedBy}
                                  <div class="flex items-center gap-2">
                                    <UserX class="h-4 w-4" />
                                    <span
                                      >{m.vehicleAssignments_unassignedByAdmin()}</span
                                    >
                                  </div>
                                {/if}
                              </div>
                            </div>

                            <!-- Status Badge -->
                            <div class="flex flex-col items-end gap-2">
                              <Badge
                                variant={assignment.isActive
                                  ? "default"
                                  : "secondary"}
                              >
                                {assignment.isActive
                                  ? m.vehicleAssignments_activeStatus()
                                  : m.vehicleAssignments_completedStatus()}
                              </Badge>

                              <Button
                                size="sm"
                                variant="outline"
                                href="/rides/drivers/{assignment.driverUID}/details"
                              >
                                {m.vehicleAssignments_viewDriver()}
                              </Button>
                            </div>
                          </div>
                        </Card.Content>
                      </Card.Root>
                    </div>
                  </div>
                {/each}
              </div>
            </div>
          </Card.Content>
        </Card.Root>
      </div>
    {/if}
  </Card.Content>
</Card.Root>
