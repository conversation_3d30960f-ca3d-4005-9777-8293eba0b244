<script lang="ts">
  import { onMount, onDestroy } from "svelte";
  import * as Card from "$lib/components/ui/card/index.js";
  import { Button } from "$lib/components/ui/button";
  import { Badge } from "$lib/components/ui/badge";
  import { Input } from "$lib/components/ui/input";
  import Spinner from "$lib/components/ui/Spinner.svelte";
  import { Search, User, Car, Calendar, History } from "lucide-svelte";
  import { format, formatDistanceToNow } from "date-fns";
  import * as m from "$lib/paraglide/messages";

  import {
    init,
    destroy,
    getFilteredAssignments,
    getLoading,
    getError,
    getShowOnlyActive,
    setShowOnlyActive,
    getAssignmentReasonDisplayName,
    type VehicleAssignment,
  } from "$lib/stores/vehicle_assignments.svelte";

  import { getVehiclesMap } from "$lib/stores/vehicles.svelte";
  import { getUsersMap } from "$lib/stores/mobile_users.svelte";

  let searchQuery = $state("");
  let showOnlyActive = $state(true);

  onMount(() => {
    init();
  });

  onDestroy(() => {
    destroy();
  });

  let loading = $derived(getLoading());
  let error = $derived(getError());
  let assignments = $derived(getFilteredAssignments());
  let vehiclesMap = $derived(getVehiclesMap());
  let usersMap = $derived(getUsersMap());

  let filteredAssignments = $derived(
    assignments.filter((assignment) => {
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const vehicle = assignment.vehicle;
        const driver = assignment.driver;

        return (
          vehicle?.brand.toLowerCase().includes(query) ||
          vehicle?.model.toLowerCase().includes(query) ||
          vehicle?.registrationNumber.toLowerCase().includes(query) ||
          driver?.displayName?.toLowerCase().includes(query) ||
          driver?.email?.toLowerCase().includes(query) ||
          assignment.reason.toLowerCase().includes(query)
        );
      }
      return true;
    }),
  );

  function handleShowOnlyActiveChange(checked: boolean) {
    showOnlyActive = checked;
    setShowOnlyActive(checked);
    destroy();
    init();
  }

  function getDuration(assignment: VehicleAssignment) {
    if (assignment.isActive) {
      return formatDistanceToNow(assignment.assignedAt, { addSuffix: false });
    }
    if (assignment.unassignedAt) {
      const duration =
        assignment.unassignedAt.getTime() - assignment.assignedAt.getTime();
      const days = Math.floor(duration / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (duration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
      );

      if (days > 0) {
        return `${days} day${days !== 1 ? "s" : ""} ${hours} hour${hours !== 1 ? "s" : ""}`;
      }
      return `${hours} hour${hours !== 1 ? "s" : ""}`;
    }
    return "N/A";
  }
</script>

<svelte:head>
  <title>{m.vehicleAssignmentsList_pageTitle()}</title>
</svelte:head>

<Card.Root class="h-[calc(100vh-90px)]">
  <Card.Content class="h-full overflow-y-auto p-6">
    <div class="mb-8">
      <h1 class="text-3xl font-bold">{m.vehicleAssignmentsList_title()}</h1>
      <p class="text-muted-foreground mt-2">
        {m.vehicleAssignmentsList_description()}
      </p>
    </div>

    {#if error}
      <div
        class="mb-6 p-4 border rounded-lg bg-destructive/10 text-destructive"
      >
        {m.vehicleAssignmentsList_errorPrefix()}{error}
      </div>
    {/if}

    <!-- Filters -->
    <Card.Root class="mb-6">
      <Card.Content class="pt-6">
        <div class="flex items-center gap-4">
          <div class="relative flex-1">
            <Search
              class="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"
            />
            <Input
              type="text"
              placeholder={m.vehicleAssignmentsList_searchPlaceholder()}
              bind:value={searchQuery}
              class="pl-9"
            />
          </div>

          <div class="flex items-center gap-2">
            <input
              type="checkbox"
              id="show-active"
              bind:checked={showOnlyActive}
              onchange={() => handleShowOnlyActiveChange(showOnlyActive)}
              class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label
              for="show-active"
              class="text-sm font-medium text-gray-700 cursor-pointer"
            >
              {m.vehicleAssignmentsList_showOnlyActiveLabel()}
            </label>
          </div>

          <Badge variant="secondary">
            {filteredAssignments.length === 1
              ? m.vehicleAssignmentsList_assignmentCount_one({
                  count: filteredAssignments.length.toString(),
                })
              : m.vehicleAssignmentsList_assignmentCount_other({
                  count: filteredAssignments.length.toString(),
                })}
          </Badge>
        </div>
      </Card.Content>
    </Card.Root>

    <!-- Assignments List -->
    <Card.Root>
      <Card.Content class="p-0">
        {#if loading}
          <div class="flex items-center justify-center py-12">
            <Spinner className="h-8 w-8" />
          </div>
        {:else if filteredAssignments.length === 0}
          <div
            class="flex flex-col items-center justify-center py-12 text-center"
          >
            <History class="h-12 w-12 text-muted-foreground mb-4" />
            <h3 class="text-lg font-semibold mb-2">
              {m.vehicleAssignmentsList_noAssignmentsFoundTitle()}
            </h3>
            <p class="text-muted-foreground">
              {searchQuery || !showOnlyActive
                ? m.vehicleAssignmentsList_noAssignmentsFoundDescription()
                : m.vehicleAssignmentsList_noAssignmentsYetDescription()}
            </p>
          </div>
        {:else}
          <div class="divide-y">
            {#each filteredAssignments as assignment}
              {@const duration = getDuration(assignment)}

              <div class="p-6 hover:bg-muted/50 transition-colors">
                <div class="flex items-start justify-between gap-4">
                  <div class="flex-1">
                    <div class="flex items-start gap-4">
                      <div class="flex flex-col gap-2">
                        <div class="p-2 bg-muted rounded-lg">
                          <Car class="h-5 w-5" />
                        </div>
                        <div class="p-2 bg-muted rounded-lg">
                          <User class="h-5 w-5" />
                        </div>
                      </div>

                      <div class="flex-1">
                        <!-- Vehicle Info -->
                        <div class="mb-3">
                          <h3 class="font-semibold">
                            {assignment.vehicle?.displayName ||
                              m.vehicleAssignmentsList_unknownVehicle()}
                          </h3>
                          <p class="text-sm text-muted-foreground">
                            {assignment.vehicle?.registrationNumber || "N/A"}
                          </p>
                        </div>

                        <!-- Driver Info -->
                        <div class="mb-3">
                          <p class="font-medium">
                            {assignment.driver?.displayName ||
                              assignment.driver?.email ||
                              m.vehicleAssignmentsList_unknownDriver()}
                          </p>
                          <p class="text-sm text-muted-foreground">
                            {assignment.driver?.phoneNumber ||
                              m.vehicleAssignmentsList_noPhone()}
                          </p>
                        </div>

                        <!-- Assignment Details -->
                        <div
                          class="flex items-center gap-4 text-sm text-muted-foreground"
                        >
                          <span class="font-medium">
                            {getAssignmentReasonDisplayName(assignment.reason)}
                          </span>
                          <span>•</span>
                          <div class="flex items-center gap-1">
                            <Calendar class="h-3 w-3" />
                            <span>
                              {format(assignment.assignedAt, "MMM d, yyyy")}
                            </span>
                          </div>
                          <span>•</span>
                          <span>
                            {m.vehicleAssignmentsList_durationLabel()}
                            {duration}
                          </span>
                        </div>

                        {#if assignment.notes}
                          <p class="text-sm mt-2 italic">
                            "{assignment.notes}"
                          </p>
                        {/if}
                      </div>
                    </div>
                  </div>

                  <div class="flex items-center gap-3">
                    <Badge
                      variant={assignment.isActive ? "default" : "secondary"}
                    >
                      {assignment.isActive
                        ? m.vehicleAssignmentsList_activeStatus()
                        : m.vehicleAssignmentsList_completedStatus()}
                    </Badge>

                    <div class="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        href="/vehicles/{assignment.vehicleId}"
                      >
                        {m.vehicleAssignmentsList_viewVehicleButton()}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        href="/rides/drivers/{assignment.driverUID}/details"
                      >
                        {m.vehicleAssignmentsList_viewDriverButton()}
                      </Button>
                    </div>
                  </div>
                </div>

                {#if !assignment.isActive && assignment.unassignedAt}
                  <div class="mt-4 pt-4 border-t text-sm text-muted-foreground">
                    {m.vehicleAssignmentsList_unassignedOnPrefix()}
                    {format(assignment.unassignedAt, "MMM d, yyyy h:mm a")}
                    {#if assignment.unassignedBy}
                      {m.vehicleAssignmentsList_byAdminSuffix()}
                    {/if}
                  </div>
                {/if}
              </div>
            {/each}
          </div>
        {/if}
      </Card.Content>
    </Card.Root>
  </Card.Content>
</Card.Root>
