<script lang="ts">
  import { onMount } from "svelte";
  import * as Card from "$lib/components/ui/card/index.js";
  import { Button } from "$lib/components/ui/button";
  import { Input } from "$lib/components/ui/input";
  import { Label } from "$lib/components/ui/label";
  import * as Select from "$lib/components/ui/select/index.js";
  import Spinner from "$lib/components/ui/Spinner.svelte";
  import { ArrowLeft, Car, Save } from "lucide-svelte";
  import { toast } from "svelte-sonner";
  import { localizedGoto } from "$lib/utils";
  import { createVehicle } from "$lib/stores/vehicles.svelte";
  import * as m from "$lib/paraglide/messages";

  // Form fields
  let brand = $state("");
  let customBrand = $state("");
  let isCustomBrand = $state(false);
  let model = $state("");
  let color = $state("");
  let year = $state(new Date().getFullYear());
  let registrationNumber = $state("");
  let maxPassengers = $state(4);

  let isSubmitting = $state(false);
  let errors = $state<Record<string, string>>({});

  // Common vehicle brands (sorted alphabetically)
  const brands = [
    "Audi",
    "BMW",
    "Chevrolet",
    "Citroen",
    "Ford",
    "Honda",
    "Hyundai",
    "Mazda",
    "Mercedes-Benz",
    "Mitsubishi",
    "Nissan",
    "Peugeot",
    "Renault",
    "Suzuki",
    "Toyota",
    "Volkswagen",
    "Other",
  ];

  // Common vehicle colors (with hex values)
  const colors = [
    { value: "Beige", hex: "#F5F5DC" },
    { value: "Black", hex: "#000000" },
    { value: "Blue", hex: "#0000FF" },
    { value: "Brown", hex: "#8B4513" },
    { value: "Gold", hex: "#FFD700" },
    { value: "Gray", hex: "#808080" },
    { value: "Green", hex: "#008000" },
    { value: "Orange", hex: "#FFA500" },
    { value: "Purple", hex: "#800080" },
    { value: "Red", hex: "#FF0000" },
    { value: "Silver", hex: "#C0C0C0" },
    { value: "White", hex: "#FFFFFF" },
    { value: "Yellow", hex: "#FFFF00" },
  ];

  // Get translated color name
  function getColorName(colorValue: string): string {
    switch (colorValue) {
      case "Beige":
        return m.vehiclesAdd_colorBeige();
      case "Black":
        return m.vehiclesAdd_colorBlack();
      case "Blue":
        return m.vehiclesAdd_colorBlue();
      case "Brown":
        return m.vehiclesAdd_colorBrown();
      case "Gold":
        return m.vehiclesAdd_colorGold();
      case "Gray":
        return m.vehiclesAdd_colorGray();
      case "Green":
        return m.vehiclesAdd_colorGreen();
      case "Orange":
        return m.vehiclesAdd_colorOrange();
      case "Purple":
        return m.vehiclesAdd_colorPurple();
      case "Red":
        return m.vehiclesAdd_colorRed();
      case "Silver":
        return m.vehiclesAdd_colorSilver();
      case "White":
        return m.vehiclesAdd_colorWhite();
      case "Yellow":
        return m.vehiclesAdd_colorYellow();
      default:
        return colorValue;
    }
  }

  // Sort colors based on translated names
  let sortedColors = $derived(
    [...colors].sort((a, b) => {
      const nameA = getColorName(a.value);
      const nameB = getColorName(b.value);
      return nameA.localeCompare(nameB);
    }),
  );

  // Validate form
  function validateForm() {
    const newErrors: Record<string, string> = {};

    const effectiveBrand = isCustomBrand ? customBrand : brand;
    if (!effectiveBrand.trim()) {
      newErrors.brand = m.vehiclesAdd_brandRequiredError();
    }

    if (!model.trim()) {
      newErrors.model = m.vehiclesAdd_modelRequiredError();
    }

    if (!color.trim()) {
      newErrors.color = m.vehiclesAdd_colorRequiredError();
    }

    if (!year || year < 1900 || year > new Date().getFullYear()) {
      newErrors.year = m.vehiclesAdd_yearInvalidError({
        currentYear: new Date().getFullYear(),
      });
    }

    if (!registrationNumber.trim()) {
      newErrors.registrationNumber = m.vehiclesAdd_registrationRequiredError();
    }

    if (!maxPassengers || maxPassengers < 1 || maxPassengers > 50) {
      newErrors.maxPassengers = m.vehiclesAdd_maxPassengersInvalidError();
    }

    errors = newErrors;
    return Object.keys(newErrors).length === 0;
  }

  async function handleSubmit() {
    if (!validateForm() || isSubmitting) return;

    isSubmitting = true;
    try {
      const effectiveBrand = isCustomBrand ? customBrand : brand;
      const vehicleData = {
        brand: effectiveBrand.trim(),
        model: model.trim(),
        color: color.trim(),
        year,
        registrationNumber: registrationNumber.trim().toUpperCase(),
        maxPassengers,
      };

      const result = await createVehicle(vehicleData);

      if (result.vehicleId) {
        toast.success(m.vehiclesAdd_createSuccessToast());
        // Navigate to the newly created vehicle details page
        await localizedGoto(`/vehicles/${result.vehicleId}`);
      } else {
        toast.error(m.vehiclesAdd_createPartialErrorToast());
        await localizedGoto("/vehicles");
      }
    } catch (error) {
      console.error("Error creating vehicle:", error);
      toast.error(m.vehiclesAdd_createErrorToast());
    } finally {
      isSubmitting = false;
    }
  }

  function handleCancel() {
    localizedGoto("/vehicles");
  }

  function handleBrandChange(value: string | undefined) {
    if (value === "Other") {
      isCustomBrand = true;
      brand = "";
      customBrand = "";
    } else {
      isCustomBrand = false;
      brand = value || "";
      customBrand = "";
    }
    errors.brand = "";
  }
</script>

<svelte:head>
  <title>{m.vehiclesAdd_pageTitle()}</title>
</svelte:head>

<Card.Root class="h-[calc(100vh-90px)]">
  <Card.Header>
    <Card.Title class="flex items-center gap-2">
      <Car class="h-5 w-5" />
      {m.vehiclesAdd_cardTitle()}
    </Card.Title>
    <Card.Description>
      {m.vehiclesAdd_cardDescription()}
    </Card.Description>
  </Card.Header>
  <Card.Content class="h-[calc(100vh-196px)] overflow-y-auto">
    <form
      onsubmit={(e) => {
        e.preventDefault();
        handleSubmit();
      }}
      class="space-y-6"
      novalidate
    >
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Brand -->
        <div class="space-y-2">
          <Label for="brand" class="required"
            >{m.vehiclesAdd_brandLabel()}</Label
          >
          {#if isCustomBrand}
            <Input
              id="custom-brand"
              type="text"
              bind:value={customBrand}
              placeholder={m.vehiclesAdd_customBrandPlaceholder()}
              disabled={isSubmitting}
              class={errors.brand ? "border-destructive" : ""}
              oninput={() => (errors.brand = "")}
            />
          {:else}
            <Select.Root
              type="single"
              onValueChange={handleBrandChange}
              disabled={isSubmitting}
            >
              <Select.Trigger
                id="brand"
                class={errors.brand ? "border-destructive" : ""}
              >
                {#if brand}
                  {brand}
                {:else}
                  <span class="text-muted-foreground"
                    >{m.vehiclesAdd_brandPlaceholder()}</span
                  >
                {/if}
              </Select.Trigger>
              <Select.Content>
                {#each brands as brandOption}
                  <Select.Item value={brandOption}>
                    {brandOption === "Other"
                      ? m.vehiclesAdd_otherBrandOption()
                      : brandOption}
                  </Select.Item>
                {/each}
              </Select.Content>
            </Select.Root>
          {/if}
          {#if errors.brand}
            <p class="text-sm text-destructive">{errors.brand}</p>
          {/if}
        </div>

        <!-- Model -->
        <div class="space-y-2">
          <Label for="model" class="required"
            >{m.vehiclesAdd_modelLabel()}</Label
          >
          <Input
            id="model"
            type="text"
            bind:value={model}
            placeholder={m.vehiclesAdd_modelPlaceholder()}
            disabled={isSubmitting}
            class={errors.model ? "border-destructive" : ""}
            oninput={() => (errors.model = "")}
          />
          {#if errors.model}
            <p class="text-sm text-destructive">{errors.model}</p>
          {/if}
        </div>

        <!-- Color -->
        <div class="space-y-2">
          <Label for="color" class="required"
            >{m.vehiclesAdd_colorLabel()}</Label
          >
          <Select.Root
            type="single"
            onValueChange={(value) => {
              color = value || "";
              errors.color = "";
            }}
            disabled={isSubmitting}
          >
            <Select.Trigger
              id="color"
              class={errors.color ? "border-destructive" : ""}
            >
              {#if color}
                {@const selectedColor = colors.find((c) => c.value === color)}
                <div class="flex items-center gap-2">
                  {#if selectedColor}
                    <div
                      class="w-4 h-4 rounded-full border border-gray-300"
                      style="background-color: {selectedColor.hex}"
                    ></div>
                  {/if}
                  {getColorName(color)}
                </div>
              {:else}
                <span class="text-muted-foreground"
                  >{m.vehiclesAdd_colorPlaceholder()}</span
                >
              {/if}
            </Select.Trigger>
            <Select.Content>
              {#each sortedColors as colorOption}
                <Select.Item value={colorOption.value}>
                  <div class="flex items-center gap-2">
                    <div
                      class="w-4 h-4 rounded-full border {colorOption.value ===
                      'White'
                        ? 'border-gray-300'
                        : 'border-gray-200'}"
                      style="background-color: {colorOption.hex}"
                    ></div>
                    {getColorName(colorOption.value)}
                  </div>
                </Select.Item>
              {/each}
            </Select.Content>
          </Select.Root>
          {#if errors.color}
            <p class="text-sm text-destructive">{errors.color}</p>
          {/if}
        </div>

        <!-- Year -->
        <div class="space-y-2">
          <Label for="year" class="required">{m.vehiclesAdd_yearLabel()}</Label>
          <Input
            id="year"
            type="number"
            bind:value={year}
            disabled={isSubmitting}
            class={errors.year ? "border-destructive" : ""}
            oninput={() => (errors.year = "")}
          />
          {#if errors.year}
            <p class="text-sm text-destructive">{errors.year}</p>
          {/if}
        </div>

        <!-- Registration Number -->
        <div class="space-y-2">
          <Label for="registrationNumber" class="required"
            >{m.vehiclesAdd_registrationLabel()}</Label
          >
          <Input
            id="registrationNumber"
            type="text"
            bind:value={registrationNumber}
            placeholder={m.vehiclesAdd_registrationPlaceholder()}
            disabled={isSubmitting}
            class={errors.registrationNumber ? "border-destructive" : ""}
            oninput={() => (errors.registrationNumber = "")}
            style="text-transform: uppercase;"
          />
          {#if errors.registrationNumber}
            <p class="text-sm text-destructive">{errors.registrationNumber}</p>
          {/if}
        </div>

        <!-- Max Passengers -->
        <div class="space-y-2">
          <Label for="maxPassengers" class="required"
            >{m.vehiclesAdd_maxPassengersLabel()}</Label
          >
          <Input
            id="maxPassengers"
            type="number"
            bind:value={maxPassengers}
            min="1"
            max="50"
            disabled={isSubmitting}
            class={errors.maxPassengers ? "border-destructive" : ""}
            oninput={() => (errors.maxPassengers = "")}
          />
          <p class="text-xs text-muted-foreground">
            {m.vehiclesAdd_maxPassengersDescription()}
          </p>
          {#if errors.maxPassengers}
            <p class="text-sm text-destructive">{errors.maxPassengers}</p>
          {/if}
        </div>
      </div>

      <!-- Information Card -->
      <Card.Root class="border-blue-200 bg-blue-50/50">
        <Card.Content class="pt-6">
          <div class="space-y-2 text-sm">
            <p class="font-medium text-blue-900">{m.vehiclesAdd_infoTitle()}</p>
            <ul class="space-y-1 text-blue-700 ml-4">
              <li>{m.vehiclesAdd_infoPoint1()}</li>
              <li>{m.vehiclesAdd_infoPoint2()}</li>
              <li>{m.vehiclesAdd_infoPoint3()}</li>
              <li>{m.vehiclesAdd_infoPoint4()}</li>
            </ul>
          </div>
        </Card.Content>
      </Card.Root>

      <!-- Actions -->
      <div class="flex gap-3 pt-4 border-t">
        <Button
          type="button"
          variant="outline"
          onclick={handleCancel}
          disabled={isSubmitting}
          class="flex-1"
        >
          {m.vehiclesAdd_cancelButton()}
        </Button>
        <Button type="submit" disabled={isSubmitting} class="flex-1">
          {#if isSubmitting}
            <Spinner className="mr-2 h-4 w-4" />
            {m.vehiclesAdd_creatingButton()}
          {:else}
            <Save class="mr-2 h-4 w-4" />
            {m.vehiclesAdd_createButton()}
          {/if}
        </Button>
      </div>
    </form>
  </Card.Content>
</Card.Root>

<style>
  :global(.required::after) {
    content: " *";
    color: rgb(239 68 68);
  }
</style>
