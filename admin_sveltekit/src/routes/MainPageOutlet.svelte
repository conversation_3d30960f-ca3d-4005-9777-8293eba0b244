<script lang="ts">
  import { Button } from "$lib/components/ui/button";
  import Spinner from "$lib/components/ui/Spinner.svelte";
  import type { User } from "firebase/auth";

  let {
    loading = false,
    error = null,
    user = false,
    onSignInClick = () => {},
  } = $props<{
    loading: boolean;
    error: string | null;
    user: User | boolean;
    onSignInClick: () => void;
  }>();
</script>

<div
  class="h-[calc(100vh-3.5rem)] w-full flex flex-col items-center justify-center"
>
  {#if user === false}
    <Spinner class="w-8 h-8" />
  {:else if user === true}
    <div>
      {#if loading}
        <div class="flex items-center space-x-4 space-y-2">
          <Spinner class="w-4 h-4" />
          <span>Signing you in...</span>
        </div>
      {:else}
        <Button variant="outline" onclick={onSignInClick}>
          <img src="/google-icon.svg" alt="Google" class="w-5 h-5 mr-2" />
          <span>Sign in with Google</span>
        </Button>
      {/if}
    </div>
  {:else}
    <p class="text-sm">You are signed in as {user.email}</p>
  {/if}

  {#if error}
    <p class="mt-4 text-sm text-red-600">{error}</p>
  {/if}
</div>
