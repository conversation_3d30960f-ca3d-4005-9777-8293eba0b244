<script lang="ts">
  import { page } from "$app/state";
  import { onMount } from "svelte";
  import * as Card from "$lib/components/ui/card/index.js";
  import { Button } from "$lib/components/ui/button";
  import { Badge } from "$lib/components/ui/badge";
  import { Textarea } from "$lib/components/ui/textarea/index.js";
  import { Label } from "$lib/components/ui/label";
  // Alert components will be created or replaced with Card
  import Spinner from "$lib/components/ui/Spinner.svelte";
  import {
    FileText,
    FileImage,
    Check,
    X,
    User,
    AlertTriangle,
    Mail,
    Phone,
    Shield,
    Activity,
  } from "lucide-svelte";
  import { format, formatDistanceToNow } from "date-fns";
  import { toast } from "svelte-sonner";
  import * as m from "$lib/paraglide/messages";

  import {
    getDocumentById,
    getDocumentTypeDisplayName,
    getDocumentStatusDisplayName,
    getDocumentStatusColor,
    updateDocumentStatus,
    DocumentStatus,
  } from "$lib/stores/driver_documents.svelte";

  import { getUsersMap, getDisplayName } from "$lib/stores/mobile_users.svelte";

  import {
    getUsers as getAdminUsers,
    init as initAdminUsers,
    getDisplayName as getAdminDisplayName,
  } from "$lib/stores/admin_users.svelte";

  import { tenantStore } from "$lib/stores/tenant.svelte";

  const docId = $derived(page.params.docId);
  const document = $derived(getDocumentById(docId));
  const usersMap = $derived(getUsersMap());
  const adminUsers = $derived(getAdminUsers());

  const driver = $derived(
    document?.driverUID ? usersMap.get(document.driverUID) || null : null,
  );

  let adminNotes = $state("");
  let isUpdating = $state(false);
  let lastDocumentId = $state<string | null>(null);

  onMount(() => {
    initAdminUsers();
    console.log("📄 Document details page mounted for document:", docId);
  });

  // Update admin notes when switching documents
  $effect(() => {
    if (document && document.id !== lastDocumentId) {
      console.log("📝 Document changed, updating admin notes:", document.id);
      adminNotes = document.adminNotes || "";
      lastDocumentId = document.id || null;
    }
  });

  function getDaysUntilExpiry(expiryDate: Date) {
    const now = new Date();
    const days = Math.floor(
      (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
    );
    return days;
  }

  function isImageFile(url: string): boolean {
    const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp"];
    const urlLower = url.toLowerCase();
    return imageExtensions.some((ext) => urlLower.includes(ext));
  }

  async function handleStatusUpdate(newStatus: DocumentStatus) {
    if (!document || isUpdating) return;

    isUpdating = true;
    try {
      await updateDocumentStatus(
        document.id!,
        document.driverUID!,
        newStatus,
        adminNotes || undefined,
      );

      toast.success(
        newStatus === DocumentStatus.approved
          ? m.documentDetails_approvedSuccessToast()
          : m.documentDetails_rejectedSuccessToast(),
      );

      // Clear admin notes after successful update
      adminNotes = "";
    } catch (error) {
      console.error("Error updating document status:", error);
      toast.error(m.documentDetails_updateStatusErrorToast());
    } finally {
      isUpdating = false;
    }
  }
</script>

<svelte:head>
  <title>{m.documentDetails_pageTitle()}</title>
</svelte:head>

{#if !document}
  <Card.Root class="h-full">
    <Card.Content class="py-12">
      <div class="flex items-center justify-center">
        <Spinner className="h-8 w-8" />
      </div>
    </Card.Content>
  </Card.Root>
{:else}
  {@const daysUntilExpiry = getDaysUntilExpiry(document.expiryDate)}
  {@const isExpired = daysUntilExpiry <= 0}
  {@const isExpiringSoon = daysUntilExpiry <= 30 && daysUntilExpiry > 0}

  <Card.Root class="h-full">
    <Card.Header>
      <div class="flex items-start justify-between">
        <div>
          <Card.Title class="flex items-center gap-2">
            {document.documentName}
            <Badge class={getDocumentStatusColor(document.status)}>
              {getDocumentStatusDisplayName(document.status)}
            </Badge>
          </Card.Title>
          <Card.Description>
            {#if document.status === DocumentStatus.pendingReview}
              {m.documentDetails_statusDescriptionPending()}
            {:else if document.status === DocumentStatus.approved}
              {m.documentDetails_statusDescriptionApproved()}
            {:else if document.status === DocumentStatus.rejected}
              {m.documentDetails_statusDescriptionRejected()}
            {/if}
          </Card.Description>
        </div>
      </div>
    </Card.Header>
    <Card.Content class="h-[calc(100vh-156px)] overflow-y-auto space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Document Details -->
        <Card.Root>
          <Card.Header>
            <div class="flex items-center gap-2">
              <FileText class="h-5 w-5 text-muted-foreground" />
              <Card.Title>{m.documentDetails_documentDetailsTitle()}</Card.Title
              >
            </div>
          </Card.Header>
          <Card.Content>
            <div class="space-y-4">
              <div>
                <Label class="text-muted-foreground"
                  >{m.documentDetails_typeLabel()}</Label
                >
                <p class="font-medium">
                  {getDocumentTypeDisplayName(document.documentType)}
                </p>
              </div>

              <div>
                <Label class="text-muted-foreground"
                  >{m.documentDetails_statusLabel()}</Label
                >
                <Badge class={getDocumentStatusColor(document.status)}>
                  {getDocumentStatusDisplayName(document.status)}
                </Badge>
              </div>

              <div>
                <Label class="text-muted-foreground"
                  >{m.documentDetails_uploadedLabel()}</Label
                >
                <p class="font-medium">
                  {format(document.uploadedAt, "MMM d, yyyy h:mm a")}
                </p>
                <p class="text-sm text-muted-foreground">
                  {formatDistanceToNow(document.uploadedAt, {
                    addSuffix: true,
                  })}
                </p>
              </div>

              <div>
                <Label class="text-muted-foreground"
                  >{m.documentDetails_expiryDateLabel()}</Label
                >
                <p class="font-medium">
                  {format(document.expiryDate, "MMM d, yyyy")}
                </p>
                {#if isExpired}
                  <p class="text-sm text-destructive font-medium">
                    {m.documentDetails_expiredDaysAgo({
                      days: Math.abs(daysUntilExpiry),
                    })}
                  </p>
                {:else if isExpiringSoon}
                  <p class="text-sm text-warning font-medium">
                    {m.documentDetails_expiresInDays({ days: daysUntilExpiry })}
                  </p>
                {:else}
                  <p class="text-sm text-muted-foreground">
                    {m.documentDetails_validForDays({ days: daysUntilExpiry })}
                  </p>
                {/if}
              </div>

              {#if document.notes}
                <div>
                  <Label class="text-muted-foreground"
                    >{m.documentDetails_driverNotesLabel()}</Label
                  >
                  <p class="font-medium">{document.notes}</p>
                </div>
              {/if}

              {#if document.reviewedAt}
                <div>
                  <Label class="text-muted-foreground"
                    >{m.documentDetails_reviewedLabel()}</Label
                  >
                  <p class="font-medium">
                    {format(document.reviewedAt, "MMM d, yyyy h:mm a")}
                  </p>
                  <p class="text-sm text-muted-foreground">
                    {formatDistanceToNow(document.reviewedAt, {
                      addSuffix: true,
                    })}
                  </p>
                </div>
              {/if}
            </div>
          </Card.Content>
        </Card.Root>

        <!-- Driver Information -->
        <Card.Root>
          <Card.Header>
            <div class="flex items-center gap-2">
              <User class="h-5 w-5 text-muted-foreground" />
              <Card.Title>{m.documentDetails_driverInfoTitle()}</Card.Title>
            </div>
          </Card.Header>
          <Card.Content>
            {#if driver}
              {@const isServiceActive = driver.isServiceActiveByTenant?.[tenantStore.currentId] ?? false}
              <div class="space-y-4">
                {#if driver.photoURL}
                  <div class="flex justify-center mb-4">
                    <img
                      src={driver.photoURL}
                      alt={getDisplayName(driver)}
                      class="w-20 h-20 rounded-full object-cover border-2 border-muted"
                    />
                  </div>
                {/if}

                <div>
                  <Label class="text-muted-foreground flex items-center gap-1">
                    <User class="h-3 w-3" />
                    {m.documentDetails_nameLabel()}
                  </Label>
                  <p class="font-medium">
                    {driver.displayName || m.documentDetails_notProvided()}
                  </p>
                </div>

                <div>
                  <Label class="text-muted-foreground flex items-center gap-1">
                    <Mail class="h-3 w-3" />
                    {m.documentDetails_emailLabel()}
                  </Label>
                  <p class="font-medium">
                    {driver.email || m.documentDetails_notProvided()}
                  </p>
                </div>

                <div>
                  <Label class="text-muted-foreground flex items-center gap-1">
                    <Phone class="h-3 w-3" />
                    {m.documentDetails_phoneLabel()}
                  </Label>
                  <p class="font-medium">
                    {driver.phoneNumber || m.documentDetails_notProvided()}
                  </p>
                </div>

                <div>
                  <Label class="text-muted-foreground flex items-center gap-1">
                    <Shield class="h-3 w-3" />
                    {m.documentDetails_driverStatusLabel()}
                  </Label>
                  <div class="flex gap-2 mt-1">
                    <Badge
                      variant={isServiceActive ? "default" : "secondary"}
                    >
                      <Activity class="h-3 w-3 mr-1" />
                      {isServiceActive
                        ? m.documentDetails_activeStatus()
                        : m.documentDetails_inactiveStatus()}
                    </Badge>
                    <Badge
                      variant={driver.isDriverConfirmed
                        ? "default"
                        : "secondary"}
                    >
                      <Shield class="h-3 w-3 mr-1" />
                      {driver.isDriverConfirmed
                        ? m.documentDetails_verifiedStatus()
                        : m.documentDetails_unverifiedStatus()}
                    </Badge>
                  </div>
                </div>

                <Button
                  variant="outline"
                  href="/rides/drivers/{driver.uid}/details"
                  class="w-full"
                >
                  <User class="h-4 w-4 mr-2" />
                  {m.documentDetails_viewDriverProfileButton()}
                </Button>
              </div>
            {:else if document?.driverUID}
              <div class="space-y-4">
                <p class="text-muted-foreground text-sm">
                  {m.documentDetails_driverNotFoundText()}
                  <code class="font-mono text-xs bg-muted px-1 py-0.5 rounded"
                    >{document.driverUID}</code
                  >
                  {m.documentDetails_driverNotFoundInTenant()}
                </p>
                <p class="text-muted-foreground text-sm">
                  {m.documentDetails_driverNotAssignedText()}
                </p>
              </div>
            {:else}
              <p class="text-muted-foreground">
                {m.documentDetails_noDriverInfoAvailable()}
              </p>
            {/if}
          </Card.Content>
        </Card.Root>
      </div>

      <!-- Review Actions -->
      {#if document.status === DocumentStatus.pendingReview}
        <Card.Root class="mt-6">
          <Card.Header>
            <Card.Title>{m.documentDetails_reviewDocumentTitle()}</Card.Title>
          </Card.Header>
          <Card.Content>
            {#if isExpired}
              <div
                class="mb-4 p-4 border border-destructive/50 bg-destructive/10 rounded-lg flex items-start gap-3"
              >
                <AlertTriangle class="h-4 w-4 text-destructive mt-0.5" />
                <p class="text-sm text-destructive">
                  {m.documentDetails_documentExpiredWarning()}
                </p>
              </div>
            {/if}

            <div class="space-y-4">
              <div>
                <Label for="admin-notes"
                  >{m.documentDetails_adminNotesLabel()}</Label
                >
                <Textarea
                  id="admin-notes"
                  bind:value={adminNotes}
                  placeholder={m.documentDetails_adminNotesPlaceholder()}
                  rows={3}
                  disabled={isUpdating}
                />
              </div>

              <div class="flex gap-3">
                <Button
                  variant="default"
                  onclick={() => handleStatusUpdate(DocumentStatus.approved)}
                  disabled={isUpdating || isExpired}
                  class="flex-1"
                >
                  {#if isUpdating}
                    <Spinner className="mr-2 h-4 w-4" />
                  {:else}
                    <Check class="h-4 w-4 mr-2" />
                  {/if}
                  {m.documentDetails_approveButton()}
                </Button>

                <Button
                  variant="destructive"
                  onclick={() => handleStatusUpdate(DocumentStatus.rejected)}
                  disabled={isUpdating}
                  class="flex-1"
                >
                  {#if isUpdating}
                    <Spinner className="mr-2 h-4 w-4" />
                  {:else}
                    <X class="h-4 w-4 mr-2" />
                  {/if}
                  {m.documentDetails_rejectButton()}
                </Button>
              </div>
            </div>
          </Card.Content>
        </Card.Root>
      {:else if document.reviewedAt}
        {@const reviewer = document?.reviewedBy
          ? adminUsers.find((admin) => admin.uid === document?.reviewedBy)
          : null}
        <Card.Root class="mt-6">
          <Card.Header>
            <Card.Title>{m.documentDetails_reviewHistoryTitle()}</Card.Title>
          </Card.Header>
          <Card.Content>
            <div class="space-y-4">
              <div>
                <Label class="text-muted-foreground"
                  >{m.documentDetails_reviewedOnLabel()}</Label
                >
                <p class="font-medium">
                  {format(document.reviewedAt, "MMM d, yyyy h:mm a")}
                </p>
                <p class="text-sm text-muted-foreground">
                  {formatDistanceToNow(document.reviewedAt, {
                    addSuffix: true,
                  })}
                </p>
              </div>

              {#if reviewer}
                <div>
                  <Label class="text-muted-foreground"
                    >{m.documentDetails_reviewedByLabel()}</Label
                  >
                  <p class="font-medium">{getAdminDisplayName(reviewer)}</p>
                  <p class="text-sm text-muted-foreground">{reviewer.email}</p>
                </div>
              {/if}

              {#if document.adminNotes}
                <div>
                  <Label class="text-muted-foreground"
                    >{m.documentDetails_adminNotesHistoryLabel()}</Label
                  >
                  <p class="font-medium">{document.adminNotes}</p>
                </div>
              {/if}
            </div>
          </Card.Content>
        </Card.Root>
      {/if}

      <!-- Document Preview -->
      <Card.Root class="mt-6">
        <Card.Header>
          <div class="flex items-center gap-2">
            <FileImage class="h-5 w-5 text-muted-foreground" />
            <Card.Title>{m.documentDetails_documentPreviewTitle()}</Card.Title>
          </div>
        </Card.Header>
        <Card.Content>
          {#if isImageFile(document.fileURL)}
            <div
              class="relative aspect-[3/4] bg-muted rounded-lg overflow-hidden"
            >
              <img
                src={document.fileURL}
                alt={document.documentName}
                class="w-full h-full object-contain"
                loading="lazy"
                onerror={(e) => {
                  console.error("Failed to load image:", e);
                  const target = e.currentTarget as HTMLImageElement;
                  const fallback =
                    target.nextElementSibling as HTMLElement | null;
                  if (target && fallback) {
                    target.style.display = "none";
                    fallback.style.display = "flex";
                  }
                }}
              />
              <div class="absolute inset-0 items-center justify-center hidden">
                <div class="text-center">
                  <FileText
                    class="h-16 w-16 text-muted-foreground mx-auto mb-4"
                  />
                  <p class="text-muted-foreground">
                    {m.documentDetails_failedToLoadImage()}
                  </p>
                </div>
              </div>
            </div>
          {:else}
            <div
              class="aspect-[3/4] bg-muted rounded-lg flex items-center justify-center"
            >
              <div class="text-center">
                <FileText
                  class="h-16 w-16 text-muted-foreground mx-auto mb-4"
                />
                <p class="text-muted-foreground">
                  {m.documentDetails_previewNotAvailable()}
                </p>
                <p class="text-sm text-muted-foreground mt-2">
                  {m.documentDetails_onlyImagesPreviewable()}
                </p>
              </div>
            </div>
          {/if}
        </Card.Content>
      </Card.Root>
    </Card.Content>
  </Card.Root>
{/if}
