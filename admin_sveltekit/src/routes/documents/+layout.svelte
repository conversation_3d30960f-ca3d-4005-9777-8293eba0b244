<script lang="ts">
  import { onMount, onDestroy } from "svelte";
  import * as Card from "$lib/components/ui/card/index.js";
  import { Button } from "$lib/components/ui/button";
  import { Badge } from "$lib/components/ui/badge";
  import { Input } from "$lib/components/ui/input";
  import { Toggle } from "$lib/components/ui/toggle";
  import Spinner from "$lib/components/ui/Spinner.svelte";
  import { FileText, Search } from "lucide-svelte";
  import { format } from "date-fns";
  import { page } from "$app/state";
  import { localizedGoto } from "$lib/utils";
  import * as m from "$lib/paraglide/messages";

  import {
    init,
    destroy,
    getFilteredDocuments,
    getLoading,
    getError,
    getDocumentTypeDisplayName,
    getDocumentStatusDisplayName,
    getDocumentStatusColor,
    setStatusFilter,
    setTypeFilter,
    DocumentType,
    DocumentStatus,
    type DriverDocument,
  } from "$lib/stores/driver_documents.svelte";

  import {
    getUsersMap,
    init as initMobileUsers,
    destroy as destroyMobileUsers,
  } from "$lib/stores/mobile_users.svelte";

  let searchQuery = $state("");
  let selectedStatus = $state<DocumentStatus | "all">("all");
  let selectedType = $state<DocumentType | "all">("all");

  onMount(() => {
    init();
    initMobileUsers();
  });

  onDestroy(() => {
    // Don't destroy the stores, keep for reuse
  });

  let loading = $derived(getLoading());
  let error = $derived(getError());
  let documents = $derived(getFilteredDocuments());
  let usersMap = $derived(getUsersMap());
  let selectedDocId = $derived(page.params.docId || null);

  let filteredDocuments = $derived(
    documents.filter((doc) => {
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const driverName = usersMap.get(doc.driverUID || "")?.displayName || "";
        return (
          doc.documentName.toLowerCase().includes(query) ||
          driverName.toLowerCase().includes(query)
        );
      }
      return true;
    }),
  );

  function handleStatusChange(value: string) {
    selectedStatus = value as DocumentStatus | "all";
    setStatusFilter(value === "all" ? null : (value as DocumentStatus));
  }

  function handleTypeChange(value: string) {
    selectedType = value as DocumentType | "all";
    setTypeFilter(value === "all" ? null : (value as DocumentType));
  }

  function getDaysUntilExpiry(expiryDate: Date) {
    const now = new Date();
    const days = Math.floor(
      (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
    );
    return days;
  }

  const { children } = $props();
</script>

<svelte:head>
  <title>{m.documentsLayout_pageTitle()}</title>
</svelte:head>

<div class="p-4">
  <div class="flex h-full gap-4">
    <!-- Left section - Documents list -->
    <div class="w-1/3">
      <Card.Root>
        <Card.Header>
          <Card.Title>{m.documentsLayout_title()}</Card.Title>
          <Card.Description>{m.documentsLayout_description()}</Card.Description>
        </Card.Header>
        <Card.Content>
          {#if error}
            <div class="text-destructive mb-4">
              {m.documentsLayout_errorPrefix()}{error}
            </div>
          {/if}

          <!-- Search input -->
          <div class="relative mb-4">
            <Search
              class="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"
            />
            <Input
              type="text"
              placeholder={m.documentsLayout_searchPlaceholder()}
              bind:value={searchQuery}
              class="pl-9"
            />
          </div>

          <!-- Filters -->
          <div class="flex flex-wrap gap-2 mb-4">
            <Toggle
              pressed={selectedStatus === DocumentStatus.pendingReview}
              onPressedChange={(pressed: boolean) =>
                handleStatusChange(
                  pressed ? DocumentStatus.pendingReview : "all",
                )}
              variant="outline"
              size="sm"
              class="data-[state=on]:bg-yellow-500"
            >
              {m.documentsLayout_pendingFilter()}
            </Toggle>
            <Toggle
              pressed={selectedStatus === DocumentStatus.approved}
              onPressedChange={(pressed: boolean) =>
                handleStatusChange(pressed ? DocumentStatus.approved : "all")}
              variant="outline"
              size="sm"
              class="data-[state=on]:bg-green-500"
            >
              {m.documentsLayout_approvedFilter()}
            </Toggle>
            <Toggle
              pressed={selectedStatus === DocumentStatus.rejected}
              onPressedChange={(pressed: boolean) =>
                handleStatusChange(pressed ? DocumentStatus.rejected : "all")}
              variant="outline"
              size="sm"
              class="data-[state=on]:bg-red-500"
            >
              {m.documentsLayout_rejectedFilter()}
            </Toggle>
          </div>

          <!-- Documents count -->
          <div class="flex justify-between items-center mb-4">
            <span class="text-sm text-muted-foreground">
              {filteredDocuments.length === 1
                ? m.documentsLayout_documentCount_one({
                    count: filteredDocuments.length,
                  })
                : m.documentsLayout_documentCount_other({
                    count: filteredDocuments.length,
                  })}
            </span>
          </div>
        </Card.Content>
        <Card.Content class="h-[calc(100vh-351px)] overflow-y-auto">
          <!-- Documents list -->
          <div class="space-y-2">
            {#if loading}
              <div class="flex items-center justify-center py-8">
                <Spinner className="h-6 w-6" />
              </div>
            {:else if filteredDocuments.length === 0}
              <div class="text-center py-8">
                <FileText
                  class="h-12 w-12 text-muted-foreground mx-auto mb-2"
                />
                <p class="text-sm text-muted-foreground">
                  {m.documentsLayout_noDocumentsFound()}
                </p>
              </div>
            {:else}
              {#each filteredDocuments as document}
                {@const driver = usersMap.get(document.driverUID || "")}
                {@const daysUntilExpiry = getDaysUntilExpiry(
                  document.expiryDate,
                )}

                <button
                  onclick={() => localizedGoto(`/documents/${document.id}`)}
                  class="w-full text-left p-3 rounded-lg hover:bg-muted transition-colors {selectedDocId ===
                  document.id
                    ? 'bg-muted'
                    : ''}"
                >
                  <div class="flex items-start justify-between gap-2">
                    <div class="flex-1 min-w-0">
                      <h4 class="font-medium truncate">
                        {document.documentName}
                      </h4>
                      <p class="text-sm text-muted-foreground truncate">
                        {getDocumentTypeDisplayName(document.documentType)}
                      </p>
                      {#if driver}
                        <p class="text-xs text-muted-foreground truncate mt-1">
                          {driver.displayName ||
                            driver.email ||
                            m.documentsLayout_unknownDriver()}
                        </p>
                      {/if}
                      <div class="flex items-center gap-2 mt-2">
                        <Badge
                          class={getDocumentStatusColor(document.status) +
                            " text-xs"}
                        >
                          {getDocumentStatusDisplayName(document.status)}
                        </Badge>
                        {#if daysUntilExpiry <= 30 && daysUntilExpiry > 0 && document.status === DocumentStatus.approved}
                          <Badge
                            variant="secondary"
                            class="text-xs text-warning"
                          >
                            {m.documentsLayout_daysExpiringSuffix({
                              days: daysUntilExpiry,
                            })}
                          </Badge>
                        {:else if daysUntilExpiry <= 0}
                          <Badge variant="destructive" class="text-xs">
                            {m.documentsLayout_expiredBadge()}
                          </Badge>
                        {/if}
                      </div>
                    </div>
                  </div>
                </button>
              {/each}
            {/if}
          </div>
        </Card.Content>
      </Card.Root>
    </div>

    <!-- Right section - Content -->
    <div class="flex-1">
      {@render children()}
    </div>
  </div>
</div>
