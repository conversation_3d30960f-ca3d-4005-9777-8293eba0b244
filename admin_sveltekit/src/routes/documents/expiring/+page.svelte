<script lang="ts">
  import * as Card from "$lib/components/ui/card/index.js";
  import { But<PERSON> } from "$lib/components/ui/button";
  import { Badge } from "$lib/components/ui/badge";
  import { Alert, AlertDescription } from "$lib/components/ui/alert";
  import Spinner from "$lib/components/ui/Spinner.svelte";
  import { AlertTriangle, FileText, ExternalLink, Clock } from "lucide-svelte";
  import { format, formatDistanceToNow } from "date-fns";
  import * as m from "$lib/paraglide/messages";

  import {
    getExpiringDocuments,
    getDocumentsByStatus,
    getLoading,
    getError,
    getDocumentTypeDisplayName,
    getDocumentStatusDisplayName,
    getDocumentStatusColor,
    DocumentStatus,
    type DriverDocument,
  } from "$lib/stores/driver_documents.svelte";

  import { getUsers, getUsersMap } from "$lib/stores/mobile_users.svelte";

  let daysThreshold = $state(30);

  let loading = $derived(getLoading());
  let error = $derived(getError());
  let expiringDocuments = $derived(getExpiringDocuments(daysThreshold));
  let expiredDocuments = $derived(getDocumentsByStatus(DocumentStatus.expired));
  let usersMap = $derived(getUsersMap());

  // Group documents by days until expiry
  let groupedDocuments = $derived.by(() => {
    const groups: Record<string, DriverDocument[]> = {
      expired: expiredDocuments,
      week: [],
      twoWeeks: [],
      month: [],
    };

    expiringDocuments.forEach((doc) => {
      const days = getDaysUntilExpiry(doc.expiryDate);
      if (days <= 7) {
        groups.week.push(doc);
      } else if (days <= 14) {
        groups.twoWeeks.push(doc);
      } else {
        groups.month.push(doc);
      }
    });

    return groups;
  });

  function getDaysUntilExpiry(expiryDate: Date) {
    const now = new Date();
    const days = Math.floor(
      (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
    );
    return days;
  }

  function openDocument(url: string) {
    window.open(url, "_blank");
  }

  function getUrgencyColor(days: number) {
    if (days <= 0) return "text-destructive";
    if (days <= 7) return "text-red-600";
    if (days <= 14) return "text-orange-600";
    return "text-yellow-600";
  }
</script>

<svelte:head>
  <title>{m.expiringDocuments_pageTitle()}</title>
</svelte:head>

<div class="container max-w-7xl mx-auto p-6">
  <div class="mb-8">
    <h1 class="text-3xl font-bold">{m.expiringDocuments_pageHeading()}</h1>
    <p class="text-muted-foreground mt-2">
      {m.expiringDocuments_pageDescription({ daysThreshold })}
    </p>
  </div>

  {#if error}
    <Alert variant="destructive" class="mb-6">
      <AlertTriangle class="h-4 w-4" />
      <AlertDescription
        >{m.expiringDocuments_errorPrefix()}{error}</AlertDescription
      >
    </Alert>
  {/if}

  <!-- Summary Stats -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
    <Card.Root>
      <Card.Header class="pb-2">
        <Card.Title class="text-sm font-medium text-muted-foreground">
          {m.expiringDocuments_expiredTitle()}
        </Card.Title>
      </Card.Header>
      <Card.Content>
        <div class="text-2xl font-bold text-destructive">
          {groupedDocuments.expired.length}
        </div>
      </Card.Content>
    </Card.Root>

    <Card.Root>
      <Card.Header class="pb-2">
        <Card.Title class="text-sm font-medium text-muted-foreground">
          {m.expiringDocuments_expiring7DaysTitle()}
        </Card.Title>
      </Card.Header>
      <Card.Content>
        <div class="text-2xl font-bold text-red-600">
          {groupedDocuments.week.length}
        </div>
      </Card.Content>
    </Card.Root>

    <Card.Root>
      <Card.Header class="pb-2">
        <Card.Title class="text-sm font-medium text-muted-foreground">
          {m.expiringDocuments_expiring14DaysTitle()}
        </Card.Title>
      </Card.Header>
      <Card.Content>
        <div class="text-2xl font-bold text-orange-600">
          {groupedDocuments.twoWeeks.length}
        </div>
      </Card.Content>
    </Card.Root>

    <Card.Root>
      <Card.Header class="pb-2">
        <Card.Title class="text-sm font-medium text-muted-foreground">
          {m.expiringDocuments_expiring30DaysTitle()}
        </Card.Title>
      </Card.Header>
      <Card.Content>
        <div class="text-2xl font-bold text-yellow-600">
          {groupedDocuments.month.length}
        </div>
      </Card.Content>
    </Card.Root>
  </div>

  {#if loading}
    <Card.Root>
      <Card.Content class="py-12">
        <div class="flex items-center justify-center">
          <Spinner className="h-8 w-8" />
        </div>
      </Card.Content>
    </Card.Root>
  {:else}
    <!-- Expired Documents -->
    {#if groupedDocuments.expired.length > 0}
      <Card.Root class="mb-6 border-destructive">
        <Card.Header>
          <Card.Title class="flex items-center gap-2 text-destructive">
            <AlertTriangle class="h-5 w-5" />
            {m.expiringDocuments_expiredSectionTitle({
              count: groupedDocuments.expired.length,
            })}
          </Card.Title>
        </Card.Header>
        <Card.Content>
          <div class="divide-y">
            {#each groupedDocuments.expired as document}
              {@const driver = usersMap.get(document.driverUID || "")}
              <div class="py-4 first:pt-0 last:pb-0">
                <div class="flex items-start justify-between gap-4">
                  <div class="flex-1">
                    <h4 class="font-semibold">{document.documentName}</h4>
                    <p class="text-sm text-muted-foreground">
                      {getDocumentTypeDisplayName(document.documentType)}
                      {#if driver}
                        • {m.expiringDocuments_driverPrefix()}{driver.displayName ||
                          driver.email ||
                          m.expiringDocuments_unknownDriver()}
                      {/if}
                    </p>
                    <p class="text-sm text-destructive font-medium mt-1">
                      {m.expiringDocuments_expiredTimeAgo()}
                      {formatDistanceToNow(document.expiryDate, {
                        addSuffix: true,
                      })}
                    </p>
                  </div>
                  <div class="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onclick={() => openDocument(document.fileURL)}
                    >
                      <ExternalLink class="h-4 w-4" />
                    </Button>
                    <Button size="sm" href="/documents/{document.id}">
                      {m.expiringDocuments_reviewButton()}
                    </Button>
                  </div>
                </div>
              </div>
            {/each}
          </div>
        </Card.Content>
      </Card.Root>
    {/if}

    <!-- Expiring Soon -->
    {#if groupedDocuments.week.length > 0}
      <Card.Root class="mb-6">
        <Card.Header>
          <Card.Title class="flex items-center gap-2 text-red-600">
            <Clock class="h-5 w-5" />
            {m.expiringDocuments_expiring7DaysSectionTitle({
              count: groupedDocuments.week.length,
            })}
          </Card.Title>
        </Card.Header>
        <Card.Content>
          <div class="divide-y">
            {#each groupedDocuments.week as document}
              {@const driver = usersMap.get(document.driverUID || "")}
              {@const days = getDaysUntilExpiry(document.expiryDate)}
              <div class="py-4 first:pt-0 last:pb-0">
                <div class="flex items-start justify-between gap-4">
                  <div class="flex-1">
                    <h4 class="font-semibold">{document.documentName}</h4>
                    <p class="text-sm text-muted-foreground">
                      {getDocumentTypeDisplayName(document.documentType)}
                      {#if driver}
                        • {m.expiringDocuments_driverPrefix()}{driver.displayName ||
                          driver.email ||
                          m.expiringDocuments_unknownDriver()}
                      {/if}
                    </p>
                    <p class="text-sm {getUrgencyColor(days)} font-medium mt-1">
                      {days === 1
                        ? m.expiringDocuments_expiresInDays_one({ days })
                        : m.expiringDocuments_expiresInDays_other({ days })} ({format(
                        document.expiryDate,
                        "MMM d, yyyy",
                      )})
                    </p>
                  </div>
                  <div class="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onclick={() => openDocument(document.fileURL)}
                    >
                      <ExternalLink class="h-4 w-4" />
                    </Button>
                    <Button size="sm" href="/documents/{document.id}">
                      {m.expiringDocuments_reviewButton()}
                    </Button>
                  </div>
                </div>
              </div>
            {/each}
          </div>
        </Card.Content>
      </Card.Root>
    {/if}

    <!-- Other expiring documents -->
    {#if groupedDocuments.twoWeeks.length > 0 || groupedDocuments.month.length > 0}
      <Card.Root>
        <Card.Header>
          <Card.Title>{m.expiringDocuments_otherExpiringTitle()}</Card.Title>
        </Card.Header>
        <Card.Content>
          <div class="divide-y">
            {#each [...groupedDocuments.twoWeeks, ...groupedDocuments.month] as document}
              {@const driver = usersMap.get(document.driverUID || "")}
              {@const days = getDaysUntilExpiry(document.expiryDate)}
              <div class="py-4 first:pt-0 last:pb-0">
                <div class="flex items-start justify-between gap-4">
                  <div class="flex-1">
                    <h4 class="font-semibold">{document.documentName}</h4>
                    <p class="text-sm text-muted-foreground">
                      {getDocumentTypeDisplayName(document.documentType)}
                      {#if driver}
                        • {m.expiringDocuments_driverPrefix()}{driver.displayName ||
                          driver.email ||
                          m.expiringDocuments_unknownDriver()}
                      {/if}
                    </p>
                    <p class="text-sm {getUrgencyColor(days)} font-medium mt-1">
                      {m.expiringDocuments_expiresInDays_other({ days })} ({format(
                        document.expiryDate,
                        "MMM d, yyyy",
                      )})
                    </p>
                  </div>
                  <div class="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onclick={() => openDocument(document.fileURL)}
                    >
                      <ExternalLink class="h-4 w-4" />
                    </Button>
                    <Button size="sm" href="/documents/{document.id}">
                      {m.expiringDocuments_reviewButton()}
                    </Button>
                  </div>
                </div>
              </div>
            {/each}
          </div>
        </Card.Content>
      </Card.Root>
    {/if}

    {#if groupedDocuments.expired.length === 0 && expiringDocuments.length === 0}
      <Card.Root>
        <Card.Content class="py-12">
          <div class="flex flex-col items-center justify-center text-center">
            <FileText class="h-12 w-12 text-muted-foreground mb-4" />
            <h3 class="text-lg font-semibold mb-2">
              {m.expiringDocuments_allUpToDateTitle()}
            </h3>
            <p class="text-muted-foreground">
              {m.expiringDocuments_allUpToDateDescription({ daysThreshold })}
            </p>
          </div>
        </Card.Content>
      </Card.Root>
    {/if}
  {/if}
</div>
