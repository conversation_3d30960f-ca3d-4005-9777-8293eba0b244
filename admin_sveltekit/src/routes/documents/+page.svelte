<script lang="ts">
  import * as Card from "$lib/components/ui/card/index.js";
  import { FileText } from "lucide-svelte";
  import * as m from "$lib/paraglide/messages";
</script>

<Card.Root class="h-full">
  <Card.Content class="h-full flex items-center justify-center">
    <div class="text-center">
      <FileText class="h-16 w-16 text-muted-foreground mx-auto mb-4" />
      <h3 class="text-lg font-semibold mb-2">
        {m.documentsPage_selectDocumentTitle()}
      </h3>
      <p class="text-muted-foreground">
        {m.documentsPage_selectDocumentDescription()}
      </p>
    </div>
  </Card.Content>
</Card.Root>
