<script lang="ts">
  import { Button } from "$lib/components/ui/button";
  import { cn } from "$lib/utils";
  import { page } from "$app/state";
  import * as m from "$lib/paraglide/messages";
  import { i18n } from "$lib/i18n";
  import { getFirebaseUser } from "$lib/stores/auth.svelte";
  import {
    Home,
    CarIcon,
    WalletIcon,
    ServerIcon,
    UsersIcon,
    MessageSquareIcon,
    FileText,
    Car,
  } from "lucide-svelte";

  let { class: className } = $props<{
    class?: string;
  }>();

  // Get current user
  const firebaseUser = $derived(getFirebaseUser());

  const navigation = [
    {
      title: m.mainPage_navDashboard(),
      icon: Home,
      href: "/",
    },
    {
      title: m.mainPage_navRides(),
      icon: CarIcon,
      submenu: [
        { title: m.mainPage_navRidesTrips(), href: "/rides/trips" },
        { title: m.mainPage_navRidesClients(), href: "/rides/clients" },
        { title: m.mainPage_navRidesDrivers(), href: "/rides/drivers" },
      ],
    },
    {
      title: m.mainPage_navFinance(),
      icon: WalletIcon,
      submenu: [
        {
          title: m.mainPage_navFinancePayments(),
          href: "/finance/payments",
        },
        // { title: "Reports", href: "/finance/reports" },
      ],
    },
    {
      title: m.mainPage_navReports(),
      icon: ServerIcon,
      submenu: [
        {
          title: m.mainPage_navReportsEvents(),
          href: "/reports/events",
        },
      ],
    },
    {
      title: m.mainPage_navSupport(),
      icon: MessageSquareIcon,
      submenu: [
        {
          title: m.mainPage_navSupportFeedbacks(),
          href: "/support/feedbacks",
        },
        {
          title: m.mainPage_navSupportChats(),
          href: "/support/chats",
        },
      ],
    },
    {
      title: m.mainPage_navDocuments(),
      icon: FileText,
      submenu: [
        {
          title: m.mainPage_navDocumentsAll(),
          href: "/documents",
        },
        {
          title: m.mainPage_navDocumentsExpiring(),
          href: "/documents/expiring",
        },
      ],
    },
    {
      title: m.mainPage_navVehicles(),
      icon: Car,
      submenu: [
        {
          title: m.mainPage_navVehiclesAll(),
          href: "/vehicles",
        },
        {
          title: m.mainPage_navVehiclesAssignments(),
          href: "/vehicles/assignments",
        },
      ],
    },
    {
      title: m.mainPage_navAdmin(),
      icon: UsersIcon,
      submenu: [
        {
          title: m.mainPage_navAdminNotifications(),
          href: "/admin/notifications",
        },
        {
          title: m.mainPage_navAdminConfigurations(),
          href: "/admin/configurations",
        },
        {
          title: m.mainPage_navAdminAccounts(),
          href: "/admin/accounts",
        },
      ],
    },
  ];


  let isActive = (href: string) => {
    // Get the canonical (non-localized) path
    const canonicalPath = i18n.route(page.url.pathname);

    if (href === "/") {
      return canonicalPath === href;
    }

    // Special case: highlight "All Vehicles" when viewing individual vehicle details
    if (
      href === "/vehicles" &&
      canonicalPath.startsWith("/vehicles/") &&
      !canonicalPath.startsWith("/vehicles/assignments")
    ) {
      return true;
    }

    // Special case: highlight "Chats" when viewing an individual chat
    if (
      href === "/support/chats" &&
      canonicalPath.startsWith("/support/chats")
    ) {
      return true;
    }

    // Special case: highlight "Feedbacks" when viewing an individual feedback
    if (
      href === "/support/feedbacks" &&
      canonicalPath.startsWith("/support/feedbacks")
    ) {
      return true;
    }

    // For other submenu items, use exact match
    return canonicalPath === href;
  };

  let isParentActive = (item: any) => {
    // Get the canonical (non-localized) path
    const canonicalPath = i18n.route(page.url.pathname);

    if (!item.submenu) return false;

    // Check if any submenu item is exactly active
    const hasActiveSubmenu = item.submenu.some(
      (sub: { href: string }) => canonicalPath === sub.href,
    );

    if (hasActiveSubmenu) return true;

    // For special cases like /vehicles/[vehicleId], check if path starts with any submenu base
    // This handles dynamic routes under parent sections
    return item.submenu.some((sub: { href: string }) => {
      // Extract the base path (e.g., "/vehicles" from "/vehicles")
      const basePath = sub.href.split("/").slice(0, 2).join("/");
      return (
        basePath &&
        canonicalPath.startsWith(basePath) &&
        canonicalPath !== sub.href
      );
    });
  };
</script>

<div
  class={cn(
    "pb-12 w-56 border-r bg-background h-[calc(100vh-3.6rem)] overflow-y-auto scrollbar-none",
    className,
  )}
>
  <div class="space-y-4 py-4">
    <div class="px-3 py-2">
      <div class="space-y-10">
        {#each navigation as item}
          <div class="space-y-1">
            {#if item.submenu}
              <h4
                class={cn(
                  "px-2 py-1 font-semibold text-sm tracking-tight",
                  isParentActive(item) && "text-primary",
                )}
              >
                <div class="flex items-center gap-x-2">
                  <item.icon class="h-4 w-4" />
                  {item.title}
                </div>
              </h4>
              {#each item.submenu as submenuItem}
                <Button
                  variant="ghost"
                  class={cn(
                    "w-full justify-start px-2 text-sm font-normal",
                    isActive(submenuItem.href) && "bg-muted font-medium",
                  )}
                  href={submenuItem.href}
                >
                  {submenuItem.title}
                </Button>
              {/each}
            {:else}
              <Button
                variant="ghost"
                class={cn(
                  "w-full justify-start px-2 text-sm font-normal",
                  isActive(item.href!) && "bg-muted font-medium",
                )}
                href={item.href}
              >
                <div class="flex items-center gap-x-2">
                  <item.icon class="h-4 w-4" />
                  {item.title}
                </div>
              </Button>
            {/if}
          </div>
        {/each}

      </div>
    </div>
  </div>
</div>
