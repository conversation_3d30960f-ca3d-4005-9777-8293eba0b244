<script lang="ts">
  import { i18n } from '$lib/i18n';
  import { ParaglideJS } from '@inlang/paraglide-sveltekit';
  import type { AvailableLanguageTag } from '$lib/paraglide/runtime';
  import { page } from '$app/state';
  import { goto } from '$app/navigation';
  import { getFirebaseUser } from '$lib/stores/auth.svelte';
  import { signOut } from 'firebase/auth';
  import { auth } from '$lib/firebase.client';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import { Button } from '$lib/components/ui/button';
  import Sidebar from './Sidebar.svelte';
  import LogOut from 'lucide-svelte/icons/log-out';
  import User from 'lucide-svelte/icons/user';
  import '../app.css';
  import { Toaster } from '$lib/components/ui/sonner';
  import { toast } from 'svelte-sonner';
  import { ModeWatcher } from 'mode-watcher';
  import { beforeNavigate } from '$app/navigation';
  import { updated } from '$app/state';
  import { getDatabase, ref, onValue } from 'firebase/database';
  import { ServerIcon } from 'lucide-svelte';
  import { VERSION } from './version';
  import {
    fetchAdminUser,
    getCurrentAdminUser,
    clearCurrentAdminUser,
    syncUserProfile,
    loadUserTenantAccess,
  } from '$lib/stores/admin_users.svelte';
  import TenantSelector from '$lib/components/TenantSelector.svelte';
  import Spinner from '$lib/components/ui/Spinner.svelte';
  import * as m from '$lib/paraglide/messages';
  import { setLanguageTag, languageTag } from '$lib/paraglide/runtime';
  import { onMount } from 'svelte';
  import { webPushService } from '$lib/services/WebPushService';
  import { imageCacheService } from '$lib/services/ImageCacheService';

  let { children } = $props();

  // Language persistence with localStorage
  const LANGUAGE_STORAGE_KEY = 'fiaranow-language';

  // Reactive language state to trigger re-renders
  let currentLanguage = $state(languageTag());

  // The {#key currentLanguage} block will force re-render when language changes

  // Initialize language from localStorage on client
  onMount(() => {
    const savedLanguage = localStorage.getItem(LANGUAGE_STORAGE_KEY);

    if (savedLanguage && ['en-us', 'fr-fr'].includes(savedLanguage)) {
      // Set language tag without page reload
      setLanguageTag(savedLanguage as AvailableLanguageTag);
      currentLanguage = savedLanguage as AvailableLanguageTag;
    } else {
      // Initialize current language state
      currentLanguage = languageTag();
    }
  });

  // Reactive firebase user
  const firebaseUser = $derived(getFirebaseUser());

  // Add server selection state
  let usingEmulator = $state(localStorage.getItem('useEmulator') === 'true');
  let showServerSelection = $state(import.meta.env.DEV);

  // Function to toggle server and reload the application
  function toggleServer() {
    const newValue = !usingEmulator;
    localStorage.setItem('useEmulator', newValue.toString());

    // Force reload the application to apply new server settings
    window.location.reload();
  }

  // Clock drift detection
  let clockDriftToastId = $state<string | number | undefined>(undefined);
  let serverTimeOffset = $state(0);

  const checkClockDrift = () => {
    try {
      const db = getDatabase();
      const offsetRef = ref(db, '.info/serverTimeOffset');

      onValue(offsetRef, (snapshot) => {
        serverTimeOffset = snapshot.val() || 0;
        const drift = Math.abs(serverTimeOffset);

        console.log('Clock Drift:', drift);

        if (drift > 15000) {
          // More than 15 seconds - show error toast
          if (!clockDriftToastId) {
            clockDriftToastId = toast.error(m.mainPage_clockDriftErrorTitle(), {
              description: m.mainPage_clockDriftErrorDescription(),
              duration: 60000, // 60 seconds
              onDismiss: () => {
                clockDriftToastId = undefined;
              },
            });
          }
        } else if (drift > 5000) {
          // Between 5-15 seconds - show warning toast
          if (!clockDriftToastId) {
            clockDriftToastId = toast.warning(m.mainPage_clockDriftWarningTitle(), {
              description: m.mainPage_clockDriftWarningDescription(),
              duration: 60000, // 60 seconds
              onDismiss: () => {
                clockDriftToastId = undefined;
              },
            });
          }
        } else if (clockDriftToastId) {
          // If drift is fixed and toast is showing, dismiss it
          toast.dismiss(clockDriftToastId);
          clockDriftToastId = undefined;
        }
      });
    } catch (error) {
      console.error('Error setting up clock drift detection:', error);
    }
  };

  $effect(() => {
    if (firebaseUser && !(firebaseUser === true)) {
      checkClockDrift();

      // First sync the profile
      syncUserProfile(firebaseUser)
        .then(() => {
          // Then fetch admin user details
          return fetchAdminUser(firebaseUser.uid);
        })
        .then(async (adminUser) => {
          // Load tenant access
          if (adminUser) {
            await loadUserTenantAccess(firebaseUser.uid);
          }
          // Initialize web push notifications for admin user
          if (adminUser && adminUser.isActive) {
            try {
              const initialized = await webPushService.initializeForUser(firebaseUser.uid);
              if (initialized) {
                console.log('Web push notifications initialized');
              }
            } catch (error) {
              console.error('Failed to initialize web push:', error);
            }
          }

          // Initialize image cache service
          try {
            await imageCacheService.initialize();
            console.log('🖼️ Image cache service initialized');
          } catch (error) {
            console.error('❌ Failed to initialize image cache service:', error);
          }
        })
        .catch((error) => {
          console.error('Failed to verify admin access:', error);
          toast.error(m.mainPage_adminAccessVerificationFailed());
        });
    } else if (firebaseUser === true) {
      // User signed out
      clearCurrentAdminUser();
    }
  });

  function switchToLanguage(newLanguage: AvailableLanguageTag) {
    // Set language tag and update localStorage
    setLanguageTag(newLanguage);
    localStorage.setItem(LANGUAGE_STORAGE_KEY, newLanguage);

    // Update reactive state to trigger UI re-render
    currentLanguage = newLanguage;
  }

  const handleLogout = async () => {
    if (!auth) return;
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  beforeNavigate(({ willUnload, to }) => {
    if (updated.current && !willUnload && to?.url) {
      location.href = to.url.href;
    }
  });
</script>

<ModeWatcher />
<ParaglideJS {i18n}>
  {#key currentLanguage}
    <div class="h-screen flex flex-col">
      <div class="hidden max-xl:flex items-center justify-center h-screen bg-red-50 text-red-600 p-4">
        {m.mainPage_screenTooSmall()}
      </div>
      <div class="max-xl:hidden h-full flex flex-col">
        {#if firebaseUser !== false && firebaseUser !== true}
          {#if getCurrentAdminUser() === undefined}
            <div class="h-full flex flex-col items-center justify-center space-y-4">
              <Spinner class="w-8 h-8" />
              {#if showServerSelection}
                <div class="flex items-center gap-2">
                  <Button
                    variant={usingEmulator ? 'default' : 'outline'}
                    size="sm"
                    onclick={toggleServer}
                    class="flex items-center gap-1"
                  >
                    <ServerIcon class="h-3.5 w-3.5" />
                    <span>{usingEmulator ? m.mainPage_serverEmulator() : m.mainPage_serverLive()}</span>
                  </Button>
                </div>
              {/if}
            </div>
          {:else if !getCurrentAdminUser()?.isActive}
            <div class="h-full flex flex-col items-center justify-center p-4 space-y-4">
              <h2 class="text-xl font-semibold text-red-600">
                {m.mainPage_accessDeniedTitle()}
              </h2>
              <p class="text-center text-muted-foreground">
                {@html m.mainPage_accessDeniedDescription()}
              </p>
              <Button variant="outline" onclick={handleLogout}>
                <LogOut class="mr-2 h-4 w-4" />
                <span>{m.mainPage_signOutButton()}</span>
              </Button>
            </div>
          {:else}
            <header class="border-b w-full">
              <div class="flex h-14 items-center px-6 w-full justify-between">
                <div class="flex items-center gap-4">
                  <div class="font-semibold">
                    {m.mainPage_headerTitle()}
                    <span class="text-sm text-muted-foreground">{VERSION}</span>
                  </div>
                  {#if showServerSelection}
                    <div class="flex items-center gap-2">
                      <Button
                        variant={usingEmulator ? 'default' : 'outline'}
                        size="sm"
                        onclick={toggleServer}
                        class="flex items-center gap-1"
                      >
                        <ServerIcon class="h-3.5 w-3.5" />
                        <span>{usingEmulator ? m.mainPage_serverEmulator() : m.mainPage_serverLive()}</span>
                      </Button>
                    </div>
                  {/if}
                </div>
                <div class="flex items-center gap-4">
                  <TenantSelector />
                  <DropdownMenu.Root>
                    <DropdownMenu.Trigger>
                      <Button variant="ghost" class="flex items-center space-x-2">
                        <img
                          class="inline-block h-8 w-8 rounded-full"
                          src={firebaseUser.photoURL}
                          alt={m.mainPage_profileAlt()}
                        />
                        <span>{firebaseUser.displayName}</span>
                      </Button>
                    </DropdownMenu.Trigger>
                    <DropdownMenu.Content class="w-48">
                      <DropdownMenu.Label>{m.mainPage_menuMyAccount()}</DropdownMenu.Label>
                      <DropdownMenu.Separator />
                      <DropdownMenu.Item>
                        <User class="mr-2 h-4 w-4" />
                        <span>{m.mainPage_menuProfile()}</span>
                      </DropdownMenu.Item>
                      <DropdownMenu.Separator />
                      <DropdownMenu.Label>{m.mainPage_menuLanguage()}</DropdownMenu.Label>
                      <DropdownMenu.Item onclick={() => switchToLanguage('en-us')}>
                        🇺🇸&nbsp;&nbsp;
                        <span>{m.mainPage_menuLanguageEnglish()}</span>
                      </DropdownMenu.Item>
                      <DropdownMenu.Item onclick={() => switchToLanguage('fr-fr')}>
                        🇫🇷&nbsp;&nbsp;
                        <span>{m.mainPage_menuLanguageFrench()}</span>
                      </DropdownMenu.Item>
                      <DropdownMenu.Separator />
                      <DropdownMenu.Item onclick={handleLogout}>
                        <LogOut class="mr-2 h-4 w-4" />
                        <span>{m.mainPage_menuLogout()}</span>
                      </DropdownMenu.Item>
                    </DropdownMenu.Content>
                  </DropdownMenu.Root>
                </div>
              </div>
            </header>
            <div class="flex h-[calc(100vh-3.5rem)]">
              <Sidebar />
              <main class="flex-1">
                {@render children()}
              </main>
            </div>
          {/if}
        {:else}
          <main class="flex-1 h-full">
            {@render children()}
          </main>
        {/if}
      </div>
    </div>

    <Toaster position="bottom-right" richColors closeButton />
  {/key}
</ParaglideJS>
