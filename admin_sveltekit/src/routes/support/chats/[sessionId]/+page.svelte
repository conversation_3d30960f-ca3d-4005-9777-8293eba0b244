<script lang="ts">
  import { page } from '$app/state';
  import { onMount, onDestroy } from 'svelte';
  import * as Card from '$lib/components/ui/card/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import * as m from '$lib/paraglide/messages';
  import {
    getChatSessionById,
    getChatSessionByIdDirect,
    loadChatMessages,
    getCurrentMessages,
    sendMessage,
    updateChatStatus,
    isMessagesLoading,
    ChatStatus,
    SenderType,
    MessageType,
    type ChatSession,
  } from '$lib/stores/chat_sessions.svelte';
  import { getUsersMap, getDisplayName } from '$lib/stores/mobile_users.svelte';
  import { getFeedbackById } from '$lib/stores/feedbacks.svelte';
  import { formatTime } from '$lib/utils/datetime';
  import { toast } from 'svelte-sonner';
  import { Send, Image, Check, Archive, RefreshCw, Paperclip, MessageSquare } from 'lucide-svelte';
  import { auth, db } from '$lib/firebase.client';
  import Spinner from '$lib/components/ui/Spinner.svelte';
  import StorageImage from '$lib/components/StorageImage.svelte';
  import { ChatPresenceManager } from '$lib/services/chatPresence';

  let sessionId = $derived(page.params.sessionId);
  let session = $derived(getChatSessionById(sessionId));
  let messages = $derived(getCurrentMessages());
  let fallbackSession = $state<ChatSession | null>(null);

  // Use fallback session if main session is not found
  let currentSession = $derived(session || fallbackSession);
  let usersMap = $derived(getUsersMap());
  let loading = $derived(isMessagesLoading());
  let linkedFeedback = $derived(currentSession?.feedbackId ? getFeedbackById(currentSession.feedbackId) : null);

  let messageInput = $state('');
  let isSending = $state(false);
  let isLoadingMore = $state(false);
  let messagesContainer = $state<HTMLDivElement>();
  let shouldAutoScroll = $state(true);
  let currentAdminUid = auth?.currentUser?.uid || 'admin';
  let previousSessionId = $state<string | null>(null);
  let presenceManager: ChatPresenceManager | null = null;

  // Focus management function
  function focusMessageInput() {
    // Small delay to ensure DOM is ready
    setTimeout(() => {
      // Try to find by ID first
      let input = document.getElementById('chat-message-input');

      // Fallback to finding by placeholder
      if (!input) {
        input = document.querySelector('form input[placeholder*="message"]:not([disabled])');
      }

      // Fallback to any input in form
      if (!input) {
        input = document.querySelector('form input:not([disabled]):not([type="button"]):not([type="submit"])');
      }

      if (input instanceof HTMLInputElement && !input.disabled) {
        input.focus();
      }
    }, 100); // Increased delay slightly
  }

  // Handle session changes reactively
  $effect(() => {
    if (sessionId && sessionId !== previousSessionId) {
      // Clear previous state immediately
      messageInput = '';
      shouldAutoScroll = true;
      fallbackSession = null;

      // Update previousSessionId
      previousSessionId = sessionId;

      // Leave previous chat session if any
      if (presenceManager && previousSessionId) {
        presenceManager.leaveChatSession();
      }

      // Load new session messages with reset flag
      loadChatMessages(sessionId, true)
        .then(() => {
          // If session is not found in local array, fetch directly from Firestore
          if (!session) {
            getChatSessionByIdDirect(sessionId).then((directSession) => {
              if (directSession) {
                fallbackSession = directSession;
              }
            });
          }

          // Enter the new chat session for presence tracking
          if (!presenceManager && db) {
            presenceManager = new ChatPresenceManager(db, currentAdminUid, 'admin');
            presenceManager.enterChatSession(sessionId);
          }

          // Focus the input field after loading messages
          setTimeout(focusMessageInput, 100);
        })
        .catch((error) => {
          toast.error(m.chatDetails_errorLoadingMessagesToast());
        });
    }
  });

  onMount(() => {
    // Focus the input on initial mount if session exists
    if (currentSession) {
      setTimeout(focusMessageInput, 100);
    }
  });

  onDestroy(() => {
    // Cleanup presence tracking
    if (presenceManager) {
      presenceManager.leaveChatSession();
      presenceManager.dispose();
      presenceManager = null;
    }
  });

  async function handleSendMessage() {
    if (!messageInput.trim() || isSending || !currentSession) return;

    const message = messageInput.trim();
    messageInput = '';
    isSending = true;

    try {
      await sendMessage(sessionId, message, currentAdminUid, SenderType.ADMIN);
      shouldAutoScroll = true;
    } catch (error) {
      toast.error(m.chatDetails_errorSendingMessageToast());
      messageInput = message; // Restore message on error
    } finally {
      isSending = false;
      // Focus input after send completes (success or error)
      focusMessageInput();
    }
  }

  async function handleLoadMore() {
    if (isLoadingMore || !sessionId) return;

    isLoadingMore = true;
    try {
      await loadChatMessages(sessionId, false);
    } catch (error) {
      toast.error(m.chatDetails_errorLoadingMessagesToast());
    } finally {
      isLoadingMore = false;
    }
  }

  async function handleStatusChange(status: ChatStatus) {
    if (!currentSession) return;

    try {
      await updateChatStatus(sessionId, status);
      toast.success(m.chatDetails_statusUpdatedSuccessToast());

      // Update fallback session if it exists
      if (fallbackSession) {
        fallbackSession = { ...fallbackSession, status };
      }
    } catch (error) {
      toast.error(m.chatDetails_errorUpdatingStatusToast());
    }
  }

  function handleScroll() {
    if (messagesContainer) {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainer;
      // Disable auto-scroll if user scrolls up significantly
      shouldAutoScroll = scrollTop + clientHeight >= scrollHeight - 50;

      // Load more messages when scrolled to the top
      if (scrollTop === 0 && !isLoadingMore && messages.length > 0) {
        handleLoadMore();
      }
    }
  }

  function scrollToBottom(force = false) {
    if (messagesContainer && (shouldAutoScroll || force)) {
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
  }

  $effect(() => {
    if (messages.length) {
      // Use a microtask to allow the DOM to update before scrolling
      queueMicrotask(() => scrollToBottom());
    }
  });
</script>

{#if currentSession}
  <Card.Root class="h-[calc(100vh-90px)] flex flex-col">
    <Card.Header class="flex-shrink-0 border-b p-6 pb-4">
      <div class="flex items-start justify-between gap-4">
        <div class="flex-1 min-w-0">
          <Card.Title class="flex items-center gap-2">
            <span class="break-words">{currentSession.title}</span>
            <Badge class="flex-shrink-0 text-xs">
              {currentSession.status === ChatStatus.ACTIVE
                ? m.chatDetails_statusActiveLabel()
                : currentSession.status === ChatStatus.RESOLVED
                  ? m.chatDetails_statusResolvedLabel()
                  : m.chatDetails_statusArchivedLabel()}
            </Badge>
          </Card.Title>
          <Card.Description>
            {@const participantUid = currentSession.participantUids.find((uid) => uid !== currentAdminUid)}
            {@const participant = participantUid ? usersMap.get(participantUid) : undefined}
            {m.chatDetails_chatWithLabel()}
            {participant ? getDisplayName(participant) : m.unknown_user()}
          </Card.Description>
        </div>
        <div class="flex flex-shrink-0 gap-2">
          {#if currentSession.status === ChatStatus.ACTIVE}
            <Button size="sm" variant="outline" onclick={() => handleStatusChange(ChatStatus.RESOLVED)}>
              <Check class="w-4 h-4 mr-1" />
              {m.chatDetails_markResolvedButton()}
            </Button>
          {:else if currentSession.status === ChatStatus.RESOLVED}
            <Button size="sm" variant="outline" onclick={() => handleStatusChange(ChatStatus.ACTIVE)}>
              <RefreshCw class="w-4 h-4 mr-1" />
              {m.chatDetails_reopenChatButton()}
            </Button>
          {/if}
          {#if currentSession.status !== ChatStatus.ARCHIVED}
            <Button size="sm" variant="outline" onclick={() => handleStatusChange(ChatStatus.ARCHIVED)}>
              <Archive class="w-4 h-4 mr-1" />
              {m.chatDetails_archiveChatButton()}
            </Button>
          {/if}
        </div>
      </div>
    </Card.Header>

    <!-- Linked Feedback Banner -->
    {#if linkedFeedback}
      <div class="px-6 py-3 bg-muted border-b flex items-center justify-between flex-shrink-0">
        <div class="flex items-center gap-2">
          <Paperclip class="w-4 h-4" />
          <span class="text-sm">
            {m.linked_feedback()}: {linkedFeedback.type === 'trip' ? m.trip_feedback() : m.chatDetails_appFeedbackLabel()}
            {#if linkedFeedback.rating}
              - {linkedFeedback.rating}/5 ⭐
            {/if}
          </span>
        </div>
        <Button size="sm" variant="outline" href="/support/feedbacks/{linkedFeedback.id}">
          {m.view_feedback()}
        </Button>
      </div>
    {/if}

    <Card.Content class="flex-1 flex flex-col p-0 overflow-hidden">
      <div bind:this={messagesContainer} class="flex-1 overflow-y-auto p-6" onscroll={handleScroll}>
        {#if loading && messages.length === 0}
          <div class="flex justify-center items-center h-full">
            <Spinner />
          </div>
        {:else if messages.length === 0}
          <div class="flex justify-center items-center h-full">
            <p class="text-muted-foreground">
              {m.chatDetails_noMessagesYet()}
            </p>
          </div>
        {:else}
          <div class="space-y-4">
            {#if isLoadingMore}
              <div class="flex justify-center py-2">
                <Spinner />
              </div>
            {/if}

            {#each messages.toReversed() as message (message.id)}
              {@const sender = usersMap.get(message.senderUid)}
              {@const isAdmin = message.senderType === SenderType.ADMIN}

              <div class="flex w-full {isAdmin ? 'justify-end' : 'justify-start'}">
                <div class="flex flex-col max-w-[80%] {isAdmin ? 'items-end' : 'items-start'}">
                  <div class="flex items-center gap-2 mb-1 px-1">
                    <span class="text-sm font-medium text-muted-foreground">
                      {isAdmin ? m.chatDetails_youLabel() : sender ? getDisplayName(sender) : m.unknown_user()}
                    </span>
                    <span class="text-xs text-muted-foreground">
                      {formatTime(message.timestamp)}
                    </span>
                  </div>

                  <div class="rounded-lg px-3 py-2 {isAdmin ? 'bg-primary text-primary-foreground' : 'bg-muted'}">
                    {#if message.messageType === MessageType.IMAGE && message.imageUrl}
                      <button
                        type="button"
                        onclick={() => window.open(message.imageUrl, '_blank')}
                        class="block p-0 border-0 bg-transparent max-w-full"
                      >
                        <StorageImage
                          pathOrUrl={message.imageUrl}
                          alt="Chat attachment"
                          class="max-w-xs h-auto rounded cursor-pointer"
                        />
                      </button>
                    {/if}
                    {#if message.message}
                      <p class="whitespace-pre-wrap break-words leading-relaxed m-0">
                        {message.message}
                      </p>
                    {/if}
                  </div>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </div>
    </Card.Content>

    <Card.Footer class="flex-shrink-0 p-4 border-t">
      <form
        onsubmit={(e) => {
          e.preventDefault();
          handleSendMessage();
        }}
        class="flex gap-2 w-full"
      >
        <Button type="button" size="icon" variant="outline" disabled title={m.chatDetails_imageUploadTooltip()}>
          <Image class="w-4 h-4" />
        </Button>
        <Input
          id="chat-message-input"
          bind:value={messageInput}
          placeholder={m.chatDetails_messageInputPlaceholder()}
          disabled={isSending || currentSession.status === ChatStatus.ARCHIVED}
          class="flex-1"
        />
        <Button
          type="submit"
          size="icon"
          disabled={!messageInput.trim() || isSending || currentSession.status === ChatStatus.ARCHIVED}
        >
          {#if isSending}
            <Spinner />
          {:else}
            <Send class="w-4 h-4" />
          {/if}
        </Button>
      </form>
    </Card.Footer>
  </Card.Root>
{:else}
  <Card.Root>
    <Card.Content class="flex items-center justify-center h-[calc(100vh-112px)]">
      {#if !sessionId}
        <div class="text-center text-muted-foreground">
          <MessageSquare class="w-12 h-12 mx-auto mb-4" />
          <h3 class="text-lg font-semibold">
            {m.chatsPage_selectChatTitle()}
          </h3>
          <p>{m.chatsPage_selectChatDescription()}</p>
        </div>
      {:else}
        <p class="text-center text-muted-foreground">
          {m.chatDetails_notFoundMessage()}
        </p>
      {/if}
    </Card.Content>
  </Card.Root>
{/if}
