<script lang="ts">
  import * as m from "$lib/paraglide/messages";
  import * as Card from "$lib/components/ui/card/index.js";
  import { Input } from "$lib/components/ui/input/index.js";
  import { Button } from "$lib/components/ui/button/index.js";
  import { Badge } from "$lib/components/ui/badge/index.js";
  import {
    getChatSessions,
    init as initChatSessions,
    ChatStatus,
    getCategoryDisplayName,
    cleanupMessages,
  } from "$lib/stores/chat_sessions.svelte";
  import type { ChatSession } from "$lib/stores/chat_sessions.svelte";
  import { onMount, onDestroy } from "svelte";
  import { localizedGoto } from "$lib/utils";
  import { page } from "$app/state";
  import {
    getUsersMap,
    getDisplayName,
    init as initMobileUsers,
  } from "$lib/stores/mobile_users.svelte";
  import { formatShortDateTime } from "$lib/utils/datetime";
  import { Search, MessageSquare, Plus } from "lucide-svelte";

  let { children } = $props();

  let searchQuery = $state("");
  let showActiveOnly = $state(true);

  onMount(() => {
    initChatSessions();
    initMobileUsers();
  });

  onDestroy(() => {
    // Clean up messages when leaving the chat section
    cleanupMessages();
  });

  function handleChatClick(session: ChatSession) {
    localizedGoto(`/support/chats/${session.id}`);
  }

  function getStatusBadgeColor(status: ChatStatus) {
    switch (status) {
      case ChatStatus.ACTIVE:
        return "bg-green-500";
      case ChatStatus.RESOLVED:
        return "bg-blue-500";
      case ChatStatus.ARCHIVED:
        return "bg-gray-500";
    }
  }

  function getStatusDisplayName(status: ChatStatus) {
    switch (status) {
      case ChatStatus.ACTIVE:
        return m.chatsLayout_statusActive();
      case ChatStatus.RESOLVED:
        return m.chatsLayout_statusResolved();
      case ChatStatus.ARCHIVED:
        return m.chatsLayout_statusArchived();
    }
  }

  let selectedSessionId = $derived(page.params.sessionId);
  let usersMap = $derived(getUsersMap());
  let chatSessions = $derived(getChatSessions());

  let filteredSessions = $derived(
    chatSessions.filter((session) => {
      if (showActiveOnly && session.status !== ChatStatus.ACTIVE) return false;

      if (searchQuery) {
        const query = searchQuery.toLowerCase();

        // Search by title
        if (session.title.toLowerCase().includes(query)) return true;

        // Search by participant names
        for (const uid of session.participantUids) {
          const user = usersMap.get(uid);
          if (user && getDisplayName(user).toLowerCase().includes(query)) {
            return true;
          }
        }

        // Search by last message
        if (session.lastMessage?.toLowerCase().includes(query)) return true;

        return false;
      }

      return true;
    }),
  );
</script>

<div class="p-4">
  <div class="flex h-full gap-4">
    <!-- Left section - List -->
    <div class="w-1/3 flex-shrink-0">
      <Card.Root>
        <Card.Header class="flex-shrink-0">
          <div class="flex flex-col gap-4">
            <div class="flex items-center justify-between">
              <div class="min-w-0 flex-1">
                <Card.Title>{m.chatsLayout_title()}</Card.Title>
                <Card.Description
                  >{m.chatsLayout_description()}</Card.Description
                >
              </div>
              <Button size="sm" variant="outline" class="flex-shrink-0">
                <Plus class="w-4 h-4 mr-1" />
                {m.chatsLayout_newChatButton()}
              </Button>
            </div>
            <div class="relative">
              <Search
                class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"
              />
              <Input
                type="text"
                placeholder={m.chatsLayout_searchPlaceholder()}
                bind:value={searchQuery}
                class="pl-10"
              />
            </div>
            <div class="flex items-center gap-2">
              <Button
                variant={showActiveOnly ? "default" : "outline"}
                size="sm"
                onclick={() => (showActiveOnly = true)}
              >
                {m.chatsLayout_activeChatsFilter()}
              </Button>
              <Button
                variant={!showActiveOnly ? "default" : "outline"}
                size="sm"
                onclick={() => (showActiveOnly = false)}
              >
                {m.chatsLayout_allChatsFilter()}
              </Button>
            </div>
          </div>
        </Card.Header>
        <Card.Content class="h-[calc(100vh-262px)] overflow-y-auto">
          <div class="space-y-2">
            {#each filteredSessions as session (session.id)}
              {@const participantUid = session.participantUids.find(
                (uid) => uid !== "admin",
              )}
              {@const participant = participantUid
                ? usersMap.get(participantUid)
                : undefined}
              <div
                class="flex items-start gap-3 rounded-lg border p-3 hover:bg-muted/50 cursor-pointer {selectedSessionId ===
                session.id
                  ? 'bg-muted'
                  : ''}"
                onclick={() => handleChatClick(session)}
                onkeydown={(e) => e.key === "Enter" && handleChatClick(session)}
                role="button"
                tabindex="0"
              >
                <div class="flex-1 min-w-0">
                  <div class="flex items-center gap-2 mb-1">
                    <MessageSquare
                      class="w-4 h-4 text-muted-foreground flex-shrink-0"
                    />
                    <p class="font-medium break-words text-sm">
                      {session.title}
                    </p>
                  </div>
                  <div
                    class="flex items-center gap-2 text-xs text-muted-foreground mb-1"
                  >
                    <span class="truncate max-w-[120px]"
                      >{participant
                        ? getDisplayName(participant)
                        : m.chatsLayout_unknownUser()}</span
                    >
                    <span class="flex-shrink-0">•</span>
                    <span class="truncate"
                      >{getCategoryDisplayName(session.category)}</span
                    >
                  </div>
                  {#if session.lastMessage}
                    <p
                      class="text-xs break-words text-muted-foreground max-w-full"
                    >
                      {session.lastMessage}
                    </p>
                  {/if}
                </div>
                <div class="flex flex-col items-end gap-1 flex-shrink-0">
                  <Badge
                    class={getStatusBadgeColor(session.status) +
                      " text-white text-xs"}
                  >
                    {getStatusDisplayName(session.status)}
                  </Badge>
                  <span class="text-xs text-muted-foreground whitespace-nowrap">
                    {formatShortDateTime(session.lastMessageAt)}
                  </span>
                  {#if session.unreadCount && session.unreadCount > 0}
                    <div
                      class="bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                    >
                      {session.unreadCount}
                    </div>
                  {/if}
                </div>
              </div>
            {/each}
            {#if !filteredSessions.length}
              <p class="text-sm text-muted-foreground text-center py-4">
                {searchQuery
                  ? m.chatsLayout_noChatsFoundSearch()
                  : m.chatsLayout_noChatsFound()}
              </p>
            {/if}
          </div>
        </Card.Content>
      </Card.Root>
    </div>

    <!-- Right section - Content -->
    <div class="flex-1 min-w-0">
      {@render children()}
    </div>
  </div>
</div>
