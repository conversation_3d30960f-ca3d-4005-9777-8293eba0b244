<script lang="ts">
  import * as Card from "$lib/components/ui/card/index.js";
  import * as m from "$lib/paraglide/messages";
</script>

<Card.Root class="h-full">
  <Card.Header>
    <Card.Title>{m.chatsPage_selectChatTitle()}</Card.Title>
    <Card.Description>
      {m.chatsPage_selectChatDescription()}
    </Card.Description>
  </Card.Header>
  <Card.Content>
    <div class="flex items-center justify-center h-[calc(100vh-260px)]">
      <div class="text-center">
        <svg
          class="mx-auto h-12 w-12 text-gray-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
          />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">
          {m.chatsPage_noChatSelected()}
        </h3>
        <p class="mt-1 text-sm text-gray-500">
          {m.chatsPage_selectChatFromList()}
        </p>
      </div>
    </div>
  </Card.Content>
</Card.Root>
