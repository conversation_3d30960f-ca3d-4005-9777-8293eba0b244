<script lang="ts">
  import { page } from "$app/state";
  import { onMount } from "svelte";
  import * as Card from "$lib/components/ui/card/index.js";
  import { Button } from "$lib/components/ui/button/index.js";
  import { Badge } from "$lib/components/ui/badge/index.js";
  import * as m from "$lib/paraglide/messages";
  import {
    getFeedbackById,
    updateFeedbackStatus,
    FeedbackType,
    FeedbackStatus,
  } from "$lib/stores/feedbacks.svelte";
  import { getUsersMap, getDisplayName } from "$lib/stores/mobile_users.svelte";
  import { getTrips } from "$lib/stores/trips.svelte";
  import {
    createChatSession,
    ChatCategory,
  } from "$lib/stores/chat_sessions.svelte";
  import { formatDateTime } from "$lib/utils/datetime";
  import { localizedGoto } from "$lib/utils";
  import { toast } from "svelte-sonner";
  import { <PERSON>, MessageSquare, <PERSON>, Check, Archive } from "lucide-svelte";

  let feedbackId = $derived(page.params.feedbackId);
  let feedback = $derived(getFeedbackById(feedbackId));
  let usersMap = $derived(getUsersMap());
  let trips = $derived(getTrips());
  let user = $derived(feedback ? usersMap.get(feedback.uid) : null);
  let trip = $derived(
    feedback && feedback.tripId
      ? trips.find((t) => t.id === feedback.tripId)
      : null,
  );

  let isUpdatingStatus = $state(false);
  let isCreatingChat = $state(false);

  async function handleUpdateStatus(status: FeedbackStatus) {
    if (!feedback) return;

    isUpdatingStatus = true;
    try {
      await updateFeedbackStatus(feedback.id, status);
      toast.success(m.feedback_status_updated());
    } catch (error) {
      toast.error(m.error_updating_feedback());
    } finally {
      isUpdatingStatus = false;
    }
  }

  async function handleInitiateChat() {
    if (!feedback || !user) return;

    isCreatingChat = true;
    try {
      const title =
        feedback.type === FeedbackType.TRIP
          ? `${m.trip_feedback_from()} ${getDisplayName(user)}`
          : `${m.app_feedback_from()} ${getDisplayName(user)}`;

      const sessionId = await createChatSession(
        title,
        ChatCategory.FEEDBACK_FOLLOWUP,
        [feedback.uid, "admin"], // Admin will be replaced with actual admin UID
        true,
        feedback.id,
      );

      toast.success(m.chat_session_created());
      localizedGoto(`/support/chats/${sessionId}`);
    } catch (error) {
      toast.error(m.error_creating_chat());
    } finally {
      isCreatingChat = false;
    }
  }

  function getStatusBadgeColor(status: FeedbackStatus) {
    switch (status) {
      case FeedbackStatus.NEW:
        return "bg-red-500";
      case FeedbackStatus.SEEN:
        return "bg-yellow-500";
      case FeedbackStatus.ADDRESSED:
        return "bg-green-500";
      case FeedbackStatus.ARCHIVED:
        return "bg-gray-500";
    }
  }

  function getStatusDisplayName(status: FeedbackStatus) {
    switch (status) {
      case FeedbackStatus.NEW:
        return m.feedback_status_new();
      case FeedbackStatus.SEEN:
        return m.feedback_status_seen();
      case FeedbackStatus.ADDRESSED:
        return m.feedback_status_addressed();
      case FeedbackStatus.ARCHIVED:
        return m.feedback_status_archived();
    }
  }
</script>

{#if feedback}
  <Card.Root class="h-full">
    <Card.Header>
      <div class="flex items-start justify-between">
        <div>
          <Card.Title class="flex items-center gap-2">
            {feedback.type === FeedbackType.TRIP
              ? m.trip_feedback()
              : m.application_feedback()}
            <Badge class={getStatusBadgeColor(feedback.status) + " text-white"}>
              {getStatusDisplayName(feedback.status)}
            </Badge>
          </Card.Title>
          <Card.Description>
            {m.submitted_on()}
            {formatDateTime(feedback.createdAt)}
          </Card.Description>
        </div>
        <div class="flex gap-2">
          {#if feedback.status === FeedbackStatus.NEW}
            <Button
              size="sm"
              variant="outline"
              onclick={() => handleUpdateStatus(FeedbackStatus.SEEN)}
              disabled={isUpdatingStatus}
            >
              <Eye class="w-4 h-4 mr-1" />
              {m.mark_as_seen()}
            </Button>
          {/if}
          {#if feedback.status !== FeedbackStatus.ADDRESSED}
            <Button
              size="sm"
              variant="outline"
              onclick={() => handleUpdateStatus(FeedbackStatus.ADDRESSED)}
              disabled={isUpdatingStatus}
            >
              <Check class="w-4 h-4 mr-1" />
              {m.mark_as_addressed()}
            </Button>
          {/if}
          {#if feedback.status !== FeedbackStatus.ARCHIVED}
            <Button
              size="sm"
              variant="outline"
              onclick={() => handleUpdateStatus(FeedbackStatus.ARCHIVED)}
              disabled={isUpdatingStatus}
            >
              <Archive class="w-4 h-4 mr-1" />
              {m.archive()}
            </Button>
          {/if}
        </div>
      </div>
    </Card.Header>
    <Card.Content class="h-[calc(100vh-262px)] overflow-y-auto space-y-6">
      <!-- User Information -->
      <div>
        <h3 class="text-lg font-semibold mb-2">{m.user_information()}</h3>
        <div class="space-y-1">
          <p>
            <span class="font-medium">{m.name()}:</span>
            {user ? getDisplayName(user) : m.unknown_user()}
          </p>
          {#if user?.phoneNumber}
            <p>
              <span class="font-medium">{m.phone()}:</span>
              {user.phoneNumber}
            </p>
          {/if}
          {#if user?.email}
            <p>
              <span class="font-medium">{m.email()}:</span>
              {user.email}
            </p>
          {/if}
        </div>
      </div>

      <!-- Rating (for trip feedback) -->
      {#if feedback.type === FeedbackType.TRIP && feedback.rating}
        <div>
          <h3 class="text-lg font-semibold mb-2">{m.rating()}</h3>
          <div class="flex items-center gap-1">
            {#each Array(5) as _, i}
              <Star
                class="w-6 h-6 {i < feedback.rating
                  ? 'fill-yellow-400 text-yellow-400'
                  : 'text-gray-300'}"
              />
            {/each}
            <span class="ml-2 text-lg">{feedback.rating}/5</span>
          </div>
        </div>
      {/if}

      <!-- Trip Information (for trip feedback) -->
      {#if feedback.type === FeedbackType.TRIP && trip}
        <div>
          <h3 class="text-lg font-semibold mb-2">{m.trip_information()}</h3>
          <div class="space-y-1">
            <p>
              <span class="font-medium">{m.trip_id()}:</span>
              <a
                href="/rides/trips/{trip.id}"
                class="text-primary hover:underline"
              >
                {trip.id}
              </a>
            </p>
            <p>
              <span class="font-medium">{m.pickup()}:</span>
              {trip.startLocationName}
            </p>
            <p>
              <span class="font-medium">{m.destination()}:</span>
              {trip.arrivalLocationName}
            </p>
            <p>
              <span class="font-medium">{m.date()}:</span>
              {formatDateTime(trip.createdAt)}
            </p>
          </div>
        </div>
      {/if}

      <!-- Feedback Message -->
      <div>
        <h3 class="text-lg font-semibold mb-2">{m.feedback_message()}</h3>
        <p class="whitespace-pre-wrap">
          {feedback.message || m.no_message_provided()}
        </p>
      </div>

      <!-- Images -->
      {#if feedback.images.length > 0}
        <div>
          <h3 class="text-lg font-semibold mb-2">{m.attached_images()}</h3>
          <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
            {#each feedback.images as imageUrl}
              <a
                href={imageUrl}
                target="_blank"
                rel="noopener noreferrer"
                class="relative group"
              >
                <img
                  src={imageUrl}
                  alt="Feedback attachment"
                  class="w-full h-40 object-cover rounded-lg border hover:opacity-90 transition-opacity"
                />
                <div
                  class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all rounded-lg"
                ></div>
              </a>
            {/each}
          </div>
        </div>
      {/if}

      <!-- Actions -->
      <div class="border-t pt-4">
        <div class="flex gap-2">
          {#if !feedback.chatSessionId}
            <Button onclick={handleInitiateChat} disabled={isCreatingChat}>
              <MessageSquare class="w-4 h-4 mr-1" />
              {isCreatingChat ? m.creating_chat() : m.initiate_chat()}
            </Button>
          {:else}
            <Button href="/support/chats/{feedback.chatSessionId}">
              <MessageSquare class="w-4 h-4 mr-1" />
              {m.view_chat()}
            </Button>
          {/if}
        </div>
      </div>
    </Card.Content>
  </Card.Root>
{:else}
  <Card.Root>
    <Card.Content class="py-8">
      <p class="text-center text-muted-foreground">{m.feedback_not_found()}</p>
    </Card.Content>
  </Card.Root>
{/if}
