<script lang="ts">
  import * as m from "$lib/paraglide/messages";
  import * as Card from "$lib/components/ui/card/index.js";
  import {
    getFeedbacks,
    init as initFeedbacks,
    reinitialize as reinitializeFeedbacks,
    FeedbackType,
    FeedbackStatus,
    isLoading,
  } from "$lib/stores/feedbacks.svelte";
  import type { Feedback } from "$lib/stores/feedbacks.svelte";
  import { onMount, onDestroy } from "svelte";
  import { localizedGoto } from "$lib/utils";
  import { page } from "$app/state";
  import {
    getUsersMap,
    getDisplayName,
    init as initMobileUsers,
    reinitialize as reinitializeMobileUsers,
  } from "$lib/stores/mobile_users.svelte";
  import {
    init as initTrips,
    destroy as destroyTrips,
  } from "$lib/stores/trips.svelte";
  import { tenantStore } from "$lib/stores/tenant.svelte";
  import { formatShortDateTime } from "$lib/utils/datetime";
  import { Toggle } from "$lib/components/ui/toggle/index.js";
  import { Badge } from "$lib/components/ui/badge/index.js";
  import { Star, Search } from "lucide-svelte";
  import { Input } from "$lib/components/ui/input/index.js";

  let { children } = $props();

  let showTripOnly = $state(false);
  let showApplicationOnly = $state(false);
  let showNewOnly = $state(false);
  let searchQuery = $state("");

  onMount(() => {
    initFeedbacks();
    initMobileUsers();
    initTrips();
  });

  onDestroy(() => {
    // Don't destroy the stores, keep for reuse
  });

  // Re-initialize stores when tenant changes
  $effect(() => {
    if (tenantStore.current) {
      reinitializeFeedbacks();
      reinitializeMobileUsers();
      initTrips(); // Re-initialize trips for the new tenant
    }
  });

  function handleFeedbackClick(feedback: Feedback) {
    localizedGoto(`/support/feedbacks/${feedback.id}`);
  }

  function getStatusBadgeColor(status: FeedbackStatus) {
    switch (status) {
      case FeedbackStatus.NEW:
        return "bg-red-500";
      case FeedbackStatus.SEEN:
        return "bg-yellow-500";
      case FeedbackStatus.ADDRESSED:
        return "bg-green-500";
      case FeedbackStatus.ARCHIVED:
        return "bg-gray-500";
    }
  }

  function getStatusDisplayName(status: FeedbackStatus) {
    switch (status) {
      case FeedbackStatus.NEW:
        return m.feedback_status_new();
      case FeedbackStatus.SEEN:
        return m.feedback_status_seen();
      case FeedbackStatus.ADDRESSED:
        return m.feedback_status_addressed();
      case FeedbackStatus.ARCHIVED:
        return m.feedback_status_archived();
    }
  }

  let selectedFeedbackId = $derived(page.params.feedbackId);
  let usersMap = $derived(getUsersMap());
  let feedbacks = $derived(getFeedbacks());
  let loading = $derived(isLoading());

  let filteredFeedbacks = $derived(
    feedbacks.filter((feedback) => {
      if (showTripOnly && feedback.type !== FeedbackType.TRIP) return false;
      if (showApplicationOnly && feedback.type !== FeedbackType.APPLICATION)
        return false;
      if (showNewOnly && feedback.status !== FeedbackStatus.NEW) return false;

      if (searchQuery) {
        const query = searchQuery.toLowerCase();

        // Search by message
        if (feedback.message?.toLowerCase().includes(query)) return true;

        // Search by user name
        const user = usersMap.get(feedback.uid);
        if (user && getDisplayName(user).toLowerCase().includes(query)) {
          return true;
        }

        return false;
      }

      return true;
    }),
  );
</script>

<div class="p-4">
  <div class="flex h-full gap-4">
    <!-- Left section - List -->
    <div class="w-1/3">
      <Card.Root>
        <Card.Header>
          <div class="flex flex-col gap-4">
            <div>
              <Card.Title>{m.feedbacks_title()}</Card.Title>
              <Card.Description>{m.feedbacks_description()}</Card.Description>
            </div>
            <div class="relative">
              <Search
                class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"
              />
              <Input
                type="text"
                placeholder="Search feedbacks..."
                bind:value={searchQuery}
                class="pl-10"
              />
            </div>
            <div class="flex items-center gap-2">
              <Toggle
                pressed={showTripOnly}
                onPressedChange={(pressed: boolean) => {
                  showTripOnly = pressed;
                  if (pressed) showApplicationOnly = false;
                }}
                variant="outline"
                size="sm"
              >
                {m.feedback_filter_trip()}
              </Toggle>
              <Toggle
                pressed={showApplicationOnly}
                onPressedChange={(pressed: boolean) => {
                  showApplicationOnly = pressed;
                  if (pressed) showTripOnly = false;
                }}
                variant="outline"
                size="sm"
              >
                {m.feedback_filter_application()}
              </Toggle>
              <Toggle
                pressed={showNewOnly}
                onPressedChange={(pressed: boolean) => (showNewOnly = pressed)}
                variant="outline"
                size="sm"
                class="data-[state=on]:bg-red-500"
              >
                {m.feedback_filter_new()}
              </Toggle>
            </div>
          </div>
        </Card.Header>
        <Card.Content class="h-[calc(100vh-262px)] overflow-y-auto">
          {#if loading}
            <div class="flex justify-center py-8">
              <span class="loading loading-spinner loading-lg"></span>
            </div>
          {:else}
            <div class="space-y-2">
              {#each filteredFeedbacks as feedback (feedback.id)}
                {@const user = usersMap.get(feedback.uid)}
                <div
                  class="flex items-start justify-between rounded-lg border p-3 hover:bg-muted/50 cursor-pointer {selectedFeedbackId ===
                  feedback.id
                    ? 'bg-muted'
                    : ''}"
                  onclick={() => handleFeedbackClick(feedback)}
                  onkeydown={(e) =>
                    e.key === "Enter" && handleFeedbackClick(feedback)}
                  role="button"
                  tabindex="0"
                >
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center gap-2 mb-1">
                      <p class="font-medium truncate">
                        {user ? getDisplayName(user) : m.unknown_user()}
                      </p>
                      <Badge
                        class={getStatusBadgeColor(feedback.status) +
                          " text-white"}
                      >
                        {getStatusDisplayName(feedback.status)}
                      </Badge>
                    </div>
                    <div
                      class="flex items-center gap-2 text-sm text-muted-foreground"
                    >
                      {#if feedback.type === FeedbackType.TRIP}
                        <span class="inline-flex items-center">
                          {#if feedback.rating}
                            {#each Array(5) as _, i}
                              <Star
                                class="w-3 h-3 {i < feedback.rating
                                  ? 'fill-yellow-400 text-yellow-400'
                                  : 'text-gray-300'}"
                              />
                            {/each}
                          {/if}
                          <span class="ml-1">{m.feedback_type_trip()}</span>
                        </span>
                      {:else}
                        <span>{m.feedback_type_application()}</span>
                      {/if}
                    </div>
                    <p class="text-sm truncate mt-1">
                      {feedback.message || m.no_message()}
                    </p>
                  </div>
                  <div class="text-sm text-muted-foreground ml-2">
                    {formatShortDateTime(feedback.createdAt)}
                  </div>
                </div>
              {/each}
              {#if !filteredFeedbacks.length}
                <p class="text-sm text-muted-foreground text-center py-4">
                  {searchQuery
                    ? "No feedbacks found matching your search"
                    : m.no_feedbacks_found()}
                </p>
              {/if}
            </div>
          {/if}
        </Card.Content>
      </Card.Root>
    </div>

    <!-- Right section - Content -->
    <div class="flex-1">
      {@render children()}
    </div>
  </div>
</div>
