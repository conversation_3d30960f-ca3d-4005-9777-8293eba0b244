<script lang="ts">
  import * as Card from "$lib/components/ui/card/index.js";
  import * as m from "$lib/paraglide/messages";
</script>

<Card.Root class="h-full">
  <Card.Header>
    <Card.Title>{m.select_feedback()}</Card.Title>
    <Card.Description>
      {m.select_feedback_description()}
    </Card.Description>
  </Card.Header>
  <Card.Content>
    <div class="flex items-center justify-center h-[calc(100vh-260px)]">
      <div class="text-center">
        <svg
          class="mx-auto h-12 w-12 text-gray-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
          />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">
          {m.no_feedback_selected()}
        </h3>
        <p class="mt-1 text-sm text-gray-500">
          {m.select_feedback_from_list()}
        </p>
      </div>
    </div>
  </Card.Content>
</Card.Root>
