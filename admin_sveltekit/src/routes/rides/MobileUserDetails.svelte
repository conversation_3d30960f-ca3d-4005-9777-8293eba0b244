<script lang="ts">
    import type { MobileUser } from "$lib/stores/mobile_users.svelte";
    import * as Card from "$lib/components/ui/card/index.js";
    import { Button } from "$lib/components/ui/button";
    import {
        getDisplayName,
        getInitials,
        updateUser,
    } from "$lib/stores/mobile_users.svelte";
    import { getFirebaseUser } from "$lib/stores/auth.svelte";
    import { deleteField } from "firebase/firestore";
    import { callFunction } from "$lib/firebase.client";
    import Spinner from "$lib/components/ui/Spinner.svelte";
    import { onMount } from "svelte";
    import { toast } from "svelte-sonner";
    import ConfirmationDialog from "$lib/components/ui/ConfirmationDialog.svelte";
    import DriverTagManager from "$lib/components/DriverTagManager.svelte";
    import {
        init as initDriverTags,
        destroy as destroyDriverTags,
    } from "$lib/stores/driver_tags.svelte";
    import {
        getDriverRating,
        init as initTenantStates,
        destroy as destroyTenantStates,
    } from "$lib/stores/mobile_user_tenant_states.svelte";
    import { Star } from "lucide-svelte";
    import * as m from "$lib/paraglide/messages";
    import { localizedGoto } from "$lib/utils";
    import { tenantStore } from "$lib/stores/tenant.svelte";

    let { user } = $props<{
        user: MobileUser | undefined;
    }>();

    let isDisconnecting = $state(false);
    let showDisconnectConfirmation = $state(false);

    const firebaseUser = $derived(getFirebaseUser());
    
    // Get current tenant ID for service status
    const currentTenantId = $derived(tenantStore.currentId);
    
    // Helper to get service active status for current tenant
    const isServiceActive = $derived(() => {
        if (!user || user.primaryUserType !== 1) return false;
        return user.isServiceActiveByTenant?.[currentTenantId] ?? false;
    });

    // Initialize driver tags store for tag management
    onMount(() => {
        initDriverTags();
        initTenantStates(); // 🔑 Initialize tenant states for ratings
        return () => {
            destroyDriverTags();
            destroyTenantStates(); // Clean up tenant states
        };
    });

    function formatDate(date: Date | undefined): string {
        if (!date) return m.mobileUserDetails_never();
        return new Intl.DateTimeFormat("fr-FR", {
            day: "2-digit",
            month: "2-digit",
            year: "numeric",
            hour: "2-digit",
            minute: "2-digit",
        }).format(date);
    }

    async function toggleDriverConfirmation() {
        if (!user || user.primaryUserType !== 1) return;

        const adminUser = firebaseUser;
        if (typeof adminUser !== "object") return;

        await updateUser(user.uid, {
            isDriverConfirmed: user.isDriverConfirmed
                ? deleteField()
                : adminUser.uid,
        });
    }

    function confirmDisconnect() {
        showDisconnectConfirmation = true;
    }

    async function disconnectFromTrip() {
        if (!user || !user.occupiedByTripId) return;

        try {
            isDisconnecting = true;
            showDisconnectConfirmation = false;

            await callFunction("disconnectDriverFromTrip", {
                driverUid: user.uid,
            });

            toast.success(m.mobileUserDetails_disconnectSuccessToast());
        } catch (error) {
            console.error("Error disconnecting driver:", error);
            toast.error(m.mobileUserDetails_disconnectErrorToast());
        } finally {
            isDisconnecting = false;
        }
    }
</script>

{#if !user}
    <Card.Root>
        <Card.Header>
            <Card.Title>{m.mobileUserDetails_userNotFoundTitle()}</Card.Title>
            <Card.Description>
                {m.mobileUserDetails_userNotFoundDescription()}
            </Card.Description>
        </Card.Header>
        <Card.Content>
            <p class="text-center text-muted-foreground">
                {m.mobileUserDetails_userNotFoundInstructions()}
            </p>
        </Card.Content>
    </Card.Root>
{:else}
    <div class="space-y-4">
        <!-- User Profile Section -->
        <Card.Root>
            <Card.Header>
                <Card.Title>{m.mobileUserDetails_profileCardTitle()}</Card.Title
                >
            </Card.Header>
            <Card.Content>
                <div class="flex items-start gap-4">
                    <!-- Avatar -->
                    <div class="flex-shrink-0">
                        {#if user.photoURL}
                            <img
                                src={user.photoURL}
                                alt={getDisplayName(user)}
                                class="w-16 h-16 rounded-full"
                            />
                        {:else}
                            <div
                                class="w-16 h-16 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xl"
                            >
                                {getInitials(getDisplayName(user))}
                            </div>
                        {/if}
                    </div>

                    <!-- User Info -->
                    <div class="flex-grow space-y-4">
                        <div>
                            <h3 class="text-lg font-semibold">
                                {getDisplayName(user)}
                            </h3>
                            <p class="text-sm text-muted-foreground">
                                {user.email ||
                                    m.mobileUserDetails_noEmailProvided()}
                            </p>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm font-medium">
                                    {m.mobileUserDetails_phoneNumberLabel()}
                                </p>
                                <p class="text-sm text-muted-foreground">
                                    {user.phoneNumber ||
                                        m.mobileUserDetails_notProvided()}
                                </p>
                            </div>
                            <div>
                                <p class="text-sm font-medium">
                                    {m.mobileUserDetails_lastSeenLabel()}
                                </p>
                                <p class="text-sm text-muted-foreground">
                                    {formatDate(user.lastSeen)}
                                </p>
                            </div>
                            <div>
                                <p class="text-sm font-medium">
                                    {m.mobileUserDetails_userTypeLabel()}
                                </p>
                                <p class="text-sm text-muted-foreground">
                                    {user.primaryUserType !== undefined
                                        ? user.primaryUserType === 0
                                            ? m.mobileUserDetails_userTypePassenger()
                                            : m.mobileUserDetails_userTypeDriver()
                                        : m.mobileUserDetails_userTypeNotSpecified()}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </Card.Content>
        </Card.Root>

        <!-- Driver Actions Section (for drivers only) -->
        {#if user.primaryUserType === 1}
            <Card.Root>
                <Card.Header>
                    <Card.Title
                        >{m.mobileUserDetails_driverManagementTitle()}</Card.Title
                    >
                    <!-- "Driver Management" -->
                    <Card.Description>
                        {m.mobileUserDetails_driverManagementDescription()}
                        <!-- "Manage driver verification and vehicle assignments" -->
                    </Card.Description>
                </Card.Header>
                <Card.Content>
                    <div class="flex gap-6">
                        <!-- Driver Status Information (Left side - 2/3 width) -->
                        <div class="flex-1">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <p class="text-sm font-medium">
                                        {m.mobileUserDetails_driverVerificationLabel()}
                                    </p>
                                    <p class="text-sm mt-1">
                                        <span
                                            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {user.isDriverConfirmed
                                                ? 'bg-green-100 text-green-800'
                                                : 'bg-yellow-100 text-yellow-800'}"
                                        >
                                            {user.isDriverConfirmed
                                                ? m.mobileUserDetails_driverStatusVerified()
                                                : m.mobileUserDetails_driverStatusPending()}
                                        </span>
                                    </p>
                                </div>
                                
                                <div>
                                    <p class="text-sm font-medium">Service Status</p>
                                    <p class="text-sm mt-1">
                                        <span
                                            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {isServiceActive()
                                                ? 'bg-green-100 text-green-800'
                                                : 'bg-red-100 text-red-800'}"
                                        >
                                            {isServiceActive() ? 'Active' : 'Inactive'}
                                        </span>
                                    </p>
                                </div>
                                
                                {#if user.occupiedByTripId}
                                    <div class="col-span-2">
                                        <p class="text-sm font-medium">
                                            {m.mobileUserDetails_currentTripStatusLabel()}
                                        </p>
                                        <p class="text-sm mt-1">
                                            <span
                                                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                            >
                                                {m.mobileUserDetails_currentTripStatusActive()}
                                            </span>
                                        </p>
                                    </div>
                                {/if}
                            </div>
                        </div>

                        <!-- Action Buttons (Right side - 1/3 width) -->
                        <div class="w-1/3 space-y-2">
                            <Button
                                variant="outline"
                                onclick={() => localizedGoto("/vehicles")}
                                class="w-full"
                            >
                                {m.mobileUserDetails_manageVehiclesButton()}
                                <!-- "Manage Vehicles" -->
                            </Button>

                            {#if user.occupiedByTripId}
                                <Button
                                    variant="destructive"
                                    onclick={confirmDisconnect}
                                    disabled={isDisconnecting}
                                    class="w-full"
                                >
                                    {#if isDisconnecting}
                                        <Spinner className="mr-2 h-4 w-4" />
                                        {m.mobileUserDetails_disconnectingButtonText()}
                                    {:else}
                                        {m.mobileUserDetails_disconnectButtonText()}
                                    {/if}
                                </Button>
                            {/if}

                            <Button
                                variant={user.isDriverConfirmed
                                    ? "destructive"
                                    : "default"}
                                onclick={toggleDriverConfirmation}
                                class="w-full"
                            >
                                {user.isDriverConfirmed
                                    ? m.mobileUserDetails_revokeVerificationButtonText()
                                    : m.mobileUserDetails_verifyDriverButtonText()}
                            </Button>
                        </div>
                    </div>
                </Card.Content>
            </Card.Root>

            <!-- 🌟 Driver Rating Section -->
            {@const rating = getDriverRating(user.uid)}
            <Card.Root>
                <Card.Header>
                    <Card.Title
                        >{m.mobileUserDetails_driverRatingTitle()}</Card.Title
                    >
                    <!-- "Driver Rating" -->
                </Card.Header>
                <Card.Content>
                    {#if rating && rating.totalTrips > 0}
                        <div class="space-y-4">
                            <div class="flex items-center gap-4">
                                <div class="flex items-center gap-1">
                                    {#each Array(5) as _, i}
                                        <Star
                                            class="w-6 h-6 {i <
                                            Math.floor(rating.averageRating)
                                                ? 'fill-yellow-400 text-yellow-400'
                                                : i < rating.averageRating
                                                  ? 'fill-yellow-400/50 text-yellow-400'
                                                  : 'text-gray-300'}"
                                        />
                                    {/each}
                                </div>
                                <span class="text-2xl font-bold">
                                    {rating.averageRating.toFixed(1)}
                                </span>
                                <span class="text-muted-foreground">
                                    ({rating.totalTrips}
                                    {m.mobileUserDetails_tripsLabel()}) <!-- "trips" -->
                                </span>
                            </div>

                            <!-- Rating distribution -->
                            <div class="space-y-2">
                                {#each [5, 4, 3, 2, 1] as stars}
                                    <div class="flex items-center gap-2">
                                        <span
                                            class="text-sm w-4 text-muted-foreground"
                                            >{stars}</span
                                        >
                                        <Star class="w-4 h-4 text-yellow-400" />
                                        <div
                                            class="flex-1 bg-gray-200 rounded-full h-2"
                                        >
                                            <div
                                                class="bg-yellow-400 h-2 rounded-full"
                                                style="width: {((rating
                                                    .distribution[stars] || 0) /
                                                    rating.totalTrips) *
                                                    100}%"
                                            ></div>
                                        </div>
                                        <span
                                            class="text-sm text-muted-foreground w-12 text-right"
                                        >
                                            {rating.distribution[stars] || 0}
                                        </span>
                                    </div>
                                {/each}
                            </div>

                            {#if rating.lastRatingDate}
                                <p class="text-sm text-muted-foreground">
                                    Last rated: {formatDate(
                                        rating.lastRatingDate,
                                    )}
                                </p>
                            {/if}
                        </div>
                    {:else}
                        <p class="text-muted-foreground">
                            {m.mobileUserDetails_noRatingsYet()}
                        </p>
                        <!-- "No ratings yet" -->
                    {/if}
                </Card.Content>
            </Card.Root>

            <!-- Driver Tags Management Section -->
            <DriverTagManager
                driverUid={user.uid}
                currentTags={user.driverTags || []}
            />
        {/if}
    </div>
{/if}

<ConfirmationDialog
    open={showDisconnectConfirmation}
    title={m.mobileUserDetails_disconnectConfirmTitle()}
    description={m.mobileUserDetails_disconnectConfirmDescription()}
    onConfirm={disconnectFromTrip}
    onCancel={() => (showDisconnectConfirmation = false)}
/>
