<script module lang="ts">
    import { type TripLocation, getTrips } from "$lib/stores/trips.svelte";

    let mapContainer: HTMLElement | null = null;
    let mapsLoaded = false;

    declare global {
        interface Window {
            initMap: () => Promise<void>;
        }
    }
</script>

<script lang="ts">
    import { onMount, onDestroy } from "svelte";
    import { env } from "$env/dynamic/public";
    import { page } from "$app/state";
    import {
        getMapStore,
        setMap,
        removeMarker,
        centerMap,
        addMarker,
        smoothCenterMap,
    } from "$lib/stores/map.svelte";
    import {
        getFollowedTripId,
        stopFollowingTrip,
    } from "$lib/stores/trips.svelte";
    import {
        init as initMobileUsers,
        getUsersMap,
    } from "$lib/stores/mobile_users.svelte";
    /// <reference types="@types/google.maps" />

    let { children } = $props();

    let mapDiv: HTMLElement;
    const map = $derived(getMapStore());

    // Check if we're on a trips route
    const isOnTripsRoute = $derived(page.url.pathname.includes("/rides/trips"));

    const loadGoogleMapsScript = async () => {
        if (mapsLoaded) {
            // If maps already loaded, just restore the map
            if (map.map && mapContainer && mapDiv) {
                mapDiv.appendChild(mapContainer);
                google.maps.event.trigger(map.map, "resize");
            }
            return;
        }

        window.initMap = async () => {
            let attempts = 0;
            const maxAttempts = 3;
            const delay = 2000; // 2 seconds

            while (attempts < maxAttempts) {
                try {
                    const { Map } = (await google.maps.importLibrary(
                        "maps",
                    )) as google.maps.MapsLibrary;
                    const { PlacesService } = (await google.maps.importLibrary(
                        "places",
                    )) as google.maps.PlacesLibrary;
                    const { DrawingManager } = (await google.maps.importLibrary(
                        "drawing",
                    )) as google.maps.DrawingLibrary;
                    const { AdvancedMarkerElement } = (await google.maps.importLibrary(
                        "marker",
                    )) as google.maps.MarkerLibrary;

                    if (!map.map && mapDiv) {
                        const mapInstance = new Map(mapDiv, {
                            center: { lat: -18.8792, lng: 47.5079 }, // Antananarivo center
                            zoom: 12,
                            mapTypeId: "roadmap",
                            fullscreenControl: false,
                            mapTypeControl: true,
                            streetViewControl: false,
                            mapId: import.meta.env.PROD ? env.PUBLIC_GOOGLE_MAPS_ID_PROD : env.PUBLIC_GOOGLE_MAPS_ID_DEV, // Required for AdvancedMarkerElement
                        });
                        setMap(mapInstance);
                        mapContainer = mapDiv;
                    } else if (mapContainer && mapDiv && map.map) {
                        // Restore existing map instance
                        mapDiv.appendChild(mapContainer);
                        google.maps.event.trigger(map.map, "resize");
                    }
                    mapsLoaded = true;
                    break;
                } catch (error) {
                    attempts++;
                    if (attempts === maxAttempts) {
                        throw error;
                    }
                    await new Promise((resolve) => setTimeout(resolve, delay));
                }
            }
        };

        // Check if Google Maps script is already loaded
        const existingScript = document.querySelector('script[src*="maps.googleapis.com"]');
        if (existingScript) {
            // Script already exists, just initialize if Google Maps is available
            if (typeof google !== 'undefined' && google.maps) {
                window.initMap();
            }
            return;
        }

        const script = document.createElement("script");
        script.src = `https://maps.googleapis.com/maps/api/js?key=${env.PUBLIC_GOOGLE_MAPS_API_KEY}&callback=initMap&v=weekly&loading=async`;
        script.async = true;
        script.defer = true;
        document.head.appendChild(script);
    };

    onMount(async () => {
        try {
            await loadGoogleMapsScript();
            // Initialize mobile users store if not already initialized
            await initMobileUsers();
        } catch (error) {
            console.error("Error loading Google Maps:", error);
        }
    });

    onDestroy(() => {
        // Store the map container but don't destroy the map instance
        if (mapContainer?.parentNode) {
            mapContainer.parentNode.removeChild(mapContainer);
        }

        // Clean up any trip-driver marker
        removeMarker(`trip-driver`);

        // Stop following a trip
        stopFollowingTrip();
    });

    // Trip following logic - only get followed trip if on trips route
    let followedTrip = $derived(
        isOnTripsRoute
            ? getTrips().find((trip) => trip.id === getFollowedTripId())
            : null,
    );

    // Get the driver from mobile_users store
    let followedDriver = $derived(
        followedTrip?.uidChosenDriver
            ? getUsersMap().get(followedTrip.uidChosenDriver)
            : null,
    );

    // Get driver position from the driver object
    let driverPosition = $derived(
        followedDriver && followedDriver.lat && followedDriver.lon
            ? ({
                  lat: followedDriver.lat,
                  lon: followedDriver.lon,
              } as TripLocation)
            : null,
    );

    let previousDriverPosition: TripLocation | null = null;

    // Effect to stop following when leaving trips route
    $effect(() => {
        if (!isOnTripsRoute && getFollowedTripId()) {
            stopFollowingTrip();
            removeMarker(`trip-driver`);
            previousDriverPosition = null;
        }
    });

    // Effect to update trip following marker
    $effect(() => {
        if (followedTrip && followedTrip._isLive && isOnTripsRoute) {
            if (driverPosition) {
                // Only update if position has changed
                if (
                    !previousDriverPosition ||
                    previousDriverPosition.lat !== driverPosition.lat ||
                    previousDriverPosition.lon !== driverPosition.lon
                ) {
                    // Add/update driver marker
                    addMarker(`trip-driver`, driverPosition, {
                        opacity: 1,
                        icon: {
                            url: "http://maps.google.com/mapfiles/ms/icons/purple-dot.png",
                            scaledSize: { width: 40, height: 40 }, // Double the default size (20x20 -> 40x40)
                        },
                    });

                    // Smooth center on new position
                    smoothCenterMap(driverPosition.lat, driverPosition.lon);

                    previousDriverPosition = driverPosition;
                }
            }
        } else {
            // Stop following a trip
            removeMarker(`trip-driver`);
            previousDriverPosition = null;
        }
    });
</script>

<div class="flex h-full gap-4 p-4">
    <!-- Map container -->
    <div
        bind:this={mapDiv}
        class="flex-1 h-full rounded-lg overflow-hidden"
    ></div>

    <!-- Right sidebar -->
    <div class="w-2/5">
        {@render children()}
    </div>
</div>

<style>
    /* Ensure the page takes full height */
    :global(body) {
        height: 100vh;
    }
</style>
