<script lang="ts">
    import type { MobileUser } from "$lib/stores/mobile_users.svelte";
    import type { RatingStats } from "$lib/stores/mobile_user_tenant_states.svelte";
    import { Button } from "$lib/components/ui/button";
    import { Badge } from "$lib/components/ui/badge";
    import {
        getDisplayName,
        getInitials,
    } from "$lib/stores/mobile_users.svelte";
    import { MoreHorizontal, Trash2, Star } from "lucide-svelte";
    import Spinner from "$lib/components/ui/Spinner.svelte";
    import * as m from "$lib/paraglide/messages";
    import DriverTagBadge from "$lib/components/DriverTagBadge.svelte";
    import { tenantStore } from "$lib/stores/tenant.svelte";

    interface Props {
        user: MobileUser;
        isSelected?: boolean;
        onUserClick?: (user: MobileUser) => void;
        onDetailsClick?: (user: MobileUser) => void;
        onDeleteClick?: (user: MobileUser) => void;
        isDeleting?: boolean;
        rating?: RatingStats;
    }

    let {
        user,
        isSelected = false,
        onUserClick = () => {},
        onDetailsClick,
        onDeleteClick,
        isDeleting = false,
        rating,
    }: Props = $props();

    // Get current tenant ID
    const currentTenantId = $derived(tenantStore.currentId);

    // Helper to get service active status for current tenant
    const isServiceActive = $derived(
        user.isServiceActiveByTenant?.[currentTenantId] ?? false,
    );

    function handleDetails(e: Event) {
        e.stopPropagation();
        onDetailsClick?.(user);
    }

    function handleDelete(e: Event) {
        e.stopPropagation();
        onDeleteClick?.(user);
    }
</script>

<div
    class="flex items-center justify-between p-4 rounded-lg border {isSelected
        ? 'bg-accent'
        : 'hover:bg-muted'} cursor-pointer"
    onclick={() => onUserClick(user)}
    onkeydown={(e) => {
        if (e.key === "Enter") onUserClick(user);
    }}
    role="button"
    tabindex="0"
>
    <div class="flex items-center gap-4">
        <div class="relative">
            {#if user.photoURL}
                <img
                    src={user.photoURL}
                    alt={getDisplayName(user)}
                    class="w-10 h-10 rounded-full {user._isOnline
                        ? 'ring-2 ring-green-500 ring-offset-2'
                        : ''}"
                />
            {:else}
                <div
                    class="w-10 h-10 rounded-full bg-primary text-primary-foreground flex items-center justify-center {user._isOnline
                        ? 'ring-2 ring-green-500 ring-offset-2'
                        : ''}"
                >
                    {getInitials(getDisplayName(user))}
                </div>
            {/if}
        </div>
        <div>
            <div class="flex items-center gap-2">
                <h3 class="font-medium">{getDisplayName(user)}</h3>
                {#if user.primaryUserType === 1}
                    {#if rating?.averageRating}
                        <div class="flex items-center gap-1">
                            <Star
                                class="w-4 h-4 fill-yellow-400 text-yellow-400"
                            />
                            <span class="text-sm font-medium">
                                {rating.averageRating.toFixed(1)}
                            </span>
                            {#if rating.totalTrips >= 5}
                                <span class="text-sm text-muted-foreground">
                                    ({rating.totalTrips})
                                </span>
                            {/if}
                        </div>
                    {:else}
                        <Badge variant="secondary" class="text-xs"
                            >{m.mobileUserListItem_newDriverBadge()}</Badge
                        >
                        <!-- "New Driver" -->
                    {/if}
                {/if}
            </div>
            <div class="flex items-center gap-2">
                <p class="text-sm text-muted-foreground">
                    {user.phoneNumber || m.mobileUserListItem_noPhoneNumber()}
                </p>
                {#if user.primaryUserType === 1}
                    <span
                        class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium {isServiceActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'}"
                    >
                        {isServiceActive
                            ? m.mobileUserListItem_driverStatusActive()
                            : m.mobileUserListItem_driverStatusInactive()}
                    </span>
                    <span
                        class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium {user.isDriverConfirmed
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-yellow-100 text-yellow-800'}"
                    >
                        {user.isDriverConfirmed
                            ? m.mobileUserListItem_driverVerified()
                            : m.mobileUserListItem_driverUnverified()}
                    </span>
                {/if}
            </div>
            {#if user.driverTags && user.driverTags.length > 0}
                <div class="flex flex-wrap gap-1 mt-1">
                    {#each user.driverTags.slice(0, 3) as tag}
                        <DriverTagBadge {tag} size="sm" />
                    {/each}
                    {#if user.driverTags.length > 3}
                        <span class="text-xs text-muted-foreground px-2 py-0.5">
                            +{user.driverTags.length - 3} more
                        </span>
                    {/if}
                </div>
            {/if}
        </div>
    </div>
    <div class="flex items-center gap-2">
        {#if onDeleteClick}
            <Button
                variant="ghost"
                size="icon"
                onclick={handleDelete}
                class="text-destructive hover:text-destructive flex items-center justify-center"
                disabled={isDeleting}
            >
                <span class="sr-only"
                    >{m.mobileUserListItem_removeButtonSrLabel()}</span
                >
                {#if isDeleting}
                    <Spinner class="w-4 h-6" />
                {:else}
                    <Trash2 class="h-4 w-4" />
                {/if}
            </Button>
        {/if}
        {#if onDetailsClick}
            <Button variant="ghost" size="icon" onclick={handleDetails}>
                <span class="sr-only"
                    >{m.mobileUserListItem_detailsButtonSrLabel()}</span
                >
                <MoreHorizontal class="h-4 w-4" />
            </Button>
        {/if}
    </div>
</div>
