<script module lang="ts">
  let showOnlineOnly = $state(false);
  let selectedUserId = $state<string | null>(null);
</script>

<script lang="ts">
  import { localizedGoto } from '$lib/utils';
  import * as Card from '$lib/components/ui/card/index.js';
  import { getUsers, init, type MobileUser, getDisplayName, getInitials } from '$lib/stores/mobile_users.svelte';
  import MobileUserListItem from '../MobileUserListItem.svelte';
  import { onMount } from 'svelte';
  import {
    removeMobileUsersMarkers,
    updateMobileUsersMarkers,
    addMarker,
    centerMap,
    smoothCenterMap,
    removeMarker,
  } from '$lib/stores/map.svelte';
  import type { TripLocation } from '$lib/stores/trips.svelte';
  import { Toggle } from '$lib/components/ui/toggle/index.js';
  import * as m from '$lib/paraglide/messages';

  onMount(() => {
    init();

    return () => {
      // Clean up all client markers when component unmounts
      removeMobileUsersMarkers('client-');
    };
  });

  // Get all users from the store
  const allUsers = $derived(getUsers());

  // Filter to show only client users
  let clients = $derived(
    allUsers.filter((user) => {
      if (user.primaryUserType !== 0) return false;
      if (showOnlineOnly && !user._isOnline) return false;
      return true;
    })
  );

  // Get all clients with position data for map display
  let clientsWithPositions = $derived(clients.filter((client) => client.lat && client.lon));

  // Effect to update map markers when clients change or selection changes
  $effect(() => {
    // Create clients with initials for markers
    const clientsWithInitials = clientsWithPositions.map((client) => ({
      ...client,
      initials: getInitials(getDisplayName(client)),
    }));

    updateMobileUsersMarkers('client-', clientsWithInitials, {
      opacity: 0.65, // Default opacity for non-selected clients
    });

    // Update opacity for selected client if they have a position
    if (selectedUserId) {
      const selectedClient = clientsWithPositions.find((c) => c.uid === selectedUserId);
      if (selectedClient) {
        addMarker(
          `client-${selectedUserId}`,
          {
            lat: selectedClient.lat,
            lon: selectedClient.lon,
          } as TripLocation,
          {
            opacity: 1, // Full opacity for selected client
            title: getDisplayName(selectedClient),
            initials: getInitials(getDisplayName(selectedClient)),
          } as any
        );
      }
    }
  });

  // Remove the automatic centering effect - centering is now handled only on click

  function handleDetailsClick(user: MobileUser) {
    localizedGoto(`/rides/clients/${user.uid}/details`);
  }

  function handleUserClick(user: MobileUser) {
    if (selectedUserId === user.uid) {
      // Deselect the user
      selectedUserId = null;
    } else {
      // Select the new user
      selectedUserId = user.uid;

      // Immediately center on the user's position
      if (user.lat && user.lon) {
        smoothCenterMap(user.lat, user.lon);
      }
    }
  }
</script>

<div>
  <Card.Root>
    <Card.Header class="pt-3">
      <div class="flex items-center justify-between">
        <Card.Title>{m.clientsList_pageTitle()}</Card.Title>
        <div class="flex gap-3">
          <Toggle
            pressed={showOnlineOnly}
            onPressedChange={(pressed) => (showOnlineOnly = pressed)}
            variant="outline"
            size="sm"
            class="data-[state=on]:bg-green-500"
          >
            {m.clientsList_onlineToggleLabel()}
          </Toggle>
        </div>
      </div>
      <Card.Description>
        {clients.length === 1
          ? m.clientsList_countListed_one({ count: clients.length })
          : m.clientsList_countListed_other({
              count: clients.length,
            })}
      </Card.Description>
    </Card.Header>
    <Card.Content class="max-h-[75vh] overflow-y-auto">
      <div class="space-y-2">
        {#if clients.length > 0}
          {#each clients as client (client.uid)}
            <MobileUserListItem
              user={client}
              isSelected={selectedUserId === client.uid}
              onUserClick={handleUserClick}
              onDetailsClick={handleDetailsClick}
            />
          {/each}
        {:else}
          <p class="text-sm text-muted-foreground">
            {m.clientsList_noClientsAvailable()}
          </p>
        {/if}
      </div>
    </Card.Content>
  </Card.Root>
</div>
