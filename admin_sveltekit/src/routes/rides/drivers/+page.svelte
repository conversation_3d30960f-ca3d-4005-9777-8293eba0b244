<script module lang="ts">
  let selectedUserId = $state<string | null>(null);
  let showOnlineOnly = $state(false);
  let showInactive = $state(false);
  let showPending = $state(false);
  let selectedFilterTags = $state<string[]>([]);
  let sortBy = $state<'name' | 'rating' | 'distance'>('rating');
</script>

<script lang="ts">
  import { localizedGoto } from '$lib/utils';
  import * as Card from '$lib/components/ui/card/index.js';
  import { getUsers, init, type MobileUser, getDisplayName, getInitials } from '$lib/stores/mobile_users.svelte';
  import { onMount, onDestroy } from 'svelte';
  import MobileUserListItem from '../MobileUserListItem.svelte';
  import { removeMobileUsersMarkers, updateMobileUsersMarkers, addMarker, smoothCenterMap } from '$lib/stores/map.svelte';
  import type { TripLocation } from '$lib/stores/trips.svelte';
  import { Toggle } from '$lib/components/ui/toggle/index.js';
  import * as m from '$lib/paraglide/messages';
  import DriverTagSelector from '$lib/components/DriverTagSelector.svelte';
  import { init as initDriverTags, destroy as destroyDriverTags } from '$lib/stores/driver_tags.svelte';
  import {
    getDriverRating,
    init as initTenantStates,
    destroy as destroyTenantStates,
  } from '$lib/stores/mobile_user_tenant_states.svelte';
  import { Star } from 'lucide-svelte';
  import { Button } from '$lib/components/ui/button';
  import { tenantStore } from '$lib/stores/tenant.svelte';

  onMount(() => {
    init();
    initDriverTags();
    initTenantStates(); // 🔑 Initialize tenant states for ratings

    return () => {
      removeMobileUsersMarkers('driver-');
      destroyDriverTags();
      destroyTenantStates(); // Clean up tenant states on unmount
    };
  });

  // Get all users from the store
  const allUsers = $derived(getUsers());

  // Get current tenant ID
  const currentTenantId = $derived(tenantStore.currentId);

  // Filter to show only driver users based on filters
  let drivers = $derived(
    allUsers.filter((user) => {
      if (user.primaryUserType !== 1) return false;

      if (showOnlineOnly && !user._isOnline) return false;

      // Handle pending (unverified) filter
      if (showPending) {
        // When showing pending drivers, only filter by verification status
        // Don't filter by active/inactive status
        return !user.isDriverConfirmed;
      } else {
        // When not showing pending drivers, show verified drivers
        // and apply the active/inactive filter
        if (!user.isDriverConfirmed) return false;

        // Use service active status for current tenant
        const isServiceActive = user.isServiceActiveByTenant?.[currentTenantId] ?? false;
        if (!showInactive && !isServiceActive) return false;
      }

      // Filter by tags
      if (selectedFilterTags.length > 0) {
        // If filter tags are selected, exclude drivers without any tags
        if (!user.driverTags || user.driverTags.length === 0) {
          return false;
        }

        // Check if driver has any of the selected tags
        const hasMatchingTag = selectedFilterTags.some((tag) => user.driverTags?.includes(tag));
        if (!hasMatchingTag) return false;
      }

      return true;
    })
  );

  // Sort drivers based on selected criteria
  let sortedDrivers = $derived(
    sortBy === 'name'
      ? [...drivers].sort((a, b) => getDisplayName(a).localeCompare(getDisplayName(b)))
      : sortBy === 'rating'
        ? [...drivers].sort((a, b) => {
            const ratingA = getDriverRating(a.uid);
            const ratingB = getDriverRating(b.uid);

            // If both have ratings, sort by average rating (descending)
            if (ratingA && ratingB) {
              return ratingB.averageRating - ratingA.averageRating;
            }

            // Drivers with ratings come first
            if (ratingA && !ratingB) return -1;
            if (!ratingA && ratingB) return 1;

            // If neither has ratings, sort by name
            return getDisplayName(a).localeCompare(getDisplayName(b));
          })
        : [...drivers] // Default case for "distance" or any other value
  );

  // Get all drivers with position data for map display
  let driversWithPositions = $derived(drivers.filter((driver) => driver.lat && driver.lon));

  // Effect to update map markers when drivers change or selection changes
  $effect(() => {
    // Create drivers with initials for markers
    const driversWithInitials = driversWithPositions.map((driver) => ({
      ...driver,
      initials: getInitials(getDisplayName(driver)),
    }));

    updateMobileUsersMarkers('driver-', driversWithInitials, {
      opacity: 0.65, // Default opacity for non-selected drivers
    });

    // Update opacity for selected driver if they have a position
    if (selectedUserId) {
      const selectedDriver = driversWithPositions.find((d) => d.uid === selectedUserId);
      if (selectedDriver) {
        addMarker(
          `driver-${selectedUserId}`,
          {
            lat: selectedDriver.lat,
            lon: selectedDriver.lon,
          } as TripLocation,
          {
            opacity: 1, // Full opacity for selected driver
            title: getDisplayName(selectedDriver),
            initials: getInitials(getDisplayName(selectedDriver)),
          } as any
        );
      }
    }
  });

  // Remove the automatic centering effect - centering is now handled only on click

  function handleDetailsClick(user: MobileUser) {
    localizedGoto(`/rides/drivers/${user.uid}/details`);
  }

  function handleUserClick(user: MobileUser) {
    if (selectedUserId === user.uid) {
      // Deselect the user
      selectedUserId = null;
    } else {
      // Select the new user
      selectedUserId = user.uid;

      // Immediately center on the user's position
      if (user.lat && user.lon) {
        smoothCenterMap(user.lat, user.lon);
      }
    }
  }
</script>

<div>
  <Card.Root>
    <Card.Header class="pt-3">
      <div class="flex items-center justify-between">
        <Card.Title>{m.driversList_pageTitle()}</Card.Title>
        <div class="flex gap-3">
          <Toggle
            pressed={showOnlineOnly}
            onPressedChange={(pressed) => (showOnlineOnly = pressed)}
            variant="outline"
            size="sm"
            class="data-[state=on]:bg-green-500"
          >
            {m.driversList_onlineToggleLabel()}
          </Toggle>
          <Toggle
            pressed={showInactive}
            onPressedChange={(pressed) => (showInactive = pressed)}
            variant="outline"
            size="sm"
            class="data-[state=on]:bg-yellow-500"
            disabled={showPending}
            title={showPending ? m.driversList_inactiveFilterDisabledTooltip() : ''}
          >
            {m.driversList_inactiveToggleLabel()}
          </Toggle>
          <Toggle
            pressed={showPending}
            onPressedChange={(pressed) => (showPending = pressed)}
            variant="outline"
            size="sm"
            class="data-[state=on]:bg-red-500"
          >
            {m.driversList_pendingToggleLabel()}
          </Toggle>
        </div>
      </div>

      <Card.Description>
        {drivers.length === 1
          ? m.driversList_countListed_one({ count: drivers.length })
          : m.driversList_countListed_other({
              count: drivers.length,
            })}
        {#if showPending}
          {' • '}
          <span class="text-red-600">{m.driversList_showingUnverifiedOnly()}</span>
        {:else if showInactive}
          {' • '}
          <span class="text-yellow-600">{m.driversList_includingInactive()}</span>
        {/if}
        {#if showOnlineOnly}
          {' • '}
          <span class="text-green-600">{m.driversList_onlineOnly()}</span>
        {/if}
      </Card.Description>

      <div class="mt-4 space-y-3">
        <DriverTagSelector
          selectedTags={selectedFilterTags}
          onTagsChange={(tags) => (selectedFilterTags = tags)}
          placeholder={m.driversList_filterByTagsPlaceholder()}
          allowCreation={false}
        />
        <!-- "Filter by tags..." -->

        <!-- 🌟 Sort Options -->
        <div class="flex items-center gap-2">
          <span class="text-sm text-muted-foreground">{m.driversList_sortByLabel()}</span>
          <!-- "Sort by:" -->
          <div class="flex gap-2">
            <Button variant={sortBy === 'rating' ? 'default' : 'outline'} size="sm" onclick={() => (sortBy = 'rating')}>
              <Star class="w-4 h-4 mr-1" />
              {m.driversList_sortByRating()}
              <!-- "Rating" -->
            </Button>
            <Button variant={sortBy === 'name' ? 'default' : 'outline'} size="sm" onclick={() => (sortBy = 'name')}>
              {m.driversList_sortByName()}
              <!-- "Name" -->
            </Button>
          </div>
        </div>
      </div>
    </Card.Header>
    <Card.Content class="max-h-[75vh] overflow-y-auto">
      <div class="space-y-2">
        {#if sortedDrivers.length > 0}
          {#each sortedDrivers as driver (driver.uid)}
            <MobileUserListItem
              user={driver}
              isSelected={selectedUserId === driver.uid}
              onUserClick={handleUserClick}
              onDetailsClick={handleDetailsClick}
            />
          {/each}
        {:else}
          <p class="text-sm text-muted-foreground text-center py-8">
            {m.driversList_noDriversAvailable()}
          </p>
        {/if}
      </div>
    </Card.Content>
  </Card.Root>
</div>
