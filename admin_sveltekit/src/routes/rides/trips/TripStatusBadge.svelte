<script lang="ts">
    import { TripStatus } from "$lib/stores/trips.svelte";
    import { ClockIcon } from "lucide-svelte";
    import * as m from "$lib/paraglide/messages";

    let { status } = $props<{
        status: TripStatus;
    }>();

    function getStatusColor(status: TripStatus): string {
        switch (status) {
            case TripStatus.Preparing:
                return "bg-yellow-500/20 text-yellow-700";
            case TripStatus.RequestingDriver:
                return "bg-blue-500/20 text-blue-700";
            case TripStatus.Reserved:
                return "bg-purple-500/20 text-purple-700";
            case TripStatus.DriverApproaching:
                return "bg-cyan-500/20 text-cyan-700";
            case TripStatus.DriverAwaiting:
                return "bg-indigo-500/20 text-indigo-700";
            case TripStatus.InProgress:
                return "bg-green-500/20 text-green-700";
            case TripStatus.Completed:
                return "bg-gray-500/20 text-gray-700";
            case TripStatus.Cancelled:
                return "bg-red-500/20 text-red-700";
            case TripStatus.Paid:
                return "bg-emerald-500/20 text-emerald-700";
            default:
                return "bg-gray-500/20 text-gray-700";
        }
    }

    function getStatusLabel(status: TripStatus): string {
        switch (status) {
            case TripStatus.Preparing:
                return m.tripStatusBadge_preparing();
            case TripStatus.RequestingDriver:
                return m.tripStatusBadge_requestingDriver();
            case TripStatus.Reserved:
                return m.tripStatusBadge_reserved();
            case TripStatus.DriverApproaching:
                return m.tripStatusBadge_driverApproaching();
            case TripStatus.DriverAwaiting:
                return m.tripStatusBadge_driverAwaiting();
            case TripStatus.InProgress:
                return m.tripStatusBadge_inProgress();
            case TripStatus.Completed:
                return m.tripStatusBadge_completed();
            case TripStatus.Cancelled:
                return m.tripStatusBadge_cancelled();
            case TripStatus.Paid:
                return m.tripStatusBadge_paid();
            default:
                return m.tripStatusBadge_unknown();
        }
    }

    let statusColor = $derived(getStatusColor(status));
    let statusLabel = $derived(getStatusLabel(status));
</script>

<span
    class="px-2.5 {status === TripStatus.Reserved &&
        'pl-1'} py-0.5 rounded-full text-xs font-medium flex items-center gap-1 {statusColor}"
>
    {#if status === TripStatus.Reserved}
        <ClockIcon class="w-4 h-4" />
    {/if}
    <span>{statusLabel}</span>
</span>
