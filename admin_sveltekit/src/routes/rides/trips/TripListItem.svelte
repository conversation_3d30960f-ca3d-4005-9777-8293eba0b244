<script lang="ts">
  import type { Trip } from '$lib/stores/trips.svelte';
  import * as Button from '$lib/components/ui/button/index.js';
  import TripStatusBadge from './TripStatusBadge.svelte';
  import { InTakeSource, formatTimeUntilPickup, getPickupTimeStyle } from '$lib/stores/trips.svelte';
  import { CalendarDays } from 'lucide-svelte';
  import {
    getDisplayName,
    getInitials,
    getUsersMap,
    init as initMobileUsers,
    type MobileUser,
  } from '$lib/stores/mobile_users.svelte';
  import { onMount } from 'svelte';
  import { formatShortDateTime } from '$lib/utils/datetime';

  let {
    trip,
    isSelected = false,
    onTripClick = () => {},
    onDetailsClick = () => {},
    onFollowClick,
    following = false,
  } = $props<{
    trip: Trip;
    isSelected?: boolean;
    onTripClick?: (trip: Trip) => void;
    onDetailsClick?: (trip: Trip) => void;
    onFollowClick?: (trip: Trip, following: boolean) => void;
    following?: boolean;
  }>();

  // Initialize mobile users store
  onMount(() => {
    initMobileUsers();
  });

  // Get the actual passenger from the mobile users store
  let actualPassenger: MobileUser | any = $derived(getUsersMap().get(trip.uidPassenger) || trip.passenger);

  function handleDetails(e: Event) {
    e.stopPropagation();
    onDetailsClick(trip);
  }

  function toggleFollow(e: Event) {
    e.stopPropagation();
    onFollowClick(trip, !following);
  }

  function formatDate(date: Date): string {
    return formatShortDateTime(date);
  }
</script>

<div
  class="card p-4 rounded-lg transition-colors hover:bg-muted/50 {isSelected ? 'bg-muted' : ''}"
  onclick={() => onTripClick(trip)}
  onkeydown={(e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onTripClick(trip);
    }
  }}
  role="button"
  tabindex="0"
>
  <div class="flex items-center gap-4">
    {#if actualPassenger.photoURL}
      <img src={actualPassenger.photoURL} alt={getDisplayName(actualPassenger)} class="w-12 h-12 rounded-full object-cover" />
    {:else}
      <div class="w-12 h-12 rounded-full bg-surface-300 flex items-center justify-center text-surface-900">
        {getInitials(getDisplayName(actualPassenger))}
      </div>
    {/if}

    <div class="flex-1">
      <div class="flex items-center justify-between">
        <h3 class="h3">{getDisplayName(actualPassenger)}</h3>
        <div class="flex items-center gap-2">
          <TripStatusBadge status={trip.status} />
          {#if trip.inTakeSource === InTakeSource.Reservation}
            <div
              class="flex items-center justify-center w-6 h-6 bg-purple-100 dark:bg-purple-900/20 rounded-full border border-purple-300 dark:border-purple-600"
            >
              <CalendarDays class="w-3 h-3 text-purple-600 dark:text-purple-400" />
            </div>
          {/if}
        </div>
      </div>
      <div class="flex flex-col gap-1">
        <div class="flex gap-2 items-center text-sm opacity-75">
          <span>{formatDate(trip.createdAt)}</span>
          {#if trip.startLocationName}
            <span>•</span>
            <span>{trip.startLocationName}</span>
          {/if}
          {#if trip.arrivalLocationName}
            <span>→</span>
            <span>{trip.arrivalLocationName}</span>
          {/if}
        </div>
        {#if trip.distanceTotalMeters && trip.distanceTotalMeters > 0}
          <div class="text-sm opacity-75">
            Distance traveled: {(trip.distanceTotalMeters / 1000).toFixed(2)} km
          </div>
        {/if}
        {#if trip.passengerCount && trip.passengerCount > 1}
          <div class="text-sm opacity-75">
            👥 {trip.passengerCount} passengers
          </div>
        {/if}
        {#if trip.inTakeSource === InTakeSource.Reservation && trip._timeTillPickupSec !== undefined}
          <div style={getPickupTimeStyle(trip._timeTillPickupSec, trip.status)} class="font-bold">
            Pickup {formatTimeUntilPickup(trip._timeTillPickupSec, formatDate)}
          </div>
        {/if}
      </div>
    </div>
  </div>

  <div class="mt-3 -mb-1 border-t flex gap-1 justify-end pt-0">
    <Button.Root variant="ghost" size="sm" class="h-7 px-2 text-xs mt-0 rounded-none" onclick={handleDetails}>
      Details
    </Button.Root>
    {#if onFollowClick}
      <Button.Root
        variant={following ? 'default' : 'outline'}
        size="sm"
        class="h-7 px-2 text-xs mt-0 rounded-none"
        onclick={toggleFollow}
      >
        {following ? 'Following' : 'Follow'}
      </Button.Root>
    {/if}
  </div>
</div>

<style>
  :global(.card button) {
    min-height: unset;
  }
</style>
