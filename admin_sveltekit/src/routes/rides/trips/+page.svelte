<script module lang="ts">
  let selectedTripId = $state<string | null>(null);
  let showLiveOnly = $state(false);
  let showReservationsOnly = $state(false);
  let showCompletedOnly = $state(false);
  let useAlgoliaSearch = $state(false);
</script>

<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import { onMount, onDestroy } from 'svelte';
  import { InTakeSource, TripStatus, type Trip, getTrips, init, getLatestTripPosition } from '$lib/stores/trips.svelte';
  import TripListItem from './TripListItem.svelte';
  import { localizedGoto } from '$lib/utils';
  import { clearPolylines, clearMarkers, centerMap, addPolyline, fitBounds, addMarker } from '$lib/stores/map.svelte';
  import { Toggle } from '$lib/components/ui/toggle/index.js';
  import { doFollowTrip, getFollowedTripId, stopFollowingTrip } from '$lib/stores/trips.svelte';
  import * as m from '$lib/paraglide/messages';
  import AlgoliaSearch from '$lib/components/algolia/AlgoliaSearch.svelte';
  import { algoliaClient } from '$lib/algolia';
  import type { Hit } from 'algoliasearch';

  // Helper function to generate status badge HTML
  function getStatusBadgeHTML(status: string): string {
    const statusColorClass =
      {
        preparing: 'bg-yellow-500/20 text-yellow-700',
        requestingDriver: 'bg-blue-500/20 text-blue-700',
        reserved: 'bg-purple-500/20 text-purple-700',
        driverApproaching: 'bg-cyan-500/20 text-cyan-700',
        driverAwaiting: 'bg-indigo-500/20 text-indigo-700',
        inProgress: 'bg-green-500/20 text-green-700',
        completed: 'bg-gray-500/20 text-gray-700',
        cancelled: 'bg-red-500/20 text-red-700',
        paid: 'bg-emerald-500/20 text-emerald-700',
      }[status] || 'bg-gray-500/20 text-gray-700';

    const statusLabels = {
      preparing: m.tripStatusBadge_preparing(),
      requestingDriver: m.tripStatusBadge_requestingDriver(),
      reserved: m.tripStatusBadge_reserved(),
      driverApproaching: m.tripStatusBadge_driverApproaching(),
      driverAwaiting: m.tripStatusBadge_driverAwaiting(),
      inProgress: m.tripStatusBadge_inProgress(),
      completed: m.tripStatusBadge_completed(),
      cancelled: m.tripStatusBadge_cancelled(),
      paid: m.tripStatusBadge_paid(),
    };

    const label = statusLabels[status as keyof typeof statusLabels] || m.tripStatusBadge_unknown?.() || status;
    const isReserved = status === 'reserved';

    return `<span class="px-2.5 ${isReserved ? 'pl-1' : ''} py-0.5 rounded-full text-xs font-medium flex items-center gap-1 ${statusColorClass}">
            ${isReserved ? '<svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>' : ''}
            <span>${label}</span>
        </span>`;
  }

  onMount(() => {
    init();
  });

  onDestroy(() => {
    // Clean up map elements when leaving the page
    clearPolylines();
    clearMarkers();
    // Stop following any trip when leaving the page
    stopFollowingTrip();
  });

  // Filter trips based on applied filters
  let filteredTrips = $derived(
    getTrips().filter((trip) => {
      // Live filter (inProgress)
      if (showLiveOnly && trip.status !== TripStatus.InProgress) return false;

      // Reservations filter
      if (showReservationsOnly && trip.status !== TripStatus.Reserved && trip.inTakeSource !== InTakeSource.Reservation)
        return false;

      // Completed filter
      if (showCompletedOnly && trip.status !== TripStatus.Completed) return false;

      return true;
    })
  );

  function handleDetails(trip: Trip) {
    handleTripClick(trip);

    localizedGoto(`/rides/trips/${trip.id}/details`);
  }

  function handleTripClick(trip: Trip) {
    selectedTripId = trip.id;

    // Clear existing elements on the Map
    clearPolylines();
    clearMarkers();

    // Add route polyline in blue
    if (trip.routeData?.mapsPolylines) {
      addPolyline(`route-${trip.id}`, trip.routeData.mapsPolylines, {
        strokeColor: '#2196F3', // Blue
        strokeWeight: 7,
      });

      // Add markers for start and end positions
      if (trip.startLocation) {
        addMarker(`start-${trip.id}`, trip.startLocation, {
          title: m.tripsList_startMarkerTitle(),
          label: '1',
          color: '#2196F3', // Blue for start position
        } as any);
      }
      if (trip.arrivalLocation) {
        addMarker(`end-${trip.id}`, trip.arrivalLocation, {
          title: m.tripsList_arrivalMarkerTitle(),
          label: '2',
          color: '#4CAF50', // Green for destination
        } as any);
      }

      // Fit map to route bounds
      if (trip.routeData.bounds) {
        const bounds = trip.routeData.bounds;
        fitBounds({
          north: bounds.northeast.lat,
          south: bounds.southwest.lat,
          east: bounds.northeast.lng,
          west: bounds.southwest.lng,
        });
      }
    }

    // Add driver route polyline in orange if it exists
    if (trip.driverRouteData?.mapsPolylines) {
      addPolyline(`driver-route-${trip.id}`, trip.driverRouteData.mapsPolylines, {
        strokeColor: '#FF9800', // Orange
        strokeWeight: 7,
      });

      // Get latest position from mobile_users store
      const latestPosition = getLatestTripPosition(trip.id);
      const driverPosition = latestPosition || trip.driverLocation;

      if (driverPosition && trip.uidChosenDriver) {
        // Use consistent driver marker ID based on driver UID
        addMarker(`driver-${trip.uidChosenDriver}`, driverPosition, {
          title: m.tripsList_driverStartMarkerTitle(),
          label: '0',
          color: '#FF9800', // Orange for driver start position
        } as any);
      }
    }

    // Add final route polyline in green if it exists
    if (trip.finalRouteData?.mapsPolylines) {
      addPolyline(`final-route-${trip.id}`, trip.finalRouteData.mapsPolylines, {
        strokeColor: '#4CAF50', // Green
        strokeWeight: 7,
      });
    }
  }

  function handleFollow(trip: Trip) {
    if (getFollowedTripId() === trip.id) {
      stopFollowingTrip();
      return;
    }

    if (trip._isLive) {
      doFollowTrip(trip.id);
      return;
    }

    stopFollowingTrip();
  }
</script>

<div>
  <Card.Root>
    <Card.Header class="pt-3">
      <div class="flex items-center justify-between">
        <Card.Title>{m.tripsList_title()}</Card.Title>
        <div class="flex items-center gap-2">
          {#if algoliaClient}
            <Toggle
              pressed={useAlgoliaSearch}
              onPressedChange={(pressed) => {
                useAlgoliaSearch = pressed;
                if (!pressed) {
                  // Reset filters when switching off Algolia
                  showLiveOnly = false;
                  showReservationsOnly = false;
                  showCompletedOnly = false;
                }
              }}
              variant="outline"
              size="sm"
              class="data-[state=on]:bg-blue-500"
            >
              {m.tripsList_searchModeToggle()}
            </Toggle>
          {/if}
          <Toggle
            pressed={showLiveOnly}
            onPressedChange={(pressed) => (showLiveOnly = pressed)}
            variant="outline"
            size="sm"
            class="data-[state=on]:bg-green-500"
          >
            {m.tripsList_liveFilter()}
          </Toggle>
          <Toggle
            pressed={showReservationsOnly}
            onPressedChange={(pressed) => (showReservationsOnly = pressed)}
            variant="outline"
            size="sm"
            class="data-[state=on]:bg-purple-500"
          >
            {m.tripsList_reservationsFilter()}
          </Toggle>
          <Toggle
            pressed={showCompletedOnly}
            onPressedChange={(pressed) => (showCompletedOnly = pressed)}
            variant="outline"
            size="sm"
            class="data-[state=on]:bg-gray-500"
          >
            {m.tripsList_completedFilter()}
          </Toggle>
        </div>
      </div>
    </Card.Header>
    <Card.Content class="h-[calc(100vh-140px)] overflow-y-auto">
      {#if useAlgoliaSearch && algoliaClient}
        <AlgoliaSearch
          indexName="trips"
          placeholder={m.tripsList_algoliaSearchPlaceholder()}
          searchableAttributes={[
            // IMPORTANT: Keep in sync with firebase/data-tools/on-demand/algolia-configure-indexes.js
            'startLocationName',
            'arrivalLocationName',
            'passengerName',
            'passengerPhone',
            'driverName',
            'driverPhone',
            'vehiclePlate',
            'vehicleModel',
          ]}
          renderHit={(hit: Hit<any>) => {
            const trip = hit as unknown as Trip;
            const unknownPassenger = m.tripsList_unknownPassenger();
            const notAvailable = m.tripsList_notAvailable();

            // Generate passenger image HTML
            const passengerName = hit.passengerName || unknownPassenger;
            const initials = passengerName
              .split(' ')
              .map((word: string) => word.charAt(0))
              .join('')
              .toUpperCase();
            const passengerPhotoURL = hit.passenger?.photoURL;
            const passengerImageHTML = passengerPhotoURL
              ? `<img src="${passengerPhotoURL}" alt="${passengerName}" class="w-12 h-12 rounded-full object-cover" />`
              : `<div class="w-12 h-12 rounded-full bg-surface-300 flex items-center justify-center text-surface-900">
                                ${initials}
                               </div>`;

            // Format date if available (createdAt is ISO string from Algolia)
            let createdAtFormatted = '';
            if (hit.createdAt) {
              try {
                const date = new Date(hit.createdAt);
                createdAtFormatted =
                  date.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                  }) +
                  ' at ' +
                  date.toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true,
                  });
              } catch (e) {
                createdAtFormatted = '';
              }
            }

            // Distance display
            const distanceHTML =
              hit.distanceTotalMeters && hit.distanceTotalMeters > 0
                ? `<div class="text-sm opacity-75">Distance traveled: ${(hit.distanceTotalMeters / 1000).toFixed(2)} km</div>`
                : '';

            // Passenger count display
            const passengerCountHTML =
              hit.passengerCount && hit.passengerCount > 1
                ? `<div class="text-sm opacity-75">👥 ${hit.passengerCount} passengers</div>`
                : '';

            return `
                            <div class="flex items-center gap-4">
                                ${passengerImageHTML}
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h3 class="h3">${passengerName}</h3>
                                        <div class="flex items-center gap-2">
                                            ${hit.status ? getStatusBadgeHTML(hit.status) : ''}
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-1">
                                        <div class="flex gap-2 items-center text-sm opacity-75">
                                            ${createdAtFormatted ? `<span>${createdAtFormatted}</span>` : ''}
                                            ${hit.startLocationName ? `${createdAtFormatted ? '<span>•</span>' : ''}<span>${hit.startLocationName}</span>` : ''}
                                            ${hit.arrivalLocationName ? `<span>→</span><span>${hit.arrivalLocationName}</span>` : ''}
                                        </div>
                                        ${distanceHTML}
                                        ${passengerCountHTML}
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3 -mb-1 border-t flex gap-1 justify-end pt-0">
                                <button class="h-7 px-2 text-xs mt-0 rounded-none bg-transparent hover:bg-muted text-foreground border-none cursor-pointer">
                                    Details
                                </button>
                            </div>
                        `;
          }}
          onHitClick={(hit: Hit<any>) => {
            // Convert Algolia hit to trip format and handle click
            const tripId = hit.objectID;
            // TODO: Fetch full trip data from Firebase if needed
            localizedGoto(`/rides/trips/${tripId}/details`);
          }}
        />
      {:else}
        <div class="space-y-2 flex flex-col gap-4">
          {#each filteredTrips as trip (trip.id)}
            <TripListItem
              {trip}
              isSelected={selectedTripId === trip.id}
              onTripClick={handleTripClick}
              onDetailsClick={handleDetails}
              onFollowClick={trip._isLive ? handleFollow : undefined}
              following={getFollowedTripId() === trip.id}
            />
          {/each}
          {#if !filteredTrips.length}
            <p class="text-sm text-muted-foreground">
              {m.tripsList_noTripsAvailable()}
            </p>
          {/if}
        </div>
      {/if}
    </Card.Content>
  </Card.Root>
</div>

<style>
  /* Ensure the page takes full height */
  :global(body) {
    height: 100vh;
  }
</style>
