<script lang="ts">
  import type { Trip } from '$lib/stores/trips.svelte';
  import {
    TripStatus,
    formatTimeUntilPickup,
    getPickupTimeStyle,
    confirmPayment,
    deleteTrip,
    cancelTrip,
    updateSkippedDrivers,
    doFollowTrip,
    getFollowedTripId,
    stopFollowingTrip,
    adminOverrideTripCost, // Import admin override function
  } from '$lib/stores/trips.svelte';
  import { getFirebaseUser } from '$lib/stores/auth.svelte';
  import * as Card from '$lib/components/ui/card/index.js';
  import { Button } from '$lib/components/ui/button';
  import { localizedGoto } from '$lib/utils';
  import ConfirmationDialog from '$lib/components/ui/ConfirmationDialog.svelte';
  import { format, formatDistance, formatDistanceToNow } from 'date-fns';
  import { fr, enUS } from 'date-fns/locale';
  import PaymentForm from './PaymentForm.svelte';
  import * as Dialog from '$lib/components/ui/dialog';
  import { toast } from 'svelte-sonner';
  import TripStatusBadge from '../../TripStatusBadge.svelte';
  import {
    getDisplayName,
    getInitials,
    init as initMobileUsers,
    getUsersMap,
    type MobileUser,
  } from '$lib/stores/mobile_users.svelte';
  import { CopyIcon, ClockIcon, EuroIcon, UsersIcon, TrashIcon, BanIcon, CalendarDays } from 'lucide-svelte';
  import type { User } from 'firebase/auth';
  import { onMount, onDestroy } from 'svelte';
  import MobileUserListItem from '../../../MobileUserListItem.svelte';
  import { init as initPayments } from '$lib/stores/payments.svelte';
  import type { Payment } from '$lib/stores/payments.svelte';
  import { collection, query, where, orderBy, onSnapshot } from 'firebase/firestore';
  import { fdb } from '$lib/firebase.client';
  import Spinner from '$lib/components/ui/Spinner.svelte';
  import AriaryCurrency from '$lib/components/ui/AriaryCurrency.svelte';
  import { formatLongDateTime } from '$lib/utils/datetime';
  import { formatCurrency } from '$lib/utils/currency';
  import * as m from '$lib/paraglide/messages';
  import {
    clearPolylines,
    clearMarkers,
    addPolyline,
    addMarker,
    fitBounds,
  } from '$lib/stores/map.svelte';
  import { getLatestTripPosition } from '$lib/stores/trips.svelte';

  let { trip } = $props<{ trip: Trip }>();
  let showDeleteConfirmation = $state(false);
  let showPaymentDialog = $state(false);
  let showCancelConfirmation = $state(false);
  let showCostOverrideDialog = $state(false); // Cost override dialog state
  let showRecalculateDialog = $state(false); // Recalculate dialog state
  let overrideCost = $state<number>(Math.round(trip.costTotal || 0)); // Override cost input
  let overrideReason = $state<string>(''); // Override reason input
  let isOverriding = $state(false); // Override loading state
  let isRecalculating = $state(false); // Recalculate loading state
  let recalculationResult = $state<any>(null); // Store recalculation result
  const isFollowingCurrentTrip = $derived(getFollowedTripId() === trip.id);
  let tripPayments = $state<Payment[]>([]);
  let paymentsUnsubscribe: (() => void) | null = null;

  // Get the actual passenger and driver from the mobile users store
  let actualPassenger = $derived(getUsersMap().get(trip.uidPassenger) || trip.passenger);

  let actualDriver = $derived(trip.uidChosenDriver ? getUsersMap().get(trip.uidChosenDriver) || trip.driver : trip.driver);

  const firebaseUser = $derived(getFirebaseUser());

  // TODO: This should come from your i18n system
  let currentLocale = $state<'fr' | 'en'>('en');

  let locales = $derived({
    fr,
    en: enUS,
  });

  function formatDate(date: Date): string {
    return formatLongDateTime(date, currentLocale);
  }

  // Use state for the relative time so we can update it independently
  let currentTimeAgo = $state('');

  // Update the relative time
  function updateTimeAgo() {
    currentTimeAgo = formatDistanceToNow(trip.createdAt, {
      addSuffix: true,
      locale: locales[currentLocale],
    });
  }

  // Set up the interval when the component is mounted
  onMount(() => {
    // Initialize currentTimeAgo immediately
    updateTimeAgo();

    // Set up interval to update every minute
    const timeInterval = setInterval(updateTimeAgo, 60000);

    initMobileUsers();

    // Initialize payments store
    initPayments();

    // Set up real-time listener for payments
    setupPaymentsListener();

    // Display trip on map with initial bounds fitting
    displayTripOnMap(true);

    // Return cleanup function
    return () => {
      clearInterval(timeInterval);
    };
  });

  let hasInitiallyFitBounds = false;

  function displayTripOnMap(shouldFitBounds = false) {
    // Clear existing elements on the Map
    clearPolylines();
    clearMarkers();

    // Add route polyline in blue
    if (trip.routeData?.mapsPolylines) {
      addPolyline(`route-${trip.id}`, trip.routeData.mapsPolylines, {
        strokeColor: "#2196F3", // Blue
        strokeWeight: 7,
      });

      // Add markers for start and end positions
      if (trip.startLocation) {
        addMarker(`start-${trip.id}`, trip.startLocation, {
          title: m.tripsList_startMarkerTitle(),
          label: "1",
          color: "#2196F3", // Blue for start position
        } as any);
      }
      if (trip.arrivalLocation) {
        addMarker(`end-${trip.id}`, trip.arrivalLocation, {
          title: m.tripsList_arrivalMarkerTitle(),
          label: "2",
          color: "#4CAF50", // Green for destination
        } as any);
      }

      // Only fit bounds on initial load or when explicitly requested
      if (shouldFitBounds && trip.routeData.bounds) {
        const bounds = trip.routeData.bounds;
        fitBounds({
          north: bounds.northeast.lat,
          south: bounds.southwest.lat,
          east: bounds.northeast.lng,
          west: bounds.southwest.lng,
        });
        hasInitiallyFitBounds = true;
      }
    }

    // Add driver route polyline in orange if it exists
    if (trip.driverRouteData?.mapsPolylines) {
      addPolyline(
        `driver-route-${trip.id}`,
        trip.driverRouteData.mapsPolylines,
        {
          strokeColor: "#FF9800", // Orange
          strokeWeight: 7,
        },
      );

      // Get latest position from trips store
      const latestPosition = getLatestTripPosition(trip.id);
      const driverPosition = latestPosition || trip.driverLocation;

      if (driverPosition && trip.uidChosenDriver) {
        // Use consistent driver marker ID based on driver UID
        addMarker(`driver-${trip.uidChosenDriver}`, driverPosition, {
          title: m.tripsList_driverStartMarkerTitle(),
          label: "0",
          color: "#FF9800", // Orange for driver start position
        } as any);
      }
    }

    // Add final route polyline in green if it exists
    if (trip.finalRouteData?.mapsPolylines) {
      addPolyline(
        `final-route-${trip.id}`,
        trip.finalRouteData.mapsPolylines,
        {
          strokeColor: "#4CAF50", // Green
          strokeWeight: 7,
        },
      );
    }
  }

  // Re-display trip on map when relevant data changes
  $effect(() => {
    // Dependencies that should trigger map update
    trip.routeData;
    trip.driverRouteData;
    trip.finalRouteData;
    trip.startLocation;
    trip.arrivalLocation;
    trip.driverLocation;
    actualDriver;
    
    // Re-display the trip on map without changing viewport
    displayTripOnMap(false);
  });

  // Set up the payments listener
  function setupPaymentsListener() {
    try {
      // Query Firestore for payments related to this trip
      if (!fdb) return;
      const paymentsRef = collection(fdb, 'payments');
      const q = query(paymentsRef, where('tripId', '==', trip.id), orderBy('createdAt', 'desc'));

      // Set up the listener and store the unsubscribe function
      paymentsUnsubscribe = onSnapshot(
        q,
        (querySnapshot) => {
          // Process the payments
          tripPayments = querySnapshot.docs.map((doc) => {
            const data = doc.data();
            return {
              id: doc.id,
              tripId: data.tripId,
              customerId: data.customerId,
              driverId: data.driverId,
              customerRequestedPaymentMethod: data.customerRequestedPaymentMethod,
              customerRequestedPaymentMethodRemark: data.customerRequestedPaymentMethodRemark,
              finalPaymentMethod: data.finalPaymentMethod,
              finalPaymentMethodRemark: data.finalPaymentMethodRemark,
              amount: data.amount,
              amountDue: data.amountDue,
              discount: data.discount,
              discountReason: data.discountReason,
              remark: data.remark,
              status: data.status,
              createdAt: data.createdAt?.toDate(),
              processedAt: data.processedAt?.toDate(),
              completedAt: data.completedAt?.toDate(),
              processedByUid: data.processedByUid,
              completedByUid: data.completedByUid,
              transactionId: data.transactionId,
              receiptNumber: data.receiptNumber,
              metadata: data.metadata,
              ref: doc.ref,
            };
          });
        },
        (error) => {
          console.error('Error listening to trip payments:', error);
          try {
            toast.error(m.tripDetails_paymentListenerErrorToast());
          } catch (e) {
            // Catch if toast is called before mount
          }
        }
      );
    } catch (error) {
      console.error('Error setting up payments listener:', error);
      try {
        toast.error(m.tripDetails_paymentSetupErrorToast());
      } catch (e) {
        // Catch if toast is called before mount
      }
    }
  }

  // Use onDestroy for cleanup when the component is unmounted
  onDestroy(() => {
    if (paymentsUnsubscribe) {
      paymentsUnsubscribe();
      paymentsUnsubscribe = null;
    }
  });

  // Add loading state variables
  let isDeleting = $state(false);
  let isCancelling = $state(false);

  async function handleDelete() {
    if (!trip) return;

    try {
      isDeleting = true;
      await deleteTrip(trip.id);
      toast.success(m.tripDetails_deleteSuccessToast());
      console.log('Trip deleted successfully, navigating to trips list');
      localizedGoto('/rides/trips');
    } catch (error) {
      console.error('Failed to delete trip:', error);
      toast.error(m.tripDetails_deleteErrorToast());
    } finally {
      isDeleting = false;
    }
  }

  async function handleCancel() {
    if (!trip) return;

    try {
      isCancelling = true;
      const uid = typeof firebaseUser === 'object' ? (firebaseUser as unknown as User).uid : 'admin';
      await cancelTrip(trip.id, uid);
      toast.success(m.tripDetails_cancelSuccessToast());
      showCancelConfirmation = false;
    } catch (error) {
      console.error('Failed to cancel trip:', error);
      toast.error(m.tripDetails_cancelErrorToast());
    } finally {
      isCancelling = false;
    }
  }

  async function handlePaymentSuccess() {
    showPaymentDialog = false;
    toast.success(m.tripDetails_paymentSuccessToast());

    // If the Trip status is Completed, transition to Paid state through the state machine
    if (trip.status === TripStatus.Completed) {
      try {
        await confirmPayment(trip.id);
      } catch (error) {
        console.error('Error confirming payment:', error);
        toast.error(m.tripDetails_confirmPaymentFailedToast());
      }
    }
  }

  function handleToggleFollow() {
    if (!trip) return;

    if (isFollowingCurrentTrip) {
      stopFollowingTrip();
    } else {
      doFollowTrip(trip.id);
    }
  }

  let canDelete = $derived(trip.status === TripStatus.Preparing || trip.status === TripStatus.RequestingDriver);

  let canCancel = $derived(
    trip.status === TripStatus.Reserved ||
      trip.status === TripStatus.DriverApproaching ||
      trip.status === TripStatus.DriverAwaiting ||
      trip.status === TripStatus.InProgress
  );

  let canStart = $derived(
    trip.status === TripStatus.Preparing || trip.status === TripStatus.RequestingDriver || trip.status === TripStatus.Reserved
  );

  let canReceivePayment = $derived(
    trip.status === TripStatus.Completed || trip.status === TripStatus.Paid || trip.status === TripStatus.Cancelled
  );

  let canFollow = $derived(
    trip.status === TripStatus.InProgress ||
      trip.status === TripStatus.DriverApproaching ||
      trip.status === TripStatus.DriverAwaiting
  );

  let skippedDrivers = $derived(
    trip.skippedDriverIds
      ?.map((id: string) => getUsersMap().get(id))
      .filter((driver: MobileUser | undefined): driver is MobileUser => driver != null)
  );

  let deletingDriverIds = $state(new Set<string>());

  async function handleRemoveSkippedDriver(driverId: string) {
    if (!trip || deletingDriverIds.has(driverId)) return;

    // Create a new Set to trigger reactivity
    deletingDriverIds = new Set(deletingDriverIds).add(driverId);
    try {
      await updateSkippedDrivers(trip.id, [driverId], 'remove');
      toast.success(m.tripDetails_removeSkippedDriverSuccessToast());
    } catch (error) {
      console.error('Failed to remove driver from skip list:', error);
      toast.error(m.tripDetails_removeSkippedDriverErrorToast());
    } finally {
      // Create a new Set again when removing
      const newSet = new Set(deletingDriverIds);
      newSet.delete(driverId);
      deletingDriverIds = newSet;
    }
  }

  // Helper to determine if this is a full day reservation
  const isFullDayReservation = $derived(trip.inTakeSource === 'reservation' && trip.reservationType === 'fullDay');

  // Cost override handler
  async function handleCostOverride(event: Event) {
    event.preventDefault();
    if (!trip || isOverriding || !overrideCost || overrideCost < 0) return;

    try {
      isOverriding = true;
      await adminOverrideTripCost(trip.id, overrideCost, overrideReason || undefined);
      toast.success(
        m.tripDetails_costOverrideSuccessToast({
          amount: formatCurrency(overrideCost),
        })
      );
      showCostOverrideDialog = false;
      overrideReason = ''; // Reset form
    } catch (error) {
      console.error('Failed to override trip cost:', error);
      toast.error(m.tripDetails_costOverrideFailedToast());
    } finally {
      isOverriding = false;
    }
  }

  // Recalculate from all logs handler
  async function handleRecalculate() {
    if (!trip || isRecalculating) return;

    try {
      isRecalculating = true;

      // Call the Firebase function to recalculate from all logs
      const { callAdminFunction } = await import('$lib/firebase.client');
      const { tenantStore } = await import('$lib/stores/tenant.svelte');
      const result = await callAdminFunction('recalculateTripFromAllLogs', {
        tripId: trip.id,
        tenantId: tenantStore.currentId,
      });

      recalculationResult = result;

      if (result.differences) {
        const hasSignificantDifferences = result.differences.distanceMeters > 0.01 || result.differences.finalCost > 1;

        if (hasSignificantDifferences) {
          toast.success(
            m.tripDetails_recalculateSuccessToast({
              distance: (result.differences.distanceMeters / 1000).toFixed(2),
              cost: formatCurrency(result.differences.finalCost),
            })
          );
        } else {
          toast.info(m.tripDetails_recalculateNoChangesToast());
        }
      }

      showRecalculateDialog = false;
    } catch (error) {
      console.error('Failed to recalculate trip:', error);
      toast.error(m.tripDetails_recalculateFailedToast());
    } finally {
      isRecalculating = false;
    }
  }
</script>

<Card.Root>
  <Card.Header>
    <div class="flex justify-between items-start">
      <div>
        <Card.Title>{m.tripDetails_title()}</Card.Title>
        <Card.Description>
          {m.tripDetails_creationDate({
            createdAt: formatDate(trip.createdAt),
            timeAgo: currentTimeAgo,
          })}
          <div class="text-muted-foreground text-xs mt-1 flex items-center gap-1">
            {m.tripDetails_tripIdLabel({ tripId: trip.id })}
            <button
              class="hover:text-primary-foreground transition-colors"
              onclick={() => navigator.clipboard.writeText(trip.id)}
            >
              <CopyIcon class="w-4 h-4" />
            </button>
          </div>
        </Card.Description>
      </div>
      <div class="space-x-2">
        {#if canReceivePayment}
          <Button variant="default" onclick={() => (showPaymentDialog = true)}>
            <EuroIcon class="w-4 h-4 mr-1" />
            {m.tripDetails_receivePaymentButton()}
          </Button>
        {/if}
        <div class="flex gap-2">
          {#if canStart}
            <Button variant="secondary" onclick={() => localizedGoto(`/rides/trips/${trip.id}/assign-driver`)}>
              <UsersIcon class="w-4 h-4 mr-1" />
              {m.tripDetails_assignDriverButton()}
            </Button>
          {/if}
          {#if canDelete}
            <Button variant="destructive" onclick={() => (showDeleteConfirmation = true)} disabled={isDeleting}>
              {#if isDeleting}
                <Spinner className="mr-2 h-4 w-4" />
                <span class="opacity-50">
                  {m.tripDetails_deletingButton()}
                </span>
              {:else}
                <TrashIcon class="w-4 h-4 mr-1" />
                {m.tripDetails_deleteButton()}
              {/if}
            </Button>
          {/if}
          {#if canFollow}
            <Button variant={isFollowingCurrentTrip ? 'default' : 'outline'} onclick={handleToggleFollow}>
              {isFollowingCurrentTrip ? m.tripDetails_followingButton() : m.tripDetails_followButton()}
            </Button>
          {/if}
          {#if canCancel}
            <Button variant="destructive" onclick={() => (showCancelConfirmation = true)} disabled={isCancelling}>
              {#if isCancelling}
                <Spinner className="mr-2 h-4 w-4" />
                <span class="opacity-50">
                  {m.tripDetails_cancellingButton()}
                </span>
              {:else}
                <BanIcon class="w-4 h-4 mr-1" />
                {m.tripDetails_cancelButton()}
              {/if}
            </Button>
          {/if}
        </div>
      </div>
    </div>
  </Card.Header>
  <Card.Content class="max-h-[calc(100vh-174px)] overflow-y-auto">
    <div class="space-y-8">
      <div class="grid grid-cols-2 gap-8">
        <div>
          <h4 class="font-medium mb-2">
            {m.tripDetails_passengerLabel()}
          </h4>
          <div class="flex items-center gap-3">
            {#if actualPassenger.photoURL}
              <img
                src={actualPassenger.photoURL}
                alt={getDisplayName(actualPassenger) || 'User avatar'}
                class="w-12 h-12 rounded-full object-cover"
              />
            {:else}
              <div class="w-12 h-12 rounded-full bg-surface-300 flex items-center justify-center">
                {getInitials(getDisplayName(actualPassenger))}
              </div>
            {/if}
            <div>
              <p class="font-medium">
                {getDisplayName(actualPassenger)}
              </p>
              {#if actualPassenger.phoneNumber}
                <p class="text-sm text-muted-foreground">
                  {actualPassenger.phoneNumber}
                </p>
              {/if}
            </div>
          </div>
        </div>

        {#if actualDriver}
          <div>
            <h4 class="font-medium mb-2">
              {m.tripDetails_driverLabel()}
            </h4>
            <div class="flex items-center gap-3">
              {#if actualDriver.photoURL}
                <img
                  src={actualDriver.photoURL}
                  alt={getDisplayName(actualDriver) || 'Driver avatar'}
                  class="w-12 h-12 rounded-full object-cover"
                />
              {:else}
                <div class="w-12 h-12 rounded-full bg-surface-300 flex items-center justify-center">
                  {getInitials(getDisplayName(actualDriver))}
                </div>
              {/if}
              <div>
                <p class="font-medium">
                  {getDisplayName(actualDriver)}
                </p>
                {#if actualDriver.phoneNumber}
                  <p class="text-sm text-muted-foreground">
                    {actualDriver.phoneNumber}
                  </p>
                {/if}
              </div>
            </div>
          </div>
        {/if}
      </div>

      <div>
        <h4 class="font-medium mb-2">
          {m.tripDetails_tripInformationLabel()}
        </h4>
        <div class="space-y-2">
          <div class="flex justify-between">
            <span class="text-muted-foreground">{m.tripDetails_statusLabel()}</span>
            <div class="flex items-center gap-2">
              <TripStatusBadge status={trip.status} />
              {#if trip.inTakeSource === 'reservation'}
                <div
                  class="flex items-center justify-center w-6 h-6 bg-purple-100 dark:bg-purple-900/20 rounded-full border border-purple-300 dark:border-purple-600"
                >
                  <CalendarDays class="w-3 h-3 text-purple-600 dark:text-purple-400" />
                </div>
              {/if}
            </div>
          </div>
          <div class="flex justify-between">
            <span class="text-muted-foreground">
              <UsersIcon class="w-4 h-4 inline mr-1" />
              {m.tripDetails_passengersLabel()}
            </span>
            <span class="font-medium">{trip.passengerCount || 1}</span>
          </div>
          {#if trip.inTakeSource === 'reservation'}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_reservationTypeLabel()}</span>
              <span class="capitalize">{isFullDayReservation ? m.tripDetails_fullDayType() : m.tripDetails_punctualType()}</span>
            </div>
          {/if}
          {#if isFullDayReservation && trip.fullDayPriceType}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_pricingOptionLabel()}</span>
              <span class="flex items-center">
                <EuroIcon class="w-4 h-4 mr-1" />
                {trip.fullDayPriceType === 'fixed' ? m.tripDetails_fixedPriceOption() : m.tripDetails_gasExcludedPriceOption()}
              </span>
            </div>
          {/if}
          {#if trip.startLocationName}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_fromLabel()}</span>
              <span>{trip.startLocationName}</span>
            </div>
          {/if}
          {#if trip.arrivalLocationName && !isFullDayReservation}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_toLabel()}</span>
              <span>{trip.arrivalLocationName}</span>
            </div>
          {/if}
          {#if trip.distanceTotalMeters && trip.distanceTotalMeters > 0}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_distanceTraveledLabel()}</span>
              <span>{(trip.distanceTotalMeters / 1000).toFixed(2)} km</span>
            </div>
          {/if}
          {#if trip.routeData?.distance && !isFullDayReservation}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_estimatedDistanceLabel()}</span>
              <span>{trip.routeData.distance.toFixed(2)} km</span>
            </div>
          {/if}
          {#if trip.customerRequestedPaymentMethod}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_requestedPaymentMethodLabel()}</span>
              <span class="capitalize">{trip.customerRequestedPaymentMethod}</span>
            </div>
          {/if}
          {#if trip.distance && !isFullDayReservation}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_distanceLabel()}</span>
              <span>{trip.distance.toFixed(2)} km</span>
            </div>
          {/if}
          {#if trip.routeData?.durationSec && !isFullDayReservation}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_estimatedDurationLabel()}</span>
              <span>{Math.round(trip.routeData.durationSec / 60)} min</span>
            </div>
          {/if}
          {#if trip.inTakeSource}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_intakeSourceLabel()}</span>
              <span>{trip.inTakeSource}</span>
            </div>
          {/if}
          {#if trip.pickupTime}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_pickupTimeLabel()}</span>
              <div class="flex items-center gap-1">
                <ClockIcon class="w-4 h-4 mr-1" />
                <div class="flex flex-col items-end">
                  <span>{formatDate(trip.pickupTime)}</span>
                  {#if trip._timeTillPickupSec !== undefined}
                    <span style={getPickupTimeStyle(trip._timeTillPickupSec, trip.status)} class="text-xs">
                      {formatTimeUntilPickup(trip._timeTillPickupSec, formatDate)}
                    </span>
                  {:else}
                    <span class="text-xs text-muted-foreground">
                      ({formatDistanceToNow(trip.pickupTime, {
                        addSuffix: true,
                        locale: locales[currentLocale],
                      })})
                    </span>
                  {/if}
                </div>
              </div>
            </div>
          {/if}
        </div>
      </div>

      <div>
        <h4 class="font-medium mb-2">
          {m.tripDetails_tripTimelineLabel()}
        </h4>
        <div class="space-y-2">
          {#if trip.driverAwaitingTime}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_driverArrivedAwaitingLabel()}</span>
              <span class="text-right">
                {formatDate(trip.driverAwaitingTime)}
                <div class="text-muted-foreground text-xs">
                  {#if trip.driverStartTime}
                    ({formatDistance(trip.driverStartTime, trip.driverAwaitingTime, {
                      addSuffix: false,
                      locale: locales[currentLocale],
                    })})
                  {/if}
                </div>
              </span>
            </div>
          {/if}
          {#if trip.passengerStartTime}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_passengerStartTimeLabel()}</span>
              <span>{formatDate(trip.passengerStartTime)}</span>
            </div>
          {/if}
          {#if trip.driverStartTime}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_driverStartTimeLabel()}</span>
              <span>{formatDate(trip.driverStartTime)}</span>
            </div>
          {/if}
          {#if trip.completedAt}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_completedLabel()}</span>
              <span>{formatDate(trip.completedAt)}</span>
            </div>
          {/if}
          {#if trip.cancelledAt}
            <div class="flex justify-between">
              <span class="text-muted-foreground">{m.tripDetails_cancelledLabel()}</span>
              <span>{formatDate(trip.cancelledAt)}</span>
            </div>
          {/if}
        </div>
      </div>

      {#if trip.costTotal || trip.costPrepaid || trip.costDistance || trip.costDuration || isFullDayReservation || trip.realCost || trip.estimatedCost}
        <div>
          <h4 class="font-medium mb-2">
            {m.tripDetails_costBreakdownLabel()}
          </h4>

          <!-- Enhanced cost display with real vs capped costs -->
          {#if trip.estimatedCost || trip.realCost || trip.costCappedAt10Percent}
            <div class="bg-muted/50 p-3 rounded-lg mb-3 space-y-2">
              {#if trip.estimatedCost}
                <div class="flex justify-between">
                  <span class="text-muted-foreground">{m.tripDetails_initialEstimateLabel()}</span>
                  <span class="font-medium"><AriaryCurrency amount={trip.estimatedCost} /></span>
                </div>
              {/if}

              {#if trip.realCost}
                <div class="flex justify-between">
                  <span class="text-muted-foreground">{m.tripDetails_realCostLabel()}</span>
                  <span class="font-medium"><AriaryCurrency amount={trip.realCost} /></span>
                </div>
              {/if}

              {#if trip.costCappedAt10Percent}
                <div class="flex justify-between items-center bg-orange-100 dark:bg-orange-900/20 p-2 rounded">
                  <span class="text-orange-700 dark:text-orange-300 text-sm">{m.tripDetails_costCappedLabel()}</span>
                  <span class="text-orange-700 dark:text-orange-300 font-medium">{m.tripDetails_costCappedValue()}</span>
                </div>
              {/if}

              {#if trip.adminOverrideCost}
                <div class="flex justify-between items-center bg-blue-100 dark:bg-blue-900/20 p-2 rounded">
                  <span class="text-blue-700 dark:text-blue-300 text-sm">{m.tripDetails_adminOverrideLabel()}</span>
                  <span class="text-blue-700 dark:text-blue-300 font-medium"
                    ><AriaryCurrency amount={trip.adminOverrideCost} /></span
                  >
                </div>
                {#if trip.adminOverrideReason}
                  <div class="text-xs text-muted-foreground italic">
                    {m.tripDetails_adminOverrideReasonPrefix()}
                    {trip.adminOverrideReason}
                  </div>
                {/if}
              {/if}

              <div class="flex justify-between font-bold text-lg border-t pt-2">
                <span>{m.tripDetails_finalCostLabel()}</span>
                <span class="text-green-600 dark:text-green-400"><AriaryCurrency amount={trip.costTotal || 0} /></span>
              </div>

              <!-- Admin Override Button -->
              {#if trip.status === 'completed' || trip.status === 'paid'}
                <div class="flex justify-end mt-3 gap-2">
                  <Button variant="outline" size="sm" onclick={() => (showCostOverrideDialog = true)}>
                    {m.tripDetails_overridePriceButton()}
                  </Button>
                  <Button variant="outline" size="sm" onclick={() => (showRecalculateDialog = true)}>
                    {m.tripDetails_recalculateButton()}
                  </Button>
                </div>
              {/if}
            </div>
          {/if}

          <div class="space-y-2">
            {#if isFullDayReservation}
              <div class="flex justify-between">
                <span class="text-muted-foreground">{m.tripDetails_basePriceLabel()}</span>
                <span class="flex items-center">
                  <EuroIcon class="w-4 h-4 mr-1" />
                  {trip.fullDayPriceType === 'fixed' ? '75' : '25'}
                </span>
              </div>

              {#if trip.fullDayPriceType === 'gasExcluded' && trip.costDistance}
                <div class="flex justify-between">
                  <span class="text-muted-foreground">{m.tripDetails_distanceCostGasLabel()}</span>
                  <span><AriaryCurrency amount={trip.costDistance} /></span>
                </div>
              {/if}
            {/if}

            <!--                         {#if trip.costPrepaid && !isFullDayReservation}
                            <div class="flex justify-between">
                                <span class="text-muted-foreground"
                                    >Prepaid Amount</span
                                >
                                <span><AriaryCurrency amount={trip.costPrepaid} /></span>
                            </div>
                        {/if} -->

            {#if trip.costDistance && !isFullDayReservation}
              <div class="flex justify-between">
                <span class="text-muted-foreground">{m.tripDetails_distanceCostLabel()}</span>
                <span><AriaryCurrency amount={trip.costDistance} /></span>
              </div>
            {/if}

            {#if trip.costDuration && !isFullDayReservation}
              <div class="flex justify-between">
                <span class="text-muted-foreground">{m.tripDetails_durationCostLabel()}</span>
                <span><AriaryCurrency amount={trip.costDuration} /></span>
              </div>
            {/if}

            {#if trip.costTotal && !isFullDayReservation}
              <div class="flex justify-between font-medium">
                <span class="text-muted-foreground">{m.tripDetails_totalCostLabel()}</span>
                <span><AriaryCurrency amount={trip.costTotal} /></span>
              </div>
            {/if}

            {#if isFullDayReservation && trip.fullDayPriceType === 'gasExcluded' && trip.costDistance}
              <div class="flex justify-between font-medium">
                <span class="text-muted-foreground">{m.tripDetails_totalCostLabel()}</span>
                <span class="flex items-center">
                  <EuroIcon class="w-4 h-4 mr-1" />
                  25 + <AriaryCurrency amount={trip.costDistance} />
                </span>
              </div>
            {/if}

            {#if isFullDayReservation && trip.fullDayPriceType === 'fixed'}
              <div class="flex justify-between font-medium">
                <span class="text-muted-foreground">{m.tripDetails_totalCostLabel()}</span>
                <span class="flex items-center">
                  <EuroIcon class="w-4 h-4 mr-1" />
                  75
                </span>
              </div>
            {/if}
          </div>
        </div>
      {/if}

      {#if tripPayments.length > 0}
        <div>
          <h4 class="font-medium mb-2">
            {m.tripDetails_paymentsReceivedLabel()}
          </h4>
          <div class="space-y-2">
            {#each tripPayments as payment}
              <div
                class="flex justify-between items-center p-2 bg-muted rounded hover:bg-muted/80 cursor-pointer transition-colors"
                onclick={() => localizedGoto(`/finance/payments/${payment.id}`)}
                onkeydown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    localizedGoto(`/finance/payments/${payment.id}`);
                  }
                }}
                tabindex="0"
                role="button"
                aria-label={m.tripDetails_viewPaymentDetailsAriaLabel()}
              >
                <div>
                  <div class="font-medium">
                    <AriaryCurrency amount={payment.amount} />
                    <span class="text-xs capitalize ml-1 text-muted-foreground">
                      ({payment.finalPaymentMethod || m.tripDetails_unknownPaymentMethod()})
                    </span>
                  </div>
                  {#if payment.finalPaymentMethodRemark}
                    <div class="text-xs text-muted-foreground">
                      {payment.finalPaymentMethodRemark}
                    </div>
                  {/if}
                </div>
                <div class="text-right">
                  <div class="text-xs text-muted-foreground">
                    {payment.completedAt ? formatDate(payment.completedAt) : formatDate(payment.createdAt)}
                  </div>
                  <div class="text-xs">
                    <span
                      class="inline-block px-2 py-0.5 rounded-full text-xs capitalize
                                            {payment.status === 'completed'
                        ? 'bg-green-100 text-green-800'
                        : payment.status === 'pending'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-gray-100 text-gray-800'}"
                    >
                      {payment.status}
                    </span>
                  </div>
                </div>
              </div>
            {/each}
          </div>
        </div>
      {/if}

      {#if skippedDrivers && skippedDrivers.length > 0}
        <div>
          <h4 class="font-medium mb-2">
            {m.tripDetails_skippedDriversLabel()}
          </h4>
          <div class="space-y-2">
            {#each skippedDrivers as driver}
              <MobileUserListItem
                user={driver}
                onDeleteClick={() => handleRemoveSkippedDriver(driver.uid)}
                isDeleting={deletingDriverIds.has(driver.uid)}
              />
            {/each}
          </div>
        </div>
      {/if}
    </div>
  </Card.Content>
</Card.Root>

<!-- Cost Override Dialog -->
<Dialog.Root open={showCostOverrideDialog} onOpenChange={(open) => (showCostOverrideDialog = open)}>
  <Dialog.Content class="max-w-md">
    <Dialog.Header>
      <Dialog.Title>{m.tripDetails_overrideTripCostDialogTitle()}</Dialog.Title>
      <Dialog.Description>
        {m.tripDetails_overrideTripCostDialogDescription()}
      </Dialog.Description>
    </Dialog.Header>
    <form onsubmit={handleCostOverride} class="space-y-4">
      <div>
        <label for="override-cost" class="block text-sm font-medium mb-1">{m.tripDetails_newCostLabel()}</label>
        <input
          id="override-cost"
          type="number"
          min="0"
          step="1"
          bind:value={overrideCost}
          class="w-full px-3 py-2 border rounded-md"
          placeholder={m.tripDetails_newCostPlaceholder()}
          required
        />
      </div>
      <div>
        <label for="override-reason" class="block text-sm font-medium mb-1">{m.tripDetails_reasonLabel()}</label>
        <textarea
          id="override-reason"
          bind:value={overrideReason}
          class="w-full px-3 py-2 border rounded-md"
          placeholder={m.tripDetails_reasonPlaceholder()}
          rows="3"
        ></textarea>
      </div>
      <div class="flex justify-end space-x-2">
        <Button type="button" variant="outline" onclick={() => (showCostOverrideDialog = false)}>
          {m.tripDetails_cancelButton()}
        </Button>
        <Button type="submit" disabled={isOverriding || !overrideCost || overrideCost < 0}>
          {#if isOverriding}
            {m.tripDetails_updatingButton()}
          {:else}
            {m.tripDetails_overrideCostButton()}
          {/if}
        </Button>
      </div>
    </form>
  </Dialog.Content>
</Dialog.Root>

<ConfirmationDialog
  open={showDeleteConfirmation}
  title={m.tripDetails_deleteDialogTitle()}
  description={m.tripDetails_deleteDialogDescription()}
  onConfirm={handleDelete}
  onCancel={() => (showDeleteConfirmation = false)}
/>

<Dialog.Root open={showPaymentDialog} onOpenChange={(open) => (showPaymentDialog = open)}>
  <Dialog.Content class="max-w-xl">
    <Dialog.Header>
      <Dialog.Title>{m.tripDetails_receivePaymentDialogTitle()}</Dialog.Title>
      <Dialog.Description>
        {m.tripDetails_receivePaymentDialogDescription()}
      </Dialog.Description>
    </Dialog.Header>
    <div class="mt-2">
      <PaymentForm {trip} onSuccess={handlePaymentSuccess} />
    </div>
  </Dialog.Content>
</Dialog.Root>

<ConfirmationDialog
  open={showCancelConfirmation}
  title={m.tripDetails_cancelTripDialogTitle()}
  description={m.tripDetails_cancelTripDialogDescription()}
  onConfirm={handleCancel}
  onCancel={() => (showCancelConfirmation = false)}
  loading={isCancelling}
/>

<!-- Recalculate Dialog -->
<Dialog.Root open={showRecalculateDialog} onOpenChange={(open) => (showRecalculateDialog = open)}>
  <Dialog.Content class="max-w-md">
    <Dialog.Header>
      <Dialog.Title>{m.tripDetails_recalculateDialogTitle()}</Dialog.Title>
      <Dialog.Description>
        {m.tripDetails_recalculateDialogDescription()}
      </Dialog.Description>
    </Dialog.Header>
    <div class="space-y-4">
      <div class="bg-yellow-100 dark:bg-yellow-900/20 p-3 rounded-lg">
        <p class="text-sm text-yellow-800 dark:text-yellow-200">
          <strong>{m.tripDetails_recalculateNoteTitle()}</strong>
          {m.tripDetails_recalculateNoteDescription()}
        </p>
        <ul class="list-disc list-inside text-sm text-yellow-800 dark:text-yellow-200 mt-2">
          <li>{m.tripDetails_recalculateNote1()}</li>
          <li>{m.tripDetails_recalculateNote2()}</li>
          <li>{m.tripDetails_recalculateNote3()}</li>
          <li>{m.tripDetails_recalculateNote4()}</li>
        </ul>
      </div>

      {#if recalculationResult && recalculationResult.differences}
        <div class="bg-blue-100 dark:bg-blue-900/20 p-3 rounded-lg">
          <p class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
            {m.tripDetails_lastRecalculationResultsTitle()}
          </p>
          <div class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
            <p>
              {m.tripDetails_distanceDifferenceLabel()}
              {(recalculationResult.differences.distanceMeters / 1000).toFixed(3)} km
            </p>
            <p>
              {m.tripDetails_costDifferenceLabel()}
              <AriaryCurrency amount={recalculationResult.differences.finalCost} />
            </p>
          </div>
        </div>
      {/if}

      <div class="flex justify-end space-x-2">
        <Button type="button" variant="outline" onclick={() => (showRecalculateDialog = false)} disabled={isRecalculating}>
          {m.tripDetails_cancelButton()}
        </Button>
        <Button onclick={handleRecalculate} disabled={isRecalculating}>
          {#if isRecalculating}
            <Spinner className="mr-2 h-4 w-4" />
            {m.tripDetails_recalculatingButton()}
          {:else}
            {m.tripDetails_recalculateNowButton()}
          {/if}
        </Button>
      </div>
    </div>
  </Dialog.Content>
</Dialog.Root>
