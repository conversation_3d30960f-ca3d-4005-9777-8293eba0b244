<script lang="ts">
    import { page } from "$app/state";
    import { getTrips, init, destroy } from "$lib/stores/trips.svelte";
    import TripDetailsComponent from "./TripDetails.svelte";
    import * as Card from "$lib/components/ui/card/index.js";
    import { onMount, onDestroy } from "svelte";
    import * as m from "$lib/paraglide/messages";
    import { localizedGoto } from "$lib/utils";

    let tripId = page.params.tripId;
    let trip = $derived(getTrips().find((t) => t.id === tripId));
    let hasNavigated = false;

    onMount(() => {
        init();
    });

    onDestroy(() => {
        // Don't clean up so that the store can be reused
        // destroy();
    });

    // Navigate away when trip is deleted
    $effect(() => {
        // Only navigate if we had a trip before and now we don't
        // This prevents navigation on initial load when trip hasn't loaded yet
        if (!trip && tripId && getTrips().length > 0 && !hasNavigated) {
            hasNavigated = true;
            // Small delay to ensure any deletion toast has time to show
            setTimeout(() => {
                localizedGoto("/rides/trips");
            }, 100);
        }
    });
</script>

{#if trip}
    <TripDetailsComponent {trip} />
{:else}
    <Card.Root>
        <Card.Header>
            <Card.Title>{m.tripDetails_notFoundTitle()}</Card.Title>
            <Card.Description>
                {m.tripDetails_notFoundDescription({ tripId })}
            </Card.Description>
        </Card.Header>
        <Card.Content>
            <p class="text-sm text-muted-foreground">
                {m.tripDetails_notFoundExplanation()}
            </p>
        </Card.Content>
    </Card.Root>
{/if}
