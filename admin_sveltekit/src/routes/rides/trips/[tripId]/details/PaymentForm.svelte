<script lang="ts" module>
    import { z } from "zod";
    import { PaymentMethod } from "$lib/stores/payments.svelte";
    import * as m from "$lib/paraglide/messages";

    export const paymentSchema = z.object({
        method: z.nativeEnum(PaymentMethod, {
            message: m.paymentForm_paymentMethodRequiredError(),
        }),
        mobileOperator: z
            .enum(["mvola", "airtel", "orange"], {
                message: m.paymentForm_mobileOperatorRequiredError(),
            })
            .optional(),
        amount: z
            .number({
                message: m.paymentForm_amountInvalidError(),
            })
            .min(0, { message: m.paymentForm_amountMinError() }),
        remark: z.string().optional().nullable(),
    });

    export type PaymentSchema = typeof paymentSchema;
</script>

<script lang="ts">
    import type { Trip } from "$lib/stores/trips.svelte";
    import { Button } from "$lib/components/ui/button";
    import * as Form from "$lib/components/ui/form/index.js";
    import { Input } from "$lib/components/ui/input";
    import * as RadioGroup from "$lib/components/ui/radio-group/index.js";
    import { superForm } from "sveltekit-superforms";
    import { zodClient } from "sveltekit-superforms/adapters";
    import { toast } from "svelte-sonner";
    import AriaryCurrency from "$lib/components/ui/AriaryCurrency.svelte";
    import { createPayment } from "$lib/stores/payments.svelte";

    let { trip, onSuccess } = $props<{
        trip: Trip;
        onSuccess: () => void;
    }>();

    // Create the form
    const form = superForm(
        paymentSchema.parse({
            method: PaymentMethod.Cash,
            mobileOperator: undefined,
            amount: Math.round(trip.costTotal ?? 0),
            remark: null,
        }),
        {
            validators: zodClient(paymentSchema as any),
        },
    );

    const { form: formData, submitting } = form;

    let isMobileMethod = $derived($formData.method === PaymentMethod.Mobile);

    // Create a temporary variable for the mobile operator
    let mobileOperator = $state<"mvola" | "airtel" | "orange" | undefined>(
        $formData.mobileOperator,
    );

    // Update the form data when the mobile operator changes
    function updateMobileOperator(value: "mvola" | "airtel" | "orange") {
        mobileOperator = value;
        $formData.mobileOperator = value;
    }

    async function handleSubmit(event: SubmitEvent) {
        event.preventDefault();

        try {
            await createPayment({
                tripId: trip.id,
                customerId: trip.uidPassenger,
                driverId: trip.uidChosenDriver,
                finalMethod: $formData.method,
                finalMethodRemark: $formData.remark ?? undefined,
                amount: $formData.amount,
                metadata:
                    $formData.method === PaymentMethod.Mobile
                        ? { mobileOperator: $formData.mobileOperator }
                        : undefined,
            });
            onSuccess();
        } catch (error) {
            console.error("Failed to create payment:", error);
            toast.error(m.paymentForm_createPaymentFailedToast());
        }
    }
</script>

<form onsubmit={handleSubmit} class="space-y-6">
    <Form.Fieldset {form} name="method" class="space-y-3">
        <Form.Legend>{m.paymentForm_paymentMethodLabel()}</Form.Legend>
        <RadioGroup.Root
            bind:value={$formData.method}
            class="flex flex-col space-y-1"
        >
            <div class="flex items-center space-x-3 space-y-0">
                <Form.Control>
                    {#snippet children({ props })}
                        <RadioGroup.Item
                            value={PaymentMethod.Cash}
                            {...props}
                        />
                        <Form.Label class="font-normal"
                            >{m.paymentForm_cashPaymentLabel()}</Form.Label
                        >
                    {/snippet}
                </Form.Control>
            </div>
            <div class="flex items-center space-x-3 space-y-0">
                <Form.Control>
                    {#snippet children({ props })}
                        <RadioGroup.Item
                            value={PaymentMethod.Mobile}
                            {...props}
                        />
                        <Form.Label class="font-normal"
                            >{m.paymentForm_mobileMoneyLabel()}</Form.Label
                        >
                    {/snippet}
                </Form.Control>
            </div>
        </RadioGroup.Root>
        <Form.FieldErrors />
    </Form.Fieldset>

    {#if isMobileMethod}
        <Form.Fieldset {form} name="mobileOperator" class="space-y-3">
            <Form.Legend>{m.paymentForm_mobileOperatorLabel()}</Form.Legend>
            <RadioGroup.Root
                value={mobileOperator}
                class="flex flex-col space-y-1"
            >
                <div class="flex items-center space-x-3 space-y-0">
                    <Form.Control>
                        {#snippet children({ props })}
                            <RadioGroup.Item
                                value="mvola"
                                {...props}
                                onclick={() => updateMobileOperator("mvola")}
                            />
                            <Form.Label class="font-normal"
                                >{m.paymentForm_mvolaLabel()}</Form.Label
                            >
                        {/snippet}
                    </Form.Control>
                </div>
                <div class="flex items-center space-x-3 space-y-0">
                    <Form.Control>
                        {#snippet children({ props })}
                            <RadioGroup.Item
                                value="airtel"
                                {...props}
                                onclick={() => updateMobileOperator("airtel")}
                            />
                            <Form.Label class="font-normal"
                                >{m.paymentForm_airtelMoneyLabel()}</Form.Label
                            >
                        {/snippet}
                    </Form.Control>
                </div>
                <div class="flex items-center space-x-3 space-y-0">
                    <Form.Control>
                        {#snippet children({ props })}
                            <RadioGroup.Item
                                value="orange"
                                {...props}
                                onclick={() => updateMobileOperator("orange")}
                            />
                            <Form.Label class="font-normal"
                                >{m.paymentForm_orangeMoneyLabel()}</Form.Label
                            >
                        {/snippet}
                    </Form.Control>
                </div>
            </RadioGroup.Root>
            <Form.FieldErrors />
        </Form.Fieldset>
    {/if}

    <Form.Field {form} name="amount">
        <Form.Control>
            {#snippet children({ props })}
                <Form.Label>{m.paymentForm_amountLabel()}</Form.Label>
                <Input
                    {...props}
                    type="number"
                    min="0"
                    bind:value={$formData.amount}
                    placeholder={m.paymentForm_amountPlaceholder()}
                />
                <Form.Description>
                    {m.paymentForm_suggestedAmountLabel()}
                    <AriaryCurrency amount={trip.costTotal ?? 0} />
                </Form.Description>
                <Form.FieldErrors />
            {/snippet}
        </Form.Control>
    </Form.Field>

    <Form.Field {form} name="remark">
        <Form.Control>
            {#snippet children({ props })}
                <Form.Label>{m.paymentForm_remarksLabel()}</Form.Label>
                <Input
                    {...props}
                    bind:value={$formData.remark}
                    placeholder={m.paymentForm_remarksPlaceholder()}
                />
                <Form.FieldErrors />
            {/snippet}
        </Form.Control>
    </Form.Field>

    <div class="flex justify-end space-x-2">
        <Button type="submit" disabled={$submitting}>
            {#if $submitting}
                {m.paymentForm_processingButton()}
            {:else}
                {m.paymentForm_createPaymentButton()}
            {/if}
        </Button>
    </div>
</form>
