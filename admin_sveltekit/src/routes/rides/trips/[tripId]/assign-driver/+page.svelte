<script lang="ts">
  import { page } from '$app/state';
  import * as Card from '$lib/components/ui/card/index.js';
  import { Button } from '$lib/components/ui/button';
  import { getUsers, init as initMobileUsers, type MobileUser } from '$lib/stores/mobile_users.svelte';
  import {
    TripStatus,
    type Trip,
    type TripLocation,
    getTrips,
    init as initTrips,
    adminRequestDriver,
    adminAssignDriver,
  } from '$lib/stores/trips.svelte';
  import {
    fetchAvailableDrivers,
    getAvailableDrivers,
    isLoading as isLoadingDrivers,
  } from '$lib/stores/driver_availability.svelte';
  import { addMarker, centerMap, updateMobileUsersMarkers, removeMobileUsersMarkers } from '$lib/stores/map.svelte';
  import MobileUserListItem from '../../../MobileUserListItem.svelte';
  import { onMount, onDestroy } from 'svelte';
  import * as Dialog from '$lib/components/ui/dialog';
  import { toast } from 'svelte-sonner';
  import Spinner from '$lib/components/ui/Spinner.svelte';
  import * as m from '$lib/paraglide/messages';

  let selectedDriverId = $state<string | null>(null);
  let tripId = page.params.tripId;
  let trip = $state<Trip | null>(null);
  let isLoading = $state(true);
  let showActionDialog = $state(false);
  let selectedDriverForAction = $state<MobileUser | null>(null);
  let hasNavigatedBack = $state(false);
  let isAssigningDriver = $state(false);
  let isRequestingDriver = $state(false);

  // Function to check if a trip is already assigned to a driver
  function isTripAlreadyAssigned(trip: Trip | null): boolean {
    if (!trip) return false;

    // A trip is considered assigned if it has a driver and its status is beyond RequestingDriver
    return (
      !!trip.uidChosenDriver &&
      [
        TripStatus.DriverApproaching,
        TripStatus.DriverAwaiting,
        TripStatus.InProgress,
        TripStatus.Completed,
        TripStatus.Paid,
      ].includes(trip.status)
    );
  }

  // Safe navigation function to prevent multiple back navigations
  function safeNavigateBack() {
    if (!hasNavigatedBack) {
      hasNavigatedBack = true;
      history.back();
    }
  }

  // Get available drivers from enhanced availability system
  let enhancedAvailableDrivers = $derived(getAvailableDrivers());
  let loadingDrivers = $derived(isLoadingDrivers());

  // Map enhanced drivers to mobile users for compatibility
  let availableDrivers = $derived(
    enhancedAvailableDrivers
      .map((driver) => {
        const user = getUsers().find((u) => u.uid === driver.uid);
        return user
          ? {
              ...user,
              // Override with enhanced vehicle info
              vehicleInfo: driver.vehicleInfo,
              maxPassengers: driver.maxPassengers,
            }
          : null;
      })
      .filter((user) => user !== null) as MobileUser[]
  );

  onMount(() => {
    // Initialize stores
    initMobileUsers();
    initTrips();
  });

  onDestroy(() => {
    // Clean up available driver markers when leaving page
    removeMobileUsersMarkers('available-driver-');
  });

  // Fetch available drivers when trip is loaded
  $effect(() => {
    if (trip && !isTripAlreadyAssigned(trip)) {
      const passengerCount = trip.passengerCount || 1;
      fetchAvailableDrivers(passengerCount);
    }
  });

  // Effect to find the trip when trips store updates
  $effect(() => {
    const allTrips = getTrips();
    const foundTrip = allTrips.find((t) => t.id === tripId);
    trip = foundTrip || null;
    isLoading = false;

    // If trip is already assigned, show a toast and navigate back
    if (isTripAlreadyAssigned(trip) && !hasNavigatedBack) {
      toast.info(m.assignDriver_tripAlreadyAssignedToast());
      // Use a shorter delay since we're checking for hasNavigatedBack
      setTimeout(() => safeNavigateBack(), 2500);
    }
  });

  // Effect to update map markers when drivers change
  $effect(() => {
    // Update driver markers
    updateMobileUsersMarkers('available-driver-', availableDrivers, {
      opacity: 0.65,
    });
  });

  async function handleRequestDriver(driver: MobileUser) {
    if (!trip) return;

    // Ensure driver has valid lat and lon properties
    if (!driver.lat || !driver.lon) {
      toast.error(m.assignDriver_driverLocationUnknownToast());
      return;
    }

    const driverLocation = {
      lat: driver.lat,
      lon: driver.lon,
    };

    try {
      isRequestingDriver = true;
      await adminRequestDriver(trip.id, driver, driverLocation);
      toast.success(
        m.assignDriver_requestSuccessToast({
          driverName: driver.displayName || 'driver',
        })
      );
      showActionDialog = false;
      safeNavigateBack();
    } catch (error) {
      console.error('Failed to request driver:', error);
      toast.error(m.assignDriver_requestFailedToast());
    } finally {
      isRequestingDriver = false;
    }
  }

  async function handleAssignDriver(driver: MobileUser) {
    if (!trip) return;

    try {
      isAssigningDriver = true;
      const result = await adminAssignDriver(trip.id, driver);
      showActionDialog = false;

      switch (result) {
        case 0:
          toast.success(
            m.assignDriver_assignSuccessToast({
              driverName: driver.displayName || 'Unknown',
            })
          );
          safeNavigateBack();
          break;
        case 1:
          console.error('Driver not found');
          toast.error(m.assignDriver_driverNotFoundToast());
          break;
        case 2:
          console.error('Driver is already occupied');
          toast.error(m.assignDriver_driverOccupiedToast());
          break;
        default:
          console.error('Unknown error occurred');
          toast.error(m.assignDriver_assignUnknownErrorToast());
      }
    } catch (error) {
      console.error('Failed to assign driver:', error);
      toast.error(m.assignDriver_assignFailedToast());
    } finally {
      isAssigningDriver = false;
    }
  }

  function handleDriverAction(driver: MobileUser) {
    selectedDriverForAction = driver;
    showActionDialog = true;
  }

  // Check if selected driver has enough capacity using actual vehicle data
  let hasCapacityMismatch = $derived(
    selectedDriverForAction && trip ? getDriverActualCapacity(selectedDriverForAction.uid) < (trip.passengerCount || 1) : false
  );

  // Helper function to get driver's actual vehicle capacity
  function getDriverActualCapacity(driverUID: string): number {
    const enhancedDriver = enhancedAvailableDrivers.find((d) => d.uid === driverUID);
    return enhancedDriver?.maxPassengers || 4; // Fallback to 4 only if not found in enhanced system
  }

  function handleSelectDriver(driver: MobileUser) {
    // Reset the opacity of the previously selected marker
    if (selectedDriverId) {
      const previouslySelectedUser = availableDrivers.find((d) => d.uid === selectedDriverId);
      if (previouslySelectedUser && 'lat' in previouslySelectedUser && 'lon' in previouslySelectedUser) {
        addMarker(
          `available-driver-${selectedDriverId}`,
          {
            lat: previouslySelectedUser.lat,
            lon: previouslySelectedUser.lon,
          } as TripLocation,
          { opacity: 0.65 }
        );
      }
    }

    selectedDriverId = driver.uid;

    // Update the opacity of the selected marker
    if (driver.lat && driver.lon) {
      addMarker(`available-driver-${driver.uid}`, { lat: driver.lat, lon: driver.lon } as TripLocation, { opacity: 1 });
    }

    // Center the map on the selected driver
    if (driver.lat && driver.lon) {
      centerMap(driver.lat, driver.lon);
    }
  }

  let canRequest = $derived(
    trip &&
      (trip.status === TripStatus.Preparing || trip.status === TripStatus.RequestingDriver || trip.status === TripStatus.Reserved)
  );
</script>

{#if isLoading || loadingDrivers}
  <Card.Root>
    <Card.Header>
      <Card.Title>{m.assignDriver_loadingTitle()}</Card.Title>
      <Card.Description>
        {m.assignDriver_loadingDescription()}
      </Card.Description>
    </Card.Header>
    <Card.Content class="pb-6">
      <div class="flex justify-center py-4">
        <div class="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    </Card.Content>
  </Card.Root>
{:else if trip && !isTripAlreadyAssigned(trip)}
  <Card.Root>
    <Card.Header>
      <Card.Title>{m.assignDriver_assignDriverTitle()}</Card.Title>
      <Card.Description>
        {m.assignDriver_selectDriverDescription({ tripId: trip.id })}
      </Card.Description>
    </Card.Header>
    <Card.Content class="pb-6">
      {#if availableDrivers.length > 0}
        <div class="space-y-4">
          {#each availableDrivers as driver (driver.uid)}
            {@const driverCapacity = getDriverActualCapacity(driver.uid)}
            {@const passengerCount = trip.passengerCount || 1}
            {@const hasEnoughSeats = driverCapacity >= passengerCount}
            <div class="rounded-lg">
              <MobileUserListItem
                user={driver}
                isSelected={selectedDriverId === driver.uid}
                onUserClick={handleSelectDriver}
                onDetailsClick={handleDriverAction}
              />
              {#if !hasEnoughSeats}
                <div class="ml-16 -mt-2 mb-2 flex items-center gap-2 text-sm text-destructive">
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                  <span
                    >{m.assignDriver_notEnoughSeats({
                      driverCapacity,
                      passengerCount,
                    })}</span
                  >
                </div>
              {/if}
              <!-- Vehicle info will be available through the new vehicle system -->
            </div>
          {/each}
        </div>
      {:else}
        <p class="text-muted-foreground text-sm">
          {m.assignDriver_noAvailableDrivers()}
        </p>
      {/if}
    </Card.Content>
  </Card.Root>
{:else if trip && isTripAlreadyAssigned(trip)}
  <Card.Root>
    <Card.Header>
      <Card.Title>{m.assignDriver_tripAlreadyAssignedTitle()}</Card.Title>
      <Card.Description>
        {m.assignDriver_tripAlreadyAssignedDescription({
          tripId: trip.id,
        })}
      </Card.Description>
    </Card.Header>
    <Card.Content class="pb-6">
      <p class="text-sm text-muted-foreground mb-4">
        {m.assignDriver_tripAlreadyAssignedMessage()}
      </p>
      <Button variant="default" onclick={() => safeNavigateBack()}>
        {m.assignDriver_goBackButton()}
      </Button>
    </Card.Content>
  </Card.Root>
{:else}
  <Card.Root>
    <Card.Header>
      <Card.Title>{m.assignDriver_tripNotFoundTitle()}</Card.Title>
      <Card.Description>
        {m.assignDriver_tripNotFoundDescription({ tripId })}
      </Card.Description>
    </Card.Header>
    <Card.Content class="pb-6">
      <p class="text-sm text-muted-foreground">
        {m.assignDriver_tripNotFoundMessage()}
      </p>
    </Card.Content>
  </Card.Root>
{/if}

<Dialog.Root open={showActionDialog} onOpenChange={(open) => (showActionDialog = open)}>
  <Dialog.Content>
    <Dialog.Header>
      <Dialog.Title>{m.assignDriver_dialogTitle()}</Dialog.Title>
      <Dialog.Description>
        {#if selectedDriverForAction}
          {m.assignDriver_dialogDescription({
            driverName: selectedDriverForAction.displayName || 'this driver',
          })}
          {#if hasCapacityMismatch}
            <div class="mt-2 p-3 bg-destructive/10 text-destructive rounded-md flex items-start gap-2">
              <svg class="h-5 w-5 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
              <div class="text-sm">
                <strong>{m.assignDriver_capacityWarningTitle()}</strong>
                {m.assignDriver_capacityWarningMessage({
                  capacity: getDriverActualCapacity(selectedDriverForAction.uid),
                  passengerCount: trip?.passengerCount || 1,
                })}
              </div>
            </div>
          {/if}
        {/if}
      </Dialog.Description>
    </Dialog.Header>
    <div class="flex justify-end gap-2">
      <Button
        variant="outline"
        onclick={() => {
          showActionDialog = false;
        }}
        disabled={isAssigningDriver || isRequestingDriver}
      >
        {m.assignDriver_cancelButton()}
      </Button>
      {#if canRequest}
        <Button
          variant="secondary"
          onclick={() => handleRequestDriver(selectedDriverForAction as MobileUser)}
          disabled={isAssigningDriver || isRequestingDriver}
        >
          {#if isRequestingDriver}
            <Spinner className="mr-2 h-4 w-4" />
            {m.assignDriver_requestingButton()}
          {:else}
            {m.assignDriver_requestButton()}
          {/if}
        </Button>
      {/if}
      <Button
        variant="default"
        onclick={() => handleAssignDriver(selectedDriverForAction as MobileUser)}
        disabled={isAssigningDriver || isRequestingDriver}
      >
        {#if isAssigningDriver}
          <Spinner className="mr-2 h-4 w-4" />
          {m.assignDriver_assigningButton()}
        {:else}
          {m.assignDriver_assignButton()}
        {/if}
      </Button>
    </div>
  </Dialog.Content>
</Dialog.Root>
