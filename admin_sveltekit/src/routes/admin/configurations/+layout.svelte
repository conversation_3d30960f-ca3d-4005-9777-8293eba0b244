<script lang="ts">
    import * as Card from "$lib/components/ui/card/index.js";
    import {
        getConfigurations,
        init as initConfigurations,
        getTitleOfConfigurationId,
    } from "$lib/stores/configurations.svelte";
    import { onMount, onDestroy } from "svelte";
    import { page } from "$app/state";
    import * as m from "$lib/paraglide/messages";

    const configurations = $derived(getConfigurations());

    let { children } = $props();

    onMount(() => {
        initConfigurations();
    });

    onDestroy(() => {
        // Don't clean up so that the store can be reused
        // cleanup();
    });
</script>

<div class="p-4">
    <div class="flex h-full gap-4">
        <!-- Left section - List -->
        <div class="w-1/3">
            <Card.Root>
                <Card.Header>
                    <Card.Title>{m.adminConfigsLayout_listTitle()}</Card.Title>
                </Card.Header>
                <Card.Content>
                    <div class="space-y-2">
                        {#if configurations.length === 0}
                            <p class="text-sm text-muted-foreground">
                                {m.adminConfigsLayout_noConfigsAvailable()}
                            </p>
                        {:else}
                            {#each configurations as config}
                                <a
                                    href="/admin/configurations/{config.key}"
                                    class="flex items-center justify-between rounded-lg border p-3 hover:bg-muted/50 cursor-pointer {page
                                        .params.key === config.key
                                        ? 'bg-muted'
                                        : ''}"
                                >
                                    <div>
                                        <h4 class="text-sm font-medium">
                                            {config.key === "tripConfiguration"
                                                ? m.adminConfigDetailsComponent_tripConfigTitle()
                                                : config.key === "chat"
                                                  ? m.adminConfigDetailsComponent_chatConfigTitle()
                                                  : config.key ===
                                                      "passengerNotifications"
                                                    ? m.adminConfigDetailsComponent_passengerNotificationsTitle()
                                                    : getTitleOfConfigurationId(
                                                          config.key,
                                                      )}
                                        </h4>
                                    </div>
                                </a>
                            {/each}
                        {/if}
                    </div>
                </Card.Content>
            </Card.Root>
        </div>

        <!-- Right section - Details -->
        <div class="flex-1">
            {@render children?.()}
        </div>
    </div>
</div>

<style>
    /* Ensure the page takes full height */
    :global(body) {
        height: 100vh;
    }
</style>
