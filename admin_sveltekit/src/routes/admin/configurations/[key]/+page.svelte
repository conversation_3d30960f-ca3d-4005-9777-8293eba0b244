<script lang="ts">
    import { page } from "$app/state";
    import { getConfigurations } from "$lib/stores/configurations.svelte";
    import ConfigurationDetails from "./ConfigurationDetails.svelte";
    import * as Card from "$lib/components/ui/card/index.js";
    import * as m from '$lib/paraglide/messages';

    const configurations = $derived(getConfigurations());
    const configuration = $derived(
        configurations.find((c) => c.key === page.params.key) ?? null
    );
</script>

{#if configuration}
    <ConfigurationDetails {configuration} />
{:else}
    <Card.Root>
        <Card.Header>
            <Card.Title>{m.adminConfigDetailPage_notFoundTitle()}</Card.Title>
            <Card.Description>
                {m.adminConfigDetailPage_notFoundDescription()}
            </Card.Description>
        </Card.Header>
    </Card.Root>
{/if}
