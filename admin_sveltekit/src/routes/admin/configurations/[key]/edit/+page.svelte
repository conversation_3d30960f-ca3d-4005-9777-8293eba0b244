<script lang="ts">
    import { page } from "$app/state";
    import { getConfigurations } from "$lib/stores/configurations.svelte";
    import ConfigurationForm from "./ConfigurationForm.svelte";
    import * as Card from "$lib/components/ui/card/index.js";
    import * as m from '$lib/paraglide/messages';

    const configurations = $derived(getConfigurations());
    const configuration = $derived(
        configurations.find((c) => c.key === page.params.key) ?? null
    );
</script>

{#if configuration}
    <ConfigurationForm {configuration} />
{:else}
    <Card.Root>
        <Card.Header>
            <Card.Title>{m.adminConfigDetailPage_notFoundTitle()}</Card.Title>
        </Card.Header>
        <Card.Content>
            <p class="text-sm text-muted-foreground">
                {m.adminConfigDetailPage_notFoundDescription()}
            </p>
        </Card.Content>
    </Card.Root>
{/if}
