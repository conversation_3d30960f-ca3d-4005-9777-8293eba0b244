<script lang="ts">
    import type { ChatConfiguration } from "$lib/stores/configurations.svelte";
    import * as Form from "$lib/components/ui/form";
    import { Input } from "$lib/components/ui/input";
    import { Textarea } from "$lib/components/ui/textarea/index.js";
    import { Toggle } from "$lib/components/ui/toggle";
    import { Checkbox } from "$lib/components/ui/checkbox";
    import type { SuperForm } from "sveltekit-superforms";

    interface Props {
        form: any; // SuperForm type with ChatConfiguration
        formData: ChatConfiguration;
    }

    const { form, formData }: Props = $props();

    const categoryOptions = [
        { value: "supportGeneral", label: "General Support" },
        { value: "supportTrip", label: "Trip Support" },
        { value: "supportPayment", label: "Payment Support" },
        { value: "supportTechnical", label: "Technical Support" },
        { value: "feedbackFollowup", label: "Feedback Follow-up" },
    ];

    const dayOptions = [
        { value: 0, label: "Sunday" },
        { value: 1, label: "Monday" },
        { value: 2, label: "Tuesday" },
        { value: 3, label: "Wednesday" },
        { value: 4, label: "Thursday" },
        { value: 5, label: "Friday" },
        { value: 6, label: "Saturday" },
    ];
</script>

<div class="space-y-6">
    <!-- General Settings -->
    <div class="space-y-4">
        <h3 class="text-lg font-medium">General Settings</h3>

        <Form.Field {form} name="showAdminNamesInChat">
            <Form.Control>
                {#snippet children({ props })}
                    <div class="flex items-center space-x-2">
                        <Toggle
                            {...props}
                            bind:pressed={formData.showAdminNamesInChat}
                        />
                        <Form.Label class="!mt-0"
                            >Show admin names in chat</Form.Label
                        >
                    </div>
                {/snippet}
            </Form.Control>
            <Form.FieldErrors />
        </Form.Field>

        <Form.Field {form} name="enableChatNotifications">
            <Form.Control>
                {#snippet children({ props })}
                    <div class="flex items-center space-x-2">
                        <Toggle
                            {...props}
                            bind:pressed={formData.enableChatNotifications}
                        />
                        <Form.Label class="!mt-0"
                            >Enable chat notifications</Form.Label
                        >
                    </div>
                {/snippet}
            </Form.Control>
            <Form.FieldErrors />
        </Form.Field>

        <Form.Field {form} name="autoArchiveChatAfterDays">
            <Form.Control>
                {#snippet children({ props })}
                    <Form.Label>Auto-archive chats after (days)</Form.Label>
                    <Input
                        type="number"
                        {...props}
                        bind:value={formData.autoArchiveChatAfterDays}
                        min="1"
                        max="365"
                    />
                {/snippet}
            </Form.Control>
            <Form.FieldErrors />
        </Form.Field>
    </div>

    <!-- Chat Categories -->
    <div class="space-y-4">
        <h3 class="text-lg font-medium">Default Chat Categories</h3>

        <Form.Field {form} name="defaultChatCategories">
            <Form.Control>
                {#snippet children({ props })}
                    <div class="space-y-2">
                        {#each categoryOptions as category}
                            <label
                                class="flex items-center space-x-2 cursor-pointer"
                            >
                                <Checkbox
                                    checked={formData.defaultChatCategories.includes(
                                        category.value,
                                    )}
                                    onCheckedChange={(checked) => {
                                        if (checked) {
                                            formData.defaultChatCategories = [
                                                ...formData.defaultChatCategories,
                                                category.value,
                                            ];
                                        } else {
                                            formData.defaultChatCategories =
                                                formData.defaultChatCategories.filter(
                                                    (c) => c !== category.value,
                                                );
                                        }
                                    }}
                                />
                                <span
                                    class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                    {category.label}
                                </span>
                            </label>
                        {/each}
                    </div>
                {/snippet}
            </Form.Control>
            <Form.FieldErrors />
        </Form.Field>
    </div>

    <!-- Auto-Reply Settings -->
    <div class="space-y-4">
        <h3 class="text-lg font-medium">Auto-Reply Settings</h3>

        <Form.Field {form} name="enableAutoReplyForOffHours">
            <Form.Control>
                {#snippet children({ props })}
                    <div class="flex items-center space-x-2">
                        <Toggle
                            {...props}
                            bind:pressed={formData.enableAutoReplyForOffHours}
                        />
                        <Form.Label class="!mt-0"
                            >Enable auto-reply for off-hours</Form.Label
                        >
                    </div>
                {/snippet}
            </Form.Control>
            <Form.FieldErrors />
        </Form.Field>

        <Form.Field {form} name="autoReplyMessage">
            <Form.Control>
                {#snippet children({ props })}
                    <Form.Label>Auto-reply message</Form.Label>
                    <Textarea
                        {...props}
                        bind:value={formData.autoReplyMessage}
                        rows={3}
                    />
                {/snippet}
            </Form.Control>
            <Form.FieldErrors />
        </Form.Field>
    </div>

    <!-- Support Hours -->
    <div class="space-y-4">
        <h3 class="text-lg font-medium">Support Hours</h3>

        <div class="grid grid-cols-2 gap-4">
            <Form.Field {form} name="supportHours">
                <Form.Control>
                    {#snippet children({ props })}
                        <Form.Label>Start hour (24h format)</Form.Label>
                        <Input
                            type="number"
                            {...props}
                            bind:value={formData.supportHours[0]}
                            min="0"
                            max="23"
                        />
                    {/snippet}
                </Form.Control>
                <Form.FieldErrors />
            </Form.Field>

            <Form.Field {form} name="supportHours">
                <Form.Control>
                    {#snippet children({ props })}
                        <Form.Label>End hour (24h format)</Form.Label>
                        <Input
                            type="number"
                            {...props}
                            bind:value={formData.supportHours[1]}
                            min="0"
                            max="23"
                        />
                    {/snippet}
                </Form.Control>
                <Form.FieldErrors />
            </Form.Field>
        </div>

        <Form.Field {form} name="supportDays">
            <Form.Control>
                {#snippet children({ props })}
                    <Form.Label>Support days</Form.Label>
                    <div class="space-y-2">
                        {#each dayOptions as day}
                            <label
                                class="flex items-center space-x-2 cursor-pointer"
                            >
                                <Checkbox
                                    checked={formData.supportDays.includes(
                                        day.value,
                                    )}
                                    onCheckedChange={(checked) => {
                                        if (checked) {
                                            formData.supportDays = [
                                                ...formData.supportDays,
                                                day.value,
                                            ];
                                        } else {
                                            formData.supportDays =
                                                formData.supportDays.filter(
                                                    (d) => d !== day.value,
                                                );
                                        }
                                    }}
                                />
                                <span
                                    class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                    {day.label}
                                </span>
                            </label>
                        {/each}
                    </div>
                {/snippet}
            </Form.Control>
            <Form.FieldErrors />
        </Form.Field>
    </div>

    <!-- Image Settings -->
    <div class="space-y-4">
        <h3 class="text-lg font-medium">Image Upload Settings</h3>

        <div class="grid grid-cols-2 gap-4">
            <Form.Field {form} name="maxImagesPerMessage">
                <Form.Control>
                    {#snippet children({ props })}
                        <Form.Label>Max images per message</Form.Label>
                        <Input
                            type="number"
                            {...props}
                            bind:value={formData.maxImagesPerMessage}
                            min="1"
                            max="10"
                        />
                    {/snippet}
                </Form.Control>
                <Form.FieldErrors />
            </Form.Field>

            <Form.Field {form} name="maxImageSizeMB">
                <Form.Control>
                    {#snippet children({ props })}
                        <Form.Label>Max image size (MB)</Form.Label>
                        <Input
                            type="number"
                            {...props}
                            bind:value={formData.maxImageSizeMB}
                            min="1"
                            max="50"
                        />
                    {/snippet}
                </Form.Control>
                <Form.FieldErrors />
            </Form.Field>
        </div>
    </div>
</div>
