<script lang="ts">
    import { type PassengerNotificationConfiguration } from "$lib/stores/configurations.svelte";
    import * as m from "$lib/paraglide/messages";

    interface Props {
        configuration: PassengerNotificationConfiguration;
    }

    let { configuration = $bindable() }: Props = $props();

    function addReminderTime() {
        configuration.reservationReminderTimes = [
            ...configuration.reservationReminderTimes,
            30,
        ];
    }

    function removeReminderTime(index: number) {
        configuration.reservationReminderTimes =
            configuration.reservationReminderTimes.filter(
                (_, i) => i !== index,
            );
    }
</script>

<div class="space-y-6">
    <div>
        <label class="flex items-center space-x-2">
            <input
                type="checkbox"
                bind:checked={configuration.enableRingtoneByDefault}
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm font-medium text-gray-700">
                {m.adminConfigForm_enableRingtoneByDefaultLabel()}
            </span>
        </label>
        <p class="mt-1 text-sm text-gray-500">
            {m.adminConfigForm_enableRingtoneByDefaultDescription()}
        </p>
    </div>

    <div>
        <label class="flex items-center space-x-2">
            <input
                type="checkbox"
                bind:checked={configuration.enableDriverMovingNotification}
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm font-medium text-gray-700">
                {m.adminConfigForm_enableDriverMovingNotificationLabel()}
            </span>
        </label>
        <p class="mt-1 text-sm text-gray-500">
            {m.adminConfigForm_enableDriverMovingNotificationDescription()}
        </p>
    </div>

    <div>
        <label class="flex items-center space-x-2">
            <input
                type="checkbox"
                bind:checked={configuration.enableDriverArrivedNotification}
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm font-medium text-gray-700">
                {m.adminConfigForm_enableDriverArrivedNotificationLabel()}
            </span>
        </label>
        <p class="mt-1 text-sm text-gray-500">
            {m.adminConfigForm_enableDriverArrivedNotificationDescription()}
        </p>
    </div>

    <div>
        <label class="flex items-center space-x-2">
            <input
                type="checkbox"
                bind:checked={configuration.enableTripPaidNotification}
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm font-medium text-gray-700">
                {m.adminConfigForm_enableTripPaidNotificationLabel()}
            </span>
        </label>
        <p class="mt-1 text-sm text-gray-500">
            {m.adminConfigForm_enableTripPaidNotificationDescription()}
        </p>
    </div>

    <div>
        <label class="flex items-center space-x-2">
            <input
                type="checkbox"
                bind:checked={configuration.enableReservationReminders}
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm font-medium text-gray-700">
                {m.adminConfigForm_enableReservationRemindersLabel()}
            </span>
        </label>
        <p class="mt-1 text-sm text-gray-500">
            {m.adminConfigForm_enableReservationRemindersDescription()}
        </p>
    </div>

    {#if configuration.enableReservationReminders}
        <fieldset class="ml-6">
            <legend class="block text-sm font-medium text-gray-700 mb-2">
                {m.adminConfigForm_reservationReminderTimesLabel()}
            </legend>
            <div class="space-y-2">
                {#each configuration.reservationReminderTimes as time, index}
                    <div class="flex items-center space-x-2">
                        <input
                            type="number"
                            bind:value={
                                configuration.reservationReminderTimes[index]
                            }
                            min="5"
                            max="1440"
                            class="block w-24 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                        <span class="text-sm text-gray-600"
                            >{m.adminConfigForm_minutesBeforeLabel()}</span
                        >
                        <button
                            type="button"
                            onclick={() => removeReminderTime(index)}
                            class="text-red-600 hover:text-red-800"
                        >
                            {m.remove()}
                        </button>
                    </div>
                {/each}
                <button
                    type="button"
                    onclick={addReminderTime}
                    class="text-sm text-blue-600 hover:text-blue-800"
                >
                    {m.addReminderTime()}
                </button>
            </div>
        </fieldset>
    {/if}
</div>
