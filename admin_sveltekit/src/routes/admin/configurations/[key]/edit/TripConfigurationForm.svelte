<script lang="ts">
    import type { TripConfiguration } from "$lib/stores/configurations.svelte";
    import * as Form from "$lib/components/ui/form";
    import { Input } from "$lib/components/ui/input";
    import * as m from "$lib/paraglide/messages";

    interface Props {
        form: any; // SuperForm with TripConfiguration
        formData: TripConfiguration;
    }

    const { form, formData }: Props = $props();

    // Get the form store for reactive updates
    const { form: formStore } = form;
</script>

<div class="grid grid-cols-2 gap-6">
    <Form.Field {form} name="costPerKilometer">
        <Form.Control>
            {#snippet children({ props })}
                <Form.Label
                    >{m.adminConfigForm_costPerKilometerLabel()}</Form.Label
                >
                <Input
                    type="number"
                    {...props}
                    value={formData.costPerKilometer}
                    oninput={(e) => {
                        const value = parseFloat(e.currentTarget.value) || 0;
                        formStore.update((data: any) => ({
                            ...data,
                            costPerKilometer: value,
                        }));
                    }}
                />
            {/snippet}
        </Form.Control>
        <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="costPerHour">
        <Form.Control>
            {#snippet children({ props })}
                <Form.Label>{m.adminConfigForm_costPerHourLabel()}</Form.Label>
                <Input
                    type="number"
                    {...props}
                    value={formData.costPerHour}
                    oninput={(e) => {
                        const value = parseFloat(e.currentTarget.value) || 0;
                        formStore.update((data: any) => ({
                            ...data,
                            costPerHour: value,
                        }));
                    }}
                />
            {/snippet}
        </Form.Control>
        <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="minimumTripCost">
        <Form.Control>
            {#snippet children({ props })}
                <Form.Label
                    >{m.adminConfigForm_minimumTripCostLabel()}</Form.Label
                >
                <Input
                    type="number"
                    {...props}
                    value={formData.minimumTripCost}
                    oninput={(e) => {
                        const value = parseFloat(e.currentTarget.value) || 0;
                        formStore.update((data: any) => ({
                            ...data,
                            minimumTripCost: value,
                        }));
                    }}
                />
            {/snippet}
        </Form.Control>
        <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="waitTimeAfterExtraPayment">
        <Form.Control>
            {#snippet children({ props })}
                <Form.Label
                    >{m.adminConfigForm_waitTimeAfterExtraPaymentLabel()}</Form.Label
                >
                <Input
                    type="number"
                    {...props}
                    value={formData.waitTimeAfterExtraPayment}
                    oninput={(e) => {
                        const value = parseFloat(e.currentTarget.value) || 0;
                        formStore.update((data: any) => ({
                            ...data,
                            waitTimeAfterExtraPayment: value,
                        }));
                    }}
                />
            {/snippet}
        </Form.Control>
        <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="costPerExtraWaitChunk">
        <Form.Control>
            {#snippet children({ props })}
                <Form.Label
                    >{m.adminConfigForm_costPerExtraWaitChunkLabel()}</Form.Label
                >
                <Input
                    type="number"
                    {...props}
                    value={formData.costPerExtraWaitChunk}
                    oninput={(e) => {
                        const value = parseFloat(e.currentTarget.value) || 0;
                        formStore.update((data: any) => ({
                            ...data,
                            costPerExtraWaitChunk: value,
                        }));
                    }}
                />
            {/snippet}
        </Form.Control>
        <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="cancelCostPreStart">
        <Form.Control>
            {#snippet children({ props })}
                <Form.Label
                    >{m.adminConfigForm_cancelCostPreStartLabel()}</Form.Label
                >
                <Input
                    type="number"
                    {...props}
                    value={formData.cancelCostPreStart}
                    oninput={(e) => {
                        const value = parseFloat(e.currentTarget.value) || 0;
                        formStore.update((data: any) => ({
                            ...data,
                            cancelCostPreStart: value,
                        }));
                    }}
                />
            {/snippet}
        </Form.Control>
        <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="nearbyDriverListedRadiusMeters">
        <Form.Control>
            {#snippet children({ props })}
                <Form.Label
                    >{m.adminConfigForm_nearbyDriverRadiusLabel()}</Form.Label
                >
                <Input
                    type="number"
                    {...props}
                    value={formData.nearbyDriverListedRadiusMeters}
                    oninput={(e) => {
                        const value = parseFloat(e.currentTarget.value) || 0;
                        formStore.update((data: any) => ({
                            ...data,
                            nearbyDriverListedRadiusMeters: value,
                        }));
                    }}
                />
            {/snippet}
        </Form.Control>
        <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="maxPassengerCount">
        <Form.Control>
            {#snippet children({ props })}
                <Form.Label
                    >{m.adminConfigForm_maxPassengerCountLabel()}</Form.Label
                >
                <Input
                    type="number"
                    {...props}
                    value={formData.maxPassengerCount}
                    oninput={(e) => {
                        const value = parseInt(e.currentTarget.value) || 1;
                        formStore.update((data: any) => ({
                            ...data,
                            maxPassengerCount: value,
                        }));
                    }}
                    min="1"
                    max="20"
                />
            {/snippet}
        </Form.Control>
        <Form.Description>
            {m.adminConfigForm_maxPassengerCountDescription()}
        </Form.Description>
        <Form.FieldErrors />
    </Form.Field>

    <!-- Hide In-Progress Costs Setting -->
    <div class="col-span-2">
        <Form.Field {form} name="hideInProgressCosts">
            <Form.Control>
                {#snippet children({ props })}
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input
                            type="checkbox"
                            id="hideInProgressCosts"
                            name="hideInProgressCosts"
                            class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            checked={formData.hideInProgressCosts}
                            onchange={(e) => {
                                const checked = e.currentTarget.checked;
                                formStore.update((data: any) => ({
                                    ...data,
                                    hideInProgressCosts: checked,
                                }));
                            }}
                        />
                        <span
                            class="text-sm font-medium leading-none select-none"
                        >
                            {m.adminConfigForm_hideInProgressCostsLabel()}
                        </span>
                    </label>
                {/snippet}
            </Form.Control>
            <Form.Description>
                {m.adminConfigForm_hideInProgressCostsDescription()}
            </Form.Description>
        </Form.Field>
    </div>
</div>
