<script lang="ts">
    import type {
        Configuration,
        TripConfiguration,
        ChatConfiguration,
        PassengerNotificationConfiguration,
    } from "$lib/stores/configurations.svelte";
    import { save as saveConfiguration } from "$lib/stores/configurations.svelte";
    import * as Card from "$lib/components/ui/card/index.js";
    import { Button } from "$lib/components/ui/button";
    import * as Form from "$lib/components/ui/form";
    import { Input } from "$lib/components/ui/input";
    import { superForm } from "sveltekit-superforms";
    import { zodClient } from "sveltekit-superforms/adapters";
    import { z } from "zod";
    import { localizedGoto } from "$lib/utils";
    import { toast } from "svelte-sonner";
    import * as m from "$lib/paraglide/messages";
    import TripConfigurationForm from "./TripConfigurationForm.svelte";
    import ChatConfigurationForm from "./ChatConfigurationForm.svelte";
    import PassengerNotificationForm from "./PassengerNotificationForm.svelte";

    interface Props {
        configuration: Configuration | null;
    }

    const { configuration }: Props = $props();

    // Define schemas based on configuration type
    const tripConfigSchema = z.object({
        costPerKilometer: z.number().min(0),
        costPerHour: z.number().min(0),
        minimumTripCost: z.number().min(0),
        waitTimeAfterExtraPayment: z.number().min(0),
        costPerExtraWaitChunk: z.number().min(0),
        cancelCostPreStart: z.number().min(0),
        nearbyDriverListedRadiusMeters: z.number().min(0),
        maxPassengerCount: z.number().min(1).max(20),
        hideInProgressCosts: z.boolean(),
    });

    const chatConfigSchema = z.object({
        showAdminNamesInChat: z.boolean(),
        defaultChatCategories: z.array(z.string()).min(1),
        autoArchiveChatAfterDays: z.number().min(1).max(365),
        enableChatNotifications: z.boolean(),
        enableAutoReplyForOffHours: z.boolean(),
        autoReplyMessage: z.string(),
        supportHours: z.array(z.number()).length(2),
        supportDays: z.array(z.number()).min(1),
        maxImagesPerMessage: z.number().min(1).max(10),
        maxImageSizeMB: z.number().min(1).max(50),
    });

    const passengerNotificationSchema = z.object({
        enableRingtoneByDefault: z.boolean(),
        enableDriverMovingNotification: z.boolean(),
        enableDriverArrivedNotification: z.boolean(),
        enableTripPaidNotification: z.boolean(),
        enableReservationReminders: z.boolean(),
        reservationReminderTimes: z.array(z.number()).min(1),
    });

    const generalConfigSchema = z.object({
        value: z.union([z.number(), z.string()]),
    });

    // Determine which schema to use and get the schema once
    const schema = configuration?.key === "tripConfiguration" ? tripConfigSchema
        : configuration?.key === "chat" ? chatConfigSchema
        : configuration?.key === "passengerNotifications" ? passengerNotificationSchema
        : generalConfigSchema;

    // Initialize form data based on configuration
    const data = configuration
        ? configuration.key === "tripConfiguration"
            ? { ...(configuration.value as TripConfiguration) }
            : configuration.key === "chat"
              ? { ...(configuration.value as ChatConfiguration) }
              : configuration.key === "passengerNotifications"
                ? {
                      ...(configuration.value as PassengerNotificationConfiguration),
                  }
                : { value: configuration.value }
        : {};

    // Create the form
    const form = superForm(schema.parse(data), {
        validators: zodClient(schema as any),
    });

    const { form: formData } = form;

    async function handleSubmit(event: SubmitEvent) {
        event.preventDefault();

        if (!configuration) return;

        try {
            // Conditionally construct the updated config object
            let updatedConfig: Configuration;

            if (configuration.key === "tripConfiguration") {
                updatedConfig = {
                    ...configuration,
                    value: $formData as TripConfiguration,
                };
            } else if (configuration.key === "chat") {
                updatedConfig = {
                    ...configuration,
                    value: $formData as ChatConfiguration,
                };
            } else if (configuration.key === "passengerNotifications") {
                updatedConfig = {
                    ...configuration,
                    value: $formData as PassengerNotificationConfiguration,
                };
            } else {
                updatedConfig = {
                    ...configuration,
                    value: ($formData as { value: string | number }).value,
                };
            }

            const success = await saveConfiguration(updatedConfig);

            if (success) {
                toast.success(m.adminConfigForm_saveSuccessToast());
                if (history.length > 1) {
                    history.back();
                } else {
                    localizedGoto(`/admin/configurations/${configuration.key}`);
                }
            } else {
                toast.error(m.adminConfigForm_saveErrorToast());
            }
        } catch (error) {
            console.error("Error saving configuration:", error);
            toast.error(m.adminConfigForm_saveErrorToast());
        }
    }

    // Get the appropriate title and description
    const getTitle = () => {
        if (configuration?.key === "tripConfiguration")
            return m.adminConfigDetailsComponent_tripConfigTitle();
        if (configuration?.key === "chat")
            return m.adminConfigDetailsComponent_chatConfigTitle();
        if (configuration?.key === "passengerNotifications")
            return m.adminConfigDetailsComponent_passengerNotificationsTitle();
        return m.adminConfigDetailsComponent_generalConfigTitle();
    };

    const getDescription = () => {
        if (configuration?.key === "tripConfiguration")
            return m.adminConfigForm_tripConfigDescription();
        if (configuration?.key === "chat")
            return m.adminConfigForm_chatConfigDescription();
        if (configuration?.key === "passengerNotifications")
            return m.adminConfigForm_passengerNotificationsDescription();
        return m.adminConfigForm_generalConfigDescription();
    };
</script>

{#if configuration}
    <form onsubmit={handleSubmit}>
        <Card.Root>
            <Card.Header>
                <Card.Title>{getTitle()}</Card.Title>
                <Card.Description>{getDescription()}</Card.Description>
            </Card.Header>
            <Card.Content>
                {#if configuration.key === "tripConfiguration"}
                    <TripConfigurationForm
                        {form}
                        formData={$formData as TripConfiguration}
                    />
                {:else if configuration.key === "chat"}
                    <ChatConfigurationForm
                        {form}
                        formData={$formData as ChatConfiguration}
                    />
                {:else if configuration.key === "passengerNotifications"}
                    <PassengerNotificationForm
                        bind:configuration={
                            $formData as PassengerNotificationConfiguration
                        }
                    />
                {:else}
                    <Form.Field {form} name="value">
                        <Form.Control>
                            {#snippet children({ props })}
                                <Form.Label
                                    >{m.adminConfigDetailsComponent_valueLabel()}</Form.Label
                                >
                                <Input
                                    bind:value={
                                        ($formData as { value: string | number })
                                            .value
                                    }
                                />
                            {/snippet}
                        </Form.Control>
                        <Form.FieldErrors />
                    </Form.Field>
                {/if}
            </Card.Content>
            <Card.Footer class="flex justify-end space-x-2">
                <Button type="submit" variant="default"
                    >{m.adminConfigForm_saveButton()}</Button
                >
            </Card.Footer>
        </Card.Root>
    </form>
{:else}
    <Card.Root>
        <Card.Header>
            <Card.Title>{m.adminConfigForm_fallbackTitle()}</Card.Title>
        </Card.Header>
        <Card.Content>
            <p>{m.adminConfigForm_fallbackContent()}</p>
        </Card.Content>
    </Card.Root>
{/if}
