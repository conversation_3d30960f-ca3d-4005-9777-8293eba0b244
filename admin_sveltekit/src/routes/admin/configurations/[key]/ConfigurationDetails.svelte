<script lang="ts">
    import type {
        Configuration,
        TripConfiguration,
        ChatConfiguration,
        PassengerNotificationConfiguration,
    } from "$lib/stores/configurations.svelte";
    import * as Card from "$lib/components/ui/card/index.js";
    import { Button } from "$lib/components/ui/button";
    import { PencilIcon } from "lucide-svelte";
    import { localizedGoto } from "$lib/utils";
    import * as m from "$lib/paraglide/messages";

    const { configuration } = $props<{ configuration: Configuration }>();

    function handleEdit() {
        localizedGoto(`/admin/configurations/${configuration.key}/edit`);
    }

    function formatKey(key: string, configType?: string): string {
        // For passenger notifications, use specific translations
        if (configType === "passengerNotifications") {
            switch (key) {
                case "enableRingtoneByDefault":
                    return m.adminConfigForm_enableRingtoneByDefaultLabel();
                case "enableDriverMovingNotification":
                    return m.adminConfigForm_enableDriverMovingNotificationLabel();
                case "enableDriverArrivedNotification":
                    return m.adminConfigForm_enableDriverArrivedNotificationLabel();
                case "enableTripPaidNotification":
                    return m.adminConfigForm_enableTripPaidNotificationLabel();
                case "enableReservationReminders":
                    return m.adminConfigForm_enableReservationRemindersLabel();
                case "reservationReminderTimes":
                    return m.adminConfigForm_reservationReminderTimesLabel();
            }
        }

        // For trip configuration, use specific translations if available
        if (configType === "tripConfiguration") {
            switch (key) {
                case "costPerKilometer":
                    return m.adminConfigForm_costPerKilometerLabel();
                case "costPerHour":
                    return m.adminConfigForm_costPerHourLabel();
                case "minimumTripCost":
                    return m.adminConfigForm_minimumTripCostLabel();
                case "waitTimeAfterExtraPayment":
                    return m.adminConfigForm_waitTimeAfterExtraPaymentLabel();
                case "costPerExtraWaitChunk":
                    return m.adminConfigForm_costPerExtraWaitChunkLabel();
                case "cancelCostPreStart":
                    return m.adminConfigForm_cancelCostPreStartLabel();
                case "nearbyDriverListedRadiusMeters":
                    return m.adminConfigForm_nearbyDriverRadiusLabel();
                case "maxPassengerCount":
                    return m.adminConfigForm_maxPassengerCountLabel();
                case "hideInProgressCosts":
                    return m.adminConfigForm_hideInProgressCostsLabel();
            }
        }

        // For chat configuration, use specific translations
        if (configType === "chat") {
            switch (key) {
                case "showAdminNamesInChat":
                    return m.adminConfigForm_showAdminNamesInChatLabel();
                case "defaultChatCategories":
                    return m.adminConfigForm_defaultChatCategoriesLabel();
                case "autoArchiveChatAfterDays":
                    return m.adminConfigForm_autoArchiveChatAfterDaysLabel();
                case "enableChatNotifications":
                    return m.adminConfigForm_enableChatNotificationsLabel();
                case "enableAutoReplyForOffHours":
                    return m.adminConfigForm_enableAutoReplyForOffHoursLabel();
                case "autoReplyMessage":
                    return m.adminConfigForm_autoReplyMessageLabel();
                case "supportHours":
                    return m.adminConfigForm_supportHoursLabel();
                case "supportDays":
                    return m.adminConfigForm_supportDaysLabel();
                case "maxImagesPerMessage":
                    return m.adminConfigForm_maxImagesPerMessageLabel();
                case "maxImageSizeMB":
                    return m.adminConfigForm_maxImageSizeMBLabel();
            }
        }

        // Default: Convert camelCase to Title Case with spaces
        return key
            .replace(/([A-Z])/g, " $1")
            .replace(/^./, (str) => str.toUpperCase())
            .trim();
    }

    function getTitle() {
        if (configuration.key === "tripConfiguration")
            return m.adminConfigDetailsComponent_tripConfigTitle();
        if (configuration.key === "chat")
            return m.adminConfigDetailsComponent_chatConfigTitle();
        if (configuration.key === "passengerNotifications")
            return m.adminConfigDetailsComponent_passengerNotificationsTitle();
        return m.adminConfigDetailsComponent_generalConfigTitle();
    }

    function getDescription() {
        if (configuration.key === "tripConfiguration")
            return m.adminConfigDetailsComponent_tripConfigDescription();
        if (configuration.key === "chat")
            return m.adminConfigDetailsComponent_chatConfigDescription();
        if (configuration.key === "passengerNotifications")
            return m.adminConfigDetailsComponent_passengerNotificationsDescription();
        return m.adminConfigDetailsComponent_generalConfigDescription();
    }

    function formatValue(value: any, key?: string): string {
        if (typeof value === "boolean")
            return value
                ? m.adminConfigDetailsComponent_yes()
                : m.adminConfigDetailsComponent_no();
        if (Array.isArray(value)) {
            if (value.length === 0) return m.adminConfigDetailsComponent_none();
            if (
                value.length === 2 &&
                typeof value[0] === "number" &&
                typeof value[1] === "number"
            ) {
                // Format support hours
                return `${value[0]}:00 - ${value[1]}:00`;
            }
            // Format days of week
            if (value.every((v) => typeof v === "number" && v >= 0 && v <= 6)) {
                const days = [
                    m.adminConfigDetailsComponent_sunday(),
                    m.adminConfigDetailsComponent_monday(),
                    m.adminConfigDetailsComponent_tuesday(),
                    m.adminConfigDetailsComponent_wednesday(),
                    m.adminConfigDetailsComponent_thursday(),
                    m.adminConfigDetailsComponent_friday(),
                    m.adminConfigDetailsComponent_saturday(),
                ];
                return value.map((d) => days[d]).join(", ");
            }
            // Format reservation reminder times
            if (
                key === "reservationReminderTimes" &&
                value.every((v) => typeof v === "number")
            ) {
                return value
                    .map(
                        (minutes) =>
                            `${minutes} ${m.adminConfigDetailsComponent_minutes()}`,
                    )
                    .join(", ");
            }
            // Format categories
            if (value.every((v) => typeof v === "string")) {
                return value
                    .map((v) => v.replace(/([A-Z])/g, " $1").trim())
                    .join(", ");
            }
            return value.join(", ");
        }
        return String(value);
    }
</script>

<Card.Root>
    <Card.Header class="flex flex-row items-start justify-between">
        <div>
            <Card.Title>{getTitle()}</Card.Title>
            <Card.Description>{getDescription()}</Card.Description>
        </div>
        <Button variant="ghost" size="icon" onclick={handleEdit}>
            <PencilIcon class="h-4 w-4" />
        </Button>
    </Card.Header>
    <Card.Content>
        {#if configuration.key === "tripConfiguration"}
            <div class="grid grid-cols-2 gap-6">
                {#each Object.entries(configuration.value as TripConfiguration) as [key, value]}
                    <div class="space-y-1">
                        <p class="text-sm font-medium leading-none">
                            {formatKey(key, "tripConfiguration")}
                        </p>
                        <p class="text-sm text-muted-foreground">
                            {formatValue(value, key)}
                        </p>
                    </div>
                {/each}
            </div>
        {:else if configuration.key === "chat"}
            <div class="grid grid-cols-2 gap-6">
                {#each Object.entries(configuration.value as ChatConfiguration) as [key, value]}
                    <div class="space-y-1">
                        <p class="text-sm font-medium leading-none">
                            {formatKey(key, "chat")}
                        </p>
                        <p class="text-sm text-muted-foreground">
                            {formatValue(value, key)}
                        </p>
                    </div>
                {/each}
            </div>
        {:else if configuration.key === "passengerNotifications"}
            <div class="grid grid-cols-2 gap-6">
                {#each Object.entries(configuration.value as PassengerNotificationConfiguration) as [key, value]}
                    <div class="space-y-1">
                        <p class="text-sm font-medium leading-none">
                            {formatKey(key, "passengerNotifications")}
                        </p>
                        <p class="text-sm text-muted-foreground">
                            {formatValue(value, key)}
                        </p>
                    </div>
                {/each}
            </div>
        {:else}
            <div class="space-y-1">
                <p class="text-sm font-medium leading-none">
                    {m.adminConfigDetailsComponent_valueLabel()}
                </p>
                <p class="text-sm text-muted-foreground">
                    {configuration.value}
                </p>
            </div>
        {/if}
    </Card.Content>
</Card.Root>
