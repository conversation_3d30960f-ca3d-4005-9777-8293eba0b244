<script lang="ts">
    import * as Card from "$lib/components/ui/card/index.js";
    import * as m from "$lib/paraglide/messages";
    import CacheManager from "$lib/components/CacheManager.svelte";
</script>

<div class="space-y-6">
    <Card.Root>
        <Card.Content>
            <p class="text-sm text-muted-foreground">
                {m.adminConfigsList_selectConfigPrompt()}
            </p>
        </Card.Content>
    </Card.Root>

    <!-- Cache Management Section -->
    <CacheManager />
</div>
