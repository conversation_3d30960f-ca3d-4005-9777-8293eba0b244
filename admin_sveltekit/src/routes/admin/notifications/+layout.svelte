<script lang="ts">
  import { onMount, onDestroy } from "svelte";
  import {
    collection,
    query,
    orderBy,
    limit,
    onSnapshot,
    doc,
    updateDoc,
    arrayUnion,
    type Unsubscribe,
  } from "firebase/firestore";
  import { fdb } from "$lib/firebase.client";
  import { tenantStore } from "$lib/stores/tenant.svelte";
  import {
    Bell,
    MessageSquare,
    Car,
    MessageCircle,
    Check,
    Loader,
    Search,
  } from "lucide-svelte";
  import { formatDistanceToNow } from "date-fns";
  import { getFirebaseUser } from "$lib/stores/auth.svelte";
  import { localizedGoto } from "$lib/utils";
  import * as m from "$lib/paraglide/messages";
  import * as Card from "$lib/components/ui/card/index.js";
  import { Badge } from "$lib/components/ui/badge";
  import { Input } from "$lib/components/ui/input";
  import { Toggle } from "$lib/components/ui/toggle";
  import Spinner from "$lib/components/ui/Spinner.svelte";
  import { page } from "$app/state";

  interface AdminNotification {
    id: string;
    type:
      | "chat_message"
      | "trip_booking"
      | "feedback_submitted"
      | "trip_cancelled"
      | "reservation_reminder";
    tripId?: string;
    feedbackId?: string;
    chatSessionId?: string;
    title: string;
    message: string;
    createdAt: Date;
    clickAction?: string;
    readByAdmins: string[];
  }

  let notifications = $state<AdminNotification[]>([]);
  let loading = $state(true);
  let markingAsRead = $state<Set<string>>(new Set());
  let searchQuery = $state("");
  let showUnreadOnly = $state(false);
  let unsubscribe: Unsubscribe;

  const firebaseUser = $derived(getFirebaseUser());
  const selectedNotificationId = $derived(page.params.notificationId || null);

  const notificationIcons = {
    chat_message: MessageSquare,
    trip_booking: Car,
    feedback_submitted: MessageCircle,
    trip_cancelled: Car,
    reservation_reminder: Bell,
  };

  const notificationColors = {
    chat_message: "text-blue-600",
    trip_booking: "text-green-600",
    feedback_submitted: "text-purple-600",
    trip_cancelled: "text-red-600",
    reservation_reminder: "text-orange-600",
  };

  let filteredNotifications = $derived(
    notifications.filter((notification) => {
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          notification.title.toLowerCase().includes(query) ||
          notification.message.toLowerCase().includes(query)
        );
      }
      if (showUnreadOnly) {
        return isUnread(notification);
      }
      return true;
    }),
  );

  const { children } = $props();

  onMount(() => {
    loadNotifications();
  });

  onDestroy(() => {
    if (unsubscribe) {
      unsubscribe();
    }
  });

  function loadNotifications() {
    if (!fdb) {
      console.error("Firestore not initialized");
      return;
    }
    const notificationsRef = collection(
      fdb,
      tenantStore.getTenantPath("admin_notifications"),
    );
    const q = query(notificationsRef, orderBy("createdAt", "desc"), limit(100));

    unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        notifications = snapshot.docs.map(
          (doc) =>
            ({
              id: doc.id,
              ...doc.data(),
              createdAt: doc.data().createdAt?.toDate() || new Date(),
            }) as AdminNotification,
        );
        loading = false;
      },
      (error) => {
        console.error("Error loading notifications:", error);
        loading = false;
      },
    );
  }

  async function markAsRead(notification: AdminNotification) {
    if (
      !firebaseUser ||
      typeof firebaseUser !== "object" ||
      notification.readByAdmins.includes(firebaseUser.uid)
    ) {
      return;
    }

    markingAsRead.add(notification.id);

    try {
      if (!fdb) {
        throw new Error("Firestore not initialized");
      }
      const notificationRef = doc(
        fdb,
        tenantStore.getTenantPath("admin_notifications"),
        notification.id,
      );
      await updateDoc(notificationRef, {
        readByAdmins: arrayUnion(firebaseUser.uid),
      });
    } catch (error) {
      console.error("Error marking notification as read:", error);
    } finally {
      markingAsRead.delete(notification.id);
    }
  }

  async function handleNotificationClick(notification: AdminNotification) {
    // Mark as read
    await markAsRead(notification);

    // Navigate to the notification details
    localizedGoto(`/admin/notifications/${notification.id}`);
  }

  function isUnread(notification: AdminNotification): boolean {
    return typeof firebaseUser === "object"
      ? !notification.readByAdmins.includes(firebaseUser.uid)
      : false;
  }

  function getNotificationTypeLabel(type: string): string {
    switch (type) {
      case "chat_message":
        return m.adminNotifications_typeChatMessage();
      case "trip_booking":
        return m.adminNotifications_typeTripBooking();
      case "feedback_submitted":
        return m.adminNotifications_typeFeedbackSubmitted();
      case "trip_cancelled":
        return m.adminNotifications_typeTripCancelled();
      case "reservation_reminder":
        return m.adminNotifications_typeReservationReminder();
      default:
        return type.replace(/_/g, " ");
    }
  }

  $effect(() => {
    // Auto-refresh every 30 seconds
    const interval = setInterval(() => {
      if (unsubscribe) {
        unsubscribe();
        loadNotifications();
      }
    }, 30000);

    return () => clearInterval(interval);
  });
</script>

<svelte:head>
  <title>{m.adminNotifications_pageTitle()} - Fiara Now Admin</title>
</svelte:head>

<div class="p-4">
  <div class="flex h-full gap-4">
    <!-- Left section - Notifications list -->
    <div class="w-1/3">
      <Card.Root class="h-[calc(100vh-90px)]">
        <Card.Header>
          <div class="flex items-start justify-between">
            <div>
              <Card.Title class="flex items-center gap-2">
                <Bell class="w-5 h-5" />
                {m.adminNotifications_pageTitle()}
              </Card.Title>
              <Card.Description>
                {m.adminNotifications_pageDescription()}
              </Card.Description>
            </div>
          </div>
        </Card.Header>
        <Card.Content class="h-[calc(100vh-152px)] overflow-y-auto">
          <!-- Summary Stats -->
          <div class="grid grid-cols-2 gap-2 mb-4">
            <div class="bg-muted/50 rounded-lg p-2 text-center">
              <div class="text-lg font-bold">{notifications.length}</div>
              <div class="text-xs text-muted-foreground">Total</div>
            </div>
            <div class="bg-blue-50 rounded-lg p-2 text-center">
              <div class="text-lg font-bold text-blue-600">
                {notifications.filter(isUnread).length}
              </div>
              <div class="text-xs text-muted-foreground">Unread</div>
            </div>
          </div>

          <!-- Search input -->
          <div class="relative mb-3">
            <Search
              class="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"
            />
            <Input
              type="text"
              placeholder="Search notifications..."
              bind:value={searchQuery}
              class="pl-9"
            />
          </div>

          <!-- Show unread only toggle -->
          <div class="flex items-center gap-2 mb-4">
            <Toggle
              pressed={showUnreadOnly}
              onPressedChange={(pressed: boolean) => (showUnreadOnly = pressed)}
              variant="outline"
              size="sm"
              class="data-[state=on]:bg-blue-500"
            >
              Unread Only
            </Toggle>
          </div>

          <!-- Notifications count -->
          <div class="flex justify-between items-center mb-4">
            <span class="text-sm text-muted-foreground">
              {filteredNotifications.length} notification{filteredNotifications.length !==
              1
                ? "s"
                : ""}
            </span>
          </div>

          <!-- Notifications list -->
          <div class="space-y-2">
            {#if loading}
              <div class="flex items-center justify-center py-8">
                <Spinner className="h-6 w-6" />
              </div>
            {:else if filteredNotifications.length === 0}
              <div class="text-center py-8">
                <Bell class="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                <p class="text-sm text-muted-foreground">
                  {searchQuery || showUnreadOnly
                    ? "No notifications found"
                    : m.adminNotifications_noNotificationsTitle()}
                </p>
              </div>
            {:else}
              {#each filteredNotifications as notification}
                {@const Icon = notificationIcons[notification.type]}

                <button
                  onclick={() => handleNotificationClick(notification)}
                  class="w-full text-left p-3 rounded-lg hover:bg-muted transition-colors relative overflow-hidden {selectedNotificationId ===
                  notification.id
                    ? 'bg-muted'
                    : ''} {isUnread(notification) ? 'bg-blue-50' : ''}"
                >
                  {#if isUnread(notification)}
                    <div
                      class="absolute left-0 top-0 bottom-0 w-1 bg-blue-600"
                    ></div>
                  {/if}
                  <div class="flex items-start gap-3">
                    <div
                      class={`p-2 rounded-lg bg-gray-100 ${notificationColors[notification.type]} flex-shrink-0`}
                    >
                      <Icon class="w-4 h-4" />
                    </div>

                    <div class="flex-1 min-w-0">
                      <div class="flex items-start justify-between gap-2">
                        <h4 class="font-medium truncate text-sm">
                          {notification.title}
                        </h4>
                        <span
                          class="text-xs text-muted-foreground whitespace-nowrap"
                        >
                          {formatDistanceToNow(notification.createdAt, {
                            addSuffix: true,
                          })}
                        </span>
                      </div>

                      <p
                        class="text-sm text-muted-foreground line-clamp-2 mt-1"
                      >
                        {notification.message}
                      </p>

                      <div class="flex items-center gap-2 mt-2">
                        <Badge variant="secondary" class="text-xs">
                          {getNotificationTypeLabel(notification.type)}
                        </Badge>

                        {#if markingAsRead.has(notification.id)}
                          <Loader class="w-3 h-3 animate-spin text-gray-400" />
                        {:else if !isUnread(notification)}
                          <Check class="w-3 h-3 text-green-600" />
                        {/if}
                      </div>
                    </div>
                  </div>
                </button>
              {/each}
            {/if}
          </div>
        </Card.Content>
      </Card.Root>
    </div>

    <!-- Right section - Content -->
    <div class="flex-1">
      {@render children()}
    </div>
  </div>
</div>
