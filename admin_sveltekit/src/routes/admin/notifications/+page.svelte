<script lang="ts">
	import * as Card from "$lib/components/ui/card/index.js";
	import * as m from "$lib/paraglide/messages";
	import { Bell } from "lucide-svelte";
</script>

<Card.Root class="h-full">
	<Card.Header>
		<Card.Title>{m.adminNotifications_pageTitle()}</Card.Title>
		<Card.Description>
			Select a notification from the list to view its details
		</Card.Description>
	</Card.Header>
	<Card.Content>
		<div class="flex items-center justify-center h-[calc(100vh-260px)]">
			<div class="text-center">
				<Bell class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
				<h3 class="text-lg font-semibold mb-2">No notification selected</h3>
				<p class="text-muted-foreground">
					Choose a notification from the list to view its details and take
					actions
				</p>
			</div>
		</div>
	</Card.Content>
</Card.Root>
