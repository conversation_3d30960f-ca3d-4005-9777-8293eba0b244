<script lang="ts">
  import { onMount } from 'svelte';
  import { doc, getDoc, updateDoc, arrayUnion } from 'firebase/firestore';
  import { fdb } from '$lib/firebase.client';
  import { tenantStore } from '$lib/stores/tenant.svelte';
  import { page } from '$app/state';
  import { getFirebaseUser } from '$lib/stores/auth.svelte';
  import { formatLongDateTime } from '$lib/utils/datetime';
  import { localizedGoto } from '$lib/utils';
  import * as Card from '$lib/components/ui/card/index.js';
  import { Button } from '$lib/components/ui/button';
  import { Badge } from '$lib/components/ui/badge';
  import Spinner from '$lib/components/ui/Spinner.svelte';
  import { Bell, MessageSquare, Car, MessageCircle, ExternalLink, Check, ArrowLeft } from 'lucide-svelte';
  import * as m from '$lib/paraglide/messages';
  import { toast } from 'svelte-sonner';

  interface AdminNotification {
    id: string;
    type: 'chat_message' | 'trip_booking' | 'feedback_submitted' | 'trip_cancelled' | 'reservation_reminder';
    tripId?: string;
    feedbackId?: string;
    chatSessionId?: string;
    title: string;
    message: string;
    createdAt: Date;
    clickAction?: string;
    readByAdmins: string[];
  }

  let notification = $state<AdminNotification | null>(null);
  let loading = $state(true);
  let markingAsRead = $state(false);

  const notificationId = $derived(page.params.notificationId);
  const firebaseUser = $derived(getFirebaseUser());

  const notificationIcons = {
    chat_message: MessageSquare,
    trip_booking: Car,
    feedback_submitted: MessageCircle,
    trip_cancelled: Car,
    reservation_reminder: Bell,
  };

  const notificationColors = {
    chat_message: 'bg-blue-100 text-blue-800',
    trip_booking: 'bg-green-100 text-green-800',
    feedback_submitted: 'bg-purple-100 text-purple-800',
    trip_cancelled: 'bg-red-100 text-red-800',
    reservation_reminder: 'bg-orange-100 text-orange-800',
  };

  onMount(async () => {
    if (notificationId) {
      await loadNotification();
    }
  });

  async function loadNotification() {
    if (!fdb) {
      console.error('Firestore not initialized');
      loading = false;
      return;
    }
    try {
      const notificationRef = doc(fdb, tenantStore.getTenantPath('admin_notifications'), notificationId);
      const notificationDoc = await getDoc(notificationRef);

      if (notificationDoc.exists()) {
        notification = {
          id: notificationDoc.id,
          ...notificationDoc.data(),
          createdAt: notificationDoc.data().createdAt?.toDate() || new Date(),
        } as AdminNotification;

        // Auto-mark as read if not already read
        if (notification && !isRead(notification)) {
          await markAsRead(notification);
        }
      } else {
        notification = null;
      }
    } catch (error) {
      console.error('Error loading notification:', error);
      toast.error('Failed to load notification');
    } finally {
      loading = false;
    }
  }

  async function markAsRead(notif: AdminNotification) {
    if (!firebaseUser || typeof firebaseUser !== 'object' || notif.readByAdmins.includes(firebaseUser.uid)) {
      return;
    }

    markingAsRead = true;

    try {
      if (!fdb) {
        console.error('Firestore not initialized');
        return;
      }
      const notificationRef = doc(fdb, tenantStore.getTenantPath('admin_notifications'), notif.id);
      await updateDoc(notificationRef, {
        readByAdmins: arrayUnion(firebaseUser.uid),
      });

      // Update local state
      if (notification) {
        notification.readByAdmins = [...notification.readByAdmins, firebaseUser.uid];
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    } finally {
      markingAsRead = false;
    }
  }

  function isRead(notif: AdminNotification): boolean {
    return typeof firebaseUser === 'object' ? notif.readByAdmins.includes(firebaseUser.uid) : false;
  }

  function getNotificationTypeLabel(type: string): string {
    switch (type) {
      case 'chat_message':
        return m.adminNotifications_typeChatMessage();
      case 'trip_booking':
        return m.adminNotifications_typeTripBooking();
      case 'feedback_submitted':
        return m.adminNotifications_typeFeedbackSubmitted();
      case 'trip_cancelled':
        return m.adminNotifications_typeTripCancelled();
      case 'reservation_reminder':
        return m.adminNotifications_typeReservationReminder();
      default:
        return type.replace(/_/g, ' ');
    }
  }

  async function handleActionClick() {
    if (notification?.clickAction) {
      localizedGoto(notification.clickAction);
    }
  }
</script>

{#if loading}
  <Card.Root class="h-full">
    <Card.Content class="h-full flex items-center justify-center">
      <Spinner className="h-8 w-8" />
    </Card.Content>
  </Card.Root>
{:else if !notification}
  <Card.Root class="h-full">
    <Card.Content class="h-full flex items-center justify-center">
      <div class="text-center">
        <Bell class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 class="text-lg font-semibold mb-2">Notification not found</h3>
        <p class="text-muted-foreground">The notification you're looking for doesn't exist or has been removed.</p>
      </div>
    </Card.Content>
  </Card.Root>
{:else}
  {@const Icon = notificationIcons[notification.type]}

  <Card.Root class="h-full">
    <Card.Header>
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <div class="flex items-center gap-3 mb-2">
            <div class={`p-2 rounded-lg ${notificationColors[notification.type]}`}>
              <Icon class="w-5 h-5" />
            </div>
            <Badge class={notificationColors[notification.type]}>
              {getNotificationTypeLabel(notification.type)}
            </Badge>
            {#if isRead(notification)}
              <Badge variant="outline" class="flex items-center gap-1">
                <Check class="w-3 h-3" />
                Read
              </Badge>
            {:else}
              <Badge variant="secondary">Unread</Badge>
            {/if}
          </div>
          <Card.Title class="text-xl">{notification.title}</Card.Title>
          <Card.Description>
            {formatLongDateTime(notification.createdAt)}
          </Card.Description>
        </div>
        <div class="flex items-center gap-2">
          {#if notification.clickAction}
            <Button onclick={handleActionClick} class="flex items-center gap-2">
              <ExternalLink class="w-4 h-4" />
              {m.adminNotifications_viewDetails()}
            </Button>
          {/if}
        </div>
      </div>
    </Card.Header>
    <Card.Content class="h-[calc(100vh-220px)] overflow-y-auto">
      <div class="space-y-6">
        <!-- Message Content -->
        <div>
          <h3 class="text-lg font-semibold mb-3">Message</h3>
          <div class="bg-muted/50 rounded-lg p-4">
            <p class="whitespace-pre-wrap">{notification.message}</p>
          </div>
        </div>

        <!-- Additional Details -->
        <div>
          <h3 class="text-lg font-semibold mb-3">Details</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div class="text-sm font-medium text-muted-foreground">Type</div>
              <p class="mt-1">{getNotificationTypeLabel(notification.type)}</p>
            </div>
            <div>
              <div class="text-sm font-medium text-muted-foreground">Created</div>
              <p class="mt-1">{formatLongDateTime(notification.createdAt)}</p>
            </div>
            {#if notification.tripId}
              <div>
                <div class="text-sm font-medium text-muted-foreground">Trip ID</div>
                <p class="mt-1 font-mono text-sm">{notification.tripId}</p>
              </div>
            {/if}
            {#if notification.feedbackId}
              <div>
                <div class="text-sm font-medium text-muted-foreground">Feedback ID</div>
                <p class="mt-1 font-mono text-sm">{notification.feedbackId}</p>
              </div>
            {/if}
            {#if notification.chatSessionId}
              <div>
                <div class="text-sm font-medium text-muted-foreground">Chat Session ID</div>
                <p class="mt-1 font-mono text-sm">
                  {notification.chatSessionId}
                </p>
              </div>
            {/if}
            <div>
              <div class="text-sm font-medium text-muted-foreground">Read Status</div>
              <p class="mt-1">
                {#if isRead(notification)}
                  <span class="text-green-600 flex items-center gap-1">
                    <Check class="w-4 h-4" />
                    Read
                    {#if markingAsRead}
                      <span class="text-muted-foreground">(updating...)</span>
                    {/if}
                  </span>
                {:else}
                  <span class="text-blue-600">Unread</span>
                {/if}
              </p>
            </div>
          </div>
        </div>

        <!-- Actions -->
        {#if notification.clickAction}
          <div>
            <h3 class="text-lg font-semibold mb-3">Actions</h3>
            <Button onclick={handleActionClick} class="flex items-center gap-2">
              <ExternalLink class="w-4 h-4" />
              {m.adminNotifications_viewDetails()}
            </Button>
          </div>
        {/if}
      </div>
    </Card.Content>
  </Card.Root>
{/if}
