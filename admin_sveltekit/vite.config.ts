import { paraglide } from "@inlang/paraglide-sveltekit/vite";
import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';

export default defineConfig({
	plugins: [sveltekit(), paraglide({
		project: "./project.inlang",
		outdir: "./src/lib/paraglide"
	})],
	optimizeDeps: {
		exclude: ['@inlang/paraglide-sveltekit', '@inlang/paraglide-js', 'tailwindcss']
	},
	build: {
		rollupOptions: {
			output: {
				// Group all chunks into a single bundle
				manualChunks: (id) => {
					return 'fiaranow-app';
				}
			}
		}
	},
	server: {
		// Ensure service workers are served correctly in development
		fs: {
			allow: ['..']
		}
	}
});
