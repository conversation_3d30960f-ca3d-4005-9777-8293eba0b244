{"useTabs": false, "tabWidth": 2, "semi": true, "singleQuote": true, "quoteProps": "as-needed", "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "endOfLine": "lf", "printWidth": 130, "htmlWhitespaceSensitivity": "css", "plugins": ["prettier-plugin-svelte"], "overrides": [{"files": "*.svelte", "options": {"parser": "svelte", "svelteStrictMode": false, "svelteAllowShorthand": true, "svelteIndentScriptAndStyle": true}}]}