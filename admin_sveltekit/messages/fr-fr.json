{"$schema": "https://inlang.com/schema/inlang-message-format", "hello_world": "Bon<PERSON><PERSON>, {name} de fr-fr!", "tripsList_title": "Liste des trajets", "tripsList_liveFilter": "En cours", "tripsList_reservationsFilter": "Réservations", "tripsList_completedFilter": "Te<PERSON>in<PERSON>", "tripsList_startMarkerTitle": "<PERSON><PERSON><PERSON><PERSON>", "tripsList_arrivalMarkerTitle": "Arrivée", "tripsList_driverStartMarkerTitle": "<PERSON><PERSON><PERSON><PERSON>", "tripsList_noTripsAvailable": "Aucun trajet disponible", "mainPage_clockDriftErrorTitle": "L'horloge de votre ordinateur est sévèrement désynchronisée", "mainPage_clockDriftErrorDescription": "Veuillez synchroniser immédiatement l'horloge de votre système pour que l'application fonctionne correctement.", "mainPage_clockDriftWarningTitle": "L'horloge de votre ordinateur n'est pas synchronisée", "mainPage_clockDriftWarningDescription": "Veuillez synchroniser l'horloge de votre système pour que l'application fonctionne correctement.", "mainPage_adminAccessVerificationFailed": "Échec de la vérification de l'accès administrateur", "mainPage_navDashboard": "Tableau de bord", "mainPage_navRides": "Trajets", "mainPage_navRidesTrips": "Trajets", "mainPage_navRidesClients": "Clients", "mainPage_navRidesDrivers": "Chauffeurs", "mainPage_navFinance": "Finances", "mainPage_navFinancePayments": "Paiements", "mainPage_navReports": "Rapports", "mainPage_navReportsEvents": "Événements", "mainPage_navSupport": "Support", "mainPage_navSupportFeedbacks": "Commentaires", "mainPage_navSupportChats": "Discussions", "mainPage_navAdmin": "Administration", "mainPage_navAdminConfigurations": "Configurations", "mainPage_navAdminAccounts": "Administrateurs", "mainPage_navAdminNotifications": "Notifications", "mainPage_navDocuments": "Documents", "mainPage_navDocumentsAll": "Tous les Documents", "mainPage_navDocumentsExpiring": "Expirant <PERSON>", "mainPage_navVehicles": "Véhicules", "mainPage_navVehiclesAll": "Tous les Véhicules", "mainPage_navVehiclesAssignments": "Affectations", "mainPage_screenTooSmall": "Écran trop petit", "mainPage_accessDeniedTitle": "<PERSON><PERSON>ès refusé", "mainPage_accessDeniedDescription": "Votre compte administrateur a été désactivé.<br />V<PERSON>illez contacter un autre administrateur pour obtenir de l'aide.", "mainPage_signOutButton": "Se déconnecter", "mainPage_headerTitle": "Fiaranow Admin", "mainPage_serverEmulator": "É<PERSON><PERSON><PERSON>", "mainPage_serverLive": "Production", "mainPage_profileAlt": "Profil", "mainPage_menuMyAccount": "Mon Compte", "mainPage_menuProfile": "Profil", "mainPage_menuLanguage": "<PERSON><PERSON>", "mainPage_menuLanguageEnglish": "<PERSON><PERSON><PERSON>", "mainPage_menuLanguageFrench": "Français", "mainPage_menuLogout": "Déconnexion", "profile_logoutConfirmDescription": "Êtes-vous sûr de vouloir vous déconnecter ?", "profile_logoutConfirmButton": "Déconnexion", "profile_cancelButton": "Annuler", "mobileUserDetails_userNotFoundTitle": "Utilisateur non trouvé", "mobileUserDetails_userNotFoundDescription": "Cet utilisateur n'existe pas ou a été supprimé.", "mobileUserDetails_userNotFoundInstructions": "Veuillez vérifier l'URL ou revenir à la liste des clients.", "mobileUserDetails_profileCardTitle": "<PERSON><PERSON>", "mobileUserDetails_noEmailProvided": "Aucun e-mail fourni", "mobileUserDetails_phoneNumberLabel": "Numéro de téléphone", "mobileUserDetails_notProvided": "Non fourni", "mobileUserDetails_lastSeenLabel": "Dernière connexion", "mobileUserDetails_never": "<PERSON><PERSON>", "mobileUserDetails_userTypeLabel": "Type d'utilisateur", "mobileUserDetails_userTypePassenger": "Passager", "mobileUserDetails_userTypeDriver": "<PERSON><PERSON><PERSON>", "mobileUserDetails_userTypeNotSpecified": "Non spécifié", "mobileUserDetails_driverProfileCardTitle": "<PERSON><PERSON>", "mobileUserDetails_driverProfileCardDescription": "Détails du véhicule et de la vérification du chauffeur", "mobileUserDetails_disconnectingButtonText": "Déconnexion...", "mobileUserDetails_disconnectButtonText": "Déconnecter du trajet", "mobileUserDetails_revokeVerificationButtonText": "Révoquer la vérification", "mobileUserDetails_verifyDriverButtonText": "Véri<PERSON><PERSON> le chauffeur", "mobileUserDetails_vehicleBrandLabel": "Marque du véhicule", "mobileUserDetails_vehicleModelLabel": "Mod<PERSON><PERSON> du véhicule", "mobileUserDetails_vehicleColorLabel": "Couleur du véhicule", "mobileUserDetails_vehicleYearLabel": "<PERSON><PERSON> du véhicule", "mobileUserDetails_registrationNumberLabel": "Numéro d'immatriculation", "mobileUserDetails_maxPassengersLabel": "Passagers Maximum", "mobileUserDetails_maxPassengersValue": "{count} passagers", "mobileUserDetails_editDriverProfileButton": "Modifier le Profil Chauffeur", "mobileUserDetails_driverVerificationLabel": "Vérification du chauffeur", "mobileUserDetails_driverStatusVerified": "Vérifié", "mobileUserDetails_driverStatusPending": "En attente de vérification", "mobileUserDetails_currentTripStatusLabel": "Statut du trajet actuel", "mobileUserDetails_currentTripStatusActive": "Actuellement en trajet", "mobileUserDetails_disconnectConfirmTitle": "Décon<PERSON><PERSON> le chauffeur du trajet", "mobileUserDetails_disconnectConfirmDescription": "Êtes-vous sûr de vouloir déconnecter ce chauffeur ? Si le trajet est en cours, il sera annulé et le passager devra demander un nouveau trajet.", "mobileUserDetails_disconnectSuccessToast": "<PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON> du trajet avec succès", "mobileUserDetails_disconnectErrorToast": "Échec de la déconnexion du chauffeur du trajet", "mobileUserDetails_driverManagementTitle": "Gestion du Chauffeur", "mobileUserDetails_driverManagementDescription": "<PERSON><PERSON>rer la vérification du chauffeur et les affectations de véhicules", "mobileUserDetails_driverRatingTitle": "Évaluation du Chauffeur", "mobileUserDetails_manageVehiclesButton": "<PERSON><PERSON><PERSON> les Véhicules", "mobileUserDetails_noRatingsYet": "Aucune évaluation pour le moment", "mobileUserDetails_tripsLabel": "trajets", "mobileUserListItem_noPhoneNumber": "Aucun numéro de téléphone", "mobileUserListItem_driverStatusActive": "Actif", "mobileUserListItem_driverStatusInactive": "Inactif", "mobileUserListItem_driverVerified": "Vérifié", "mobileUserListItem_driverUnverified": "Non vérifié", "mobileUserListItem_newDriverBadge": "Nouveau Chauffeur", "mobileUserListItem_removeButtonSrLabel": "<PERSON><PERSON><PERSON><PERSON>", "mobileUserListItem_detailsButtonSrLabel": "Détails", "clientsList_pageTitle": "Liste des Clients", "clientsList_onlineToggleLabel": "En ligne", "clientsList_countListed_one": "{count} client listé", "clientsList_countListed_other": "{count} clients listés", "clientsList_noClientsAvailable": "Aucun client disponible", "driversList_pageTitle": "Liste des Chauffeurs", "driversList_onlineToggleLabel": "En ligne", "driversList_inactiveToggleLabel": "Inactif", "driversList_pendingToggleLabel": "Non vérifié", "driversList_countListed_one": "{count} chauffeur listé", "driversList_countListed_other": "{count} chauffeurs listés", "driversList_noDriversAvailable": "Aucun chauffeur disponible", "driversList_showingUnverifiedOnly": "Affichage des chauffeurs non vérifiés uniquement", "driversList_includingInactive": "Incluant les chauffeurs inactifs", "driversList_onlineOnly": "En ligne uniquement", "driversList_inactiveFilterDisabledTooltip": "Le filtre Actif/Inactif ne s'applique pas aux chauffeurs en attente", "driversList_filterByTagsPlaceholder": "Filtrer par étiquettes...", "driversList_sortByLabel": "Trier par :", "driversList_sortByRating": "Évaluation", "driversList_sortByName": "Nom", "tripDetails_notFoundTitle": "Trajet introuvable", "tripDetails_notFoundDescription": "Le trajet avec l'ID={tripId} n'a pas pu être trouvé", "tripDetails_notFoundExplanation": "Ce trajet a peut-être été supprimé ou vous avez suivi un lien invalide.", "tripDetails_paymentListenerErrorToast": "Erreur lors de la mise en place de l'écouteur de paiement", "tripDetails_paymentSetupErrorToast": "Erreur lors de la mise en place de l'interface de paiement", "tripDetails_deleteSuccessToast": "Trajet supprimé avec succès", "tripDetails_deleteErrorToast": "Échec de la suppression du trajet", "tripDetails_cancelSuccessToast": "<PERSON><PERSON><PERSON> annulé avec succès", "tripDetails_cancelErrorToast": "Échec de l'annulation du trajet", "tripDetails_paymentSuccessToast": "Paiement enregistré avec succès", "tripDetails_removeSkippedDriverSuccessToast": "Cha<PERSON>eur ignoré supprimé avec succès", "tripDetails_removeSkippedDriverErrorToast": "Échec de la suppression du chauffeur ignoré", "tripDetails_title": "<PERSON><PERSON><PERSON> Trajet", "tripDetails_creationDate": "Date de création", "tripDetails_tripIdLabel": "ID du Trajet", "tripDetails_receivePaymentButton": "Recevoir le Paiement", "tripDetails_assignDriverButton": "Assigner un Chauffeur", "tripDetails_deletingButton": "Suppression...", "tripDetails_deleteButton": "<PERSON><PERSON><PERSON><PERSON> le Trajet", "tripDetails_followingButton": "Suivi...", "tripDetails_followButton": "Suivre", "tripDetails_cancellingButton": "Annulation...", "tripDetails_cancelButton": "<PERSON><PERSON><PERSON> le Trajet", "tripDetails_clientCardTitle": "Client", "tripDetails_driverCardTitle": "<PERSON><PERSON><PERSON>", "tripDetails_noDriverAssigned": "Aucun chauffeur assigné", "tripDetails_skippedDriversCardTitle": "Chauffeur<PERSON> I<PERSON> ({count})", "tripDetails_noSkippedDrivers": "Aucun chauffeur n'a ignoré ce trajet", "tripDetails_detailsCardTitle": "Détails", "tripDetails_tripStatusLabel": "Statut", "tripDetails_tripStatusPreparing": "Préparation", "tripDetails_tripStatusRequestingDriver": "Recherche de chauffeur", "tripDetails_tripStatusReserved": "Réservé", "tripDetails_tripStatusDriverApproaching": "Chauffeur en approche", "tripDetails_tripStatusDriverAwaiting": "Chauffeur en attente", "tripDetails_tripStatusInProgress": "En cours", "tripDetails_tripStatusCompleted": "<PERSON><PERSON><PERSON><PERSON>", "tripDetails_tripStatusCancelled": "<PERSON><PERSON><PERSON>", "tripDetails_tripStatusPaid": "<PERSON><PERSON>", "tripDetails_tripStatusUnknown": "Statut Inconnu", "tripDetails_paymentStatusLabel": "Paiement", "tripDetails_paymentStatusAwaiting": "En attente de paiement", "tripDetails_paymentStatusPaid": "<PERSON><PERSON>", "tripDetails_paymentStatusUnknown": "Statut de Paiement Inconnu", "tripDetails_pickupAddressLabel": "<PERSON><PERSON><PERSON>", "tripDetails_dropoffAddressLabel": "<PERSON><PERSON><PERSON> d'a<PERSON>", "tripDetails_pickupTimeLabel": "<PERSON><PERSON>", "tripDetails_dropoffTimeLabel": "<PERSON>ure d'arrivée", "tripDetails_distanceLabel": "Distance", "tripDetails_durationLabel": "<PERSON><PERSON><PERSON>", "tripDetails_driverStartTimeLabel": "<PERSON><PERSON> de d<PERSON><PERSON>t chauffeur", "tripDetails_priceLabel": "Prix", "tripDetails_paymentIntentLabel": "ID d'Intention de Paiement", "eventsLayout_title": "Événements", "eventsLayout_description": "Liste des événements", "eventsLayout_serviceStatusFilter": "Statut du Service", "eventsLayout_tripRejectedFilter": "<PERSON><PERSON><PERSON>", "eventsLayout_unknownUser": "Inconnu", "eventsLayout_unknownDriver": "<PERSON><PERSON><PERSON> inconnu", "eventsLayout_serviceStatusEvent": "Statut du Service", "eventsLayout_tripRejectedEvent": "<PERSON><PERSON><PERSON>", "eventsLayout_noEventsFound": "Aucun événement trouvé", "eventsList_selectEventPrompt": "Sélectionnez un événement dans la liste pour afficher ses détails", "eventDetail_title": "Détails de l'événement", "eventDetail_description": "Afficher les informations sur cet événement", "eventDetail_eventTypeLabel": "Type d'événement", "eventDetail_serviceStatusEvent": "Statut du Service", "eventDetail_tripRejectedEvent": "<PERSON><PERSON><PERSON>", "eventDetail_driverLabel": "<PERSON><PERSON><PERSON>", "eventDetail_timestampLabel": "Horodatage", "eventDetail_reasonLabel": "<PERSON>son", "eventDetail_noReasonProvided": "Aucune raison fournie", "eventDetail_customReasonPrefix": "<PERSON>son personnalisée:", "eventDetail_tripInfoLabel": "Informations sur le trajet", "eventDetail_tripIdPrefix": "ID du trajet:", "eventDetail_tripFromPrefix": "De:", "eventDetail_tripToPrefix": "Vers:", "eventDetail_additionalInfoLabel": "Informations supplémentaires", "eventDetail_eventNotFound": "Événement non trouvé", "paymentsLayout_title": "Paiements", "paymentsLayout_description": "Liste des paiements", "paymentsLayout_noPaymentsFound": "Aucun paiement trouvé", "paymentsList_selectPaymentPrompt": "Sélectionnez un paiement dans la liste pour voir ses détails", "paymentDetailPage_paymentNotFoundTitle": "Paiement non trouvé", "paymentDetailPage_paymentNotFoundDescription": "Le paiement avec l'ID={paymentId} n'a pas pu être trouvé", "paymentDetailPage_paymentNotFoundHint": "Ce paiement a peut-être été supprimé ou vous avez peut-être suivi un lien invalide.", "paymentDetailsComponent_title": "<PERSON>é<PERSON> du Paiement", "paymentDetailsComponent_createdOn": "<PERSON><PERSON><PERSON> {date}", "paymentDetailsComponent_changeStatusButton": "Changer <PERSON>", "paymentDetailsComponent_customerHeading": "Client", "paymentDetailsComponent_driverHeading": "<PERSON><PERSON><PERSON>", "paymentDetailsComponent_paymentInfoHeading": "Informations Paiement", "paymentDetailsComponent_amountLabel": "<PERSON><PERSON>", "paymentDetailsComponent_amountDueLabel": "<PERSON>ant <PERSON>", "paymentDetailsComponent_discountLabel": "Réduction", "paymentDetailsComponent_discountReasonLabel": "Raison : {reason}", "paymentDetailsComponent_requestedMethodLabel": "<PERSON><PERSON><PERSON><PERSON>", "paymentDetailsComponent_paidWithMethodLabel": "Payé avec {method}", "paymentDetailsComponent_receiptNumberLabel": "<PERSON><PERSON><PERSON><PERSON>", "paymentDetailsComponent_paymentMethodDetailsHeading": "<PERSON><PERSON><PERSON> M<PERSON>th<PERSON>", "paymentDetailsComponent_detailsLabel": "Détails", "paymentDetailsComponent_paymentTimelineHeading": "Chronologie Paiement", "paymentDetailsComponent_receivedLabel": "<PERSON><PERSON><PERSON>", "paymentDetailsComponent_byUserLabel": "par {user}", "paymentDetailsComponent_processedLabel": "Traité", "paymentDetailsComponent_completedLabel": "<PERSON><PERSON><PERSON><PERSON>", "paymentDetailsComponent_additionalInfoHeading": "Informations Supplémentaires", "paymentDetailsComponent_updateStatusErrorToast": "Échec mise à jour statut paiement", "paymentDetailsComponent_updateStatusSuccessToast": "Statut paiement mis à jour : {status}", "paymentDetailsComponent_changeStatusDialogTitle": "Changer <PERSON><PERSON><PERSON>", "paymentDetailsComponent_changeStatusDialogDescription": "Sélectionnez un nouveau statut pour ce paiement", "paymentDetailsComponent_cancelButton": "Annuler", "paymentDetailsComponent_paidWithMethodRemarkLabel": "<PERSON><PERSON><PERSON>", "paymentDetailsComponent_transactionIdLabel": "ID Transaction", "paymentDetailsComponent_remarkLabel": "<PERSON><PERSON><PERSON>", "financeReports_title": "Rapports Financiers", "financeReports_description": "Visualiser et générer des rapports financiers", "financeReports_comingSoon": "Cette fonctionnalité sera bientôt disponible.", "adminAccountsLayout_title": "Utilisateurs Admin", "adminAccountsLayout_description": "Liste des utilisateurs administrateurs", "adminAccountsLayout_onlineFilter": "En ligne", "adminAccountsLayout_inactiveFilter": "Inactif", "adminAccountsLayout_noUsersMatchFilter": "Aucun utilisateur admin ne correspond aux filtres actuels", "adminAccountsList_title": "<PERSON><PERSON><PERSON> Compt<PERSON>", "adminAccountsList_selectAccountPrompt": "Sélectionnez un compte pour voir les détails", "adminUserListItem_statusActive": "Actif", "adminUserListItem_statusInactive": "Inactif", "adminUserListItem_detailsButtonLabel": "Détails", "adminUserDetailsComponent_lastSeenNever": "<PERSON><PERSON>", "adminUserDetailsComponent_toggleActivationFailedToast": "Échec de la bascule de l'activation de l'utilisateur", "adminUserDetailsComponent_toggleActivationSuccessToast": "Utilisateur admin {status} avec succès", "adminUserDetailsComponent_notFoundTitle": "Utilisateur Ad<PERSON>", "adminUserDetailsComponent_notFoundDescription": "Cet utilisateur admin n'existe pas ou a été supprimé.", "adminUserDetailsComponent_notFoundPrompt": "Veuillez vérifier l'URL ou retourner à la liste des utilisateurs admin.", "adminUserDetailsComponent_profileTitle": "<PERSON><PERSON>", "adminUserDetailsComponent_profileDescription": "Détails du compte et statut", "adminUserDetailsComponent_deactivateButton": "Dés<PERSON>r le Compte", "adminUserDetailsComponent_activateButton": "<PERSON><PERSON>", "adminUserDetailsComponent_accountStatusLabel": "Statut du Compte", "adminUserDetailsComponent_lastSeenLabel": "Dernière Connexion", "adminUserDetailsComponent_onlineStatusLabel": "Statut en Ligne", "adminUserDetailsComponent_onlineStatusOnline": "En ligne", "adminUserDetailsComponent_onlineStatusOffline": "<PERSON><PERSON> ligne", "adminUserDetailsComponent_deactivateDialogTitle": "Désactiver le Compte Admin", "adminUserDetailsComponent_activateDialogTitle": "<PERSON><PERSON> <PERSON> Compte <PERSON>", "adminUserDetailsComponent_confirmToggleDescription": "Êtes-vous sûr de vouloir {action} ce compte admin ? {consequence}", "adminUserDetailsComponent_actionDeactivate": "d<PERSON><PERSON><PERSON>", "adminUserDetailsComponent_actionActivate": "activer", "adminUserDetailsComponent_consequenceDeactivate": "L'utilisateur ne pourra plus accéder au panneau d'administration.", "adminUserDetailsComponent_consequenceActivate": "L'utilisateur retrouvera l'accès au panneau d'administration.", "adminConfigsLayout_listTitle": "Liste des Configurations", "adminConfigsLayout_noConfigsAvailable": "Aucune configuration disponible", "adminConfigsList_selectConfigPrompt": "Sélectionnez une configuration dans la barre latérale pour voir ses détails", "adminConfigDetailPage_notFoundTitle": "Configuration Introuvable", "adminConfigDetailPage_notFoundDescription": "La configuration demandée n'a pas pu être trouvée", "adminConfigDetailsComponent_tripConfigTitle": "Configuration des Trajets", "adminConfigDetailsComponent_generalConfigTitle": "Configuration Générale", "adminConfigDetailsComponent_tripConfigDescription": "Paramètres liés aux trajets, y compris les coûts, les horaires et les paramètres de recherche de chauffeurs", "adminConfigDetailsComponent_generalConfigDescription": "Paramètres généraux", "adminConfigDetailsComponent_valueLabel": "<PERSON><PERSON>", "adminConfigForm_tripConfigDescription": "Configurer les paramètres liés aux trajets", "adminConfigForm_generalConfigDescription": "Configurer les paramètres généraux", "adminConfigForm_costPerKilometerLabel": "Coût par Kilomètre (Ar)", "adminConfigForm_costPerHourLabel": "Coût par Heure (Ar)", "adminConfigForm_minimumTripCostLabel": "Coût Minimum du Trajet (Ar)", "adminConfigForm_waitTimeAfterExtraPaymentLabel": "Temps d'attente après paiement supplémentaire (minutes)", "adminConfigForm_costPerExtraWaitChunkLabel": "Coût par Période d'Attente Supplémentaire (Ar)", "adminConfigForm_cancelCostPreStartLabel": "Coût d'Annulation Avant <PERSON> (Ar)", "adminConfigForm_nearbyDriverRadiusLabel": "Rayon des Chauffeurs à Proximité (mètres)", "adminConfigForm_maxPassengerCountLabel": "Nombre Maximum de Passagers", "adminConfigForm_maxPassengerCountDescription": "Nombre maximum de passagers autorisés par trajet", "adminConfigForm_hideInProgressCostsLabel": "Masquer les estimations de coût aux passagers pendant les trajets en cours", "adminConfigForm_hideInProgressCostsDescription": "Lorsqu'activé, les coûts des trajets seront masqués aux passagers pendant que le trajet est en cours. Les chauffeurs verront toujours le coût. Les passagers verront seulement le coût final après la fin du trajet.", "adminConfigForm_saveButton": "Enregistrer les Modifications", "adminConfigForm_saveSuccessToast": "Configuration enregistrée avec succès", "adminConfigForm_saveErrorToast": "Échec de l'enregistrement de la configuration", "adminConfigForm_fallbackTitle": "Détails de la Configuration", "adminConfigForm_fallbackContent": "Aucune configuration sélectionnée.", "adminNotifications_pageTitle": "Notifications Admin", "adminNotifications_pageDescription": "Notifications système pour les trajets, les commentaires et les messages de chat", "adminNotifications_noNotificationsTitle": "Aucune notification pour le moment", "adminNotifications_noNotificationsDescription": "Les nouvelles notifications apparaîtront ici lorsqu'elles arriveront", "adminNotifications_viewDetails": "Voir les détails", "adminNotifications_typeChatMessage": "Message de Chat", "adminNotifications_typeTripBooking": "Réservation de Trajet", "adminNotifications_typeFeedbackSubmitted": "Commentaire Soumis", "adminNotifications_typeTripCancelled": "<PERSON><PERSON><PERSON>", "adminNotifications_typeReservationReminder": "Rappel de Réservation", "enableRingtoneByDefault": "Activer la Sonnerie par Défaut", "enableRingtoneByDefaultHelp": "Lors<PERSON><PERSON>'activé, les nouveaux utilisateurs auront les notifications avec sonnerie activées automatiquement", "enableDriverMovingNotification": "Notification Chauffeur en Mouvement", "enableDriverMovingNotificationHelp": "Notifier les passagers lorsque leur chauffeur commence à se déplacer vers le lieu de prise en charge", "enableDriverArrivedNotification": "Notification Chauffeur <PERSON>", "enableDriverArrivedNotificationHelp": "Notifier les passagers lorsque leur chauffeur est arrivé au lieu de prise en charge", "enableTripPaidNotification": "Notification Paiement du Trajet", "enableTripPaidNotificationHelp": "Notifier les passagers lorsque le paiement de leur trajet est terminé (toujours silencieux)", "enableReservationReminders": "Rappels de Réservation", "enableReservationRemindersHelp": "Envoyer des notifications de rappel avant les trajets programmés", "reservationReminderTimes": "Heures de Rappel", "minutesBefore": "minutes avant", "remove": "<PERSON><PERSON><PERSON><PERSON>", "addReminderTime": "A<PERSON>ter une Heure de Rappel", "adminConfigDetailsComponent_chatConfigTitle": "Configuration du Chat", "adminConfigDetailsComponent_passengerNotificationsTitle": "Notifications des Passagers", "adminConfigDetailsComponent_chatConfigDescription": "Paramètres actuels du système de chat et configuration du support", "adminConfigDetailsComponent_passengerNotificationsDescription": "Paramètres actuels des notifications passagers et préférences de sonnerie", "adminConfigForm_chatConfigDescription": "Configurer les paramètres du système de chat, heures de support et options de réponse automatique", "adminConfigForm_passengerNotificationsDescription": "Configurer les paramètres de notification des passagers et préférences de sonnerie", "adminConfigDetailsComponent_yes": "O<PERSON>", "adminConfigDetailsComponent_no": "Non", "adminConfigDetailsComponent_none": "Aucun", "adminConfigDetailsComponent_minutes": "minutes", "adminConfigDetailsComponent_sunday": "<PERSON><PERSON>", "adminConfigDetailsComponent_monday": "<PERSON>n", "adminConfigDetailsComponent_tuesday": "Mar", "adminConfigDetailsComponent_wednesday": "<PERSON><PERSON>", "adminConfigDetailsComponent_thursday": "<PERSON><PERSON>", "adminConfigDetailsComponent_friday": "Ven", "adminConfigDetailsComponent_saturday": "Sam", "adminConfigForm_enableRingtoneByDefaultLabel": "Activer la Sonnerie par Défaut", "adminConfigForm_enableRingtoneByDefaultDescription": "Lors<PERSON><PERSON>'activé, les nouveaux utilisateurs auront les notifications avec sonnerie activées automatiquement", "adminConfigForm_enableDriverMovingNotificationLabel": "Notification Chauffeur en Mouvement", "adminConfigForm_enableDriverMovingNotificationDescription": "Notifier les passagers lorsque leur chauffeur commence à se déplacer vers le lieu de prise en charge", "adminConfigForm_enableDriverArrivedNotificationLabel": "Notification Chauffeur <PERSON>", "adminConfigForm_enableDriverArrivedNotificationDescription": "Notifier les passagers lorsque leur chauffeur est arrivé au lieu de prise en charge", "adminConfigForm_enableTripPaidNotificationLabel": "Notification Paiement du Trajet", "adminConfigForm_enableTripPaidNotificationDescription": "Notifier les passagers lorsque le paiement de leur trajet est terminé (toujours silencieux)", "adminConfigForm_enableReservationRemindersLabel": "Rappels de Réservation", "adminConfigForm_enableReservationRemindersDescription": "Envoyer des notifications de rappel avant les trajets programmés", "adminConfigForm_reservationReminderTimesLabel": "Heures de Rappel", "adminConfigForm_minutesBeforeLabel": "minutes avant", "adminConfigForm_showAdminNamesInChatLabel": "Afficher les Noms des Admins dans le Chat", "adminConfigForm_defaultChatCategoriesLabel": "Catégories de Chat par Défaut", "adminConfigForm_autoArchiveChatAfterDaysLabel": "Archivage Automatique Après (jours)", "adminConfigForm_enableChatNotificationsLabel": "Activer les Notifications de Chat", "adminConfigForm_enableAutoReplyForOffHoursLabel": "Activer la Réponse Automatique Hors Heures", "adminConfigForm_autoReplyMessageLabel": "Message de Réponse Automatique", "adminConfigForm_supportHoursLabel": "Heures de Support", "adminConfigForm_supportDaysLabel": "Jours de Support", "adminConfigForm_maxImagesPerMessageLabel": "Nombre Max d'Images par Message", "adminConfigForm_maxImageSizeMBLabel": "<PERSON>lle <PERSON> d'Image (MB)", "mark_as_seen": "Marquer comme Vu", "mark_as_addressed": "Marquer comme Traité", "archive": "Archiver", "user_information": "Informations Utilisateur", "name": "Nom", "unknown_user": "Utilisa<PERSON><PERSON>", "phone": "Téléphone", "email": "E-mail", "rating": "Évaluation", "trip_information": "Informations du Trajet", "trip_id": "ID du Trajet", "pickup": "Prise en charge", "destination": "Destination", "date": "Date", "feedback_message": "Message de Commentaire", "no_message_provided": "Aucun message fourni", "attached_images": "Images Jointes", "creating_chat": "Création du Chat", "initiate_chat": "<PERSON><PERSON><PERSON><PERSON> Chat", "view_chat": "Voir le Chat", "feedback_not_found": "Commentaire Introuvable", "feedbacks_title": "Commentaires des utilisateurs", "feedbacks_description": "<PERSON><PERSON>rer les commentaires et avis des utilisateurs", "feedback_filter_trip": "Voyage", "feedback_filter_application": "Application", "feedback_filter_new": "Nouveau", "feedback_status_new": "Nouveau", "feedback_status_seen": "Vu", "feedback_status_addressed": "Traité", "feedback_status_archived": "Archivé", "feedback_type_trip": "Voyage", "feedback_type_application": "Application", "no_feedbacks_found": "Aucun commentaire trouvé", "select_feedback": "Sélectionner un commentaire", "select_feedback_description": "Choisissez un commentaire dans la liste pour voir les détails", "no_feedback_selected": "Aucun commentaire sélectionné", "select_feedback_from_list": "Sélectionnez un commentaire dans la liste pour voir les détails", "no_message": "Aucun message fourni", "trip_feedback": "Commentaire de voyage", "application_feedback": "Commentaire d'application", "submitted_on": "<PERSON><PERSON><PERSON> le", "feedback_status_updated": "Statut du commentaire mis à jour", "error_updating_feedback": "E<PERSON>ur lors de la mise à jour du commentaire", "chat_session_created": "Session de chat créée", "error_creating_chat": "<PERSON><PERSON><PERSON> lors de la création du chat", "trip_feedback_from": "Commentaire de voyage de", "app_feedback_from": "Commentaire d'application de", "linked_feedback": "Commentaire lié", "view_feedback": "Voir le commentaire", "driverEdit_pageTitle": "Modifier le Profil Chauffeur", "driverEdit_driverNotFoundTitle": "<PERSON><PERSON><PERSON>", "driverEdit_driverNotFoundDescription": "Le chauffeur demandé n'a pas pu être trouvé ou n'a pas de profil de chauffeur.", "driverEdit_backToRidesButton": "Retour aux Trajets", "driverEdit_editProfileTitle": "Modifier le Profil Chauffeur", "driverEdit_editProfileDescription": "Mettre à jour les paramètres du chauffeur pour {driverName}", "driverEdit_unknownDriver": "<PERSON><PERSON><PERSON>", "driverEdit_vehicleInfoHeading": "Informations du Véhicule", "driverEdit_brandLabel": "Marque", "driverEdit_modelLabel": "<PERSON><PERSON><PERSON><PERSON>", "driverEdit_colorLabel": "<PERSON><PERSON><PERSON>", "driverEdit_yearLabel": "<PERSON><PERSON>", "driverEdit_registrationNumberLabel": "Numéro d'Immatriculation", "driverEdit_capacitySettingsHeading": "Paramètres de Capacité", "driverEdit_maxPassengersLabel": "Passagers Maximum", "driverEdit_maxPassengersDescription": "Le nombre maximum de passagers que ce chauffeur peut accueillir", "driverEdit_cancelButton": "Annuler", "driverEdit_savingButton": "Enregistrement...", "driverEdit_saveChangesButton": "Enregistrer les Modifications", "driverEdit_updateSuccessToast": "Profil du chauffeur mis à jour avec succès", "driverEdit_updateErrorToast": "Échec de la mise à jour du profil du chauffeur", "driverEdit_saveSuccessToast": "Profil du chauffeur mis à jour avec succès", "driverEdit_saveErrorToast": "Échec de la mise à jour du profil du chauffeur", "driverEdit_loadingError": "<PERSON><PERSON>ur lors du chargement du profil du chauffeur", "driverEdit_savingChanges": "Enregistrement des modifications...", "driverEdit_tagManagerTitle": "Étiquettes du Chauffeur", "driverEdit_tagManagerDescription": "<PERSON><PERSON><PERSON> les étiquettes pour ce chauffeur", "chatsLayout_title": "Chats d'Assistance", "chatsLayout_description": "<PERSON><PERSON><PERSON> les conversations d'assistance client", "chatsLayout_newChatButton": "Nouveau Chat", "chatsLayout_searchPlaceholder": "Rechercher des chats...", "chatsLayout_activeChatsFilter": "Actifs", "chatsLayout_allChatsFilter": "Tous", "chatsLayout_statusActive": "Actif", "chatsLayout_statusResolved": "R<PERSON>ol<PERSON>", "chatsLayout_statusArchived": "Archivé", "chatsLayout_noChatsFound": "Aucun chat trouvé", "chatsLayout_noChatsFoundSearch": "Aucun chat trouvé pour votre recherche", "chatsLayout_unknownUser": "Utilisa<PERSON><PERSON>", "chatsPage_selectChatTitle": "Sélectionner un Chat", "chatsPage_selectChatDescription": "Choisissez un chat dans la liste pour voir la conversation", "chatsPage_noChatSelected": "Aucun chat sélectionné", "chatsPage_selectChatFromList": "Sélectionnez un chat dans la liste pour voir les détails", "chatDetails_errorSendingMessageToast": "Erreur lors de l'envoi du message", "chatDetails_errorLoadingMessagesToast": "Erreur lors du chargement des messages", "chatDetails_statusUpdatedSuccessToast": "Statut du chat mis à jour", "chatDetails_errorUpdatingStatusToast": "Erreur lors de la mise à jour du statut", "chatDetails_chatWithLabel": "Chat avec", "chatDetails_markResolvedButton": "Marquer comme Résolu", "chatDetails_reopenChatButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chatDetails_archiveChatButton": "<PERSON><PERSON>", "chatDetails_youLabel": "Vous", "chatDetails_messageInputPlaceholder": "Tapez votre message...", "chatDetails_imageUploadTooltip": "Téléchargement d'image bientôt disponible", "chatDetails_notFoundMessage": "Chat non trouvé", "chatDetails_noMessagesYet": "Aucun message pour le moment", "chatDetails_statusActiveLabel": "Actif", "chatDetails_statusResolvedLabel": "R<PERSON>ol<PERSON>", "chatDetails_statusArchivedLabel": "Archivé", "chatDetails_appFeedbackLabel": "Commentaire d'Application", "documentsLayout_pageTitle": "Gestion des Documents", "documentsLayout_title": "Documents", "documentsLayout_description": "Examiner et gérer les documents des chauffeurs", "documentsLayout_errorPrefix": "Erreur : ", "documentsLayout_searchPlaceholder": "Rechercher des documents...", "documentsLayout_pendingFilter": "En attente", "documentsLayout_approvedFilter": "Approu<PERSON><PERSON>", "documentsLayout_rejectedFilter": "<PERSON><PERSON><PERSON>", "documentsLayout_documentCount_one": "{count} document", "documentsLayout_documentCount_other": "{count} documents", "documentsLayout_noDocumentsFound": "Aucun document trouvé", "documentsLayout_unknownDriver": "<PERSON><PERSON><PERSON> inconnu", "documentsLayout_daysExpiringSuffix": "{days}j", "documentsLayout_expiredBadge": "Expiré", "documentsPage_selectDocumentTitle": "Sélectionner un document", "documentsPage_selectDocumentDescription": "Choisissez un document dans la liste pour voir ses détails", "documentDetails_pageTitle": "Examiner le Document", "documentDetails_statusDescriptionPending": "Examinez et approuvez ou rejetez ce document", "documentDetails_statusDescriptionApproved": "Le document a été approuvé", "documentDetails_statusDescriptionRejected": "Le document a été rejeté", "documentDetails_documentDetailsTitle": "<PERSON>é<PERSON> du Document", "documentDetails_typeLabel": "Type", "documentDetails_statusLabel": "Statut", "documentDetails_uploadedLabel": "Télécharg<PERSON>", "documentDetails_expiryDateLabel": "Date d'Expiration", "documentDetails_driverNotesLabel": "<PERSON> <PERSON> Chauffeur", "documentDetails_reviewedLabel": "Examiné", "documentDetails_expiredDaysAgo": "Expiré il y a {days} jours", "documentDetails_expiresInDays": "Expire dans {days} jours", "documentDetails_validForDays": "Valide pour {days} jours", "documentDetails_driverInfoTitle": "<PERSON><PERSON> du Chauffeur", "documentDetails_nameLabel": "Nom", "documentDetails_emailLabel": "E-mail", "documentDetails_phoneLabel": "Téléphone", "documentDetails_driverStatusLabel": "Statut du Chauffeur", "documentDetails_notProvided": "Non fourni", "documentDetails_activeStatus": "Actif", "documentDetails_inactiveStatus": "Inactif", "documentDetails_verifiedStatus": "Vérifié", "documentDetails_unverifiedStatus": "Non vérifié", "documentDetails_viewDriverProfileButton": "Voir le Profil du Chauffeur", "documentDetails_driverNotFoundText": "Chauffeur avec l'ID", "documentDetails_driverNotFoundInTenant": "non trouvé dans les chauffeurs du locataire actuel.", "documentDetails_driverNotAssignedText": "<PERSON><PERSON> pourrait signifier que le chauffeur n'a pas encore été assigné à ce locataire.", "documentDetails_noDriverInfoAvailable": "Aucune information de chauffeur disponible pour ce document.", "documentDetails_reviewDocumentTitle": "Examiner le Document", "documentDetails_documentExpiredWarning": "Ce document a déjà expiré et ne peut pas être approuvé.", "documentDetails_adminNotesLabel": "Notes Admin (Optionnel)", "documentDetails_adminNotesPlaceholder": "Ajoutez des notes sur cet examen de document...", "documentDetails_approveButton": "Approuver le Document", "documentDetails_rejectButton": "Rejeter le Document", "documentDetails_reviewHistoryTitle": "Historique d'Examen", "documentDetails_reviewedOnLabel": "Examin<PERSON> le", "documentDetails_reviewedByLabel": "Examiné par", "documentDetails_adminNotesHistoryLabel": "Notes Admin", "documentDetails_documentPreviewTitle": "Aperçu du Document", "documentDetails_failedToLoadImage": "Échec du chargement de l'image.", "documentDetails_previewNotAvailable": "Aperçu du document non disponible pour ce type de fichier", "documentDetails_onlyImagesPreviewable": "Seuls les fichiers image peuvent être prévisualisés", "documentDetails_approvedSuccessToast": "Document approuvé avec succès", "documentDetails_rejectedSuccessToast": "Document rejeté avec succès", "documentDetails_updateStatusErrorToast": "Échec de la mise à jour du statut du document", "expiringDocuments_pageTitle": "Documents Expirants", "expiringDocuments_pageHeading": "Documents Expirants", "expiringDocuments_pageDescription": "Documents expirés ou expirant dans les {daysThreshold} prochains jours", "expiringDocuments_errorPrefix": "Erreur : ", "expiringDocuments_expiredTitle": "Expiré", "expiringDocuments_expiring7DaysTitle": "Expire dans 7 jours", "expiringDocuments_expiring14DaysTitle": "Expire dans 14 jours", "expiringDocuments_expiring30DaysTitle": "Expire dans 30 jours", "expiringDocuments_expiredSectionTitle": "Documents Expirés ({count})", "expiringDocuments_expiring7DaysSectionTitle": "Expirant dans les 7 Jours ({count})", "expiringDocuments_otherExpiringTitle": "Autres Documents Expirants", "expiringDocuments_driverPrefix": "Chauffeur : ", "expiringDocuments_unknownDriver": "Inconnu", "expiringDocuments_expiredTimeAgo": "Expiré", "expiringDocuments_expiresInDays_one": "Expire dans {days} jour", "expiringDocuments_expiresInDays_other": "Expire dans {days} jours", "expiringDocuments_reviewButton": "Examiner", "expiringDocuments_allUpToDateTitle": "Tous les documents sont à jour", "expiringDocuments_allUpToDateDescription": "Aucun document n'est expiré ou n'expire dans les {daysThreshold} prochains jours", "vehiclesAdd_pageTitle": "Ajouter un Nouveau Véhicule", "vehiclesAdd_cardTitle": "Ajouter un Nouveau Véhicule", "vehiclesAdd_cardDescription": "<PERSON><PERSON>er un nouveau véhicule pour votre flotte", "vehiclesAdd_brandLabel": "Marque", "vehiclesAdd_brandPlaceholder": "Sélectionner la marque...", "vehiclesAdd_customBrandPlaceholder": "Entrer le nom de la marque...", "vehiclesAdd_otherBrandOption": "<PERSON><PERSON> (préciser)", "vehiclesAdd_brandRequiredError": "La marque est requise", "vehiclesAdd_modelLabel": "<PERSON><PERSON><PERSON><PERSON>", "vehiclesAdd_modelPlaceholder": "ex: <PERSON><PERSON><PERSON>", "vehiclesAdd_modelRequiredError": "Le modèle est requis", "vehiclesAdd_colorLabel": "<PERSON><PERSON><PERSON>", "vehiclesAdd_colorPlaceholder": "Sélectionner la couleur...", "vehiclesAdd_colorRequiredError": "La couleur est requise", "vehiclesAdd_colorBeige": "Beige", "vehiclesAdd_colorBlack": "Noir", "vehiclesAdd_colorBlue": "Bleu", "vehiclesAdd_colorBrown": "<PERSON><PERSON>", "vehiclesAdd_colorGold": "Or", "vehiclesAdd_colorGray": "<PERSON><PERSON>", "vehiclesAdd_colorGreen": "<PERSON>ert", "vehiclesAdd_colorOrange": "Orange", "vehiclesAdd_colorPurple": "Violet", "vehiclesAdd_colorRed": "Rouge", "vehiclesAdd_colorSilver": "Argent", "vehiclesAdd_colorWhite": "<PERSON>", "vehiclesAdd_colorYellow": "Jaune", "vehiclesAdd_yearLabel": "<PERSON><PERSON>", "vehiclesAdd_yearInvalidError": "Veuillez entrer une année entre 1900 et {currentYear}", "vehiclesAdd_registrationLabel": "Numéro d'Immatriculation", "vehiclesAdd_registrationPlaceholder": "ex: 1234 TBC", "vehiclesAdd_registrationRequiredError": "Le numéro d'immatriculation est requis", "vehiclesAdd_maxPassengersLabel": "Passagers Maximum", "vehiclesAdd_maxPassengersDescription": "Nombre de sièges passagers (hors conducteur)", "vehiclesAdd_maxPassengersInvalidError": "Veuillez entrer une capacité de passagers valide (1-50)", "vehiclesAdd_infoTitle": "ℹ️ Information", "vehiclesAdd_infoPoint1": "• Cela créera un nouveau véhicule", "vehiclesAdd_infoPoint2": "• Le véhicule sera automatiquement disponible", "vehiclesAdd_infoPoint3": "• Vous pourrez assigner des chauffeurs au véhicule après création", "vehiclesAdd_infoPoint4": "• Les détails du véhicule peuvent être modifiés ultérieurement", "vehiclesAdd_cancelButton": "Annuler", "vehiclesAdd_creatingButton": "Création...", "vehiclesAdd_createButton": "<PERSON><PERSON>er le Véhicule", "vehiclesAdd_createSuccessToast": "Véhicule créé avec succès ! 🚗", "vehiclesAdd_createPartialErrorToast": "Véhicule créé mais ID non retourné", "vehiclesAdd_createErrorToast": "Échec de la création du véhicule. Veuillez réessayer.", "vehiclesLayout_pageTitle": "Gestion des Véhicules", "vehiclesLayout_title": "Véhicules", "vehiclesLayout_description": "<PERSON><PERSON><PERSON> les véhicules et les affectations de chauffeurs", "vehiclesLayout_addButton": "Ajouter", "vehiclesLayout_errorPrefix": "Erreur : ", "vehiclesLayout_totalLabel": "Total", "vehiclesLayout_pendingLabel": "En attente", "vehiclesLayout_assignedLabel": "<PERSON><PERSON><PERSON>", "vehiclesLayout_availableLabel": "Disponible", "vehiclesLayout_searchPlaceholder": "Rechercher des véhicules...", "vehiclesLayout_showInactiveLabel": "Afficher les véhicules inactifs", "vehiclesLayout_vehicleCount_one": "{count} véhicule", "vehiclesLayout_vehicleCount_other": "{count} véhicules", "vehiclesLayout_noVehiclesFound": "Aucun véhicule trouvé", "vehiclesLayout_statusNotLinked": "Non lié", "vehiclesLayout_statusPending": "En attente", "vehiclesLayout_statusAssigned": "<PERSON><PERSON><PERSON>", "vehiclesLayout_statusAvailable": "Disponible", "vehiclesLayout_statusInactive": "Inactif", "vehiclesLayout_userLabel": "Utilisa<PERSON>ur", "vehiclesLayout_tenantLabel": "<PERSON><PERSON><PERSON>", "vehiclesLayout_driverLabel": "<PERSON><PERSON><PERSON>", "paymentForm_paymentMethodLabel": "Méthode de Paiement", "paymentForm_cashPaymentLabel": "Paiement en Espèces", "paymentForm_mobileMoneyLabel": "Argent Mobile", "paymentForm_mobileOperatorLabel": "Opérateur d'Argent Mobile", "paymentForm_mvolaLabel": "MVola", "paymentForm_airtelMoneyLabel": "Airtel Money", "paymentForm_orangeMoneyLabel": "Orange Money", "paymentForm_amountLabel": "<PERSON><PERSON> (Ar)", "paymentForm_amountPlaceholder": "Entrez le montant du paiement", "paymentForm_suggestedAmountLabel": "<PERSON><PERSON> sugg<PERSON> :", "paymentForm_remarksLabel": "Remarques Supplémentaires (Optionnel)", "paymentForm_remarksPlaceholder": "Toute information supplémentaire sur le paiement", "paymentForm_processingButton": "Traitement...", "paymentForm_createPaymentButton": "<PERSON><PERSON><PERSON> le Paie<PERSON>", "paymentForm_paymentMethodRequiredError": "<PERSON><PERSON> de<PERSON> sélectionner une méthode de paiement", "paymentForm_mobileOperatorRequiredError": "<PERSON><PERSON> devez sélectionner un opérateur mobile", "paymentForm_amountRequiredError": "Le montant est requis", "paymentForm_amountInvalidError": "Le montant doit être un nombre", "paymentForm_amountMinError": "Le montant doit être supérieur à 0", "paymentForm_createPaymentFailedToast": "Échec de la création du paiement", "assignDriver_loadingTitle": "Chargement du Trajet", "assignDriver_loadingDescription": "Veuillez patienter pendant que nous chargeons les informations du trajet...", "assignDriver_assignDriverTitle": "Assigner un Chauffeur", "assignDriver_selectDriverDescription": "Sélectionnez un chauffeur à assigner au trajet #{tripId}", "assignDriver_noAvailableDrivers": "<PERSON>cun chauffeur disponible trouvé", "assignDriver_notEnoughSeats": "Pas assez de places (a {driverCapacity}, nécessite {passengerCount})", "assignDriver_tripAlreadyAssignedTitle": "<PERSON><PERSON><PERSON>", "assignDriver_tripAlreadyAssignedDescription": "Le trajet #{tripId} a déjà un chauffeur assigné", "assignDriver_tripAlreadyAssignedMessage": "Ce trajet a déjà été assigné à un chauffeur et est en cours.", "assignDriver_goBackButton": "Retour", "assignDriver_tripNotFoundTitle": "<PERSON><PERSON><PERSON>", "assignDriver_tripNotFoundDescription": "Le trajet avec l'ID={tripId} n'a pas pu être trouvé", "assignDriver_tripNotFoundMessage": "Ce trajet a peut-être été supprimé ou vous avez suivi un lien invalide.", "assignDriver_dialogTitle": "As<PERSON><PERSON> ou <PERSON><PERSON><PERSON> un Chauffeur", "assignDriver_dialogDescription": "Quelle action souhaitez-vous effectuer avec {driverName} ?", "assignDriver_capacityWarningTitle": "Avertissement de Capacité :", "assignDriver_capacityWarningMessage": "Ce chauffeur ne peut accueillir que {capacity} passagers, mais ce trajet nécessite {passengerCount} passagers. Vous pouvez toujours assigner le chauffeur, mais il pourrait ne pas avoir assez de places.", "assignDriver_cancelButton": "Annuler", "assignDriver_requestButton": "<PERSON><PERSON><PERSON>", "assignDriver_requestingButton": "Demande en cours...", "assignDriver_assignButton": "Assigner", "assignDriver_assigningButton": "Attribution en cours...", "assignDriver_tripAlreadyAssignedToast": "Ce trajet a déjà un chauffeur assigné", "assignDriver_driverLocationUnknownToast": "La localisation du chauffeur est inconnue. Impossible de demander le chauffeur.", "assignDriver_requestSuccessToast": "<PERSON><PERSON><PERSON> envoy<PERSON> {driver<PERSON><PERSON>}", "assignDriver_requestFailedToast": "Échec de la demande de chauffeur. Veuillez réessayer.", "assignDriver_assignSuccessToast": "<PERSON><PERSON>eur {<PERSON><PERSON><PERSON>} assigné avec succès", "assignDriver_driverNotFoundToast": "Chauffeur introuvable. Veuillez sélectionner un autre chauffeur.", "assignDriver_driverOccupiedToast": "Le chauffeur est déjà occupé. Veuillez sélectionner un autre chauffeur.", "assignDriver_assignUnknownErrorToast": "Une erreur inconnue s'est produite. Veuillez réessayer.", "assignDriver_assignFailedToast": "Échec de l'assignation du chauffeur. Veuillez réessayer.", "tripStatusBadge_preparing": "Préparation", "tripStatusBadge_requestingDriver": "Recherche de chauffeur", "tripStatusBadge_reserved": "Réservé", "tripStatusBadge_driverApproaching": "Chauffeur en approche", "tripStatusBadge_driverAwaiting": "Chauffeur en attente", "tripStatusBadge_inProgress": "En cours", "tripStatusBadge_completed": "<PERSON><PERSON><PERSON><PERSON>", "tripStatusBadge_cancelled": "<PERSON><PERSON><PERSON>", "tripStatusBadge_paid": "<PERSON><PERSON>", "tripStatusBadge_unknown": "Inconnu", "tripDetails_passengerLabel": "Passager", "tripDetails_driverLabel": "<PERSON><PERSON><PERSON>", "tripDetails_tripInformationLabel": "Informations du Trajet", "tripDetails_statusLabel": "Statut", "tripDetails_passengersLabel": "Passagers", "tripDetails_reservationTypeLabel": "Type de Réservation", "tripDetails_fullDayType": "<PERSON><PERSON><PERSON>", "tripDetails_punctualType": "Ponctuel", "tripDetails_pricingOptionLabel": "Option de Tarification", "tripDetails_fixedPriceOption": "75 (essence incluse)", "tripDetails_gasExcludedPriceOption": "25 (essence payée séparément)", "tripDetails_fromLabel": "De", "tripDetails_toLabel": "Vers", "tripDetails_distanceTraveledLabel": "Distance Parcourue", "tripDetails_estimatedDistanceLabel": "Distance Est.", "tripDetails_requestedPaymentMethodLabel": "Méthode de paiement demandée", "tripDetails_estimatedDurationLabel": "<PERSON><PERSON><PERSON>.", "tripDetails_intakeSourceLabel": "Source de Prise en Charge", "tripDetails_tripTimelineLabel": "Chronologie du Trajet", "tripDetails_driverArrivedAwaitingLabel": "Le chauffeur est arrivé et attend la prise en charge", "tripDetails_passengerStartTimeLabel": "<PERSON><PERSON> <PERSON> d<PERSON><PERSON> du passager", "tripDetails_completedLabel": "<PERSON><PERSON><PERSON><PERSON>", "tripDetails_cancelledLabel": "<PERSON><PERSON><PERSON>", "tripDetails_costBreakdownLabel": "💰 Répartition des Coûts", "tripDetails_initialEstimateLabel": "📊 Estimation Initiale", "tripDetails_realCostLabel": "🚗 <PERSON><PERSON><PERSON> (Calculé)", "tripDetails_costCappedLabel": "⚠️ <PERSON><PERSON><PERSON> (Protection Trafic)", "tripDetails_costCappedValue": "Limite de 10% appliquée", "tripDetails_adminOverrideLabel": "🔧 Remplacement Admin", "tripDetails_adminOverrideReasonPrefix": "Raison :", "tripDetails_finalCostLabel": "💳 Coût Final (Payé par le Client)", "tripDetails_overridePriceButton": "🔧 Remplacer le Prix", "tripDetails_recalculateButton": "🔄 Recalculer depuis les Logs", "tripDetails_basePriceLabel": "Prix de Base", "tripDetails_distanceCostGasLabel": "Coût Distance (Essence)", "tripDetails_distanceCostLabel": "Coût Distance", "tripDetails_durationCostLabel": "Coût <PERSON>", "tripDetails_totalCostLabel": "Coût Total", "tripDetails_paymentsReceivedLabel": "Paiements Reçus", "tripDetails_unknownPaymentMethod": "inconnu", "tripDetails_skippedDriversLabel": "Chauffeurs Ignorés", "tripDetails_overrideTripCostDialogTitle": "🔧 <PERSON><PERSON><PERSON><PERSON> le Coût du Trajet", "tripDetails_overrideTripCostDialogDescription": "Définir manuellement un coût personnalisé pour ce trajet. <PERSON><PERSON> remplacera le coût calculé.", "tripDetails_newCostLabel": "Nouveau Coût (Ar)", "tripDetails_newCostPlaceholder": "Entrez le nouveau coût...", "tripDetails_reasonLabel": "Raison (Optionnel)", "tripDetails_reasonPlaceholder": "Raison du remplacement du coût...", "tripDetails_updatingButton": "Mise à jour...", "tripDetails_overrideCostButton": "<PERSON><PERSON><PERSON><PERSON> Coû<PERSON>", "tripDetails_deleteDialogTitle": "<PERSON><PERSON><PERSON><PERSON> le Trajet", "tripDetails_deleteDialogDescription": "Êtes-vous sûr de vouloir supprimer ce trajet ?", "tripDetails_receivePaymentDialogTitle": "Recevoir un Paiement", "tripDetails_receivePaymentDialogDescription": "C<PERSON>er un nouveau paiement pour ce trajet. Pour les paiements en espèces, le chauffeur recevra le paiement.", "tripDetails_cancelTripDialogTitle": "<PERSON><PERSON><PERSON> le Trajet", "tripDetails_cancelTripDialogDescription": "Êtes-vous sûr de vouloir annuler ce trajet ? Cette action ne peut pas être annulée.", "tripDetails_recalculateDialogTitle": "🔄 Recalculer le Trajet à partir de Tous les Logs", "tripDetails_recalculateDialogDescription": "Cela recalculera la distance et les coûts du trajet à partir de TOUS les logs GPS. Cela garantit une précision de 100% en traitant chaque entrée de log depuis le début.", "tripDetails_recalculateNoteTitle": "⚠️ Note :", "tripDetails_recalculateNoteDescription": "Cette opération va :", "tripDetails_recalculateNote1": "Traiter tous les logs GPS du trajet", "tripDetails_recalculateNote2": "Recalculer la distance totale parcourue", "tripDetails_recalculateNote3": "Recalculer tous les composants de coût", "tripDetails_recalculateNote4": "Mettre à jour le trajet si des différences sont trouvées", "tripDetails_lastRecalculationResultsTitle": "📊 Derniers Résultats de Recalcul :", "tripDetails_distanceDifferenceLabel": "Différence de distance :", "tripDetails_costDifferenceLabel": "Di<PERSON><PERSON><PERSON><PERSON> de <PERSON> :", "tripDetails_recalculatingButton": "Recalcul en cours...", "tripDetails_recalculateNowButton": "Recalculer Maintenant", "tripDetails_viewPaymentDetailsAriaLabel": "Voir les détails du paiement", "tripDetails_costOverrideSuccessToast": "Coût du trajet remplacé avec succès à {amount}", "tripDetails_costOverrideFailedToast": "Échec du remplacement du coût du trajet. Veuillez réessayer.", "tripDetails_recalculateSuccessToast": "Trajet recalculé avec succès ! Distance mise à jour de {distance}km, coût mis à jour de {cost}", "tripDetails_recalculateNoChangesToast": "Recalcul terminé. Aucune différence significative trouvée.", "tripDetails_recalculateFailedToast": "Échec du recalcul du trajet. Veuillez réessayer.", "tripDetails_confirmPaymentFailedToast": "Échec de la confirmation du paiement", "vehicleDetails_pageTitle": "Détails du Véhicule", "vehicleDetails_active": "Actif", "vehicleDetails_inactive": "Inactif", "vehicleDetails_pendingApproval": "En attente d'approbation", "vehicleDetails_assigned": "<PERSON><PERSON><PERSON>", "vehicleDetails_available": "Disponible", "vehicleDetails_notLinked": "Non lié", "vehicleDetails_assignmentHistory": "Historique des Affectations", "vehicleDetails_vehicleDetailsTitle": "Détails du Véhicule", "vehicleDetails_brandLabel": "Marque", "vehicleDetails_modelLabel": "<PERSON><PERSON><PERSON><PERSON>", "vehicleDetails_yearLabel": "<PERSON><PERSON>", "vehicleDetails_colorLabel": "<PERSON><PERSON><PERSON>", "vehicleDetails_registrationLabel": "Immatriculation", "vehicleDetails_maxPassengersLabel": "Passagers Maximum", "vehicleDetails_createdLabel": "<PERSON><PERSON><PERSON>", "vehicleDetails_deactivateVehicle": "Désactiver le Véhicule", "vehicleDetails_activateVehicle": "<PERSON><PERSON> le Véhicule", "vehicleDetails_ownershipAssignmentTitle": "Propriété et Affectation", "vehicleDetails_ownerLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vehicleDetails_unknown": "Inconnu", "vehicleDetails_viewOwnerProfile": "Voir le Profil du Propriétaire", "vehicleDetails_ownershipLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vehicleDetails_tenantOwnedVehicle": "Véhicule du Locataire", "vehicleDetails_currentDriverLabel": "<PERSON><PERSON><PERSON>", "vehicleDetails_viewDriver": "Voir le Chauffeur", "vehicleDetails_unassign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vehicleDetails_assignDriverLabel": "Assigner un Chauffeur", "vehicleDetails_selectDriverPlaceholder": "Sélectionner un chauffeur...", "vehicleDetails_unknownDriver": "<PERSON><PERSON><PERSON>", "vehicleDetails_assignDriver": "<PERSON><PERSON><PERSON>", "vehicleDetails_vehicleApprovalRequired": "Approbation du Véhicule Requise", "vehicleDetails_adminRemarkLabel": "<PERSON><PERSON><PERSON> (Optionnel)", "vehicleDetails_adminRemarkPlaceholder": "Ajoutez des notes sur ce véhicule...", "vehicleDetails_approveVehicle": "Approuver le Véhicule", "vehicleDetails_rejectVehicle": "Rejeter le Véhicule", "vehicleDetails_linkToTenantTitle": "<PERSON>r au Locataire", "vehicleDetails_linkToTenantDescription": "Ce véhicule n'est pas lié à votre locataire. Liez-le pour permettre les affectations de chauffeurs.", "vehicleDetails_linkVehicleToTenant": "Lier le Véhicule au Locataire", "vehicleDetails_assignmentHistoryTitle": "Historique des Affectations", "vehicleDetails_current": "Actuel", "vehicleDetails_past": "Passé", "vehicleDetails_vehicleApprovedToast": "Véhicule approuvé avec succès", "vehicleDetails_vehicleRejectedToast": "Véhicule rejeté avec succès", "vehicleDetails_updateVehicleApprovalFailedToast": "Échec de la mise à jour de l'approbation du véhicule", "vehicleDetails_vehicleLinkedToast": "Véhicule lié au locataire avec succès", "vehicleDetails_linkVehicleFailedToast": "Échec de la liaison du véhicule au locataire", "vehicleDetails_driverAssignedToast": "<PERSON><PERSON>eur <PERSON>é avec succès", "vehicleDetails_assignDriverFailedToast": "Échec de l'assignation du chauffeur", "vehicleDetails_driverUnassignedToast": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> avec succès", "vehicleDetails_unassignDriverFailedToast": "Échec de la désassignation du chauffeur", "vehicleDetails_vehicleActivatedToast": "Véhicule activé avec succès", "vehicleDetails_vehicleDeactivatedToast": "Véhicule désactivé avec succès", "vehicleDetails_updateVehicleStatusFailedToast": "Échec de la mise à jour du statut du véhicule", "vehiclesPage_selectVehicleTitle": "Sélectionner un véhicule", "vehiclesPage_selectVehicleDescription": "Choisissez un véhicule dans la liste pour voir ses détails", "vehicleAssignments_pageTitle": "Historique des Affectations de Véhicule", "vehicleAssignments_backToVehicleDetails": "Retour aux Détails du Véhicule", "vehicleAssignments_assignmentHistoryTitle": "Historique des Affectations", "vehicleAssignments_viewVehicleDetails": "Voir les Détails du Véhicule", "vehicleAssignments_errorPrefix": "Erreur : ", "vehicleAssignments_noAssignmentHistoryTitle": "Aucun Historique d'Affectation", "vehicleAssignments_noAssignmentHistoryDescription": "Ce véhicule n'a encore été assigné à aucun chauffeur.", "vehicleAssignments_summaryTitle": "Résumé", "vehicleAssignments_totalAssignmentsLabel": "Total des Affectations", "vehicleAssignments_currentStatusLabel": "Statut Actuel", "vehicleAssignments_currentDriverLabel": "<PERSON><PERSON><PERSON>", "vehicleAssignments_assignmentTimelineTitle": "Chronologie des Affectations", "vehicleAssignments_assignmentTimelineDescription": "Historique complet des affectations de chauffeurs pour ce véhicule", "vehicleAssignments_unknownDriver": "<PERSON><PERSON><PERSON>", "vehicleAssignments_noPhoneNumber": "Aucun numéro de téléphone", "vehicleAssignments_assignedLabel": "Assigné :", "vehicleAssignments_unassignedLabel": "Désassigné :", "vehicleAssignments_durationLabel": "Durée :", "vehicleAssignments_reasonLabel": "Raison :", "vehicleAssignments_assignedByAdmin": "Assign<PERSON> par l'admin", "vehicleAssignments_unassignedByAdmin": "Désassigné par l'admin", "vehicleAssignments_activeStatus": "Actif", "vehicleAssignments_completedStatus": "<PERSON><PERSON><PERSON><PERSON>", "vehicleAssignments_viewDriver": "Voir le Chauffeur", "vehicleAssignments_activeForDuration": "Actif depuis {duration}", "vehicleAssignments_durationDays": "{days} jour{plural}", "vehicleAssignments_durationHours": "{hours} heure{plural}", "vehicleAssignments_durationDaysHours": "{days} jour{dayPlural} {hours} heure{hourPlural}", "vehicleAssignments_durationNA": "N/A", "vehicleAssignments_loadAssignmentHistoryFailed": "Échec du chargement de l'historique des affectations", "vehicleAssignmentsList_pageTitle": "Affectations de Véhicules", "vehicleAssignmentsList_title": "Historique des Affectations de Véhicules", "vehicleAssignmentsList_description": "Suivre les affectations de véhicules et l'historique des chauffeurs dans votre flotte", "vehicleAssignmentsList_errorPrefix": "Erreur : ", "vehicleAssignmentsList_searchPlaceholder": "Rechercher des affectations...", "vehicleAssignmentsList_showOnlyActiveLabel": "A<PERSON><PERSON><PERSON> uniquement les affectations actives", "vehicleAssignmentsList_assignmentCount_one": "{count} affectation", "vehicleAssignmentsList_assignmentCount_other": "{count} affectations", "vehicleAssignmentsList_noAssignmentsFoundTitle": "Aucune affectation trouvée", "vehicleAssignmentsList_noAssignmentsFoundDescription": "Essayez d'ajuster vos filtres", "vehicleAssignmentsList_noAssignmentsYetDescription": "Aucune affectation de véhicule n'a encore été effectuée", "vehicleAssignmentsList_unknownVehicle": "Véhicule Inconnu", "vehicleAssignmentsList_unknownDriver": "<PERSON><PERSON><PERSON>", "vehicleAssignmentsList_noPhone": "Aucun téléphone", "vehicleAssignmentsList_durationLabel": "Durée :", "vehicleAssignmentsList_activeStatus": "Actif", "vehicleAssignmentsList_completedStatus": "<PERSON><PERSON><PERSON><PERSON>", "vehicleAssignmentsList_viewVehicleButton": "Voir le Véhicule", "vehicleAssignmentsList_viewDriverButton": "Voir le Chauffeur", "vehicleAssignmentsList_unassignedOnPrefix": "Désassign<PERSON> le", "vehicleAssignmentsList_byAdminSuffix": "par l'admin", "tripsList_searchModeToggle": "Mode Recherche", "tripsList_algoliaSearchPlaceholder": "Rechercher trajets par lieu, passager, chauffeur...", "tripsList_unknownPassenger": "<PERSON><PERSON>", "tripsList_fromLabel": "De :", "tripsList_toLabel": "Vers :", "tripsList_statusLabel": "Statut :", "tripsList_notAvailable": "N/D", "algoliaSearch_defaultSearchPlaceholder": "Rechercher...", "algoliaSearch_noResultsFound": "Aucun résultat trouvé", "algoliaSearch_notConfigured": "La recherche n'est pas configurée", "algoliaSearch_searchFailed": "Échec de la recherche. Veuillez réessayer.", "algoliaSearch_indexNotConfigured": "L'index de recherche nécessite une configuration. Contactez l'administrateur."}