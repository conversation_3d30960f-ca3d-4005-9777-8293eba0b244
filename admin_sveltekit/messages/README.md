# Translation Management

This directory contains the translation files and utilities for the admin panel.

## Files

- `en-us.json` - English translations
- `fr-fr.json` - French translations  
- `remove-duplicates.js` - <PERSON>ript to remove duplicate translation keys

## Scripts

### Duplicate Removal

The `remove-duplicates.js` script automatically removes duplicate translation keys from both JSON files while preserving the last occurrence of each key.

**Features:**
- ✅ Removes duplicate keys from both language files
- ✅ Preserves JSON formatting and structure
- ✅ Validates JSON syntax after cleanup
- ✅ Reports detailed statistics
- ✅ Warns about key count mismatches between languages

**Usage:**

```bash
# Remove duplicates only
npm run translations:remove-duplicates

# Remove duplicates AND compile translations (recommended)
npm run translations:process

# Compile translations only
npm run translations:compile
```

**Direct usage:**
```bash
node messages/remove-duplicates.js
```

### Translation Workflow

When adding new translations:

1. Add translation keys to both `en-us.json` and `fr-fr.json`
2. Run the complete workflow: `npm run translations:process`
3. This will:
   - Remove any duplicate keys
   - Compile translations with Paraglide
   - Ensure both files have matching key counts

## Example Output

```
🔧 Translation Duplicate Remover
==================================

📁 Processing: en-us.json
  ❌ Duplicate found: "someKey" at line 123
  ✅ File processed successfully
  📊 Total keys: 834
  🗑️  Duplicates removed: 1

📁 Processing: fr-fr.json
  ✅ File processed successfully
  📊 Total keys: 834
  🗑️  Duplicates removed: 0

📋 Summary
===========
✅ Successfully removed 1 duplicate keys
   • English: 1 duplicates removed
   • French: 0 duplicates removed
📊 Total translation keys:
   • English: 834 keys
   • French: 834 keys
```

## Best Practices

1. **Always run the complete workflow** (`npm run translations:process`) after adding translations
2. **Check for warnings** about key count mismatches between language files
3. **Test the application** after running the script to ensure translations work correctly
4. **Commit both JSON files** together to maintain consistency 