{"$schema": "https://inlang.com/schema/inlang-message-format", "hello_world": "Hello, {name} from en-us!", "tripsList_title": "Trips List", "tripsList_liveFilter": "Live", "tripsList_reservationsFilter": "Reservations", "tripsList_completedFilter": "Completed", "tripsList_startMarkerTitle": "Start", "tripsList_arrivalMarkerTitle": "Arrival", "tripsList_driverStartMarkerTitle": "Driver Start", "tripsList_noTripsAvailable": "No trips available", "mainPage_clockDriftErrorTitle": "Your computer's clock is severely out of sync", "mainPage_clockDriftErrorDescription": "Please synchronize your system clock immediately for the app to work correctly.", "mainPage_clockDriftWarningTitle": "Your computer's clock is not synchronized", "mainPage_clockDriftWarningDescription": "Please synchronize your system clock for the app to work correctly.", "mainPage_adminAccessVerificationFailed": "Failed to verify admin access", "mainPage_navDashboard": "Dashboard", "mainPage_navRides": "Rides", "mainPage_navRidesTrips": "Trips", "mainPage_navRidesClients": "Clients", "mainPage_navRidesDrivers": "Drivers", "mainPage_navFinance": "Finance", "mainPage_navFinancePayments": "Payments", "mainPage_navReports": "Reports", "mainPage_navReportsEvents": "Events", "mainPage_navSupport": "Support", "mainPage_navSupportFeedbacks": "Feedbacks", "mainPage_navSupportChats": "Chats", "mainPage_navAdmin": "Admin", "mainPage_navAdminConfigurations": "Configurations", "mainPage_navAdminAccounts": "Accounts", "mainPage_navAdminNotifications": "Notifications", "mainPage_navDocuments": "Documents", "mainPage_navDocumentsAll": "All Documents", "mainPage_navDocumentsExpiring": "Expiring Soon", "mainPage_navVehicles": "Vehicles", "mainPage_navVehiclesAll": "All Vehicles", "mainPage_navVehiclesAssignments": "Assignments", "mainPage_screenTooSmall": "Screen too small", "mainPage_accessDeniedTitle": "Access Denied", "mainPage_accessDeniedDescription": "Your admin account has been deactivated.<br />Please contact another administrator for assistance.", "mainPage_signOutButton": "Sign Out", "mainPage_headerTitle": "Fiaranow Admin", "mainPage_serverEmulator": "Emulator", "mainPage_serverLive": "Live", "mainPage_profileAlt": "Profile", "mainPage_menuMyAccount": "My Account", "mainPage_menuProfile": "Profile", "mainPage_menuLanguage": "Language", "mainPage_menuLanguageEnglish": "English", "mainPage_menuLanguageFrench": "French", "mainPage_menuLogout": "Logout", "profile_logoutConfirmDescription": "Are you sure you want to log out?", "profile_logoutConfirmButton": "Logout", "profile_cancelButton": "Cancel", "mobileUserDetails_userNotFoundTitle": "User Not Found", "mobileUserDetails_userNotFoundDescription": "This user does not exist or has been deleted.", "mobileUserDetails_userNotFoundInstructions": "Please check the URL or return to the clients list.", "mobileUserDetails_profileCardTitle": "User Profile", "mobileUserDetails_noEmailProvided": "No email provided", "mobileUserDetails_phoneNumberLabel": "Phone Number", "mobileUserDetails_notProvided": "Not provided", "mobileUserDetails_lastSeenLabel": "Last Seen", "mobileUserDetails_never": "Never", "mobileUserDetails_userTypeLabel": "User Type", "mobileUserDetails_userTypePassenger": "Passenger", "mobileUserDetails_userTypeDriver": "Driver", "mobileUserDetails_userTypeNotSpecified": "Not specified", "mobileUserDetails_driverProfileCardTitle": "Driver Profile", "mobileUserDetails_driverProfileCardDescription": "Vehicle and driver verification details", "mobileUserDetails_disconnectingButtonText": "Disconnecting...", "mobileUserDetails_disconnectButtonText": "Disconnect from Trip", "mobileUserDetails_revokeVerificationButtonText": "Revoke Verification", "mobileUserDetails_verifyDriverButtonText": "Verify Driver", "mobileUserDetails_vehicleBrandLabel": "Vehicle Brand", "mobileUserDetails_vehicleModelLabel": "Vehicle Model", "mobileUserDetails_vehicleColorLabel": "Vehicle Color", "mobileUserDetails_vehicleYearLabel": "Vehicle Year", "mobileUserDetails_registrationNumberLabel": "Registration Number", "mobileUserDetails_maxPassengersLabel": "Maximum Passengers", "mobileUserDetails_maxPassengersValue": "{count} passengers", "mobileUserDetails_editDriverProfileButton": "Edit Driver Profile", "mobileUserDetails_driverVerificationLabel": "Driver Verification", "mobileUserDetails_driverStatusVerified": "Verified", "mobileUserDetails_driverStatusPending": "Pending Verification", "mobileUserDetails_currentTripStatusLabel": "Current Trip Status", "mobileUserDetails_currentTripStatusActive": "Currently on a trip", "mobileUserDetails_disconnectConfirmTitle": "Disconnect Driver from Trip", "mobileUserDetails_disconnectConfirmDescription": "Are you sure you want to disconnect this driver? If the trip is in progress, it will be cancelled and the passenger will need to request a new trip.", "mobileUserDetails_disconnectSuccessToast": "Driver disconnected from trip successfully", "mobileUserDetails_disconnectErrorToast": "Failed to disconnect driver from trip", "mobileUserDetails_driverManagementTitle": "Driver Management", "mobileUserDetails_driverManagementDescription": "Manage driver verification and vehicle assignments", "mobileUserDetails_driverRatingTitle": "Driver Rating", "mobileUserDetails_manageVehiclesButton": "Manage Vehicles", "mobileUserDetails_noRatingsYet": "No ratings yet", "mobileUserDetails_tripsLabel": "trips", "mobileUserListItem_noPhoneNumber": "No phone number", "mobileUserListItem_driverStatusActive": "Active", "mobileUserListItem_driverStatusInactive": "Inactive", "mobileUserListItem_driverVerified": "Verified", "mobileUserListItem_driverUnverified": "Unverified", "mobileUserListItem_newDriverBadge": "New Driver", "mobileUserListItem_removeButtonSrLabel": "Remove", "mobileUserListItem_detailsButtonSrLabel": "Details", "clientsList_pageTitle": "Clients List", "clientsList_onlineToggleLabel": "Online", "clientsList_countListed_one": "{count} client listed", "clientsList_countListed_other": "{count} clients listed", "clientsList_noClientsAvailable": "No clients available", "driversList_pageTitle": "Drivers List", "driversList_onlineToggleLabel": "Online", "driversList_inactiveToggleLabel": "Inactive", "driversList_pendingToggleLabel": "Pending", "driversList_countListed_one": "{count} driver listed", "driversList_countListed_other": "{count} drivers listed", "driversList_noDriversAvailable": "No drivers available", "driversList_showingUnverifiedOnly": "Showing unverified drivers only", "driversList_includingInactive": "Including inactive drivers", "driversList_onlineOnly": "Online only", "driversList_inactiveFilterDisabledTooltip": "Active/Inactive filter doesn't apply to pending drivers", "driversList_filterByTagsPlaceholder": "Filter by tags...", "driversList_sortByLabel": "Sort by:", "driversList_sortByRating": "Rating", "driversList_sortByName": "Name", "tripDetails_notFoundTitle": "Trip Not Found", "tripDetails_notFoundDescription": "Trip with ID={tripId} could not be found", "tripDetails_notFoundExplanation": "This trip may have been deleted or you may have followed an invalid link.", "tripDetails_paymentListenerErrorToast": "Error setting up payment listener", "tripDetails_paymentSetupErrorToast": "Error setting up payment UI", "tripDetails_deleteSuccessToast": "<PERSON> deleted successfully", "tripDetails_deleteErrorToast": "Failed to delete trip", "tripDetails_cancelSuccessToast": "Trip cancelled successfully", "tripDetails_cancelErrorToast": "Failed to cancel trip", "tripDetails_paymentSuccessToast": "Payment recorded successfully", "tripDetails_removeSkippedDriverSuccessToast": "Skipped driver removed successfully", "tripDetails_removeSkippedDriverErrorToast": "Failed to remove skipped driver", "tripDetails_title": "Trip Details", "tripDetails_creationDate": "Creation Date", "tripDetails_tripIdLabel": "Trip ID", "tripDetails_receivePaymentButton": "Receive Payment", "tripDetails_assignDriverButton": "Assign Driver", "tripDetails_deletingButton": "Deleting...", "tripDetails_deleteButton": "Delete Trip", "tripDetails_followingButton": "Following...", "tripDetails_followButton": "Follow", "tripDetails_cancellingButton": "Cancelling...", "tripDetails_cancelButton": "Cancel Trip", "tripDetails_clientCardTitle": "Client", "tripDetails_driverCardTitle": "Driver", "tripDetails_noDriverAssigned": "No driver assigned", "tripDetails_skippedDriversCardTitle": "Skipped Drivers ({count})", "tripDetails_noSkippedDrivers": "No drivers skipped this trip", "tripDetails_detailsCardTitle": "Details", "tripDetails_tripStatusLabel": "Status", "tripDetails_tripStatusPreparing": "Preparing", "tripDetails_tripStatusRequestingDriver": "Requesting Driver", "tripDetails_tripStatusReserved": "Reserved", "tripDetails_tripStatusDriverApproaching": "Driver Approaching", "tripDetails_tripStatusDriverAwaiting": "Driver Awaiting", "tripDetails_tripStatusInProgress": "In Progress", "tripDetails_tripStatusCompleted": "Completed", "tripDetails_tripStatusCancelled": "Cancelled", "tripDetails_tripStatusPaid": "Paid", "tripDetails_tripStatusUnknown": "Unknown Status", "tripDetails_paymentStatusLabel": "Payment", "tripDetails_paymentStatusAwaiting": "Awaiting Payment", "tripDetails_paymentStatusPaid": "Paid", "tripDetails_paymentStatusUnknown": "Unknown Payment Status", "tripDetails_pickupAddressLabel": "Pickup Address", "tripDetails_dropoffAddressLabel": "Dropoff Address", "tripDetails_pickupTimeLabel": "Pickup Time", "tripDetails_dropoffTimeLabel": "Dropoff Time", "tripDetails_distanceLabel": "Distance", "tripDetails_durationLabel": "Duration", "tripDetails_driverStartTimeLabel": "Driver Start Time", "tripDetails_priceLabel": "Price", "tripDetails_paymentIntentLabel": "Payment Intent ID", "eventsLayout_title": "Driver Events", "eventsLayout_description": "List of driver events", "eventsLayout_serviceStatusFilter": "Service Status", "eventsLayout_tripRejectedFilter": "<PERSON> Rejected", "eventsLayout_unknownUser": "Unknown", "eventsLayout_unknownDriver": "Unknown driver", "eventsLayout_serviceStatusEvent": "Service Status", "eventsLayout_tripRejectedEvent": "<PERSON> Rejected", "eventsLayout_noEventsFound": "No events found", "eventsList_selectEventPrompt": "Select an event from the list to view its details", "eventDetail_title": "Event Details", "eventDetail_description": "View information about this event", "eventDetail_eventTypeLabel": "Event Type", "eventDetail_serviceStatusEvent": "Service Status", "eventDetail_tripRejectedEvent": "<PERSON> Rejected", "eventDetail_driverLabel": "Driver", "eventDetail_timestampLabel": "Timestamp", "eventDetail_reasonLabel": "Reason", "eventDetail_noReasonProvided": "No reason provided", "eventDetail_customReasonPrefix": "Custom reason:", "eventDetail_tripInfoLabel": "Trip Information", "eventDetail_tripIdPrefix": "Trip ID:", "eventDetail_tripFromPrefix": "From:", "eventDetail_tripToPrefix": "To:", "eventDetail_additionalInfoLabel": "Additional Information", "eventDetail_eventNotFound": "Event not found", "paymentsLayout_title": "Payments", "paymentsLayout_description": "List of payments", "paymentsLayout_noPaymentsFound": "No payments found", "paymentsList_selectPaymentPrompt": "Select a payment from the list to view its details", "paymentDetailPage_paymentNotFoundTitle": "Payment Not Found", "paymentDetailPage_paymentNotFoundDescription": "Payment with ID={paymentId} could not be found", "paymentDetailPage_paymentNotFoundHint": "This payment may have been deleted or you may have followed an invalid link.", "paymentDetailsComponent_title": "Payment Details", "paymentDetailsComponent_createdOn": "Created on {date}", "paymentDetailsComponent_changeStatusButton": "Change Status", "paymentDetailsComponent_customerHeading": "Customer", "paymentDetailsComponent_driverHeading": "Driver", "paymentDetailsComponent_paymentInfoHeading": "Payment Information", "paymentDetailsComponent_amountLabel": "Amount", "paymentDetailsComponent_amountDueLabel": "Amount Due", "paymentDetailsComponent_discountLabel": "Discount", "paymentDetailsComponent_discountReasonLabel": "Reason: {reason}", "paymentDetailsComponent_requestedMethodLabel": "Requested Method", "paymentDetailsComponent_paidWithMethodLabel": "Paid with {method}", "paymentDetailsComponent_receiptNumberLabel": "Receipt Number", "paymentDetailsComponent_paymentMethodDetailsHeading": "Payment Method Details", "paymentDetailsComponent_detailsLabel": "Details", "paymentDetailsComponent_paymentTimelineHeading": "Payment Timeline", "paymentDetailsComponent_receivedLabel": "Received", "paymentDetailsComponent_byUserLabel": "by {user}", "paymentDetailsComponent_processedLabel": "Processed", "paymentDetailsComponent_completedLabel": "Completed", "paymentDetailsComponent_additionalInfoHeading": "Additional Information", "paymentDetailsComponent_updateStatusErrorToast": "Failed to update payment status", "paymentDetailsComponent_updateStatusSuccessToast": "Payment status updated to {status}", "paymentDetailsComponent_changeStatusDialogTitle": "Change Payment Status", "paymentDetailsComponent_changeStatusDialogDescription": "Select a new status for this payment", "paymentDetailsComponent_cancelButton": "Cancel", "paymentDetailsComponent_paidWithMethodRemarkLabel": "Final Method Remark", "paymentDetailsComponent_transactionIdLabel": "Transaction ID", "paymentDetailsComponent_remarkLabel": "Remark", "financeReports_title": "Financial Reports", "financeReports_description": "View and generate financial reports", "financeReports_comingSoon": "This feature is coming soon.", "adminAccountsLayout_title": "Admin Users", "adminAccountsLayout_description": "List of admin users", "adminAccountsLayout_onlineFilter": "Online", "adminAccountsLayout_inactiveFilter": "Inactive", "adminAccountsLayout_noUsersMatchFilter": "No admin users match the current filters", "adminAccountsList_title": "Account Details", "adminAccountsList_selectAccountPrompt": "Select an account to view details", "adminUserListItem_statusActive": "Active", "adminUserListItem_statusInactive": "Inactive", "adminUserListItem_detailsButtonLabel": "Details", "adminUserDetailsComponent_lastSeenNever": "Never", "adminUserDetailsComponent_toggleActivationFailedToast": "Failed to toggle user activation", "adminUserDetailsComponent_toggleActivationSuccessToast": "Admin user {status} successfully", "adminUserDetailsComponent_notFoundTitle": "Admin User Not Found", "adminUserDetailsComponent_notFoundDescription": "This admin user does not exist or has been deleted.", "adminUserDetailsComponent_notFoundPrompt": "Please check the URL or return to the admin users list.", "adminUserDetailsComponent_profileTitle": "Admin Profile", "adminUserDetailsComponent_profileDescription": "Account details and status", "adminUserDetailsComponent_deactivateButton": "Deactivate Account", "adminUserDetailsComponent_activateButton": "Activate Account", "adminUserDetailsComponent_accountStatusLabel": "Account Status", "adminUserDetailsComponent_lastSeenLabel": "Last Seen", "adminUserDetailsComponent_onlineStatusLabel": "Online Status", "adminUserDetailsComponent_onlineStatusOnline": "Online", "adminUserDetailsComponent_onlineStatusOffline": "Offline", "adminUserDetailsComponent_deactivateDialogTitle": "Deactivate <PERSON><PERSON> Account", "adminUserDetailsComponent_activateDialogTitle": "Activate Admin Account", "adminUserDetailsComponent_confirmToggleDescription": "Are you sure you want to {action} this admin account? {consequence}", "adminUserDetailsComponent_actionDeactivate": "deactivate", "adminUserDetailsComponent_actionActivate": "activate", "adminUserDetailsComponent_consequenceDeactivate": "The user will no longer be able to access the admin panel.", "adminUserDetailsComponent_consequenceActivate": "The user will regain access to the admin panel.", "adminConfigsLayout_listTitle": "Configurations List", "adminConfigsLayout_noConfigsAvailable": "No configurations available", "adminConfigsList_selectConfigPrompt": "Select a configuration from the sidebar to view its details", "adminConfigDetailPage_notFoundTitle": "Configuration Not Found", "adminConfigDetailPage_notFoundDescription": "The requested configuration could not be found", "adminConfigDetailsComponent_tripConfigTitle": "Trip Configuration", "adminConfigDetailsComponent_generalConfigTitle": "General Configuration", "adminConfigDetailsComponent_tripConfigDescription": "Trip-related settings including costs, timings, and driver search parameters", "adminConfigDetailsComponent_generalConfigDescription": "General settings", "adminConfigDetailsComponent_valueLabel": "Value", "adminConfigForm_tripConfigDescription": "Configure trip-related settings", "adminConfigForm_generalConfigDescription": "Configure general settings", "adminConfigForm_costPerKilometerLabel": "Cost per Kilometer (Ar)", "adminConfigForm_costPerHourLabel": "Cost per Hour (Ar)", "adminConfigForm_minimumTripCostLabel": "Minimum Trip Cost (Ar)", "adminConfigForm_waitTimeAfterExtraPaymentLabel": "Wait Time After Extra Payment (minutes)", "adminConfigForm_costPerExtraWaitChunkLabel": "Cost per <PERSON> (Ar)", "adminConfigForm_cancelCostPreStartLabel": "Cancel Cost Pre-Start (Ar)", "adminConfigForm_nearbyDriverRadiusLabel": "Nearby Driver Radius (meters)", "adminConfigForm_maxPassengerCountLabel": "Maximum Passenger Count", "adminConfigForm_maxPassengerCountDescription": "Maximum number of passengers allowed per trip", "adminConfigForm_hideInProgressCostsLabel": "Hide cost estimates from passengers during in-progress trips", "adminConfigForm_hideInProgressCostsDescription": "When enabled, trip costs will be hidden from passengers while the trip is in progress. Drivers will always see the cost. Passengers will only see the final cost after the trip is completed.", "adminConfigForm_saveButton": "Save Changes", "adminConfigForm_saveSuccessToast": "Configuration saved successfully", "adminConfigForm_saveErrorToast": "Failed to save configuration", "adminConfigForm_fallbackTitle": "Configuration Details", "adminConfigForm_fallbackContent": "No configuration selected.", "adminNotifications_pageTitle": "Admin Notifications", "adminNotifications_pageDescription": "System notifications for trips, feedback, and chat messages", "adminNotifications_noNotificationsTitle": "No notifications yet", "adminNotifications_noNotificationsDescription": "New notifications will appear here when they arrive", "adminNotifications_viewDetails": "View Details", "adminNotifications_typeChatMessage": "Chat Message", "adminNotifications_typeTripBooking": "Trip Booking", "adminNotifications_typeFeedbackSubmitted": "Feed<PERSON> Submitted", "adminNotifications_typeTripCancelled": "<PERSON>ed", "adminNotifications_typeReservationReminder": "Reservation Reminder", "enableRingtoneByDefault": "Enable Ringtone by <PERSON><PERSON><PERSON>", "enableRingtoneByDefaultHelp": "When enabled, new users will have ringtone notifications turned on automatically", "enableDriverMovingNotification": "Driver Moving Notification", "enableDriverMovingNotificationHelp": "Notify passengers when their driver starts moving towards pickup location", "enableDriverArrivedNotification": "Driver Arrived Notification", "enableDriverArrivedNotificationHelp": "Notify passengers when their driver has arrived at pickup location", "enableTripPaidNotification": "Trip Payment Notification", "enableTripPaidNotificationHelp": "Notify passengers when their trip payment is completed (always silent)", "enableReservationReminders": "Reservation Reminders", "enableReservationRemindersHelp": "Send reminder notifications before scheduled trips", "reservationReminderTimes": "Reminder Times", "minutesBefore": "minutes before", "remove": "Remove", "addReminderTime": "Add Reminder Time", "adminConfigDetailsComponent_chatConfigTitle": "Chat Configuration", "adminConfigDetailsComponent_passengerNotificationsTitle": "Passenger Notifications", "adminConfigDetailsComponent_chatConfigDescription": "Current chat system settings and support configuration", "adminConfigDetailsComponent_passengerNotificationsDescription": "Current passenger notification settings and ringtone preferences", "adminConfigForm_chatConfigDescription": "Configure chat system settings, support hours, and auto-reply options", "adminConfigForm_passengerNotificationsDescription": "Configure passenger notification settings and ringtone preferences", "adminConfigDetailsComponent_yes": "Yes", "adminConfigDetailsComponent_no": "No", "adminConfigDetailsComponent_none": "None", "adminConfigDetailsComponent_minutes": "minutes", "adminConfigDetailsComponent_sunday": "Sun", "adminConfigDetailsComponent_monday": "Mon", "adminConfigDetailsComponent_tuesday": "<PERSON><PERSON>", "adminConfigDetailsComponent_wednesday": "Wed", "adminConfigDetailsComponent_thursday": "<PERSON>hu", "adminConfigDetailsComponent_friday": "<PERSON><PERSON>", "adminConfigDetailsComponent_saturday": "Sat", "adminConfigForm_enableRingtoneByDefaultLabel": "Enable Ringtone by <PERSON><PERSON><PERSON>", "adminConfigForm_enableRingtoneByDefaultDescription": "When enabled, new users will have ringtone notifications turned on automatically", "adminConfigForm_enableDriverMovingNotificationLabel": "Driver Moving Notification", "adminConfigForm_enableDriverMovingNotificationDescription": "Notify passengers when their driver starts moving towards pickup location", "adminConfigForm_enableDriverArrivedNotificationLabel": "Driver Arrived Notification", "adminConfigForm_enableDriverArrivedNotificationDescription": "Notify passengers when their driver has arrived at pickup location", "adminConfigForm_enableTripPaidNotificationLabel": "Trip Payment Notification", "adminConfigForm_enableTripPaidNotificationDescription": "Notify passengers when their trip payment is completed (always silent)", "adminConfigForm_enableReservationRemindersLabel": "Reservation Reminders", "adminConfigForm_enableReservationRemindersDescription": "Send reminder notifications before scheduled trips", "adminConfigForm_reservationReminderTimesLabel": "Reminder Times", "adminConfigForm_minutesBeforeLabel": "minutes before", "adminConfigForm_showAdminNamesInChatLabel": "Show Admin Names in Chat", "adminConfigForm_defaultChatCategoriesLabel": "<PERSON><PERSON><PERSON> Chat Categories", "adminConfigForm_autoArchiveChatAfterDaysLabel": "Auto Archive After Days", "adminConfigForm_enableChatNotificationsLabel": "Enable Chat Notifications", "adminConfigForm_enableAutoReplyForOffHoursLabel": "Enable Auto Reply for Off Hours", "adminConfigForm_autoReplyMessageLabel": "Auto Reply Message", "adminConfigForm_supportHoursLabel": "Support Hours", "adminConfigForm_supportDaysLabel": "Support Days", "adminConfigForm_maxImagesPerMessageLabel": "Max Images per Message", "adminConfigForm_maxImageSizeMBLabel": "Max Image Size (MB)", "mark_as_seen": "<PERSON> as Seen", "mark_as_addressed": "<PERSON> as Addressed", "archive": "Archive", "user_information": "User Information", "name": "Name", "unknown_user": "Unknown User", "phone": "Phone", "email": "Email", "rating": "Rating", "trip_information": "Trip Information", "trip_id": "Trip ID", "pickup": "Pickup", "destination": "Destination", "date": "Date", "feedback_message": "Feedback Message", "no_message_provided": "No message provided", "attached_images": "Attached Images", "creating_chat": "Creating <PERSON>", "initiate_chat": "Initiate <PERSON><PERSON>", "view_chat": "View Chat", "feedback_not_found": "Feedback Not Found", "feedbacks_title": "User Feedbacks", "feedbacks_description": "Manage user feedback and reviews", "feedback_filter_trip": "<PERSON>", "feedback_filter_application": "<PERSON><PERSON>", "feedback_filter_new": "New Only", "feedback_status_new": "New", "feedback_status_seen": "Seen", "feedback_status_addressed": "Addressed", "feedback_status_archived": "Archived", "feedback_type_trip": "Trip", "feedback_type_application": "Application", "no_feedbacks_found": "No feedbacks found", "select_feedback": "Select Feedback", "select_feedback_description": "Choose a feedback from the list to view details", "no_feedback_selected": "No feedback selected", "select_feedback_from_list": "Select a feedback from the list to view details", "no_message": "No message provided", "trip_feedback": "<PERSON>", "application_feedback": "Application Feedback", "submitted_on": "Submitted on", "feedback_status_updated": "Feedback status updated", "error_updating_feedback": "Error updating feedback", "chat_session_created": "Chat session created", "error_creating_chat": "Error creating chat", "trip_feedback_from": "Trip feedback from", "app_feedback_from": "App feedback from", "linked_feedback": "Linked <PERSON>", "view_feedback": "View Feedback", "driverEdit_pageTitle": "Edit Driver Profile", "driverEdit_driverNotFoundTitle": "Driver Not Found", "driverEdit_driverNotFoundDescription": "The requested driver could not be found or doesn't have a driver profile.", "driverEdit_backToRidesButton": "Back to Rides", "driverEdit_editProfileTitle": "Edit Driver Profile", "driverEdit_editProfileDescription": "Update driver settings for {driverName}", "driverEdit_unknownDriver": "Unknown Driver", "driverEdit_vehicleInfoHeading": "Vehicle Information", "driverEdit_brandLabel": "Brand", "driverEdit_modelLabel": "Model", "driverEdit_colorLabel": "Color", "driverEdit_yearLabel": "Year", "driverEdit_registrationNumberLabel": "Registration Number", "driverEdit_capacitySettingsHeading": "Capacity Settings", "driverEdit_maxPassengersLabel": "Maximum Passengers", "driverEdit_maxPassengersDescription": "The maximum number of passengers this driver can accommodate", "driverEdit_cancelButton": "Cancel", "driverEdit_savingButton": "Saving...", "driverEdit_saveChangesButton": "Save Changes", "driverEdit_updateSuccessToast": "Driver profile updated successfully", "driverEdit_updateErrorToast": "Failed to update driver profile", "driverEdit_saveSuccessToast": "Driver profile updated successfully", "driverEdit_saveErrorToast": "Failed to update driver profile", "driverEdit_loadingError": "Error loading driver profile", "driverEdit_savingChanges": "Saving changes...", "driverEdit_tagManagerTitle": "Driver Tags", "driverEdit_tagManagerDescription": "Manage tags for this driver", "chatsLayout_title": "Support Chats", "chatsLayout_description": "Manage customer support conversations", "chatsLayout_newChatButton": "New Chat", "chatsLayout_searchPlaceholder": "Search chats...", "chatsLayout_activeChatsFilter": "Active", "chatsLayout_allChatsFilter": "All", "chatsLayout_statusActive": "Active", "chatsLayout_statusResolved": "Resolved", "chatsLayout_statusArchived": "Archived", "chatsLayout_noChatsFound": "No chats found", "chatsLayout_noChatsFoundSearch": "No chats found for your search", "chatsLayout_unknownUser": "Unknown User", "chatsPage_selectChatTitle": "Select Chat", "chatsPage_selectChatDescription": "Choose a chat from the list to view conversation", "chatsPage_noChatSelected": "No chat selected", "chatsPage_selectChatFromList": "Select a chat from the list to view details", "chatDetails_errorSendingMessageToast": "Error sending message", "chatDetails_errorLoadingMessagesToast": "Error loading messages", "chatDetails_statusUpdatedSuccessToast": "Chat status updated", "chatDetails_errorUpdatingStatusToast": "Error updating status", "chatDetails_chatWithLabel": "Chat with", "chatDetails_markResolvedButton": "<PERSON>solved", "chatDetails_reopenChatButton": "Reopen Chat", "chatDetails_archiveChatButton": "Archive Chat", "chatDetails_youLabel": "You", "chatDetails_messageInputPlaceholder": "Type your message...", "chatDetails_imageUploadTooltip": "Image upload coming soon", "chatDetails_notFoundMessage": "Cha<PERSON> not found", "chatDetails_noMessagesYet": "No messages yet", "chatDetails_statusActiveLabel": "Active", "chatDetails_statusResolvedLabel": "Resolved", "chatDetails_statusArchivedLabel": "Archived", "chatDetails_appFeedbackLabel": "<PERSON><PERSON>", "documentsLayout_pageTitle": "Documents Management", "documentsLayout_title": "Documents", "documentsLayout_description": "Review and manage driver documents", "documentsLayout_errorPrefix": "Error: ", "documentsLayout_searchPlaceholder": "Search documents...", "documentsLayout_pendingFilter": "Pending", "documentsLayout_approvedFilter": "Approved", "documentsLayout_rejectedFilter": "Rejected", "documentsLayout_documentCount_one": "{count} document", "documentsLayout_documentCount_other": "{count} documents", "documentsLayout_noDocumentsFound": "No documents found", "documentsLayout_unknownDriver": "Unknown driver", "documentsLayout_daysExpiringSuffix": "{days}d", "documentsLayout_expiredBadge": "Expired", "documentsPage_selectDocumentTitle": "Select a document", "documentsPage_selectDocumentDescription": "Choose a document from the list to view its details", "documentDetails_pageTitle": "Review Document", "documentDetails_statusDescriptionPending": "Review and approve or reject this document", "documentDetails_statusDescriptionApproved": "Document has been approved", "documentDetails_statusDescriptionRejected": "Document has been rejected", "documentDetails_documentDetailsTitle": "Document Details", "documentDetails_typeLabel": "Type", "documentDetails_statusLabel": "Status", "documentDetails_uploadedLabel": "Uploaded", "documentDetails_expiryDateLabel": "Expiry Date", "documentDetails_driverNotesLabel": "Driver Notes", "documentDetails_reviewedLabel": "Reviewed", "documentDetails_expiredDaysAgo": "Expired {days} days ago", "documentDetails_expiresInDays": "Expires in {days} days", "documentDetails_validForDays": "Valid for {days} days", "documentDetails_driverInfoTitle": "Driver Information", "documentDetails_nameLabel": "Name", "documentDetails_emailLabel": "Email", "documentDetails_phoneLabel": "Phone", "documentDetails_driverStatusLabel": "Driver Status", "documentDetails_notProvided": "Not provided", "documentDetails_activeStatus": "Active", "documentDetails_inactiveStatus": "Inactive", "documentDetails_verifiedStatus": "Verified", "documentDetails_unverifiedStatus": "Unverified", "documentDetails_viewDriverProfileButton": "View Driver Profile", "documentDetails_driverNotFoundText": "Driver with ID", "documentDetails_driverNotFoundInTenant": "not found in the current tenant's drivers.", "documentDetails_driverNotAssignedText": "This could mean the driver hasn't been assigned to this tenant yet.", "documentDetails_noDriverInfoAvailable": "No driver information available for this document.", "documentDetails_reviewDocumentTitle": "Review Document", "documentDetails_documentExpiredWarning": "This document has already expired and cannot be approved.", "documentDetails_adminNotesLabel": "Admin Notes (Optional)", "documentDetails_adminNotesPlaceholder": "Add any notes about this document review...", "documentDetails_approveButton": "Approve Document", "documentDetails_rejectButton": "Reject Document", "documentDetails_reviewHistoryTitle": "Review History", "documentDetails_reviewedOnLabel": "Reviewed On", "documentDetails_reviewedByLabel": "Reviewed By", "documentDetails_adminNotesHistoryLabel": "Admin Notes", "documentDetails_documentPreviewTitle": "Document Preview", "documentDetails_failedToLoadImage": "Failed to load image.", "documentDetails_previewNotAvailable": "Document preview not available for this file type", "documentDetails_onlyImagesPreviewable": "Only image files can be previewed", "documentDetails_approvedSuccessToast": "Document approved successfully", "documentDetails_rejectedSuccessToast": "Document rejected successfully", "documentDetails_updateStatusErrorToast": "Failed to update document status", "expiringDocuments_pageTitle": "Expiring Documents", "expiringDocuments_pageHeading": "Expiring Documents", "expiringDocuments_pageDescription": "Documents that are expired or expiring within the next {daysThreshold} days", "expiringDocuments_errorPrefix": "Error: ", "expiringDocuments_expiredTitle": "Expired", "expiringDocuments_expiring7DaysTitle": "Expiring in 7 days", "expiringDocuments_expiring14DaysTitle": "Expiring in 14 days", "expiringDocuments_expiring30DaysTitle": "Expiring in 30 days", "expiringDocuments_expiredSectionTitle": "Expired Documents ({count})", "expiringDocuments_expiring7DaysSectionTitle": "Expiring Within 7 Days ({count})", "expiringDocuments_otherExpiringTitle": "Other Expiring Documents", "expiringDocuments_driverPrefix": "Driver: ", "expiringDocuments_unknownDriver": "Unknown", "expiringDocuments_expiredTimeAgo": "Expired", "expiringDocuments_expiresInDays_one": "Expires in {days} day", "expiringDocuments_expiresInDays_other": "Expires in {days} days", "expiringDocuments_reviewButton": "Review", "expiringDocuments_allUpToDateTitle": "All documents are up to date", "expiringDocuments_allUpToDateDescription": "No documents are expired or expiring within the next {daysThreshold} days", "vehiclesAdd_pageTitle": "Add New Vehicle", "vehiclesAdd_cardTitle": "Add New Vehicle", "vehiclesAdd_cardDescription": "Create a new vehicle for your fleet", "vehiclesAdd_brandLabel": "Brand", "vehiclesAdd_brandPlaceholder": "Select brand...", "vehiclesAdd_customBrandPlaceholder": "Enter brand name...", "vehiclesAdd_otherBrandOption": "Other (specify)", "vehiclesAdd_brandRequiredError": "Brand is required", "vehiclesAdd_modelLabel": "Model", "vehiclesAdd_modelPlaceholder": "e.g., <PERSON><PERSON>a", "vehiclesAdd_modelRequiredError": "Model is required", "vehiclesAdd_colorLabel": "Color", "vehiclesAdd_colorPlaceholder": "Select color...", "vehiclesAdd_colorRequiredError": "Color is required", "vehiclesAdd_colorBeige": "Beige", "vehiclesAdd_colorBlack": "Black", "vehiclesAdd_colorBlue": "Blue", "vehiclesAdd_colorBrown": "<PERSON>", "vehiclesAdd_colorGold": "Gold", "vehiclesAdd_colorGray": "<PERSON>", "vehiclesAdd_colorGreen": "Green", "vehiclesAdd_colorOrange": "Orange", "vehiclesAdd_colorPurple": "Purple", "vehiclesAdd_colorRed": "Red", "vehiclesAdd_colorSilver": "Silver", "vehiclesAdd_colorWhite": "White", "vehiclesAdd_colorYellow": "Yellow", "vehiclesAdd_yearLabel": "Year", "vehiclesAdd_yearInvalidError": "Please enter a year between 1900 and {currentYear}", "vehiclesAdd_registrationLabel": "Registration Number", "vehiclesAdd_registrationPlaceholder": "e.g., 1234 TBC", "vehiclesAdd_registrationRequiredError": "Registration number is required", "vehiclesAdd_maxPassengersLabel": "Max Passengers", "vehiclesAdd_maxPassengersDescription": "Number of passenger seats (excluding driver)", "vehiclesAdd_maxPassengersInvalidError": "Please enter valid passenger capacity (1-50)", "vehiclesAdd_infoTitle": "ℹ️ Information", "vehiclesAdd_infoPoint1": "• This will create a new vehicle", "vehiclesAdd_infoPoint2": "• The vehicle will be automatically available", "vehiclesAdd_infoPoint3": "• You can assign drivers to the vehicle after creation", "vehiclesAdd_infoPoint4": "• Vehicle details can be modified later if needed", "vehiclesAdd_cancelButton": "Cancel", "vehiclesAdd_creatingButton": "Creating...", "vehiclesAdd_createButton": "Create Vehicle", "vehiclesAdd_createSuccessToast": "Vehicle created successfully! 🚗", "vehiclesAdd_createPartialErrorToast": "Vehicle created but ID not returned", "vehiclesAdd_createErrorToast": "Failed to create vehicle. Please try again.", "vehiclesLayout_pageTitle": "Vehicles Management", "vehiclesLayout_title": "Vehicles", "vehiclesLayout_description": "Manage vehicles and driver assignments", "vehiclesLayout_addButton": "Add", "vehiclesLayout_errorPrefix": "Error: ", "vehiclesLayout_totalLabel": "Total", "vehiclesLayout_pendingLabel": "Pending", "vehiclesLayout_assignedLabel": "Assigned", "vehiclesLayout_availableLabel": "Available", "vehiclesLayout_searchPlaceholder": "Search vehicles...", "vehiclesLayout_showInactiveLabel": "Show inactive vehicles", "vehiclesLayout_vehicleCount_one": "{count} vehicle", "vehiclesLayout_vehicleCount_other": "{count} vehicles", "vehiclesLayout_noVehiclesFound": "No vehicles found", "vehiclesLayout_statusNotLinked": "Not Linked", "vehiclesLayout_statusPending": "Pending", "vehiclesLayout_statusAssigned": "Assigned", "vehiclesLayout_statusAvailable": "Available", "vehiclesLayout_statusInactive": "Inactive", "vehiclesLayout_userLabel": "User", "vehiclesLayout_tenantLabel": "Fleet", "vehiclesLayout_driverLabel": "Driver", "vehicleDetails_pageTitle": "Vehicle Details", "vehicleDetails_active": "Active", "vehicleDetails_inactive": "Inactive", "vehicleDetails_pendingApproval": "Pending Approval", "vehicleDetails_assigned": "Assigned", "vehicleDetails_available": "Available", "vehicleDetails_notLinked": "Not Linked", "vehicleDetails_assignmentHistory": "Assignment History", "vehicleDetails_vehicleDetailsTitle": "Vehicle Details", "vehicleDetails_brandLabel": "Brand", "vehicleDetails_modelLabel": "Model", "vehicleDetails_yearLabel": "Year", "vehicleDetails_colorLabel": "Color", "vehicleDetails_registrationLabel": "Registration", "vehicleDetails_maxPassengersLabel": "Max Passengers", "vehicleDetails_createdLabel": "Created", "vehicleDetails_deactivateVehicle": "Deactivate Vehicle", "vehicleDetails_activateVehicle": "Activate Vehicle", "vehicleDetails_ownershipAssignmentTitle": "Ownership & Assignment", "vehicleDetails_ownerLabel": "Owner", "vehicleDetails_unknown": "Unknown", "vehicleDetails_viewOwnerProfile": "View Owner Profile", "vehicleDetails_ownershipLabel": "Ownership", "vehicleDetails_tenantOwnedVehicle": "Tenant-owned Vehicle", "vehicleDetails_currentDriverLabel": "Current Driver", "vehicleDetails_viewDriver": "View Driver", "vehicleDetails_unassign": "Unassign", "vehicleDetails_assignDriverLabel": "Assign Driver", "vehicleDetails_selectDriverPlaceholder": "Select a driver...", "vehicleDetails_unknownDriver": "Unknown Driver", "vehicleDetails_assignDriver": "Assign Driver", "vehicleDetails_vehicleApprovalRequired": "Vehicle Approval Required", "vehicleDetails_adminRemarkLabel": "<PERSON><PERSON> (Optional)", "vehicleDetails_adminRemarkPlaceholder": "Add any notes about this vehicle...", "vehicleDetails_approveVehicle": "Approve Vehicle", "vehicleDetails_rejectVehicle": "Reject Vehicle", "vehicleDetails_linkToTenantTitle": "Link to Tenant", "vehicleDetails_linkToTenantDescription": "This vehicle is not linked to your tenant. Link it to enable driver assignments.", "vehicleDetails_linkVehicleToTenant": "Link Vehicle to Tenant", "vehicleDetails_assignmentHistoryTitle": "Assignment History", "vehicleDetails_current": "Current", "vehicleDetails_past": "Past", "vehicleDetails_vehicleApprovedToast": "Vehicle approved successfully", "vehicleDetails_vehicleRejectedToast": "Vehicle rejected successfully", "vehicleDetails_updateVehicleApprovalFailedToast": "Failed to update vehicle approval", "vehicleDetails_vehicleLinkedToast": "Vehicle linked to tenant successfully", "vehicleDetails_linkVehicleFailedToast": "Failed to link vehicle to tenant", "vehicleDetails_driverAssignedToast": "Driver assigned successfully", "vehicleDetails_assignDriverFailedToast": "Failed to assign driver", "vehicleDetails_driverUnassignedToast": "Driver unassigned successfully", "vehicleDetails_unassignDriverFailedToast": "Failed to unassign driver", "vehicleDetails_vehicleActivatedToast": "Vehicle activated successfully", "vehicleDetails_vehicleDeactivatedToast": "Vehicle deactivated successfully", "vehicleDetails_updateVehicleStatusFailedToast": "Failed to update vehicle status", "vehiclesPage_selectVehicleTitle": "Select a vehicle", "vehiclesPage_selectVehicleDescription": "Choose a vehicle from the list to view its details", "vehicleAssignments_pageTitle": "Vehicle Assignment History", "vehicleAssignments_backToVehicleDetails": "Back to Vehicle Details", "vehicleAssignments_assignmentHistoryTitle": "Assignment History", "vehicleAssignments_viewVehicleDetails": "View Vehicle Details", "vehicleAssignments_errorPrefix": "Error: ", "vehicleAssignments_noAssignmentHistoryTitle": "No Assignment History", "vehicleAssignments_noAssignmentHistoryDescription": "This vehicle has not been assigned to any drivers yet.", "vehicleAssignments_summaryTitle": "Summary", "vehicleAssignments_totalAssignmentsLabel": "Total Assignments", "vehicleAssignments_currentStatusLabel": "Current Status", "vehicleAssignments_currentDriverLabel": "Current Driver", "vehicleAssignments_assignmentTimelineTitle": "Assignment Timeline", "vehicleAssignments_assignmentTimelineDescription": "Complete history of driver assignments for this vehicle", "vehicleAssignments_unknownDriver": "Unknown Driver", "vehicleAssignments_noPhoneNumber": "No phone number", "vehicleAssignments_assignedLabel": "Assigned:", "vehicleAssignments_unassignedLabel": "Unassigned:", "vehicleAssignments_durationLabel": "Duration:", "vehicleAssignments_reasonLabel": "Reason:", "vehicleAssignments_assignedByAdmin": "Assigned by admin", "vehicleAssignments_unassignedByAdmin": "Unassigned by admin", "vehicleAssignments_activeStatus": "Active", "vehicleAssignments_completedStatus": "Completed", "vehicleAssignments_viewDriver": "View Driver", "vehicleAssignments_activeForDuration": "Active for {duration}", "vehicleAssignments_durationDays": "{days} day{plural}", "vehicleAssignments_durationHours": "{hours} hour{plural}", "vehicleAssignments_durationDaysHours": "{days} day{dayPlural} {hours} hour{hourPlural}", "vehicleAssignments_durationNA": "N/A", "vehicleAssignments_loadAssignmentHistoryFailed": "Failed to load assignment history", "vehicleAssignmentsList_pageTitle": "Vehicle Assignments", "vehicleAssignmentsList_title": "Vehicle Assignment History", "vehicleAssignmentsList_description": "Track vehicle assignments and driver history across your fleet", "vehicleAssignmentsList_errorPrefix": "Error: ", "vehicleAssignmentsList_searchPlaceholder": "Search assignments...", "vehicleAssignmentsList_showOnlyActiveLabel": "Show only active assignments", "vehicleAssignmentsList_assignmentCount_one": "{count} assignment", "vehicleAssignmentsList_assignmentCount_other": "{count} assignments", "vehicleAssignmentsList_noAssignmentsFoundTitle": "No assignments found", "vehicleAssignmentsList_noAssignmentsFoundDescription": "Try adjusting your filters", "vehicleAssignmentsList_noAssignmentsYetDescription": "No vehicle assignments have been made yet", "vehicleAssignmentsList_unknownVehicle": "Unknown Vehicle", "vehicleAssignmentsList_unknownDriver": "Unknown Driver", "vehicleAssignmentsList_noPhone": "No phone", "vehicleAssignmentsList_durationLabel": "Duration:", "vehicleAssignmentsList_activeStatus": "Active", "vehicleAssignmentsList_completedStatus": "Completed", "vehicleAssignmentsList_viewVehicleButton": "View Vehicle", "vehicleAssignmentsList_viewDriverButton": "View Driver", "vehicleAssignmentsList_unassignedOnPrefix": "Unassigned on", "vehicleAssignmentsList_byAdminSuffix": "by admin", "paymentForm_paymentMethodLabel": "Payment Method", "paymentForm_cashPaymentLabel": "Cash Payment", "paymentForm_mobileMoneyLabel": "Mobile Money", "paymentForm_mobileOperatorLabel": "Mobile Money Operator", "paymentForm_mvolaLabel": "MVola", "paymentForm_airtelMoneyLabel": "Airtel Money", "paymentForm_orangeMoneyLabel": "Orange Money", "paymentForm_amountLabel": "Amount (Ar)", "paymentForm_amountPlaceholder": "Enter payment amount", "paymentForm_suggestedAmountLabel": "Suggested amount:", "paymentForm_remarksLabel": "Additional Remarks (Optional)", "paymentForm_remarksPlaceholder": "Any additional information about the payment", "paymentForm_processingButton": "Processing...", "paymentForm_createPaymentButton": "Create Payment", "paymentForm_paymentMethodRequiredError": "You need to select a payment method", "paymentForm_mobileOperatorRequiredError": "You need to select a mobile operator", "paymentForm_amountRequiredError": "Amount is required", "paymentForm_amountInvalidError": "Amount must be a number", "paymentForm_amountMinError": "Amount must be greater than 0", "paymentForm_createPaymentFailedToast": "Failed to create payment", "assignDriver_loadingTitle": "Loading Trip", "assignDriver_loadingDescription": "Please wait while we load the trip information...", "assignDriver_assignDriverTitle": "Assign Driver", "assignDriver_selectDriverDescription": "Select a driver to assign to trip #{tripId}", "assignDriver_noAvailableDrivers": "No available drivers found", "assignDriver_notEnoughSeats": "Not enough seats (has {driverCapacity}, needs {passengerCount})", "assignDriver_tripAlreadyAssignedTitle": "Trip Already Assigned", "assignDriver_tripAlreadyAssignedDescription": "Trip #{tripId} already has an assigned driver", "assignDriver_tripAlreadyAssignedMessage": "This trip has already been assigned to a driver and is in progress.", "assignDriver_goBackButton": "Go Back", "assignDriver_tripNotFoundTitle": "Trip Not Found", "assignDriver_tripNotFoundDescription": "Trip with ID={tripId} could not be found", "assignDriver_tripNotFoundMessage": "This trip may have been deleted or you may have followed an invalid link.", "assignDriver_dialogTitle": "Assign or Request Driver", "assignDriver_dialogDescription": "What action would you like to perform with {driver<PERSON><PERSON>}?", "assignDriver_capacityWarningTitle": "Capacity Warning:", "assignDriver_capacityWarningMessage": "This driver can only accommodate {capacity} passengers, but this trip requires {passengerCount} passengers. You can still assign the driver, but they may not have enough seats.", "assignDriver_cancelButton": "Cancel", "assignDriver_requestButton": "Request", "assignDriver_requestingButton": "Requesting...", "assignDriver_assignButton": "Assign", "assignDriver_assigningButton": "Assigning...", "assignDriver_tripAlreadyAssignedToast": "This trip already has an assigned driver", "assignDriver_driverLocationUnknownToast": "Driver location is unknown. Can<PERSON> request driver.", "assignDriver_requestSuccessToast": "Request sent to {driver<PERSON><PERSON>}", "assignDriver_requestFailedToast": "Failed to request driver. Please try again.", "assignDriver_assignSuccessToast": "Driver {driver<PERSON><PERSON>} assigned successfully", "assignDriver_driverNotFoundToast": "Driver not found. Please select another driver.", "assignDriver_driverOccupiedToast": "Driver is already occupied. Please select another driver.", "assignDriver_assignUnknownErrorToast": "An unknown error occurred. Please try again.", "assignDriver_assignFailedToast": "Failed to assign driver. Please try again.", "tripStatusBadge_preparing": "Preparing", "tripStatusBadge_requestingDriver": "Requesting Driver", "tripStatusBadge_reserved": "Reserved", "tripStatusBadge_driverApproaching": "Driver Approaching", "tripStatusBadge_driverAwaiting": "Driver Awaiting", "tripStatusBadge_inProgress": "In Progress", "tripStatusBadge_completed": "Completed", "tripStatusBadge_cancelled": "Cancelled", "tripStatusBadge_paid": "Paid", "tripStatusBadge_unknown": "Unknown", "tripDetails_passengerLabel": "Passenger", "tripDetails_driverLabel": "Driver", "tripDetails_tripInformationLabel": "Trip Information", "tripDetails_statusLabel": "Status", "tripDetails_passengersLabel": "Passengers", "tripDetails_reservationTypeLabel": "Reservation Type", "tripDetails_fullDayType": "Full Day", "tripDetails_punctualType": "Punctual", "tripDetails_pricingOptionLabel": "Pricing Option", "tripDetails_fixedPriceOption": "75 (including gas)", "tripDetails_gasExcludedPriceOption": "25 (gas paid separately)", "tripDetails_fromLabel": "From", "tripDetails_toLabel": "To", "tripDetails_distanceTraveledLabel": "Distance Traveled", "tripDetails_estimatedDistanceLabel": "Est. Distance", "tripDetails_requestedPaymentMethodLabel": "Requested Payment method", "tripDetails_estimatedDurationLabel": "<PERSON><PERSON><PERSON>", "tripDetails_intakeSourceLabel": "Intake Source", "tripDetails_tripTimelineLabel": "Trip Timeline", "tripDetails_driverArrivedAwaitingLabel": "Driver arrived and awaiting pickup", "tripDetails_passengerStartTimeLabel": "Passenger start time", "tripDetails_completedLabel": "Completed", "tripDetails_cancelledLabel": "Cancelled", "tripDetails_costBreakdownLabel": "💰 Cost Breakdown", "tripDetails_initialEstimateLabel": "📊 Initial Estimate", "tripDetails_realCostLabel": "🚗 Real Cost (Calculated)", "tripDetails_costCappedLabel": "⚠️ Cost Capped (Traffic Protection)", "tripDetails_costCappedValue": "10% limit applied", "tripDetails_adminOverrideLabel": "🔧 Admin Override", "tripDetails_adminOverrideReasonPrefix": "Reason:", "tripDetails_finalCostLabel": "💳 Final Cost (Customer Pays)", "tripDetails_overridePriceButton": "🔧 Override Price", "tripDetails_recalculateButton": "🔄 Recalculate from Logs", "tripDetails_basePriceLabel": "Base Price", "tripDetails_distanceCostGasLabel": "Distance Cost (Gas)", "tripDetails_distanceCostLabel": "Distance Cost", "tripDetails_durationCostLabel": "Duration Cost", "tripDetails_totalCostLabel": "Total Cost", "tripDetails_paymentsReceivedLabel": "Payments Received", "tripDetails_unknownPaymentMethod": "unknown", "tripDetails_skippedDriversLabel": "Skipped Drivers", "tripDetails_overrideTripCostDialogTitle": "🔧 Override Trip Cost", "tripDetails_overrideTripCostDialogDescription": "Manually set a custom cost for this trip. This will override the calculated cost.", "tripDetails_newCostLabel": "New Cost (Ar)", "tripDetails_newCostPlaceholder": "Enter new cost...", "tripDetails_reasonLabel": "Reason (Optional)", "tripDetails_reasonPlaceholder": "Reason for cost override...", "tripDetails_updatingButton": "Updating...", "tripDetails_overrideCostButton": "Override Cost", "tripDetails_deleteDialogTitle": "Delete Trip", "tripDetails_deleteDialogDescription": "Are you sure you want to delete this trip?", "tripDetails_receivePaymentDialogTitle": "Receive Payment", "tripDetails_receivePaymentDialogDescription": "Create a new payment for this trip. For cash payments, the driver will receive the payment.", "tripDetails_cancelTripDialogTitle": "Cancel Trip", "tripDetails_cancelTripDialogDescription": "Are you sure you want to cancel this trip? This action cannot be undone.", "tripDetails_recalculateDialogTitle": "🔄 Recalculate Trip from All Logs", "tripDetails_recalculateDialogDescription": "This will recalculate the trip distance and costs from ALL GPS logs. This ensures 100% accuracy by processing every single log entry from the beginning.", "tripDetails_recalculateNoteTitle": "⚠️ Note:", "tripDetails_recalculateNoteDescription": "This operation will:", "tripDetails_recalculateNote1": "Process all GPS logs from the trip", "tripDetails_recalculateNote2": "Recalculate total distance traveled", "tripDetails_recalculateNote3": "Recalculate all cost components", "tripDetails_recalculateNote4": "Update the trip if differences are found", "tripDetails_lastRecalculationResultsTitle": "📊 Last Recalculation Results:", "tripDetails_distanceDifferenceLabel": "Distance difference:", "tripDetails_costDifferenceLabel": "Cost difference:", "tripDetails_recalculatingButton": "Recalculating...", "tripDetails_recalculateNowButton": "Recalculate Now", "tripDetails_viewPaymentDetailsAriaLabel": "View payment details", "tripDetails_costOverrideSuccessToast": "Trip cost successfully overridden to {amount}", "tripDetails_costOverrideFailedToast": "Failed to override trip cost. Please try again.", "tripDetails_recalculateSuccessToast": "Trip recalculated successfully! Distance updated by {distance}km, cost updated by {cost}", "tripDetails_recalculateNoChangesToast": "Recalculation complete. No significant differences found.", "tripDetails_recalculateFailedToast": "Failed to recalculate trip. Please try again.", "tripDetails_confirmPaymentFailedToast": "Failed to confirm payment", "tripsList_searchModeToggle": "Search Mode", "tripsList_algoliaSearchPlaceholder": "Search trips by location, passenger, driver...", "tripsList_unknownPassenger": "Unknown Passenger", "tripsList_fromLabel": "From:", "tripsList_toLabel": "To:", "tripsList_statusLabel": "Status:", "tripsList_notAvailable": "N/A", "algoliaSearch_defaultSearchPlaceholder": "Search...", "algoliaSearch_noResultsFound": "No results found", "algoliaSearch_notConfigured": "Search is not configured", "algoliaSearch_searchFailed": "Search failed. Please try again.", "algoliaSearch_indexNotConfigured": "Search index needs configuration. Contact administrator."}