#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Remove duplicate keys from a JSON object, keeping the last occurrence
 * @param {string} filePath - Path to the JSON file
 * @returns {Object} - Object with duplicates removed and stats
 */
function removeDuplicatesFromFile(filePath) {
  console.log(`\n📁 Processing: ${path.basename(filePath)}`);

  try {
    // Read the file content
    const content = fs.readFileSync(filePath, 'utf8');

    // Parse JSON while tracking duplicates
    const lines = content.split('\n');
    const seenKeys = new Set();
    const duplicateKeys = [];
    const cleanedLines = [];

    let inObject = false;
    let braceCount = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // Track if we're inside the main object
      if (trimmedLine.includes('{')) {
        braceCount += (trimmedLine.match(/\{/g) || []).length;
        inObject = braceCount > 0;
      }
      if (trimmedLine.includes('}')) {
        braceCount -= (trimmedLine.match(/\}/g) || []).length;
        if (braceCount === 0) inObject = false;
      }

      // Check for key-value pairs (lines with quotes and colon)
      if (inObject && trimmedLine.includes(':') && trimmedLine.startsWith('"')) {
        const keyMatch = trimmedLine.match(/^"([^"]+)"\s*:/);
        if (keyMatch) {
          const key = keyMatch[1];

          if (seenKeys.has(key)) {
            duplicateKeys.push({
              key,
              line: i + 1,
              content: trimmedLine
            });
            console.log(`  ❌ Duplicate found: "${key}" at line ${i + 1}`);
            // Skip this line (remove duplicate)
            continue;
          } else {
            seenKeys.add(key);
          }
        }
      }

      cleanedLines.push(line);
    }

    // Write back the cleaned content
    const cleanedContent = cleanedLines.join('\n');
    fs.writeFileSync(filePath, cleanedContent, 'utf8');

    // Verify the JSON is still valid
    try {
      JSON.parse(cleanedContent);
      console.log(`  ✅ File processed successfully`);
      console.log(`  📊 Total keys: ${seenKeys.size}`);
      console.log(`  🗑️  Duplicates removed: ${duplicateKeys.length}`);

      return {
        success: true,
        totalKeys: seenKeys.size,
        duplicatesRemoved: duplicateKeys.length,
        duplicateKeys: duplicateKeys
      };
    } catch (parseError) {
      console.error(`  ❌ JSON parsing failed after cleanup: ${parseError.message}`);
      return {
        success: false,
        error: `JSON parsing failed: ${parseError.message}`
      };
    }

  } catch (error) {
    console.error(`  ❌ Error processing file: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Main function to process both translation files
 */
function main() {
  console.log('🔧 Translation Duplicate Remover');
  console.log('==================================');

  const messagesDir = __dirname;
  const enFile = path.join(messagesDir, 'en-us.json');
  const frFile = path.join(messagesDir, 'fr-fr.json');

  // Check if files exist
  if (!fs.existsSync(enFile)) {
    console.error(`❌ English file not found: ${enFile}`);
    process.exit(1);
  }

  if (!fs.existsSync(frFile)) {
    console.error(`❌ French file not found: ${frFile}`);
    process.exit(1);
  }

  // Process both files
  const enResult = removeDuplicatesFromFile(enFile);
  const frResult = removeDuplicatesFromFile(frFile);

  // Summary
  console.log('\n📋 Summary');
  console.log('===========');

  if (enResult.success && frResult.success) {
    const totalDuplicates = enResult.duplicatesRemoved + frResult.duplicatesRemoved;

    if (totalDuplicates > 0) {
      console.log(`✅ Successfully removed ${totalDuplicates} duplicate keys`);
      console.log(`   • English: ${enResult.duplicatesRemoved} duplicates removed`);
      console.log(`   • French: ${frResult.duplicatesRemoved} duplicates removed`);
    } else {
      console.log('✅ No duplicate keys found in either file');
    }

    console.log(`📊 Total translation keys:`);
    console.log(`   • English: ${enResult.totalKeys} keys`);
    console.log(`   • French: ${frResult.totalKeys} keys`);

    // Check for key count mismatch
    if (enResult.totalKeys !== frResult.totalKeys) {
      console.log(`⚠️  Warning: Key count mismatch between files!`);
      console.log(`   Consider reviewing the translations for completeness.`);
    }

  } else {
    console.error('❌ Failed to process one or more files');
    if (!enResult.success) {
      console.error(`   English file error: ${enResult.error}`);
    }
    if (!frResult.success) {
      console.error(`   French file error: ${frResult.error}`);
    }
    process.exit(1);
  }
}

// Run the script
main();

export { removeDuplicatesFromFile, main }; 