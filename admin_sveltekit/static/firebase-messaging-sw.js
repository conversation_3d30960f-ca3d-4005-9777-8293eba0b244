// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.7.0/firebase-messaging-compat.js');

// Initialize Firebase app in service worker
firebase.initializeApp({
  apiKey: "AIzaSyDQodhOpgAzBzEWJEAY8CxOOJhBQjm5lIE",
  authDomain: "fiaranow-384e9.firebaseapp.com",
  projectId: "fiaranow-384e9",
  storageBucket: "fiaranow-384e9.appspot.com",
  messagingSenderId: "125236271589",
  appId: "1:125236271589:web:f5c45f017d9f3bb7b0a26f"
});

// Retrieve Firebase Messaging instance
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage(function (payload) {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);

  // Determine notification tag based on type
  let tag = 'default';
  let actionTitle = 'View';

  if (payload.data) {
    switch (payload.data.type) {
      case 'chat_message':
        tag = payload.data.chatSessionId || 'chat';
        actionTitle = 'View Chat';
        break;
      case 'trip_booking':
      case 'trip_cancelled':
        tag = payload.data.tripId || 'trip';
        actionTitle = 'View Trip';
        break;
      case 'feedback_submitted':
        tag = payload.data.feedbackId || 'feedback';
        actionTitle = 'View Feedback';
        break;
    }
  }

  // Customize notification here
  const notificationTitle = payload.notification?.title || 'New Notification';
  const notificationOptions = {
    body: payload.notification?.body || 'You have a new notification',
    icon: '/icon-192.png',
    badge: '/icon-72.png',
    tag: tag,
    data: payload.data,
    actions: [
      {
        action: 'view',
        title: actionTitle
      },
      {
        action: 'close',
        title: 'Close'
      }
    ]
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click
self.addEventListener('notificationclick', function (event) {
  console.log('[firebase-messaging-sw.js] Notification click received.');

  event.notification.close();

  // Handle action clicks
  if (event.action === 'close') {
    return;
  }

  // Determine URL based on notification type
  let url = '/';
  const data = event.notification.data;

  if (data) {
    switch (data.type) {
      case 'chat_message':
        url = data.chatSessionId ? `/support/chats/${data.chatSessionId}` : '/support/chats';
        break;
      case 'trip_booking':
      case 'trip_cancelled':
        url = data.tripId ? `/rides/trips/${data.tripId}/details` : '/rides/trips';
        break;
      case 'feedback_submitted':
        url = data.feedbackId ? `/support/feedbacks/${data.feedbackId}` : '/support/feedbacks';
        break;
      default:
        url = '/';
    }
  }

  // Open the appropriate page in a new or existing window
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then(function (clientList) {
        // Check if there's already a window open with the admin panel
        for (let client of clientList) {
          if (client.url.includes(self.location.origin) && 'focus' in client) {
            // Navigate to the specific page
            client.navigate(url);
            return client.focus();
          }
        }
        // If no window is open, open a new one
        if (clients.openWindow) {
          return clients.openWindow(url);
        }
      })
  );
});

// Service worker installation and activation
self.addEventListener('install', function (event) {
  console.log('[firebase-messaging-sw.js] Service Worker installing.');
  self.skipWaiting();
});

self.addEventListener('activate', function (event) {
  console.log('[firebase-messaging-sw.js] Service Worker activating.');
  event.waitUntil(clients.claim());
});