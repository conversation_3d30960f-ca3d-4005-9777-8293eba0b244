// Google Profile Photos Caching Service Worker
// This service worker will cache Google profile photos for 1 week

const CACHE_NAME = 'fiaranow-google-photos-v1';
const CACHE_DURATION = 7 * 24 * 60 * 60 * 1000; // 1 week in milliseconds

// Check if a URL is a Google profile photo
function isGoogleProfilePhoto(url) {
  // Match Google profile photo URLs from lh3.googleusercontent.com
  return url.includes('lh3.googleusercontent.com/a/');
}

// Install event - cache initial resources
self.addEventListener('install', (event) => {
  self.skipWaiting(); // Activate immediately
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches
      .keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (
              (cacheName.startsWith('fiaranow-images-') || cacheName.startsWith('fiaranow-google-photos-')) &&
              cacheName !== CACHE_NAME
            ) {
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        return self.clients.claim(); // Take control of all clients
      })
  );
});

// Fetch event - intercept network requests
self.addEventListener('fetch', (event) => {
  const request = event.request;
  const url = request.url;

  // Only handle GET requests for Google profile photos
  if (request.method !== 'GET' || !isGoogleProfilePhoto(url)) {
    return; // Let the default handler take over
  }

  event.respondWith(
    caches.open(CACHE_NAME).then(async (cache) => {
      try {
        // Check if we have a cached version
        const cachedResponse = await cache.match(request);

        if (cachedResponse) {
          // Check if the cached response is still fresh (within 1 week)
          const cachedDate = cachedResponse.headers.get('sw-cached-date');
          if (cachedDate) {
            const cacheAge = Date.now() - parseInt(cachedDate);
            if (cacheAge < CACHE_DURATION) {
              return cachedResponse;
            } else {
              // Cache expired, delete it
              await cache.delete(request);
            }
          }
        }

        // Fetch from network
        const networkResponse = await fetch(request);

        if (networkResponse && networkResponse.status === 200) {
          // Clone the response before caching
          const responseToCache = networkResponse.clone();

          // Add custom header to track cache date
          const headers = new Headers(responseToCache.headers);
          headers.set('sw-cached-date', Date.now().toString());
          headers.set('cache-control', 'max-age=604800'); // 1 week

          // Create new response with updated headers
          const cachedResponseWithHeaders = new Response(responseToCache.body, {
            status: responseToCache.status,
            statusText: responseToCache.statusText,
            headers: headers,
          });

          // Cache the response
          await cache.put(request, cachedResponseWithHeaders);
          console.log('[Cache SW] Cached Google profile photo:', url);
        }

        return networkResponse;
      } catch (error) {
        // Try to return cached version as fallback
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
          return cachedResponse;
        }

        // If no cache and network failed, let it fail naturally
        throw error;
      }
    })
  );
});

// Message event - handle commands from main thread
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'CLEAR_IMAGE_CACHE') {
    event.waitUntil(
      caches.delete(CACHE_NAME).then(() => {
        event.ports[0].postMessage({ success: true });
      })
    );
  }
});
