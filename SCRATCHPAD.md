- Notifications
  - Notification for admins when a trip has been booked
    - Integration with push notification
      - Direct link to view the trip when clicking on the notification popup
    - Notification of multiple admins in case of booking, regardless of which account
  - Notification to admin when feedback has been submitted
  - Notification to admin when client cancels the booking
- There is already an Admin notification system there, but we want to make sure to cover all of these cases above




# Plan request

Create a solid plan:
- Plan needs to be
  - Pragmatic, no unnecessary noises
  - Actionable
  - Deeply analyzed
    - Read each line carefully
    - Go thru deeply inside every feature, class, functions involved to suggest the best approach and edits
      - Actively use Cotext Engine to help you know where everything is at
      - Features need to integrate perfectly with existing system
        - If there is already existing features, functionality, it should be used instead of creating a new one
        - If there is already a model that naturally fits the new requirements, it should be reused
    - Ultrathink for deep insight
    - Covers all parts of the codebase
  - Include detailed references or required modifications
    - File path, function name, class name of files that will need modifications
    - Folder structure for new files
    - All based on existing codebase features and codes
  - It's a plan, not an implementation
    - Code snippets to minimum, just the essentials
    - No need of timeline
    - If there are technical constraints that need to be considered, include them
    - Include reason for mutations in the mentioned code/parts
  - Only assess and report the highest level of risk
- No testing strategy required
- No implementation phase or sequence required
- No need to include a success criteria, focus on a solid plan for a solid implementation
- Ask clarifying questions if you do have, **before writing the plan**
- Save the plan to task11.md file




# Task execution

Implement the @/Users/<USER>/Projects/Fiaranow/task11.md which has already been carefully prepared for you to have the necessary references for all the updates. Time how long you execute the task (use `date`). No new `TODO` or `FIXME` should be left behind, everything must be fully implemented. Ask clarifying questions now if you have, before you start timing and implementing. If you have clarification questions, stop to get answer from me first. At the very end of the implementation, address diagostic issues related to this task that may have lingered. Follow to the least details.
No testing required here, just make sure that, in the very end, the code you write doesn't end-up having diagnostic issues (run build, analyze, ... to validte).
If needed only:
- for Flutter, check @/Users/<USER>/Projects/Fiaranow/.claude/commands/translate-flutter.md for translation instructions
- for Admin/Svelte codes, check @/Users/<USER>/Projects/Fiaranow/.claude/commands/translate-admin.md for translations instruction.
Use your ToDo list tool along the way to keep yourself on track, and I can also see your progress.


# Check lists
Can you check if the current codebase actually aligns with the original requirements? Here are some checklist (if applicable) but not exhaustive:
- No lingering TODO or FIXME in the newly created or updated files
- Firebase Functions structured correctly
  - For transactions
    - All reads go first
    - All mutations follow
- Admin
  - Only Svelte 5 runes are used, no legacy codes
Amongst other checks thru pure core analysis