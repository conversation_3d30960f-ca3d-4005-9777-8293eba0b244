# Models Documentation

## Entity Relationship Diagram

```mermaid
erDiagram
    %% Global Collections
    MobileUser ||--o{ MobileUserTenantState : "has states per tenant"
    MobileUser ||--o{ DriverDocument : "has personal documents"
    MobileUser ||--o{ Vehicle : "owns vehicles"

    Vehicle ||--o{ VehicleLinking : "linked to tenants"
    Vehicle ||--o{ DriverDocument : "has vehicle documents"

    VehicleAssignment ||--|| Vehicle : "tracks history for"
    VehicleAssignment ||--|| VehicleLinking : "references"
    VehicleAssignment ||--|| MobileUser : "assigned to"

    AdminUser ||--o{ TenantAccess : "has access per tenant"

    Tenant ||--o{ TripConfiguration : "has config"
    Tenant ||--o{ Trip : "contains trips"
    Tenant ||--o{ Payment : "contains payments"
    Tenant ||--o{ EventLog : "contains logs"
    Tenant ||--o{ Feedback : "contains feedback"
    Tenant ||--o{ ChatThread : "contains chats"
    Tenant ||--o{ AdminNotification : "contains notifications"
    Tenant ||--o{ VehicleLinking : "links vehicles"

    %% Tenant-Specific Collections
    Trip ||--|| Payment : has
    Trip ||--o{ TripLog : contains
    Trip ||--|| TripConfiguration : "uses snapshot"
    Trip ||--o{ ChatThread : "related to"

    ChatThread ||--o{ ChatMessage : contains

    EventLog ||--o{ Trip : "references"
    EventLog ||--o{ MobileUser : "references"

    Feedback ||--o{ ChatThread : "spawns"
    Feedback ||--o{ AdminNotification : triggers

    AdminNotification ||--|| AdminUser : "sent to"

    %% Multi-tenant relationships
    MobileUserTenantState ||--|| Tenant : "belongs to"
    MobileUserTenantState ||--o| VehicleLinking : "currently assigned"
    VehicleLinking ||--|| Tenant : "belongs to"
    VehicleLinking ||--|| Vehicle : "links"
    TenantAccess ||--|| Tenant : "grants access to"
```

## Data Models

### 1. `MobileUser` (Firestore Collection: `mobile_users`) - Global

Represents a user of the mobile application, who can be a passenger or a driver. This is a global collection shared across all tenants.

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `uid`                             | string                                                               | Yes      | Firebase Authentication User ID. This is the document ID.                                                    |
| `email`                           | string                                                               | No       | User's email address.                                                                                      |
| `displayName`                     | string                                                               | No       | User's display name.                                                                                       |
| `phoneNumber`                     | string                                                               | No       | User's phone number (E.164 format).                                                                        |
| `phoneNumberVerified`             | boolean                                                              | No       | Whether the phone number has been verified. Default: `false`.                                      |
| `phoneNumberVerificationMethod`   | enum (`sms`, `whatsapp`)                                             | No       | Method used for phone number verification.                                                           |
| `photoURL`                        | string                                                               | No       | URL to the user's profile picture.                                                                         |
| `lastSeen`                        | timestamp                                                            | No       | Server timestamp of the user's last activity.                                                              |
| `lastSeenDT`                      | timestamp                                                            | No       | Device timestamp of the user's last activity (for clock skew detection).                                     |
| `primaryUserType`                 | enum (`rider`, `driver`)                                             | No       | The primary role selected by the user.                                                                     |
| `primaryLanguage`                 | object (`{ languageCode: string, countryCode: string }`)             | No       | User's preferred language (e.g., `{ languageCode: "fr", countryCode: "FR" }`).                             |
| `lat`                             | number (double)                                                      | No       | Last known latitude of the user.                                                                           |
| `lon`                             | number (double)                                                      | No       | Last known longitude of the user.                                                                          |
| `position`                        | object (`{ geohash: string, geopoint: GeoPoint }`)                   | No       | Geohash and GeoPoint for location queries.                                                                 |
| `occupiedByTripId`                | string                                                               | No       | **Global field**: ID of the trip the driver is currently occupied with (across all tenants).                                                      |
| `isServiceActiveByTenant`         | map (`{ tenantId: boolean }`)                                         | No       | **Multi-tenant**: Denormalized service status per tenant. Synced automatically from `MobileUserTenantState.isServiceActive` for efficient querying. |
| `fcmToken`                        | string                                                               | No       | Firebase Cloud Messaging token for push notifications.                                                     |
| `deviceId`                        | string                                                               | No       | Unique identifier for the user's current device (used for single-device session management).               |
| `tenantIDs`                       | array of string                                                      | No       | **Multi-tenant**: Array of tenant IDs for performance optimization in queries.                                                                     |
| `notificationPreferences`         | object                                                               | No       | User's notification preferences.                                                                     |
| `notificationPreferences.enableSoundForTripUpdates` | boolean                                          | No       | Passenger's explicit preference for sound on trip updates (e.g., `true` for sound, `false` for no sound). If this field is set by the passenger, its value is used. If not set by the passenger, the system falls back to the global configuration `Configuration.tripConfiguration.adminDefaultSoundForTripUpdates`. |
| `whatsappPhoneNumber`             | string                                                               | No       | User's WhatsApp number, if different from `phoneNumber` or for specific verification.                |
| `whatsappVerified`                | boolean                                                              | No       | Whether the WhatsApp number is verified. Default: `false`.                                           |
| `driver_documents`                | subcollection (`DriverDocument`)                                     | No       | Subcollection for driver-specific documents (e.g., license). **Note**: Include `tenantId` field for multi-tenant document management.                                         |

**Subcollection: `tenant_states`** (under `mobile_users/{uid}/tenant_states`)

See `MobileUserTenantState` model below for detailed structure.

---

### 2. `Tenant` (Firestore Collection: `tenants`) - Global

Represents a taxi company/organization operating within the system.

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `id` (document ID)                | string                                                               | Yes      | Unique tenant identifier (e.g., "fiaranow", "company2").                                                   |
| `name`                            | string                                                               | Yes      | Display name of the tenant organization.                                                                   |
| `isActive`                        | boolean                                                              | Yes      | Whether the tenant can operate. Default: `true`.                                                           |
| `createdAt`                       | timestamp                                                            | Yes      | Timestamp of when the tenant was created.                                                                  |
| `settings`                        | object                                                               | No       | Tenant-specific configurations.                                                                             |
| `settings.branding`               | object (`{ primaryColor: string, logo: string }`)                    | No       | Branding configuration for the tenant.                                                                     |
| `settings.features`               | object (`{ enableChat: boolean, enableFeedback: boolean }`)          | No       | Feature toggles for the tenant.                                                                            |

**Tenant-Specific Collections**: All collections under `/tenants/{tenantId}/` are isolated per tenant.

---

### 3. `Vehicle` (Firestore Collection: `vehicles`) - Global

Represents a vehicle that can be used across tenants. This is global vehicle data without tenant-specific information.

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `id` (document ID)                | string                                                               | Yes      | Unique ID for the vehicle.                                                                                 |
| `brand`                           | string                                                               | Yes      | Vehicle brand (e.g., Toyota).                                                                              |
| `model`                           | string                                                               | Yes      | Vehicle model (e.g., Yaris).                                                                               |
| `color`                           | string                                                               | Yes      | Main color of the vehicle.                                                                                 |
| `year`                            | number (int)                                                         | Yes      | Manufacturing year of the vehicle.                                                                         |
| `registrationNumber`              | string                                                               | Yes      | Vehicle's registration number (license plate).                                                             |
| `maxPassengers`                   | number (int)                                                         | Yes      | **CRITICAL**: Number of passenger seats available (excluding driver). Source of truth for capacity.        |
| `isActive`                        | boolean                                                              | Yes      | Whether this vehicle is active. Default: `true`.                                                           |
| `ownerUID`                        | string                                                               | No       | UID of the user who owns this vehicle (for user-owned vehicles). Null for tenant-owned vehicles.          |
| `photoURL`                        | string                                                               | No       | URL of a photo of the vehicle.                                                                             |
| `createdAt`                       | timestamp                                                            | Yes      | Timestamp of when the vehicle was added.                                                                   |
| `updatedAt`                       | timestamp                                                            | Yes      | Timestamp of the last update to vehicle details.                                                           |
| `createdBy`                       | string                                                               | Yes      | UID of admin or mobile user who created the vehicle.                                                       |

**Subcollection: `vehicle_documents`** (under `vehicles/{vehicleId}/vehicle_documents`)

See `DriverDocument` model below for detailed structure.

---

### 4. `VehicleLinking` (Firestore Collection: `/tenants/{tenantId}/vehicles_linking`) - Tenant-Specific

**Layer 1 of Vehicle Management**: Manages which vehicles are available to which tenants and their approval status.

**Important Business Rule**: When `tenantApproved` is `true`, the assigned driver can accept trips regardless of document validation status. This allows operations to continue while documents are being collected.

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `id` (document ID)                | string                                                               | Yes      | Unique linking ID.                                                                                         |
| `vehicleId`                       | string                                                               | Yes      | Reference to global vehicle document.                                                                      |
| `tenantId`                        | string                                                               | Yes      | Current tenant ID.                                                                                         |
| `tenantRemark`                    | string                                                               | No       | Admin notes about the vehicle in this tenant.                                                             |
| `tenantApproved`                  | boolean                                                              | No       | Whether tenant has approved this vehicle for use. **When true, enables driver to accept trips without document validation**.                                                         |
| `currentDriverId`                 | string                                                               | No       | UID of driver currently using this vehicle in this tenant.                                                |
| `linkedAt`                        | timestamp                                                            | Yes      | When the vehicle was linked to this tenant.                                                               |
| `linkedBy`                        | string                                                               | Yes      | Admin UID who linked the vehicle.                                                                         |
| `isActive`                        | boolean                                                              | Yes      | Whether this linking is active. Default: `true`.                                                          |
| `isOwnedByTenant`                 | boolean                                                              | Yes      | `true` for internal fleet, `false` for external vehicles.                                                 |

---

### 5. `VehicleAssignment` (Firestore Collection: `vehicle_assignments`) - Global

**Layer 2 of Vehicle Management**: Historical audit trail of all vehicle assignments across all tenants.

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `id` (document ID)                | string                                                               | Yes      | Unique assignment ID.                                                                                      |
| `vehicleId`                       | string                                                               | Yes      | Reference to global vehicle document.                                                                      |
| `vehicleLinkingId`                | string                                                               | Yes      | Reference to tenant vehicle linking.                                                                       |
| `driverUID`                       | string                                                               | Yes      | UID of assigned driver.                                                                                    |
| `tenantId`                        | string                                                               | Yes      | Tenant where assignment occurred.                                                                          |
| `createdAt`                       | timestamp                                                            | Yes      | For efficient sorting.                                                                                     |
| `assignedAt`                      | timestamp                                                            | Yes      | When assignment was made.                                                                                  |
| `assignedBy`                      | string                                                               | Yes      | Admin UID or 'system' who made assignment.                                                                |
| `unassignedAt`                    | timestamp                                                            | No       | When assignment ended.                                                                                     |
| `unassignedBy`                    | string                                                               | No       | Who ended the assignment.                                                                                  |
| `reason`                          | enum (`admin_assignment`, `driver_switch`, `vehicle_maintenance`, `driver_unavailable`) | Yes      | Reason for assignment.                                                                                     |
| `notes`                           | string                                                               | No       | Additional notes about assignment.                                                                         |
| `isActive`                        | boolean                                                              | Yes      | Whether this is the current active assignment.                                                            |

---

### 6. `MobileUserTenantState` (Firestore Subcollection: `/mobile_users/{uid}/tenant_states`) - Per-Tenant State

Manages user state per tenant, including driver-specific information.

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `uid`                             | string                                                               | Yes      | User UID for grouped collection queries.                                                                   |
| `tenantId`                        | string                                                               | Yes      | Tenant ID (document ID).                                                                                   |
| `isActive`                        | boolean                                                              | Yes      | Whether user is active in this tenant.                                                                    |
| `currentVehicleLinkingId`         | string                                                               | No       | Currently assigned vehicle linking in this tenant.                                                        |
| `driverTags`                      | array of string                                                      | No       | Tags assigned to driver in this tenant.                                                                   |
| `isDriverConfirmed`               | string                                                               | No       | Admin UID who confirmed driver in this tenant.                                                            |
| `isServiceActive`                 | boolean                                                              | Yes      | **Source of truth**: Service status in this tenant. Changes automatically sync to `MobileUser.isServiceActiveByTenant`. Default: `false`. |
| `driverType`                      | enum (`internal`, `third_party`)                                     | No       | Type of driver in this tenant. Default: `third_party`.                                                    |
| `joinedAt`                        | timestamp                                                            | Yes      | When user joined this tenant.                                                                             |
| `lastActiveAt`                    | timestamp                                                            | Yes      | Last activity in this tenant.                                                                             |

---

### 7. `DriverDocument` (Firestore Subcollection)

Represents a document related to a driver or their vehicle. Can be under `mobile_users/{userId}/driver_documents` for personal documents or `vehicles/{vehicleId}/vehicle_documents` for vehicle-specific documents.

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `id` (document ID)                | string                                                               | Yes      | Unique ID for the document.                                                                                |
| `tenantId`                        | string                                                               | No       | **Multi-tenant**: Tenant ID for tenant-scoped document listing.                                           |
| `documentType`                    | enum (`license`, `insurance`, `vehicle_registration`, `national_id`, `other`) | Yes      | Type of the document.                                                                                      |
| `documentName`                    | string                                                               | Yes      | User-friendly name or title of the document (e.g., "Permis de Conduire Cat. B").                           |
| `fileURL`                         | string                                                               | Yes      | URL to the uploaded document file in Firebase Storage.                                                     |
| `issueDate`                       | timestamp                                                            | No       | Date the document was issued.                                                                              |
| `expiryDate`                      | timestamp                                                            | No       | Expiry date of the document.                                                                               |
| `notes`                           | string                                                               | No       | Short notes or remarks about the document by the driver or admin.                                          |
| `status`                          | enum (`pending_review`, `approved`, `rejected`, `expired`, `expiring_soon`) | Yes      | Current status of the document. Default: `pending_review`.                                                 |
| `uploadedAt`                      | timestamp                                                            | Yes      | Timestamp of when the document was uploaded.                                                               |
| `reviewedBy`                      | string                                                               | No       | UID of the admin who reviewed the document.                                                                |
| `reviewedAt`                      | timestamp                                                            | No       | Timestamp of when the document was reviewed.                                                               |
| `rejectionReason`                 | string                                                               | No       | Reason if the document was rejected by an admin.                                                           |

---

### 8. `AdminUser` (Firestore Collection: `admin_users`) - Global

Represents an administrator of the system. Admin access is managed per-tenant via subcollections.

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `uid` (document ID)               | string                                                               | Yes      | Firebase Authentication User ID.                                                                           |
| `email`                           | string                                                               | Yes      | Admin's email address.                                                                                     |
| `displayName`                     | string                                                               | Yes      | Admin's display name.                                                                                      |
| `photoURL`                        | string                                                               | No       | URL to the admin's profile picture.                                                                        |
| `isActive`                        | boolean                                                              | Yes      | Whether the admin account is active. Default: `false` for new admins.                                      |
| `fcmToken`                        | string                                                               | No       | Firebase Cloud Messaging token for push notifications (single token per admin).                            |
| `lastTokenUpdate`                 | timestamp                                                            | No       | Timestamp of when the FCM token was last updated.                                                         |
| `lastSeen`                        | timestamp                                                            | No       | Server timestamp of the admin's last activity.                                                               |
| `lastSeenDT`                      | timestamp                                                            | No       | Device timestamp of the admin's last activity.                                                               |
| `lastModified`                    | timestamp                                                            | No       | Timestamp of when the admin profile was last modified.                                                     |
| `whatsappPhoneNumber`             | string                                                               | No       | Admin's WhatsApp number for notifications.                                                           |
| `whatsappVerified`                | boolean                                                              | No       | Whether the admin's WhatsApp number is verified. Default: `false`.                                   |
| `whatsappVerificationCode`        | string                                                               | No       | Code sent for WhatsApp verification.                                                                 |
| `whatsappVerificationTimestamp`   | timestamp                                                            | No       | Timestamp of WhatsApp verification code generation.                                                  |
| `receiveWhatsappNotifications`    | boolean                                                              | No       | Whether the admin wishes to receive WhatsApp notifications. Default: `true`.                         |

**Subcollection: `tenants`** (under `admin_users/{uid}/tenants`)

See `TenantAccess` model below for detailed structure.

---

### 9. `TenantAccess` (Firestore Subcollection: `/admin_users/{uid}/tenants`) - Per-Tenant Access

Manages admin access and roles per tenant.

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `tenantId` (document ID)          | string                                                               | Yes      | Tenant ID.                                                                                                 |
| `isActive`                        | boolean                                                              | Yes      | Whether access to this tenant is active.                                                                  |
| `role`                            | number (int)                                                         | Yes      | Role level: 0=MANAGER, 1=ADMIN, 2=SUPER_ADMIN (only in 'fiaranow' tenant).                               |
| `assignedAt`                      | timestamp                                                            | Yes      | When access was granted.                                                                                   |
| `assignedBy`                      | string                                                               | Yes      | UID of admin who granted access.                                                                           |

---

### 10. `Trip` (Firestore Collection: `/tenants/{tenantId}/trips`) - Tenant-Specific

Represents a ride request, ongoing ride, or completed ride within a specific tenant.

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `id` (document ID)                | string                                                               | Yes      | Unique ID for the trip.                                                                                    |
| `uidPassenger`                    | string                                                               | Yes      | UID of the passenger.                                                                                      |
| `uidChosenDriver`                 | string                                                               | No       | UID of the driver chosen/assigned for the trip.                                                            |
| `createdAt`                       | timestamp                                                            | Yes      | Timestamp of when the trip was created.                                                                    |
| `status`                          | enum (`preparing`, `requestingDriver`, `reserved`, `driverApproaching`, `driverAwaiting`, `inProgress`, `completed`, `cancelled`, `paid`) | Yes      | Current status of the trip.                                                                                |
| `completedAt`                     | timestamp                                                            | No       | Timestamp of when the trip was completed.                                                                  |
| `cancelledAt`                     | timestamp                                                            | No       | Timestamp of when the trip was cancelled.                                                                  |
| `uidCancelledBy`                  | string                                                               | No       | UID of the user (passenger, driver, or admin) who cancelled the trip.                                      |
| `cancellationReasonPassenger`     | string                                                               | No       | **(New)** Reason provided by passenger for cancellation.                                                       |
| `cancellationContactRequested`    | boolean                                                              | No       | **(New)** If passenger requested contact after cancelling. Default: `false`.                                   |
| `startLocation`                   | object (`{ lat: double, lon: double }`)                              | No       | Geographic coordinates of the trip's starting point.                                                       |
| `arrivalLocation`                 | object (`{ lat: double, lon: double }`)                              | No       | Geographic coordinates of the trip's destination.                                                          |
| `driverLocation`                  | object (`{ lat: double, lon: double }`)                              | No       | Current geographic coordinates of the assigned driver.                                                     |
| `startLocationName`               | string                                                               | No       | Human-readable name of the start location.                                                                 |
| `arrivalLocationName`             | string                                                               | No       | Human-readable name of the arrival location.                                                               |
| `passengerCount`                  | number (int)                                                         | No       | **(New)** Number of passengers for the trip. Default: `1`.                                                     |
| `distanceTotalMeters`             | number (double)                                                      | No       | Total distance of the trip in meters (can be estimated or final).                                          |
| `routeData`                       | object (`RouteData`)                                                 | No       | Data for the planned route between start and arrival.                                                      |
| `driverRouteData`                 | object (`RouteData`)                                                 | No       | Data for the route from driver's current location to passenger's pickup.                                   |
| `finalRouteData`                  | object (`RouteData`)                                                 | No       | Actual route data recorded during an in-progress trip.                                                     |
| `routeOverviews`                  | array of `RouteData`                                                 | No       | Alternative route options presented to the user.                                                           |
| `selectedRouteIndex`              | number (int)                                                         | No       | Index of the route selected by the user from `routeOverviews`.                                             |
| `paymentId`                       | string                                                               | No       | ID of the associated payment document in the `payments` collection.                                        |
| `customerRequestedPaymentMethod`  | string                                                               | No       | Payment method initially requested by the customer (e.g., "cash", "mobile").                               |
| `driverStartTime`                 | timestamp                                                            | No       | Timestamp when the driver indicated the trip started.                                                      |
| `passengerStartTime`              | timestamp                                                            | No       | Timestamp when the passenger indicated the trip started.                                                   |
| `driverAwaitingTime`              | timestamp                                                            | No       | Timestamp when the driver arrived at pickup and started awaiting the passenger.                              |
| `passenger`                       | object (`{ uid: string, displayName: string, photoURL?: string, phoneNumber?: string }`) | Yes      | Snapshot of passenger details at the time of trip creation.                                                |
| `driver`                          | object (`{ uid: string, displayName: string, photoURL?: string, phoneNumber?: string, lat?: double, lon?: double }`) | No       | Snapshot of driver details when assigned. Includes live location.                                          |
| `driverDismissed`                 | boolean                                                              | Yes      | Whether the driver has dismissed/archived the trip from their view. Default: `false`.                      |
| `passengerDismissed`              | boolean                                                              | Yes      | Whether the passenger has dismissed/archived the trip from their view. Default: `false`.                   |
| `skippedDriverIds`                | array of string                                                      | No       | List of driver UIDs who have skipped or been skipped for this trip request.                                |
| `driverNotificationSent`          | boolean                                                              | No       | Whether a notification has been sent to the chosen driver for a new request.                               |
| `inTakeSource`                    | enum (`immediate`, `reservation`)                                    | No       | How the trip was initiated.                                                                                |
| `reservationType`                 | enum (`scheduled`, `fullDay`)                                        | No       | **(New)** Type of reservation, if `inTakeSource` is `reservation`.                                             |
| `fullDayPriceType`                | enum (`fixed`, `gasExcluded`)                                        | No       | **(New)** Pricing model for full-day reservations.                                                             |
| `clientAlarmTime`                 | timestamp                                                            | No       | For reservations: time to notify the client before pickup.                                                 |
| `pickupTime`                      | timestamp                                                            | No       | For reservations: scheduled pickup time.                                                                   |
| `costPrepaid`                     | number (double)                                                      | No       | Amount prepaid for the trip (e.g., for full-day reservations).                                             |
| `estimatedCost`                   | number (double)                                                      | No       | **(New)** Initial estimated cost of the trip based on `routeData`.                                           |
| `costTotal`                       | number (double)                                                      | No       | Final or currently calculated total cost of the trip. Updated dynamically for ongoing trips.               |
| `costDuration`                    | number (double)                                                      | No       | Component of the cost based on duration (calculated, may not be stored if `costTotal` is sufficient).      |
| `costDistance`                    | number (double)                                                      | No       | Component of the cost based on distance (calculated, may not be stored if `costTotal` is sufficient).      |
| `finalCostCalculationMethod`      | enum (`estimated_distance`, `estimated_duration_plus_congestion_fee`, `full_day_fixed`, `full_day_gas_excluded`) | No | **(New)** Method used to calculate the final cost.                                                             |
| `congestionSurchargeApplied`      | boolean                                                              | No       | **(New)** Whether a congestion surcharge was applied. Default: `false`.                                        |
| `congestionSurchargePercentage`   | number (double)                                                      | No       | **(New)** The percentage of congestion surcharge applied (e.g., 0.10 for 10%).                                 |
| `tripConfiguration`               | object (`TripConfigurationModel`)                                    | Yes      | Embedded copy of the trip configuration active at the time of trip creation.                               |
| `logs`                            | subcollection (`TripLog`)                                            | No       | Subcollection of location points logged during an in-progress trip.                                        |

**Embedded Object: `RouteData`** (within `Trip`)

| Field           | Type                                                                 | Required | Description                                                              |
| :-------------- | :------------------------------------------------------------------- | :------- | :----------------------------------------------------------------------- |
| `distance`      | number (double)                                                      | Yes      | Distance of the route in kilometers.                                     |
| `durationSec`   | number (int)                                                         | Yes      | Duration of the route in seconds.                                        |
| `polyline`      | string                                                               | No       | Encoded polyline string for the route.                                   |
| `bounds`        | object (`{ northeast: { lat: double, lng: double }, southwest: { lat: double, lng: double } }`) | Yes      | Bounding box for the route.                                              |
| `mapsPolylines` | array of object (`{ lat: double, lng: double }`)                     | Yes      | Decoded polyline points (client-side representation, not stored directly in Firestore if `polyline` is present). |

**Subcollection: `TripLog`** (under `trips/{tripId}/logs`)

| Field   | Type            | Required | Description                               |
| :------ | :-------------- | :------- | :---------------------------------------- |
| `lat`   | number (double) | Yes      | Latitude of the log point.                |
| `lon`   | number (double) | Yes      | Longitude of the log point.               |
| `ts`    | timestamp       | Yes      | Server timestamp of the log.              |
| `tsDT`  | timestamp       | Yes      | Device timestamp of the log.              |

---

### 11. `Payment` (Firestore Collection: `/tenants/{tenantId}/payments`) - Tenant-Specific

Represents a payment transaction for a trip within a specific tenant.

| Field                                 | Type                                                                 | Required | Description                                                                                                |
| :------------------------------------ | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `id` (document ID)                    | string                                                               | Yes      | Unique ID for the payment.                                                                                 |
| `tripId`                              | string                                                               | Yes      | ID of the associated trip.                                                                                 |
| `customerId`                          | string                                                               | Yes      | UID of the customer (passenger).                                                                           |
| `driverId`                            | string                                                               | No       | UID of the driver who received/is involved in the payment.                                                 |
| `customerRequestedPaymentMethod`      | enum (`cash`, `mobile`, `card`, `balance`, `other`)                  | Yes      | Payment method initially requested by the customer.                                                        |
| `customerRequestedPaymentMethodRemark`| string                                                               | No       | Additional remarks from customer about their requested payment method.                                     |
| `finalPaymentMethod`                  | enum (`cash`, `mobile`, `card`, `balance`, `other`)                  | No       | The actual payment method used.                                                                            |
| `finalPaymentMethodRemark`            | string                                                               | No       | Remarks about the final payment method (e.g., transaction ID for mobile money).                            |
| `amount`                              | number (double)                                                      | Yes      | Total amount of the payment transaction.                                                                   |
| `amountDue`                           | number (double)                                                      | No       | Actual amount received if different from `amount`.                                                         |
| `discount`                            | number (double)                                                      | No       | Discount amount applied to the payment.                                                                    |
| `discountReason`                      | string                                                               | No       | Reason for the discount.                                                                                   |
| `remark`                              | string                                                               | No       | General remarks about the payment.                                                                         |
| `status`                              | enum (`pending`, `processing`, `receivedByDriver`, `completed`, `failed`, `refunded`, `partiallyRefunded`, `disputed`) | Yes      | Current status of the payment.                                                                             |
| `createdAt`                           | timestamp                                                            | Yes      | Timestamp of when the payment record was created.                                                          |
| `processedAt`                         | timestamp                                                            | No       | Timestamp of when the payment started processing (e.g., for online payments).                              |
| `completedAt`                         | timestamp                                                            | No       | Timestamp of when the payment was successfully completed/confirmed.                                        |
| `processedByUid`                      | string                                                               | No       | UID of the admin who processed the payment (if applicable).                                                |
| `completedByUid`                      | string                                                               | No       | UID of the admin who marked the payment as completed (if applicable).                                      |
| `transactionId`                       | string                                                               | No       | External transaction ID from a payment gateway.                                                            |
| `receiptNumber`                       | string                                                               | No       | Internal or external receipt number.                                                                       |
| `metadata`                            | map                                                                  | No       | Additional payment-specific data (e.g., `{ "mobileOperator": "mvola" }`).                                  |

---

### 12. `Configuration` (Firestore Collection: `/tenants/{tenantId}/configurations`) - Tenant-Specific

Stores tenant-specific application configurations. Document ID is the configuration key.

**Note**: Global configurations (like app version requirements) remain in the root `/global_configurations` collection.

| Field (Document ID)      | Type                                      | Description                                                                 |
| :----------------------- | :---------------------------------------- | :-------------------------------------------------------------------------- |
| `tripConfiguration`      | object (`TripConfigurationModel`)         | Configuration related to trip pricing and behavior.                         |
| `appVersion`             | object (`{ requiredMinimumVersion: string, underMaintenanceMessage: string }`) | App version control and maintenance mode message.                           |
| *(other keys)*           | string / number / boolean                 | Other general configuration values.                                         |

**Embedded Object: `TripConfigurationModel`** (within `configurations` or `Trip`)

| Field                             | Type            | Required | Description                                                                                                |
| :-------------------------------- | :-------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `costPerKilometer`                | number (double) | Yes      | Cost per kilometer for trip calculation.                                                                   |
| `costPerHour`                     | number (double) | Yes      | Cost per hour for trip calculation.                                                                        |
| `minimumTripCost`                 | number (double) | Yes      | Minimum cost for any trip.                                                                                 |
| `waitTimeAfterExtraPayment`       | number (int)    | Yes      | Duration in minutes a driver waits after an extra payment chunk is made by passenger.                        |
| `costPerExtraWaitChunk`           | number (double) | Yes      | Cost for each additional chunk of waiting time.                                                            |
| `cancelCostPreStart`              | number (double) | Yes      | Cost incurred if a trip is cancelled before it starts (e.g., after driver is dispatched).                  |
| `nearbyDriverListedRadiusMeters`  | number (double) | Yes      | Radius in meters within which nearby drivers are listed for a passenger.                                   |
| `congestionSurchargeMaxPercentage`| number (double) | No       | **(New)** Maximum percentage for congestion surcharge (e.g., 0.10 for 10%). Default: `0.10`.                    |
| `adminDefaultSoundForTripUpdates` | boolean         | No       | **(New)** The administrator-configured default for enabling sound on trip updates for passengers. This value is used if the passenger has not set their own `enableSoundForTripUpdates` preference. Default: `true`. |

---

### 13. `EventLog` (Firestore Collection: `/tenants/{tenantId}/event_logs`) - Tenant-Specific

Records significant events in the system for auditing and debugging within a specific tenant.

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `id` (document ID)                | string                                                               | Yes      | Unique ID for the event log.                                                                               |
| `uid`                             | string                                                               | Yes      | UID of the user (driver, passenger, or admin) who triggered or is associated with the event.             |
| `type`                            | enum (`driverServiceStatusUpdate`, `driverTripRejected`, `driverRequestTimeout`, `adminNotificationSent`, `feedbackReceived`, `clientTripCancellation`) | Yes      | Type of the event.                                                                                         |
| `timestamp`                       | timestamp                                                            | Yes      | Server timestamp of when the event occurred.                                                               |
| `timestampDT`                     | timestamp                                                            | Yes      | Device timestamp of when the event occurred.                                                               |
| `driver`                          | object (`{ uid: string, displayName?: string, photoURL?: string, email?: string }`) | No       | Details of the driver involved, if applicable.                                                             |
| `trip`                            | object (`{ id: string, startLocationName?: string, arrivalLocationName?: string, passenger?: object }`) | No       | Basic details of the trip involved, if applicable.                                                         |
| `reason`                          | string                                                               | No       | Custom text reason associated with the event.                                                              |
| `serviceStatusReasonType`         | enum (`ServiceStatusReasonType`)                                     | No       | Specific reason type if `type` is `driverServiceStatusUpdate`.                                             |
| `tripRejectionReasonType`         | enum (`TripRejectionReasonType`)                                     | No       | Specific reason type if `type` is `driverTripRejected`.                                                    |
| `metadata`                        | map                                                                  | No       | Any additional relevant data for the event.                                                                |

---

### 14. `Feedback` (Firestore Collection: `/tenants/{tenantId}/feedbacks`) - Tenant-Specific

Stores feedback from users about trips or the application within a specific tenant.

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `id` (document ID)                | string                                                               | Yes      | Unique ID for the feedback.                                                                                |
| `userId`                          | string                                                               | Yes      | UID of the user who submitted the feedback.                                                                |
| `userDisplayName`                 | string                                                               | No       | Display name of the user at the time of submission.                                                        |
| `userEmail`                       | string                                                               | No       | Email of the user at the time of submission.                                                               |
| `type`                            | enum (`trip`, `application`)                                         | Yes      | Type of feedback.                                                                                          |
| `tripId`                          | string                                                               | No       | ID of the associated trip, if `type` is `trip`.                                                            |
| `rating`                          | number (int)                                                         | No       | Rating from 1 to 5, if `type` is `trip`.                                                                   |
| `comment`                         | string                                                               | No       | User's textual feedback or comments.                                                                       |
| `screenshotURL`                   | string                                                               | No       | URL to an uploaded screenshot, if `type` is `application`.                                                 |
| `deviceInfo`                      | map                                                                  | No       | Information about the user's device (e.g., OS, app version, model).                                        |
| `createdAt`                       | timestamp                                                            | Yes      | Timestamp of when the feedback was submitted.                                                              |
| `status`                          | enum (`new`, `seen`, `addressed`, `archived`)                        | Yes      | Current status of the feedback. Default: `new`.                                                            |

---

### 15. `AdminNotification` (Firestore Collection: `/tenants/{tenantId}/admin_notifications`) - Tenant-Specific

Stores records of notifications sent to admins within a specific tenant.

**Note**: Mobile user notifications are handled via FCM and don't require persistent storage.

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `id` (document ID)                | string                                                               | Yes      | Unique ID for the notification.                                                                            |
| `recipientUid`                    | string                                                               | Yes      | UID of the user (admin or mobile user) who is the recipient.                                               |
| `recipientType`                   | enum (`admin`, `mobile_user`)                                        | Yes      | Type of the recipient.                                                                                     |
| `type`                            | enum (e.g., `trip_reserved_admin`, `feedback_created_admin`, `passenger_driver_approaching`) | Yes      | Specific type of notification content/event.                                                               |
| `title`                           | string                                                               | Yes      | Notification title.                                                                                        |
| `body`                            | string                                                               | Yes      | Notification body/message.                                                                                 |
| `data`                            | map                                                                  | No       | Additional data payload (e.g., `{ "tripId": "xyz", "feedbackId": "abc" }`).                                |
| `createdAt`                       | timestamp                                                            | Yes      | Timestamp of when the notification was created/queued.                                                     |
| `sentAt`                          | timestamp                                                            | No       | Timestamp of when the notification was successfully sent.                                                  |
| `readAt`                          | timestamp                                                            | No       | Timestamp of when the recipient read the notification (if trackable).                                      |
| `status`                          | enum (`pending`, `sent`, `delivered`, `read`, `failed`)              | Yes      | Current status of the notification. Default: `pending`.                                                    |
| `channel`                         | enum (`fcm`, `whatsapp`, `in_app`)                                   | Yes      | Channel through which the notification was sent.                                                           |
| `errorMessage`                    | string                                                               | No       | Error message if the notification failed to send.                                                          |

---

### 16. `MobileUserNotification` (Firestore Collection: `/tenants/{tenantId}/mobile_user_notifications`) - Tenant-Specific

Stores meaningful notifications sent to mobile users (drivers and passengers) that they can view and manage in the app.

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `id` (document ID)                | string                                                               | Yes      | Unique ID for the notification (auto-generated).                                                           |
| `recipientUID`                    | string                                                               | Yes      | UID of the mobile user who should receive this notification.                                               |
| `type`                            | enum (see NotificationType below)                                    | Yes      | Type of notification (e.g., `driver_moving`, `trip_paid`, `driver_timeout`).                              |
| `title`                           | string                                                               | Yes      | Notification title (localized).                                                                            |
| `body`                            | string                                                               | Yes      | Notification body/message (localized).                                                                     |
| `createdAt`                       | timestamp                                                            | Yes      | Timestamp when the notification was created (serverTimestamp).                                             |
| `readAt`                          | timestamp                                                            | No       | Timestamp when the notification was marked as read (null if unread).                                       |
| `isRead`                          | boolean                                                              | Yes      | Whether the notification has been read (computed from readAt).                                             |
| `data`                            | map                                                                  | Yes      | Additional structured data for deep linking and context.                                                   |
| `tripId`                          | string                                                               | No       | ID of the related trip (if applicable).                                                                    |
| `clickAction`                     | string                                                               | No       | App route for navigation when notification is tapped.                                                      |
| `fcmMessageId`                    | string                                                               | No       | FCM message ID for tracking delivery.                                                                      |
| `isDismissed`                     | boolean                                                              | Yes      | Whether the system notification was dismissed.                                                             |

**NotificationType values:**
- `driver_moving` - Driver has started moving towards pickup
- `driver_arrived` - Driver has arrived at pickup location
- `trip_paid` - Trip payment has been completed
- `reservation_reminder` - Reminder for upcoming reserved trip
- `driver_timeout` - Driver did not respond to trip request
- `document_expiry` - Driver document is expired or expiring soon
- `trip_request` - New trip request for driver
- `trip_cancelled` - Trip has been cancelled
- `trip_completed` - Trip has been completed
- `system_update` - System updates or announcements
- `general` - General notifications

---

### 17. `ChatThread` (Firestore Collection: `/tenants/{tenantId}/chat_threads`) - Tenant-Specific

Represents a conversation thread between participants within a specific tenant.

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `id` (document ID)                | string                                                               | Yes      | Unique ID for the chat thread.                                                                             |
| `participants`                    | array of string                                                      | Yes      | Array of UIDs of the participants in the chat (e.g., [passengerUid, adminUid]).                            |
| `participantInfo`                 | map (`{ userId: { displayName: string, photoURL?: string } }`)       | Yes      | Basic info of participants for quick display.                                                              |
| `lastMessageText`                 | string                                                               | No       | Text of the last message for preview.                                                                      |
| `lastMessageAt`                   | timestamp                                                            | No       | Timestamp of the last message sent in the thread.                                                          |
| `lastMessageSenderUid`            | string                                                               | No       | UID of the sender of the last message.                                                                     |
| `unreadCounts`                    | map (`{ userId: number }`)                                           | No       | Map of userId to their unread message count in this thread.                                                |
| `tripId`                          | string                                                               | No       | ID of the trip this chat is related to, if any.                                                            |
| `feedbackId`                      | string                                                               | No       | ID of the feedback this chat originated from, if any.                                                      |
| `createdAt`                       | timestamp                                                            | Yes      | Timestamp of when the chat thread was created.                                                             |
| `updatedAt`                       | timestamp                                                            | Yes      | Timestamp of the last activity in the thread (e.g., new message, read status change).                       |
| `isArchived`                      | map (`{ userId: boolean }`)                                          | No       | Indicates if a participant has archived the thread.                                                        |
| `type`                            | enum (`direct`, `trip_related`, `feedback_related`)                  | Yes      | Type of chat thread.                                                                                       |

**Subcollection: `ChatMessage`** (under `/tenants/{tenantId}/chat_threads/{threadId}/messages`)

| Field                             | Type                                                                 | Required | Description                                                                                                |
| :-------------------------------- | :------------------------------------------------------------------- | :------- | :--------------------------------------------------------------------------------------------------------- |
| `id` (document ID)                | string                                                               | Yes      | Unique ID for the chat message.                                                                            |
| `threadId`                        | string                                                               | Yes      | ID of the parent chat thread.                                                                              |
| `senderUid`                       | string                                                               | Yes      | UID of the message sender.                                                                                 |
| `senderType`                      | enum (`passenger`, `driver`, `admin`)                                | Yes      | Type of the sender.                                                                                        |
| `text`                            | string                                                               | No       | Text content of the message.                                                                               |
| `imageUrl`                        | string                                                               | No       | URL of an image sent in the message.                                                                       |
| `timestamp`                       | timestamp                                                            | Yes      | Server timestamp of when the message was sent.                                                             |
| `readBy`                          | map (`{ userId: timestamp }`)                                        | No       | Map of user UIDs to the timestamp when they read this message.                                             |
| `isSystemMessage`                 | boolean                                                              | No       | Indicates if this is an automated system message (e.g., "Feedback submitted"). Default: `false`.           |

---

## Multi-Tenant Architecture Summary

### Global Collections (Shared Across All Tenants)
- `mobile_users` - User accounts with tenant-specific states in subcollections
- `admin_users` - Admin accounts with tenant-specific access in subcollections
- `tenants` - Registry of all tenant organizations
- `vehicles` - Global vehicle data (base information)
- `vehicle_assignments` - Assignment history across all tenants
- `global_configurations` - App-wide settings (version requirements, maintenance)

### Tenant-Specific Collections (Under `/tenants/{tenantId}/`)
- `configurations` - Tenant-specific settings
- `trips` - Trip data for the tenant
- `payments` - Payment transactions for the tenant
- `event_logs` - Audit logs for the tenant
- `feedbacks` - User feedback for the tenant
- `chat_threads` - Chat conversations for the tenant
- `admin_notifications` - Admin notifications for the tenant
- `mobile_user_notifications` - Mobile user notifications for the tenant
- `vehicles_linking` - Vehicle-tenant relationships and approvals

### Vehicle Management: Two-Layer System
1. **`vehicles_linking`** (Tenant-Specific): Manages vehicle availability and approval per tenant
2. **`vehicle_assignments`** (Global): Historical audit trail of all assignments

### Key Multi-Tenant Principles
- Mobile apps are hardcoded to one tenant (`TenantConfig.TENANT_ID`)
- Admin panel allows switching between tenants (based on `TenantAccess`)
- All Firebase Functions accept `tenantId` parameter
- Security rules enforce tenant isolation
- SUPER_ADMIN role (2) only exists in 'fiaranow' tenant
- Other tenants use ADMIN (1) and MANAGER (0) roles only

---

## Enum Definitions (Conceptual)

These enums are used within the models above and represent the possible string values for certain fields.

*   **`UserType`**: `rider`, `driver`
*   **`TripStatus`**: `preparing`, `requestingDriver`, `reserved`, `driverApproaching`, `driverAwaiting`, `inProgress`, `completed`, `cancelled`, `paid`
*   **`PaymentMethod`**: `cash`, `mobile`, `card`, `balance`, `other`
*   **`PaymentStatus`**: `pending`, `processing`, `receivedByDriver`, `completed`, `failed`, `refunded`, `partiallyRefunded`, `disputed`
*   **`EventLogType`**: `driverServiceStatusUpdate`, `driverTripRejected`, `driverRequestTimeout`, `adminNotificationSent`, `feedbackReceived`, `clientTripCancellation` *(add more as needed)*
*   **`ServiceStatusReasonType`**: `morningServiceStart`, `eveningServiceStart`, `lunchBreak`, `prayerBreak`, `fuelRefill`, `vehicleMaintenance`, `endOfShift`, `emergencyStop`, `switchActivity`, `appRelaunch`, `custom`
*   **`TripRejectionReasonType`**: `vehicleMalfunction`, `tooFarPickup`, `heavyTraffic`, `unsafeArea`, `endingShiftSoon`, `vehicleCleaning`, `passengerCapacityFull`, `batteryLow`, `weatherConditions`, `custom`
*   **`InTakeSource`**: `immediate`, `reservation`
*   **`ReservationType`**: `scheduled`, `fullDay`
*   **`FullDayPriceType`**: `fixed`, `gasExcluded`
*   **`NotificationRecipientType`**: `admin`, `mobile_user`
*   **`NotificationType`**: `trip_reserved_admin`, `feedback_created_admin`, `trip_cancelled_admin`, `passenger_driver_approaching`, `passenger_driver_arrived`, `driver_new_trip_request`, `driver_document_expiring`, `driver_document_expired`, `driver_document_approved`, `driver_document_rejected` *(add more as needed)*
*   **`NotificationChannel`**: `fcm`, `whatsapp`, `in_app`
*   **`NotificationSendStatus`**: `pending`, `sent`, `delivered`, `read`, `failed`
*   **`FeedbackType`**: `trip`, `application`
*   **`FeedbackStatus`**: `new`, `seen`, `addressed`, `archived`
*   **`ChatParticipantType`**: `passenger`, `driver`, `admin`
*   **`ChatThreadType`**: `direct`, `trip_related`, `feedback_related`
*   **`PhoneNumberVerificationMethod`**: `sms`, `whatsapp`
*   **`DriverType`**: `internal`, `third_party`
*   **`DriverDocumentType`**: `license`, `insurance`, `vehicle_registration`, `national_id`, `other`
*   **`DriverDocumentStatus`**: `pending_review`, `approved`, `rejected`, `expired`, `expiring_soon`
*   **`FinalCostCalculationMethod`**: `estimated_distance`, `estimated_duration_plus_congestion_fee`, `full_day_fixed`, `full_day_gas_excluded`
*   **`AdminRole`**: `0` (MANAGER), `1` (ADMIN), `2` (SUPER_ADMIN - fiaranow only)
*   **`VehicleAssignmentReason`**: `admin_assignment`, `driver_switch`, `vehicle_maintenance`, `driver_unavailable`

---

## Driver Availability Rules

### Document Requirements (Ideal Path)

For optimal compliance and operation, drivers should have:

1. **Driver Documents** (in `mobile_users/{uid}/driver_documents`):
   - `national_id` - National ID card (approved status)
   - `drivers_license` - Valid driver's license (approved status)

2. **Vehicle Documents** (in `vehicles/{vehicleId}/vehicle_documents`):
   - `vehicle_registration` - Vehicle registration (approved status)
   - `vehicle_insurance` - Valid insurance (approved status)

When all documents are present and approved, the driver is fully compliant and can accept trips.

### Activated Vehicle Override (Fallback Path)

**Business Rule**: Document requirements can be **BYPASSED** as a fallback when:
- The vehicle has `tenantApproved = true` in the `vehicles_linking` collection
- The driver is assigned to this approved vehicle (`currentDriverId` matches)
- But documents are missing or pending approval

This fallback mechanism ensures:
- ✅ Operations can start immediately after vehicle activation
- ✅ Document collection happens in parallel without blocking service
- ✅ Tenant administrators maintain control over their fleet
- ⚠️ Drivers are encouraged to complete documentation for full compliance

**Priority Order**:
1. **IDEAL**: Valid documents + Activated vehicle = Fully compliant driver ✅
2. **FALLBACK**: No/pending documents + Activated vehicle = Operational driver ⚠️
3. **BLOCKED**: No/pending documents + Non-activated vehicle = Cannot accept trips ❌

The system always checks documents first and only uses vehicle activation as a fallback.
