# Fiaranow Development Environment (Docker Compose)

This project now uses Docker Compose v2 for managing the development environment instead of tmux.

## Prerequisites

- Docker Desktop with Docker Compose v2
- Node.js 22+ (for local dependency installation)

## Quick Start

1. **Start all services:**
   ```bash
   docker compose up -d
   ```

2. **View logs:**
   ```bash
   docker compose logs -f
   ```

3. **Stop all services:**
   ```bash
   docker compose down
   ```

## Available Services

| Service | URL | Description |
|---------|-----|-------------|
| Firebase UI | http://localhost:9000 | Firebase Emulator Suite UI |
| Firebase Functions | http://localhost:5001 | Firebase Functions |
| Firestore | http://localhost:8080 | Firestore Emulator |
| Firebase Auth | http://localhost:9099 | Authentication Emulator |
| Admin SvelteKit | http://localhost:5173 | Admin Dashboard |

## Common Commands

### Service Management
```bash
# Start all services
docker compose up -d

# Start with build (when Dockerfiles change)
docker compose up -d --build

# Stop all services
docker compose down

# Restart a specific service
docker compose restart firebase-emulators

# View service status
docker compose ps
```

### Logs and Debugging
```bash
# View logs for all services
docker compose logs -f

# View logs for specific service
docker compose logs -f firebase-emulators

# Execute commands in a running container
docker compose exec admin-sveltekit sh
docker compose exec agent-trigger dart --version
```

### Development Workflow
```bash
# Rebuild and restart after code changes
docker compose up -d --build

# Clean restart (removes volumes)
docker compose down -v && docker compose up -d

# Run agent trigger tool directly (outside Docker)
cd fiaranow_flutter && dart run tool/agent_trigger.dart <VM_SERVICE_URI>
```

## Production Data

To use production backup data:

1. Download production backup:
   ```bash
   cd firebase/backups && ./download-backup.sh
   ```

2. Set environment variable:
   ```bash
   echo "USE_PRODUCTION_DATA=true" > .env
   ```

3. Restart services:
   ```bash
   docker compose down && docker compose up -d
   ```

## Flutter Development

Flutter runs outside of Docker. To develop with Flutter:

1. Start the development environment:
   ```bash
   docker compose up -d
   ```

2. In a separate terminal, run Flutter:
   ```bash
   cd fiaranow_flutter
   fvm flutter run --observe
   ```

3. Use the Agent Trigger tool (run directly):
   ```bash
   # Copy VM Service URI from Flutter output, then:
   cd fiaranow_flutter
   dart run tool/agent_trigger.dart <VM_SERVICE_URI> state
   ```

## Troubleshooting

### Port Conflicts
If you get port conflicts, make sure no other services are running:
```bash
# Check what's using a port
lsof -ti:5173

# Kill processes on specific ports
docker compose down
```

### Container Issues
```bash
# View container logs
docker compose logs [service-name]

# Rebuild containers
docker compose build --no-cache

# Reset everything
docker compose down -v --remove-orphans
docker compose up -d --build
```

### Node Modules Issues
If you encounter node_modules issues:
```bash
# Remove volumes and rebuild
docker compose down -v
docker compose up -d --build
``` 